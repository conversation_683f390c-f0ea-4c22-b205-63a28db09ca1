apply plugin: 'com.android.library'
apply plugin: 'jacoco'

ext {
    VERSION = LINK_SDK_VERSION
    GROUP_ID = "com.autoai.avs.link"
    ARTIFACT_ID = "linksdk"
}

apply from: file('../maven_push.gradle')

android {
    compileSdkVersion rootProject.linkCompileSdkVersion

    defaultConfig {
        minSdkVersion rootProject.linkMinSdkVersion
        targetSdkVersion rootProject.linkTargetSdkVersion

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    testOptions {
        unitTests.all {
            useJUnitPlatform()
            unitTests.returnDefaultValues = true
        }
    }
}

dependencies {
    api project(":wlhardware")
    api project(':linkCommon')

    testImplementation "org.junit.jupiter:junit-jupiter-api:${JUNIT_VERSION}"
    testRuntimeOnly "org.junit.jupiter:junit-jupiter-engine:${JUNIT_VERSION}"
    testImplementation "org.junit.jupiter:junit-jupiter-params:${JUNIT_VERSION}" // 可选，用于参数化测试
    testImplementation "org.mockito:mockito-core:${MOCKITO_VERSION}"
    testImplementation "org.mockito:mockito-inline:${MOCKITO_VERSION}" // 用于模拟 final/static 方法
    testImplementation "org.mockito:mockito-junit-jupiter:${MOCKITO_VERSION}" // 提供 MockitoExtension 支持
}

jacoco {
    toolVersion = "0.8.12" //代码覆盖库jacoco版本号
}
tasks.withType(Test) {
    jacoco.includeNoLocationClasses = true
    jacoco.excludes = ['jdk.internal.*']
}
// 适配多 Flavor 场景， 直接运行 gralde jacocoTestReportT2Debug  即可
android.libraryVariants.configureEach { variant ->
    task "jacocoTestReport${variant.flavorName.capitalize()}${variant.buildType.name.capitalize()}"(type: JacocoReport,
            dependsOn: ["test${variant.flavorName.capitalize()}${variant.buildType.name.capitalize()}UnitTest"]) {
        group = 'Reporting'
        description = 'Generate Jacoco coverage reports after running tests.'
        reports {
            xml.required = true
            csv.required =  false
        }
        def javaClasses = fileTree(dir: "build/intermediates/javac", excludes: ['**/R.class', '**/R$*.class', '**/*$ViewInjector*.*', '**/BuildConfig.*'])
        def kotlinClasses = fileTree(dir: "build/tmp/kotlin-classes/", excludes: ['**/R.class', '**/R$*.class', '**/*$ViewInjector*.*', '**/BuildConfig.*'])
        classDirectories.setFrom(files([javaClasses], [kotlinClasses]))
        sourceDirectories.setFrom(files('src/main/java'))
        executionData.setFrom(fileTree(dir: "build/", includes: ['**/*.exec']))
    }
}