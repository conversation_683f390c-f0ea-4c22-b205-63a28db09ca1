package com.autoai.welink;

import com.autoai.common.util.LogUtil;
import com.autoai.welink.jni.JniApi;
import com.autoai.welink.jni.JniStruct.strDeviceInfo;
import com.autoai.welink.jni.JniStruct.strJniCmd;
import com.autoai.welink.jni.JniStruct.strJniConfig;
import com.autoai.welink.jni.JniStruct.strJniPara;
@SuppressWarnings("all")
public class JniInterface {

    private static final String TAG = JniInterface.class.getSimpleName();
    private static JniInterface jniInterface;
    private JniApi api = new JniApi();

    public static synchronized JniInterface getInstance() {
        if(null == jniInterface){
            jniInterface = new JniInterface();
        }
        return jniInterface;
    }

    public void nativeInitialize(){
        LogUtil.d(TAG, "nativeInitialize");
        api.NativeInitialize();
    }

    public int setConfig(strJniConfig config){
        LogUtil.d(TAG, "setConfig: strJniConfig=" + config);
        return api.WlSdkSetConfig(config);
    }

    public int init(strJniPara para){
        LogUtil.d(TAG, "init: strJniPara=" + para);
        return api.SdkInit(para);
    }

    public void cmd(strJniCmd cmd){
        LogUtil.d(TAG, "cmd: strJniCmd=" + cmd.cmd);
        api.WlSdkCmd(cmd);
    }

    public int exit(){
        LogUtil.d(TAG, "exit");
        return api.WlSdkExit();
    }

    @Deprecated
    public int keyEvent(int key){
        LogUtil.d(TAG, "keyEvent： key=" + key);
        return api.WlSdkKeyEvent(key);
    }

    @Deprecated
    public int touchEvent(int x,int y,int type){
        LogUtil.d(TAG, "touchEvent： x=" + x + ", y=" + y + ", type=" + type);
        return api.WlSdkTouchEvent(x,y,type);
    }

    @Deprecated
    public int sendPoint(int x,int y,int id,int presure,int type){
        LogUtil.d(TAG, "sendPoint： x=" + x + ", y=" + y + ", id=" + id + ", presure=" + presure + ", type=" + type);
        return api.WlSdkSendPoint(x,y,id,presure,type);
    }

    @Deprecated
    public int sendPointSync(){
        LogUtil.d(TAG, "sendPointSync");
        return api.WlSdkSendPointSync();
    }

    public int deviceChange(int cmd, int para1, int para2, strDeviceInfo info){
        LogUtil.d(TAG, "deviceChange: cmd=" + cmd + ", para1=" + para1 + "para2" + para2);
        return api.WlSdkDeviceChange(cmd,para1,para2,info);
    }

    public int setFps(int fps){
        LogUtil.d(TAG, "setFps: fps=" + fps);
        return api.WlSdkSetFps(fps);
    }

    public int sendJstr(byte[] jstr,int len){
        LogUtil.d(TAG, "sendJstr: len=" + len);
        return api.WlSdkSendJstr(jstr,len);
    }

    @Deprecated
    public int sendRecord(byte[] data,int len){
        LogUtil.d(TAG, "sendRecord: len=" + len);
        return  api.WlSdkSendRecord(data,len);
    }

    @Deprecated
    public int sendUpdate(int type, byte[] data, int len){
        LogUtil.d(TAG, "sendUpdate: type=" + type + ", len=" + len);
        return api.WlSdkSendUpdate(type,data,len);
    }

    @Deprecated
    public int sendAudio(int type, byte[] data, int len){
        LogUtil.d(TAG, "sendAudio: type=" + type + ", len=" + len);
        return api.WlSdkSendAudio(type,data,len);
    }

    @Deprecated
    public int sendHuData(int type, byte[] data, int len){
        LogUtil.d(TAG, "sendHuData: type=" + type + ", len=" + len);
        return api.WlSdkSendHuData(type, data, len);
    }

    @Deprecated
    public byte[] getVersion(){
        LogUtil.d(TAG, "getVersion");
        return api.WlSdkGetVersion();
    }
}
