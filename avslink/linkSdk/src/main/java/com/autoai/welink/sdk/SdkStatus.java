package com.autoai.welink.sdk;


import com.autoai.welink.JniInterface;
import com.autoai.common.util.LogUtil;
import com.autoai.welink.SdkInteraction;
import com.autoai.welink.SdkInterface;
import com.autoai.welink.jni.Enum.WL_JNI_STATUS;
import com.autoai.welink.jni.Enum.WL_JNI_TOUCH_EVENT;
import com.autoai.welink.macro.WL_API_BUTTON_TYPE;
import com.autoai.welink.macro.WL_API_PAGE_TYPE;
import com.autoai.welink.macro.WL_API_SDK_MSG_TYPE;
import com.autoai.welink.macro.WL_API_TOUCH_TYPE;
import com.autoai.welink.sdk.Enum.WL_SDK_BTN_BAR_TYPE;
import com.autoai.welink.sdk.Enum.WL_SDK_MSG_STATUS;
import com.autoai.welink.sdk.Enum.WL_SDK_WC_RESPONSE_TYPE;
import com.autoai.welink.sdk.util.PointChg;

import org.json.JSONObject;
@SuppressWarnings("all")
public class SdkStatus {
    private static final String TAG = "WeLink";
    private JniInterface mJniInterface = JniInterface.getInstance();
    private SdkPara mSdkPara = SdkPara.getInstance();
    private SdkCmd mSdkCmd = SdkCmd.getInstance();
    private SdkInteraction mSdkInteraction = SdkInteraction.getInstance();
    private SdkInfo mSdkInfo = SdkInfo.getInstance();
    private final SdkJson mSdkJson = SdkJson.getInstance();

    private static final PointChg mPoint = PointChg.getInstance();
    private static int sPointDownFlag = 0;
    private static int sHelpBackFlag = 0;
    private static final int[] sDownFlag = new int[2];

    private SdkStatus() {
    }

    private static SdkStatus instance;

    public static synchronized SdkStatus getInstance() {
        if (instance == null) {
            instance = new SdkStatus();
        }
        return instance;
    }

    void touchResponse(int btn, int para) {
        LogUtil.v(TAG, "touchResponse:btn = " + btn);
        switch (btn) {
            case WL_API_BUTTON_TYPE.HOME:
                break;
            case WL_API_BUTTON_TYPE.LAUNCHER:
                if (WL_API_PAGE_TYPE.RUNNING_PAGE == mSdkPara.getCurPage()) {
                    sendJsonMsg(mSdkJson.getOnHuChannel(WL_SDK_BTN_BAR_TYPE.LAUNCHER));
                }
                break;
            case WL_API_BUTTON_TYPE.NAVI:
                if (WL_API_PAGE_TYPE.RUNNING_PAGE == mSdkPara.getCurPage()) {
                    sendJsonMsg(mSdkJson.getOnHuChannel(WL_SDK_BTN_BAR_TYPE.NAVI));
                }
                break;
            case WL_API_BUTTON_TYPE.PHONE:
                if (WL_API_PAGE_TYPE.RUNNING_PAGE == mSdkPara.getCurPage()) {
                    sendJsonMsg(mSdkJson.getOnHuChannel(WL_SDK_BTN_BAR_TYPE.PHONE));
                }
                break;
            case WL_API_BUTTON_TYPE.MUSIC:
                if (WL_API_PAGE_TYPE.RUNNING_PAGE == mSdkPara.getCurPage()) {
                    sendJsonMsg(mSdkJson.getOnHuChannel(WL_SDK_BTN_BAR_TYPE.MUSIC));
                }
                break;
            case WL_API_BUTTON_TYPE.MSG:
                if (WL_API_PAGE_TYPE.RUNNING_PAGE == mSdkPara.getCurPage()) {
                    sendJsonMsg(mSdkJson.getOnHuChannel(WL_SDK_BTN_BAR_TYPE.MSG));
                }
                break;
            case WL_API_BUTTON_TYPE.MORE:
                if (WL_API_PAGE_TYPE.RUNNING_PAGE == mSdkPara.getCurPage()) {
                    sendJsonMsg(mSdkJson.getOnHuChannel(WL_SDK_BTN_BAR_TYPE.MORE));
                }
                break;
            case WL_API_BUTTON_TYPE.MIC:
                if (WL_API_PAGE_TYPE.RUNNING_PAGE == mSdkPara.getCurPage()) {
                    sendJsonMsg(mSdkJson.getOnHuChannel(WL_SDK_BTN_BAR_TYPE.MIC));
                }
                break;
            case WL_API_BUTTON_TYPE.ERR_CLOSE:
                mSdkCmd.disconnectWelink();
                break;
            case WL_API_BUTTON_TYPE.GOT_IT:
                Object obj = null;
                if(0 == para) {
                    obj = (Object)1;
                }
                mSdkInteraction.updatePage(WL_API_PAGE_TYPE.INITIAL_PAGE, obj);
                break;
            case WL_API_BUTTON_TYPE.CLICK_FOR_HELP:
                sHelpBackFlag = mSdkPara.getCurPage();
                mSdkInteraction.updatePage(WL_API_PAGE_TYPE.HELP_PAGE, SdkInterface.getDeviceType());
                break;
            case WL_API_BUTTON_TYPE.HELP_BACK:
                mSdkInteraction.updatePage(sHelpBackFlag, null);
                break;
            case WL_API_BUTTON_TYPE.LIMIT_CLOSE:
                break;
            case WL_API_BUTTON_TYPE.WECHAT_QRCODE_CLOSE:
            case WL_API_BUTTON_TYPE.WECHAT_CANCEL_LOGIN:
                mSdkInteraction.updatePage(WL_API_PAGE_TYPE.RUNNING_PAGE, null);
                sendJsonMsg(mSdkJson.getOnWeChatResponse(WL_SDK_WC_RESPONSE_TYPE.NOT_SHOW));
                break;
            case WL_API_BUTTON_TYPE.OFFICIAL_ACCOUNT_SKIP:
                mSdkCmd.startWelink();
                break;
            default:
                break;
        }

    }

    private void sendJsonMsg(JSONObject obj) {
        String str = obj.toString();
        mJniInterface.sendJstr(str.getBytes(), str.length());
    }

    void setWeLinkForeground() {
        if (WL_JNI_STATUS.ERR == mSdkPara.getSdkStatus()) {
            mSdkCmd.usbReset();
        }
        if (1 == mSdkPara.getAudioRecord()) {
            mSdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.START_RECORD, null);
        }
        mSdkInfo.setWeLinkForeground();
        mSdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.REQ_BT_ADDR, null);
        postStatus(mSdkPara.getSdkStatus());
        sendOnHuAppFront(mSdkInfo.isWeLinkForeground());
//        mSdkPara.setBeginRunFlag(1);
    }

    void setWeLinkBackground() {
        // wl_printf(WL_LOG_TYPE_NORMAL, __FUNCTION__, __LINE__, "###########source off reset frame count!!!\r\n");
        //  s_iFrameCount = 0;
        mSdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.STOP_RECORD, null);
        mSdkInfo.setWeLinkBackground();
        sendOnHuAppFront(mSdkInfo.isWeLinkForeground());
//        mSdkPara.setBeginRunFlag(0);
    }

    @Deprecated
    int sendPoint(int x, int y, int press, int id, int type) {
        int videoX = mSdkInfo.getScreenX();
        int videoY = mSdkInfo.getScreenY();
        int videoWidth = mSdkInfo.getScreenWidth();
        int videoHeight = mSdkInfo.getScreenHeight();
        LogUtil.v(TAG, "videoX:" + videoX + "videoY:" + videoY + "videow:" + videoWidth + "videoH:" + videoHeight);
        int ret = -1;
        if (0 > id || 1 < id) {
            return ret;
        }
        if (WL_API_PAGE_TYPE.RUNNING_PAGE == mSdkPara.getCurPage()) {
            if (videoX > x || x > (videoX + videoWidth) || videoY > y || y > (videoY + videoHeight)) {
                if (sDownFlag[id] != 0) {
                    sDownFlag[id] = 0;
                    ret = mJniInterface.sendPoint(x - videoX, y - videoY, id, press, WL_API_TOUCH_TYPE.UP);
                }
                return ret;
            }
            switch (type) {
                case WL_API_TOUCH_TYPE.DOWN:
                    sDownFlag[id] = 1;
                    ret = mJniInterface.sendPoint(x - videoX, y, id, press, type);
                    break;
                case WL_API_TOUCH_TYPE.UP:
                    sDownFlag[id] = 0;
                    ret = mJniInterface.sendPoint(x - videoX, y, id, press, type);
                    break;
                case WL_API_TOUCH_TYPE.MOVE:
                    if (0 == sDownFlag[id]) {
                        ret = mJniInterface.sendPoint(x - videoX, y, id, press, WL_API_TOUCH_TYPE.DOWN);
                    } else {
                        ret = mJniInterface.sendPoint(x - videoX, y, id, press, type);
                    }
                    sDownFlag[id] = 1;
                    break;
                default:
                    ret = -2;//WL_ERR_PARAM
                    break;
            }

        }
        return ret;
    }

    int touchEvent(int x, int y, int type) {
        int ret = -1;
        ret = mPoint.setPoint(x, y, SdkInterface.getDeviceType());
        int chg_x = mPoint.getX();
        int chg_y = mPoint.getY();

        if (WL_API_PAGE_TYPE.RUNNING_PAGE == mSdkPara.getCurPage()) {
            switch (type) {
                case WL_JNI_TOUCH_EVENT.DOWN:
                    sPointDownFlag = 1;
                    ret = mJniInterface.touchEvent(chg_x, chg_y, type);
                    break;
                case WL_JNI_TOUCH_EVENT.UP:
                    sPointDownFlag = 0;
                    ret = mJniInterface.touchEvent(chg_x, chg_y, type);
                    break;
                case WL_JNI_TOUCH_EVENT.MOVE:
                    if (0 == sPointDownFlag) {
                        ret = mJniInterface.touchEvent(chg_x, chg_y, WL_JNI_TOUCH_EVENT.DOWN);
                    } else {
                        ret = mJniInterface.touchEvent(chg_x, chg_y, type);
                    }
                    sPointDownFlag = 1;
                    //SdkCode.gJniApi.WlSdkTouchEvent(chg_x,chg_y,type);
                    break;
                default:
                    ret = -2;//WL_ERR_PARAM
                    break;
            }
        }
        return ret;
    }

    private static void postStatus(int status) {
        switch (status) {
            case WL_JNI_STATUS.NONE:
                sendMsgToCar(WL_SDK_MSG_STATUS.WELINK_STATUS_NONE);
                break;
            case WL_JNI_STATUS.CONNECTING:
                sendMsgToCar(WL_SDK_MSG_STATUS.WELINK_CONNECTING);
                break;
            case WL_JNI_STATUS.CONNECTED:
            case WL_JNI_STATUS.RUNNING:
                sendMsgToCar(WL_SDK_MSG_STATUS.WELINK_CONNECTED);
                break;
            case WL_JNI_STATUS.ERR:
                sendMsgToCar(WL_SDK_MSG_STATUS.WELINK_CONNECT_ERR);
                break;
            default:
                break;
        }
    }

    private void sendOnHuAppFront(boolean isWeLinkForeground) {
        String str = mSdkJson.getOnHuAppFront(isWeLinkForeground).toString();
        LogUtil.d(TAG, "sendOnHuAppFront:"+str);
        mJniInterface.sendJstr(str.getBytes(), str.length());
    }

    private static void sendMsgToCar(int msg) {
    }

    /////////////////////////send json to sdk///////////////////////
    void sendAudioExit() {
        String str = mSdkJson.getOnExitAudio().toString();
        mJniInterface.sendJstr(str.getBytes(), str.length());
    }

    void sendAudioResume() {
        String str = mSdkJson.getOnResumeAudio().toString();
        mJniInterface.sendJstr(str.getBytes(), str.length());
    }

    void sendMusicStop(){
        String str = mSdkJson.getOnMusicStop().toString();
        mJniInterface.sendJstr(str.getBytes(), str.length());
    }

    void sendMusicPlay(){
        String str = mSdkJson.getOnMusicPlay().toString();
        mJniInterface.sendJstr(str.getBytes(), str.length());
    }

//    void sendAudioFocusLoss(int channel){
//        String str = mSdkJson.getOnAudioFocus(channel, 1).toString();
//        LogUtil.e("AudioFocus",  "sendAudioFocusLoss = " + str);

//        mJniInterface.sendJstr(str.getBytes(), str.length());
//    }

//    void sendAudioFocusGain(int channel){
//        String str = mSdkJson.getOnAudioFocus(channel, 2).toString();
//        LogUtil.e("AudioFocus",  "sendAudioFocusGain = " + str);
//        mJniInterface.sendJstr(str.getBytes(), str.length());
//    }
//    void sendOnControlEnAndCh(int languageType) {
//        int type = -1;
//        switch (languageType) {
//            case 0:
//                type = WL_SDK_LANGUAGE_TYPE.CH;
//                break;
//            case 1:
//                type = WL_SDK_LANGUAGE_TYPE.EN;
//                break;
//            default:
//                break;
//        }
//        String str = mSdkJson.getOnControlEnAndCh(type).toString();
//        mJniInterface.sendJstr(str.getBytes(), str.length());
//    }

	void sendBtPhoneState(int btPhoneState){
        String str = mSdkJson.getOnHuBTPhone(btPhoneState).toString();
        LogUtil.i("BTPhone",  "sendBtPhoneState = " + str);
        mJniInterface.sendJstr(str.getBytes(), str.length());
    }

    void sendHuLocalVrState(int huLocalVrState){
        String str = mSdkJson.getOnHuLocalVrState(huLocalVrState).toString();
        LogUtil.i("LocalVR",  "sendHuLocalVrState = " + str);
        mJniInterface.sendJstr(str.getBytes(), str.length());
    }

    void sendHuReqAppData(int type){
        String str = mSdkJson.getOnHuReqAppData(type).toString();
        LogUtil.i("ReqAppData",  "sendHuReqAppData = " + str);
        mJniInterface.sendJstr(str.getBytes(), str.length());
    }

    void sendHuReqPhoneIsPlayVideo(int type){
        String str = mSdkJson.getOnHuReqPhoneIsPayingVideo(type).toString();
        LogUtil.i("sendHuReqPhoneIsPlayVideo",  "sendHuReqPhoneIsPlayVideo = " + str);
        mJniInterface.sendJstr(str.getBytes(), str.length());
    }
    void sendHuReqKeyFrame(int frame, int time){
        String str = mSdkJson.getOnHuReqKeyFrame(frame,time).toString();
        LogUtil.i("sendHuReqKeyFrame",  "sendHuReqKeyFrame = " + str);
        mJniInterface.sendJstr(str.getBytes(), str.length());
    }

    public void jTtestSetJniInterface(JniInterface jniInterface) {
        this.mJniInterface = jniInterface;
    }

    public void jTestSetSdkPara(SdkPara sdkPara) {
        this.mSdkPara = sdkPara;
    }

    public void jTestSetSdkCmd(SdkCmd cmd) {
        this.mSdkCmd = cmd;
    }

    public void jTestSetSdkInteraction(SdkInteraction sdkInteraction) {
        this.mSdkInteraction = sdkInteraction;
    }

    public void jTestSetSdkInfo(SdkInfo info){
        this.mSdkInfo = info;
    }
}
