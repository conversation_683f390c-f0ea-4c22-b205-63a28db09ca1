package com.autoai.welink.param;

import java.util.Objects;

public class HidTouch {
    public boolean isSupportHID = false;
    public int angle;
    public int h264X;
    public int h264Y;
    public int h264W;
    public int h264H;
    public int videoMode;

    public String platform;
    public final VirtualButton home = new VirtualButton();
    public final VirtualButton back = new VirtualButton();
    public final VirtualButton huHome = new VirtualButton();
    public final VirtualButton phoneSize = new VirtualButton();

    public static class VirtualButton{
        public int x;
        public int y;
        public int w;
        public int h;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof HidTouch)) return false;
        HidTouch hidTouch = (HidTouch) o;
        return isSupportHID == hidTouch.isSupportHID
                && angle == hidTouch.angle
                && h264X == hidTouch.h264X
                && h264Y == hidTouch.h264Y
                && h264W == hidTouch.h264W
                && h264H == hidTouch.h264H;
    }

    @Override
    public int hashCode() {
        return Objects.hash(isSupportHID, angle, h264X, h264Y, h264W, h264H);
    }


    @Override
    public String toString() {
        return "HidTouch{" +
                "isSupportHID=" + isSupportHID +
                ", angle=" + angle +
                ", h264X=" + h264X +
                ", h264Y=" + h264Y +
                ", h264W=" + h264W +
                ", h264H=" + h264H +
                ", home=" + home +
                ", back=" + back +
                ", huHome=" + huHome +
                ",platform="+platform+
                '}';
    }
}
