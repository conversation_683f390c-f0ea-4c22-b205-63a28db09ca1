package com.autoai.welink;

public interface SdkListener {
    /** Notice update page */
    int onUpdatePage(int page, Object para);

    /** Message notice, SDK->APP */
    int onRecvMsg(int msg, Object para);

    /** Audio data callback */
    int onAudioData(byte[] data, int para);

    /** Car data callback */
    int onHuData(byte[] data, int para1, int para2);

    /** Video data callback */
    int onVideoData(byte[] data, int size, int width, int height, int spspps_data_size, int frame_type);

    /** Video data callback */

    int onUpdateData(byte[] data, int len);

}
