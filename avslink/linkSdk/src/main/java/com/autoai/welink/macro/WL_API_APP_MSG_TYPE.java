package com.autoai.welink.macro;

public interface WL_API_APP_MSG_TYPE {
    int FOREGROUND = 1;		    /*< [APP->SDK] Show welink  */
    int BACKGROUND = 2;		    /*< [APP->SDK] Hide welink  */
    int BT_ADDR = 3;			/*< [APP->SDK] Notice bluetooth address  */
    int SET_LANGUAGE = 4;		/*< [APP->SDK] Set language  */
    int SET_DRIVE_STAT = 5;	    /*< [APP->SDK] Set driving status  */
    int AUDIO_EXIT = 6;		    /*< [APP->SDK] WeLink Audio quiet mode  */
    int AUDIO_RESUME = 7;		/*< [APP->SDK] WeLink Audio resume  */
    int SPEECH_RECOGNITION = 8; /*< [APP->SDK] Speech Recognition  */
    int PREVIOUS_TRACK = 9;	    /*< [APP->SDK] Previous track  */
    int NEXT_TRACK = 10;		/*< [APP->SDK] Next track  */
    int MUSIC_STOP = 11;		/** [APP->SDK] Music stop  */
    int MUSIC_PLAY = 12;		/** [APP->SDK] Music play  */
	int HU_BT_PHONE = 13;       /** [APP->SDK] HU bt phone status  */
    int HU_LOCAL_VR_STATE = 14;     /** [APP->SDK] HU local vr status  */
    int HU_REQ_APP_DATA = 15;     /** [APP->SDK] HU req app data  */

    int CAR_DRIVER_STATUS_CHANGE = 16;     /** [APP->SDK]X向手机发送挡位变化的消息  ,*/

    /**
     * 向手机端申请关键帧
     */
    int HU_REQ_KEY_FRAME = 17;     /** [APP->SDK]X向手机请求关键帧消息  ,*/

    int MAX_NUM = 18;



}
