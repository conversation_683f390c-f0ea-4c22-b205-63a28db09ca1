package com.autoai.welink.param;
/**
 * @brief Define the navi guide gAplInfo.
 */
@SuppressWarnings("all")
public class NaviGuide {
   public int		icon;							/*< Icon, See excel document  */
   public int		limitSpeed;						/*< Limit speed  */
   public int		percentToCurrPoint;				/*< Percentage */
   public String    name;							/*< Current street name */
   public String    nextName;						/*< Next street name */
   public String    distanceToCurrPoint;			/*< The distance to the next intersection */
   public String    direction;						/*< Direction */
   public String    remainDistance;				    /*< The remaining distance */
   public String    remainTime;					    /*< The remaining time */
   public String    currSpeed;						/*< Current speed */

}
