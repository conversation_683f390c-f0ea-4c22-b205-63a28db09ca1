package com.autoai.welink.jni.Enum;
@SuppressWarnings("unused")
public interface WL_JNI_MSG_TYPE {
	int UNKNOWN = 0;		/*< Unknown message type */
	int QR_CODE = 1;		/*< QR code */
	int HEAD_PORTRAIT = 2;	/*< Head portrait */
	int LIMIT = 3;	        /*< Limit message */
	int RESUME = 4;        	/*< Resume message */
	int START_RECORD = 5;   /*< Start record */
	int STOP_RECORD = 6;    /*< Stop record */
	int RESOLUTION = 7;		/*< Phone resolution */
	int UPDATE_READY = 8;	/*< Update channel ready */
	int DRIVER_READY = 9;	/*< Driver ready */
	int NUM = 10;			/*< Max message type number */
}
