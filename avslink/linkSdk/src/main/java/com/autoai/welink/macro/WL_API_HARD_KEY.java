package com.autoai.welink.macro;
@SuppressWarnings("unused")
public interface WL_API_HARD_KEY {
     int  WL_API_HARD_KEY_RECORD = 1;				/*< Hard key recorde  */
     int  WL_API_HARD_KEY_PRE = 2;					/*< Hard key previous track  */
     int  WL_API_HARD_KEY_NEXT = 3;					/*< Hard key next track  */
     int  WL_API_HARD_KEY_VR_START = 4;             /*< Hard key VR start  */
     int  WL_API_HARD_KEY_VR_END = 5;               /*< Hard key VR end  */
     int  WL_API_HARD_KEY_NUM = 6;					/*< Hard key max num, do not care  */
}
