package com.autoai.welink.sdk;

import com.autoai.welink.JniInterface;
import com.autoai.common.util.LogUtil;
import com.autoai.welink.SdkInteraction;
import com.autoai.welink.jni.Enum.WL_JNI_DEVICE_TYPE;
import com.autoai.welink.jni.Enum.WL_JNI_ERR_CODE;
import com.autoai.welink.jni.Enum.WL_JNI_MSG_TYPE;
import com.autoai.welink.jni.Enum.WL_JNI_STATUS;
import com.autoai.welink.macro.WL_API_BUTTON_TYPE;
import com.autoai.welink.macro.WL_API_H264_FREAM_TYPE;
import com.autoai.welink.macro.WL_API_PAGE_TYPE;
import com.autoai.welink.macro.WL_API_SDK_MSG_TYPE;
import com.autoai.welink.param.HidMessage;
import com.autoai.welink.param.HidTouch;
import com.autoai.welink.param.NaviGuide;
import com.autoai.welink.param.TurnByTurnInfo;
import com.autoai.welink.sdk.Enum.WL_SDK_BTN_BAR_TYPE;
import com.autoai.welink.sdk.Enum.WL_SDK_LAUNCHER_MSG;
import com.autoai.welink.sdk.Enum.WL_SDK_WC_RESPONSE_TYPE;

import org.json.JSONObject;

import java.util.List;


@SuppressWarnings("all")
public class SdkPctl {
    private static final String TAG = "SdkPctl";
    private static final String TAG_BT = "BTPair";

    private SdkPctl() {
    }

    private static SdkPctl instance;

    public static synchronized SdkPctl getInstance() {
        if (instance == null) {
            instance = new SdkPctl();
        }
        return instance;
    }

    private final JniInterface jniInterface = JniInterface.getInstance();
    private SdkInteraction sdkInteraction = SdkInteraction.getInstance();
    private SdkJson mSdkJson = SdkJson.getInstance();
    private SdkCmd mSdkCmd = SdkCmd.getInstance();
    private SdkPara mSdkPara = SdkPara.getInstance();
    private final SdkInfo mSdkInfo = SdkInfo.getInstance();

    public void launcherDataCb(int msg, byte[] data) {
        LogUtil.v(TAG, "launcherDataCb:" + msg);
        String jstr = new String(data);
        LogUtil.v(TAG, "jstr:" + jstr);
        switch (msg) {
            case WL_SDK_LAUNCHER_MSG.AUTH_RESULT:
                JSONObject authData = mSdkJson.getCustomExtData(jstr);
                sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.RECV_MSG, authData);
                break;
            case WL_SDK_LAUNCHER_MSG.CUSTOM:
                JSONObject extData = mSdkJson.getCustomExtData(jstr);
                sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.RECV_MSG, extData);
                break;
            case WL_SDK_LAUNCHER_MSG.ON_MUCHANNEL:
                int key = mSdkJson.getOnMuChannelExtData(jstr);
                setHighLightKey(key);
                break;
            case WL_SDK_LAUNCHER_MSG.RECEIVE_MSG:
                int type = mSdkJson.getOnRecvMsgExtData(jstr);
                if (WL_API_PAGE_TYPE.RUNNING_PAGE == mSdkPara.getCurPage()) {
                    sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.RECV_MSG, type);
                }
                break;
            case WL_SDK_LAUNCHER_MSG.EXIT_WELINK:
                //launcherExit();
                break;
            case WL_SDK_LAUNCHER_MSG.WECHAT_RESPONSE:
                int code = mSdkJson.getWeChatResponseExtData(jstr);
                if (WL_SDK_WC_RESPONSE_TYPE.LOGIN_SUCCESS == code) {
                    if (0 == mSdkPara.getLimitFlag()) {
                        sdkInteraction.updatePage(WL_API_PAGE_TYPE.RUNNING_PAGE, null);
                    } else {
                        sdkInteraction.updatePage(WL_API_PAGE_TYPE.RUNNING_PAGE_WECHAT_LOGIN_SUCCESS, null);
                    }
                } else if (WL_SDK_WC_RESPONSE_TYPE.SCAN_SUCCESS == code) {
                    sdkInteraction.updatePage(WL_API_PAGE_TYPE.RUNNING_PAGE_WECHAT_CONFIRM_LOGIN, null);
                } else if (WL_SDK_WC_RESPONSE_TYPE.NOT_SHOW == code
                        || WL_SDK_WC_RESPONSE_TYPE.LOGIN_FAILED == code) {
                    sdkInteraction.updatePage(WL_API_PAGE_TYPE.RUNNING_PAGE, null);
                }
                break;
            case WL_SDK_LAUNCHER_MSG.SET_AUTO_CONNECT:
                break;
            case WL_SDK_LAUNCHER_MSG.BT_STATUS:
                int status = mSdkJson.getBluetoothStatus(jstr);
                sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.BT_STATUS, status);
                break;
            case WL_SDK_LAUNCHER_MSG.SCREEN_CHG:
                break;
            case WL_SDK_LAUNCHER_MSG.NOTIFY_GUIDE_INFO:
                if (mSdkPara.getCurPage() == WL_API_PAGE_TYPE.RUNNING_PAGE_LIMIT) {
                    NaviGuide naviInfo = mSdkJson.getNaviGuideExtData(jstr);
                    sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.NAVI_GUIDE_START, naviInfo);
                }
                break;
            case WL_SDK_LAUNCHER_MSG.STOP_GUIDE_INFO:
                sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.NAVI_GUIDE_STOP, null);
                break;
            case WL_SDK_LAUNCHER_MSG.TURN_BY_TURN:
                TurnByTurnInfo turnByTurnInfo = mSdkJson.getTurnByTurnExtData(jstr);
                sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.TURN_BY_TURN, turnByTurnInfo);
                break;
            case WL_SDK_LAUNCHER_MSG.CALL_BY_NUMBER:
                String number = mSdkJson.getCallNumber(jstr);
                sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.CALL_NUMBER, number);
                break;
            case WL_SDK_LAUNCHER_MSG.MUSIC_PLAY_STATE:
                int musicPlayState = mSdkJson.getMusicPlayState(jstr);
                sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.MUSIC_PLAY_STATE, musicPlayState);
                break;
            case WL_SDK_LAUNCHER_MSG.MEDIA_STATE:
                int mediaState = mSdkJson.getMediaState(jstr);
                sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.MEDIA_STATE, mediaState);
                break;
            case WL_SDK_LAUNCHER_MSG.HOME_KEY:
                sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.HOME_KEY, null);
                break;
            case WL_SDK_LAUNCHER_MSG.APPLY_MIC:
                sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.APPLY_MIC, null);
                break;
            case WL_SDK_LAUNCHER_MSG.RELEASE_MIC:
                sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.RELEASE_MIC, null);
                break;
            case WL_SDK_LAUNCHER_MSG.PCM_MUTE_STATE:
                int state = mSdkJson.getPCMMuteState(jstr);
                if (state == 1) {
                    sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.MUTE_START, null);
                } else if (state == 2) {
                    sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.MUTE_STOP, null);
                } else {
                    LogUtil.v(TAG, "Error PCM Mute State");
                }
                break;
            case WL_SDK_LAUNCHER_MSG.PCM_STOP_PLAY:
                int stopState = mSdkJson.getStopPCM(jstr);
                if (stopState >= 0 && stopState < 6) {
                    sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.PCM_STOP_PLAY, stopState);
                } else {
                    LogUtil.v(TAG, "Error PCM Stop Play State");
                }
                break;
            case WL_SDK_LAUNCHER_MSG.REQ_BT_MUSIC_FOCUS:
                int musicFocusState = mSdkJson.getMusicFocus(jstr);
                sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.REQ_BT_MUSIC_FOCUS, musicFocusState);
                break;
            case WL_SDK_LAUNCHER_MSG.RECORDING_SCREEN_STATE:
                int recordingScreenState = mSdkJson.getRecordScrState(jstr);
                sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.RECORDING_SCREEN_STATE, recordingScreenState);
                break;
            case WL_SDK_LAUNCHER_MSG.HID_TOUCH:
                HidTouch hidTouch = mSdkJson.getScreenRotation(jstr);
                sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.HID_TOUCH, hidTouch);
                break;
            case WL_SDK_LAUNCHER_MSG.TRAFFIC_RECORDER_CAPTURE:
                int captureState = mSdkJson.getcaptureState(jstr);
                sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.CAPTURE_STATE, captureState);
                break;
            case WL_SDK_LAUNCHER_MSG.NAVI_STATE:
                int naviState = mSdkJson.getNaviState(jstr);
                sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.NAVI_STATE, naviState);
                break;
//            case WL_SDK_LAUNCHER_MSG.ON_TOUCH_POINT_READY:
//                sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.ON_TOUCH_POINT_READY, 0);
//                break;
//            case WL_SDK_LAUNCHER_MSG.ON_TOUCH_BEGIN_POINT:
//                break;
//            case WL_SDK_LAUNCHER_MSG.ON_TOUCH_END_POINT:
//                sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.ON_TOUCH_END_POINT, 0);
//                break;
            case WL_SDK_LAUNCHER_MSG.ON_HID_TOUCH_POINT:
                HidMessage hidMessage = mSdkJson.getTouchSendPoint(jstr);
                sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.ON_HID_TOUCH_POINT, hidMessage);
                break;
            case WL_SDK_LAUNCHER_MSG.CAR_BT_CONNECTED:
                LogUtil.d(TAG + TAG_BT, "SdkPctl::launcherDataCb <<< received CAR_BT_CONNECTED message");
                // 解析连接状态和手机MAC地址
                CarBTConnectedInfo btInfo = mSdkJson.parseCarBTConnectedInfo(jstr);
                LogUtil.d(TAG + TAG_BT, "SdkPctl::launcherDataCb >>> forwarding to SdkInteraction: " + btInfo.toString());
                sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.CAR_BT_CONNECTED, btInfo);
                break;
            default:
                break;
        }
    }

    public void jniStatusCb(int status, int para1, byte[] para2) {
        switch (status) {
            case WL_JNI_STATUS.NONE:
                sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.STOP_RECORD, null);
                sdkInteraction.updatePage(WL_API_PAGE_TYPE.INITIAL_PAGE, null);
                break;
            case WL_JNI_STATUS.CHECK_DEVICE:
                // sendMsgToCar(WL_APL_MSG_STATUS.WELINK_DEVICE_IN);
                mSdkCmd.startWelink();
                // aplPctl.funUpdatePage(WL_API_PAGE_TYPE.CONNECTING_PAGE_OFFICIAL_ACCOUNT,null);
                break;
            case WL_JNI_STATUS.CONNECTING:
                mSdkPara.setBeginRunFlag(0);
                sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.CONNECTING, null);
                sdkInteraction.updatePage(WL_API_PAGE_TYPE.CONNECTING_PAGE, null);
                //_sendOnHuAppFront(SdkCode.gAplInfo.isWeLinkForeground());
                break;
            case WL_JNI_STATUS.CONNECTED:
                sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.CONNECTED, null);
                if (0 == mSdkPara.getLimitFlag()) {
                    sdkInteraction.updatePage(WL_API_PAGE_TYPE.RUNNING_PAGE, null);
                }
                //mSdkJson.getOnControlEnAndCh(mSdkPara.getLanguageType());
                /*
                _reset_status();
                s_iReConnectIos = 0;
                s_iReConnectAndroid = 0;
                */
                break;
            case WL_JNI_STATUS.RUNNING:
                if (!mSdkInfo.isWeLinkForeground()) {
                    mSdkCmd.sourceOffCmd();
                    mSdkCmd.videoStop();
                }
                //  sendMsgToCar(WL_APL_MSG_STATUS.WELINK_CONNECTED);
                if (0 == mSdkPara.getLimitFlag()) {
                    sdkInteraction.updatePage(WL_API_PAGE_TYPE.RUNNING_PAGE, null);
                }
                break;
            case WL_JNI_STATUS.ERR:
                if (WL_JNI_STATUS.ERR == mSdkPara.getSdkStatus()) {
                    break;
                }
                sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.CONNECT_ERR, null);
                sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.STOP_RECORD, null);
                setError(para1);
                break;
            default:
                break;
        }
        mSdkPara.setSdkStatus(status);
    }

    public void deviceStatusCb(int status) {
        if (status == WL_JNI_DEVICE_TYPE.OUT) {
            sdkInteraction.updatePage(WL_API_PAGE_TYPE.INITIAL_PAGE, null);
            sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.DISCONNECT, null);
            sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.STOP_RECORD, null);
        }
    }

    public void jniMsgCb(byte[] data, int size, int type, int para) {
        switch (type) {
            case WL_JNI_MSG_TYPE.QR_CODE:
                if (data == null) {
                    //Log.v("WeLink","!!!!sdkMsgCb data null!!!!");
                }
                sdkInteraction.updatePage(WL_API_PAGE_TYPE.RUNNING_PAGE_WECHAT_QRCODE, data);
                break;
            case WL_JNI_MSG_TYPE.HEAD_PORTRAIT:
                sdkInteraction.updatePage(WL_API_PAGE_TYPE.RUNNING_PAGE_WECHAT_CONFIRM_LOGIN, null);
                break;
            case WL_JNI_MSG_TYPE.LIMIT:
                mSdkPara.setLimitFlag(1);
                if (mSdkPara.getCurPage() != WL_API_PAGE_TYPE.RUNNING_PAGE_WECHAT_QRCODE) {
                    sdkInteraction.updatePage(WL_API_PAGE_TYPE.RUNNING_PAGE_LIMIT, null);
                }
                break;
            case WL_JNI_MSG_TYPE.RESUME:
                mSdkPara.setLimitFlag(0);
                sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.NAVI_GUIDE_STOP, null);
                sdkInteraction.updatePage(WL_API_PAGE_TYPE.RUNNING_PAGE, null);
                break;
            case WL_JNI_MSG_TYPE.START_RECORD:
                sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.START_RECORD, null);
                mSdkPara.setAudioRecord(1);
                break;
            case WL_JNI_MSG_TYPE.STOP_RECORD:
                sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.STOP_RECORD, null);
                mSdkPara.setAudioRecord(0);
                break;
            case WL_JNI_MSG_TYPE.RESOLUTION:
                if (null == data) {
                    break;
                }
                int width = data[0] & 0xff;
                width |= (data[1] & 0xff) << 8;
                width |= (data[2] & 0xff) << 16;
                width |= (data[3] & 0xff) << 24;

                int height = data[4] & 0xff;
                height |= (data[5] & 0xff) << 8;
                height |= (data[6] & 0xff) << 16;
                height |= (data[7] & 0xff) << 24;

                /*
                int width=data[3] & 0xFF;
                width |=((data[2] <<8)& 0xFF);
                width |=((data[1] <<16)& 0xFF);
                width |=((data[0] <<24)& 0xFF);

                int height=data[7] & 0xFF;
                height |=((data[6] <<8)& 0xFF);
                height |=((data[5] <<16)& 0xFF);
                height |=((data[4] <<24)& 0xFF);
                */
                mSdkPara.setPhoneScreen(width, height);
                break;
            case WL_JNI_MSG_TYPE.DRIVER_READY:
                sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.START_OK, null);
                mSdkPara.setConfigFps(mSdkInfo.getFps());
                // sendMsgToCar(WL_APL_MSG_STATUS.WELINK_STATUS_START_OK);
                // sendMsgToCar(WL_APL_MSG_STATUS.WELINK_REQ_BT_ADDR);
                mSdkCmd.setHUSerialnum();
                jniInterface.setFps(mSdkPara.getConfigFps());
                break;
        }
    }

    public void imageCb(byte[] data, int size, int width, int height, int spspps_data_size, int frame_type) {
        if (0 == mSdkPara.getBeginRunFlag()) {
            if (frame_type == WL_API_H264_FREAM_TYPE.SPS_IDR) {
                mSdkPara.setBeginRunFlag(1);
                sdkInteraction.videoData(data, size, width, height, spspps_data_size, frame_type);
            } else {
                 LogUtil.w(TAG,"First frame type is no sps idr");
            }
        } else {
            sdkInteraction.videoData(data, size, width, height, spspps_data_size, frame_type);
        }
        int fps = mSdkInfo.getFps();
        if (fps != mSdkPara.getConfigFps()) {
            mSdkPara.setConfigFps(fps);
            jniInterface.setFps(mSdkPara.getConfigFps());
        }
    }

    private void setError(int errNo) {
        switch (errNo) {
            case WL_JNI_ERR_CODE.E003:
                sdkInteraction.updatePage(WL_API_PAGE_TYPE.ERR_PAGE_SDK, null);
                break;
            case WL_JNI_ERR_CODE.E401:
                sdkInteraction.updatePage(WL_API_PAGE_TYPE.ERR_PAGE_HEART, null);
                break;
            default:
                sdkInteraction.updatePage(WL_API_PAGE_TYPE.ERR_PAGE_CONNECT, null);
                break;
        }
    }

    private void setHighLightKey(int key) {
        int type = 0;
        switch (key) {
            case WL_SDK_BTN_BAR_TYPE.LAUNCHER:
                type = WL_API_BUTTON_TYPE.LAUNCHER;
                break;
            case WL_SDK_BTN_BAR_TYPE.NAVI:
                type = WL_API_BUTTON_TYPE.NAVI;
                break;
            case WL_SDK_BTN_BAR_TYPE.PHONE:
                type = WL_API_BUTTON_TYPE.PHONE;
                break;
            case WL_SDK_BTN_BAR_TYPE.MUSIC:
                type = WL_API_BUTTON_TYPE.MUSIC;
                break;
            case WL_SDK_BTN_BAR_TYPE.MSG:
                type = WL_API_BUTTON_TYPE.MSG;
                break;
            case WL_SDK_BTN_BAR_TYPE.MIC:
                type = WL_API_BUTTON_TYPE.MIC;
                break;
            case WL_SDK_BTN_BAR_TYPE.MORE:
                type = WL_API_BUTTON_TYPE.MORE;
                break;
            default:
                break;
        }
        sdkInteraction.recvMsg(WL_API_SDK_MSG_TYPE.UPDATE_KEY, type);
    }

    public void jTestSetSdkInteraction(SdkInteraction interaction) {
        this.sdkInteraction = interaction;
    }

    public void jTestSetSdkPara(SdkPara para) {
        this.mSdkPara = para;
    }

    public void jTestSetSdkJson(SdkJson json) {
        this.mSdkJson = json;
    }

    public void jTestSetSdkCmd(SdkCmd cmd) {
        this.mSdkCmd = cmd;
    }
}
