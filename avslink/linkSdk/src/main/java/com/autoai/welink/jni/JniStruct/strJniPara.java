package com.autoai.welink.jni.JniStruct;

import com.autoai.welink.jni.CallBack.AUDIO_DATA_CALLBACK;
import com.autoai.welink.jni.CallBack.DEVICE_STATUS_CALLBACK;
import com.autoai.welink.jni.CallBack.HU_DATA_CALLBACK;
import com.autoai.welink.jni.CallBack.IMAGE_DATA_CALLBACK;
import com.autoai.welink.jni.CallBack.JNI_MSG_CALLBACK;
import com.autoai.welink.jni.CallBack.JNI_STATUS_CALLBACK;
import com.autoai.welink.jni.CallBack.LAUNCHER_DATA_CALLBACK;
import com.autoai.welink.jni.CallBack.UPDATE_DATA_CALLBACK;
@SuppressWarnings("unused")
public class strJniPara {
    public int						screen_width;
    public int						screen_height;
    public int						screen_format;
    public AUDIO_DATA_CALLBACK cb_audio_data;
    public DEVICE_STATUS_CALLBACK cb_device_status;
    public HU_DATA_CALLBACK cb_hu_data;
    public IMAGE_DATA_CALLBACK cb_image_data;
    public LAUNCHER_DATA_CALLBACK cb_launcher_data;
    public JNI_MSG_CALLBACK cb_sdk_msg;
    public JNI_STATUS_CALLBACK cb_sdk_status;
    public UPDATE_DATA_CALLBACK cb_update_data;
    public	byte[]					hu_version;
    public int						extra;
}