package com.autoai.welink;

import android.content.Context;
import android.util.Log;

import com.autoai.welink.param.ConfigInfo;


/**
 * SdkApi 是对传输通道native的一层封装
 * 传输协议层是通过native c++  libwl_sdk_jni.so，
 * 该类SdkApi作为其Java层的封装，方便直接调用
 */
@SuppressWarnings("all")
public class SdkApi {
    private static final String TAG_BT = "BTPair";
    private final SdkInterface sdkInterface = new SdkInterface();
    /**
     * @brief Set SDK config info.
     *
     * @param config: See ConfigInfo.
     *
     * @return 0 for success, anything else for failure.
     *
     */
    public int setConfig(ConfigInfo config) {
        Log.i("SdkApi","SdkApi::setConfig");
        return sdkInterface.setConfig(config);
    }

    /**
     * @brief SDK init.
     *
     * @return 0 for success, anything else for failure.
     *
     */
    public int init() {
        Log.i("SdkApi","SdkApi::init");
        return sdkInterface.init();
    }
    /**
     * @brief SDK deinit.
     *
     * @return 0 for success, anything else for failure.
     *
     */
    public int deinit(){
        Log.i("SdkApi","SdkApi::deinit");
        return sdkInterface.deinit();
    }

    /**
     * @brief SDK Device change.
     *
     * @param device: See WL_API_DEVICE_TYPE.
     * @param ip: Device ip address, only WLAN need.
     *
     * @return 0 for success, anything else for failure.
     *
     */
    public int deviceChg(int device, String ip) {
        Log.i("SdkApi","SdkApi::deviceChg device = " + device);
        return sdkInterface.deviceChg(device,ip);
    }

    /**
     * @brief Send button.
     *
     * @param btn: See WL_API_BUTTON_TYPE.
     * @param para: Default NULL.
     *
     * @return 0 for success, anything else for failure.
     *
     */
    public int sendButton(int btn, int para) {
        Log.i("SdkApi","SdkApi::sendButton btn = " + btn);
        return sdkInterface.sendButton(btn,para);
    }
    /**
     * No usages found in All Places
     * @brief Send hard key.
     *
     * @param key: 1-start sacilink VR.
     * @param key: 2-close sacilink VR.
     * @param key: 3-driving recording.
     * @param para: Default NULL.
     *
     * @return 0 for success, anything else for failure.
     *
     */
    @Deprecated
    public int sendHardKey(int key, int para){
        Log.i("SdkApi","SdkApi::sendHardKey key = " + key);
        return sdkInterface.sendHardKey(key,para);
    }

    /**
     * @brief Send audio state hard key.
     *
     * @param key: audio state.
     * @param para: Default NULL.
     *
     * @return 0 for success, anything else for failure.
     *
     */
//    public int sendAudioStateHardKey(int key, int para){
//        return sdkInterface.sendAudioStateHardKey(key,para);
//    }

    /**
     * @brief Send pmode hard key.
     *
     * @param key: 1-long press.
     * @param para: Default NULL.
     *
     * @return 0 for success, anything else for failure.
     *
     */
    //public int sendPModeHardKey(int key, int para){
    //    return sdkInterface.sendPModeHardKey(key,para);
    //}

    /**
     * @brief Send vehicle info.
     *
     * @param vehicleBrand: brand info.
     * @param vehicleModel: model info.
     *
     * @return 0 for success, anything else for failure.
     *
     */
    //public int sendVehicleInfo(String vehicleBrand, String vehicleModel){
      //  return sdkInterface.sendVehicleInfo(vehicleBrand, vehicleModel);
    //}

    /**
     * No usages found in All Places
     * @brief Send rvc state.
     *
     * @param rvcState: Hu rvc state.
     *
     * @return 0 for success, anything else for failure.
     *
     */
    @Deprecated
    public int sendHuRvcState(int rvcState){
        Log.i("SdkApi","SdkApi::sendHuRvcState rvcState = " + rvcState);
        return sdkInterface.sendHuRvcState(rvcState);
    }

    /**
     * @brief Send control en and ch type.
     *
     * @param controlState: En and ch control state.
     *
     * @return 0 for success, anything else for failure.
     *
     */
    public int sendControlEnAndCh(int controlState){
        Log.i("SdkApi","SdkApi::sendControlEnAndCh controlState = " + controlState );
        return sdkInterface.sendControlEnAndCh(controlState);
    }

    /**
     * @brief Send res bt music focus.
     *
     * @param controlState: bt music focus state.
     *
     * @return 0 for success, anything else for failure.
     *
     */
//    public int sendResBtMusicFocus(int btFocusState){
//        return sdkInterface.sendResBtFocusState(btFocusState);
//    }

    /**
     * No usages found in All Places
     * @brief Send hu key state.
     *
     * @param keyState: key state.
     *
     * @return 0 for success, anything else for failure.
     *
     */
    @Deprecated
    public int sendHuKeyState(int keyState){
        Log.i("SdkApi","SdkApi::sendHuKeyState keyState = " + keyState);
        return sdkInterface.sendHuKeyState(keyState);
    }

    /**
     * @brief Send bt mac address.
     *
     * @param btMacInfo: bt mac address.
     *
     * @return 0 for success, anything else for failure.
     *
     */
    public int sendBTMacInfo(String btMacInfo){
        Log.i("SdkApi","SdkApi::sendBTMacInfo" );
        return sdkInterface.sendBTMacInfo(btMacInfo);
    }

    /**
     * @brief Send A2DP connected phone bt mac address to phone.
     * 向手机端发送当前A2DP连接的手机蓝牙MAC地址
     *
     * @param macAddress: 当前A2DP连接的手机蓝牙MAC地址
     *
     * @return 0 for success, anything else for failure.
     *
     */
    public int sendPhoneBTMacAddress(String macAddress){
        Log.i("SdkApi" + TAG_BT, "SdkApi::sendPhoneBTMacAddress >>> calling with macAddress = " + macAddress);
        int result = sdkInterface.sendPhoneBTMacAddress(macAddress);
        Log.d("SdkApi" + TAG_BT, "SdkApi::sendPhoneBTMacAddress >>> result = " + result);
        return result;
    }

    /**
     * @brief Hu mic state.
     *
     * @param state: mic state.
     *
     * @return 0 for success, anything else for failure.
     *
     */
    public int sendHuMicState(boolean state){
        Log.i("SdkApi","SdkApi::sendHuMicState state = " + state );
        return sdkInterface.sendHuMicState(state);
    }

    /**
     * @brief Send message to (APP->SDK).
     *
     * @param msg: See WL_API_MSG_TYPE
     * @param para: Default NULL(if (msg == WL_API_MSG_TYPE.APP_BT_ADDR), para = "XX:XX:XX:XX:XX:XX").
     *
     * @return 0 for success, anything else for failure.
     *
     */
    public int sendMsg(int msg, Object para) {
        Log.i("SdkApi","SdkApi::sendMsg msg = " + msg );
        return  sdkInterface.sendMsg(msg,para);
    }

    /**
     * No usages found in All Places
     * @brief Send MIC data.
     *
     * @param data: Record data(pcm).
     * @param size: Record data size.
     *
     * @return size for success, anything else for failure.
     *
     */
    @Deprecated
    public int sendMicData(byte[] data, int size) {
        Log.i("SdkApi","SdkApi::sendHuMicState " );
        return sdkInterface.sendMicData(data,size);
    }

    /**
     * No usages found in All Places
     * @brief Send audio data.
     *
     * @param type: Default 0.
     * @param data: Audio data.
     * @param size: Audio data size.
     *
     * @return size for success, anything else for failure.
     *
     */
    @Deprecated
    public int sendAudioReplyData(int type, byte[] data, int size) {
        Log.i("SdkApi","SdkApi::sendAudioReplyData" );
        return sdkInterface.sendAudioReplyData(type,data,size);
    }

    /**
     * No usages found in All Places
     * @brief Send car data.
     *
     * @param type: Default 0.
     * @param data: Car data.
     * @param size: Car data size.
     *
     * @return size for success, anything else for failure.
     *
     */
    @Deprecated
    public int sendHuData(int type, byte[] data, int size) {
        Log.i("SdkApi","SdkApi::sendHuData" );
        return sdkInterface.sendHuData(type,data,size);
    }

    /**
     * No usages found in All Places
     * @brief Send point(Multi-touch).
     *
     * @param x: X coordinates.
     * @param y: Y coordinates.
     * @param id: Point id, start from 0.
     * @param pressure: Default 50.
     * @param type: See WL_API_TOUCH_TYPE.
     *
     * @return 0 for success, anything else for failure.
     *
     */
    @Deprecated
    public int sendPoint(int x, int y, int id, int pressure, int type) {
        Log.i("SdkApi","SdkApi::sendPoint" );
        return sdkInterface.sendPoint(x,y,id,pressure,type);
    }

    /**
     * No usages found in All Places
     * @brief Send point sync(Multi-touch).
     *
     * @return 0 for success, anything else for failure.
     *
     */
    @Deprecated
    public int sendPointSync() {
        Log.i("SdkApi","SdkApi::sendPointSync" );
        return sdkInterface.sendPointSync();
    }

    /**
     * No usages found in All Places
     * @brief Get SDK version.
     *
     * @return SDK version string.
     *
     */
    @Deprecated
    public String getVersion() {
        Log.i("SdkApi","SdkApi::getVersion" );
        return sdkInterface.getVersion();
    }

    /**
     * @brief Set SDK CallBack function.
     *
     * @param callback: See SdkListener.
     *
     */
    public void setCallbackFunc(SdkListener callback)
    {
        Log.i("SdkApi","SdkApi::setCallbackFunc" );
        sdkInterface.setCallbackFunc(callback);
    }

    /**
     * @brief Get SDK version.
     *
     * @return SDK version string.
     *
     */
    public void setContext(Context context) {
        Log.i("SdkApi","SdkApi::setContext" );
        sdkInterface.setContext(context);
    }


    public int getDeviceType() {
        Log.i("SdkApi","SdkApi::getDeviceType" );
        return sdkInterface.getDeviceType();
    }


    /**
     * 给 iOS 侧发送校准指令
     * 
     * @param controlState 控制状态类型，参考 HidConstants 类中的常量:
     *                    - ON_TOUCH_POINT_BEGIN(3): 开始校准
     *                    - ON_TOUCH_POINT_END(4): 校准结束
     *                    - ON_TOUCH_POINT_END_SUCCESS(5): 校准结束成功
     *                    - ON_TOUCH_POINT_END_FAIL(6): 校准结束失败
     * @return 0 表示发送成功，其他值表示失败
     */
    public int sendIosHidTouchPoint(int controlState){
        Log.i("SdkApi","SdkApi::sendIosHidTouchPoint" );
        return sdkInterface.sendIosHidTouchPoint(controlState);
    }
    
    /**
     * 给 iOS 侧发送校准指令（带移动事件控制）
     * 
     * @param controlState 控制状态类型，参考 HidConstants 类中的常量
     * @param enableMoveEvents 是否启用移动事件，当值 >= 1 时才会发送移动事件
     * @return 0 表示发送成功，其他值表示失败
     */
    public int sendIosHidTouchPoint(int controlState, int enableMoveEvents){
        Log.i("SdkApi","SdkApi::sendIosHidTouchPoint" );
        return sdkInterface.sendIosHidTouchPoint(controlState, enableMoveEvents);
    }

    /**
     * 发送录屏请求消息
     * 
     * @param key 录屏控制键值
     *           - 1: 开始录屏
     *           - 2: 停止录屏
     * @return 0 表示发送成功，其他值表示失败
     */
    public int sendReqRecordScreenMsg(int key) {
        Log.i("SdkApi","SdkApi::sendReqRecordScreenMsg" );
        return sdkInterface.sendHuKeyState(key);
    }
}
