package com.autoai.welink.jni.Enum;
@SuppressWarnings("unused")
public interface WL_JNI_CMD_TYPE {
	int START = 1;					/*< Start connection */
	int DISCONNECT = 2;				/*< Disconnect */
	int RECONNECT = 3;				/*< Retry connection */
	int PAUSE = 4;					/*< Pause connection */
	int RESUME = 5;					/*< Resume connection */
	int START_LAUNCHER = 6;			/*< Start launcher */
	int SOURCE_OFF = 7;          	/*< Source off */
	int SOURCE_ON = 8;          	/*< Source on */
	int SET_HU_BTADDR = 9;			/*< Set Hu bt address to sdk */
	int SET_HU_SERIALNUM = 10;		/*< Set Hu serial number to sdk */
	int SET_HU_GUID = 11;			/*< Set Hu guid to sdk */
	int DEVICE_CHG = 12;          	/*< Device changed message */
	int DEVICE_SWITCH = 13;       	/*< Device switch message */
	int DEVICE_RESET = 14;			/*< Device reset message */
	int SET_SSL_FILE = 15;			/*< Set ssl file info */
	int SET_BTADDR = 16;			/*< Set connect device bt address to sdk */
	int LAUNCHER_EXIT = 17;			/*< Phone APP exit */
	int NUM = 18;

}
