package com.autoai.welink.param;
@SuppressWarnings("unused")
public class WlanDevicesInfo {
    private String mac;
    private String ip;
    private String deviceName;
    private String deviceType;
    public void setMac(String mac){
        this.mac = mac;
    }

    public String getMac(){
       return mac;
    }

    public void setIp(String ip){
        this.ip = ip;
    }

    public String getIp(){
        return ip;
    }

    public void setDeviceName(String deviceName){
        this.deviceName = deviceName;
    }

    public String getDeviceName(){
        return deviceName;
    }

    public void setDeviceType(String deviceType){
        this.deviceType = deviceType;
    }

    public String getDeviceType(){
        return deviceType;
    }
}
