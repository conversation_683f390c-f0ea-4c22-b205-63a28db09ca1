package com.autoai.welink.macro;
@SuppressWarnings("unused")
public interface WL_API_BUTTON_TYPE {
   int HOME = 1;					    /*< Button home */
   int LAUNCHER = 2;					/*< Button launcher, this button will send to mobile phone  */
   int NAVI = 3;						/*< Button navi, this button will send to mobile phone  */
   int PHONE = 4;						/*< Button phone call, this button will send to mobile phone  */
   int MUSIC = 5;						/*< Button music, this button will send to mobile phone  */
   int MSG = 6;						    /*< Button message, this button will send to mobile phone  */
   int MORE = 7; 						/*< Button more, this button will send to mobile phone  */
   int MIC = 8;						    /*< Button mic, this button will send to mobile phone  */
   int INIT_CLOSE = 9;                  /*< Button init page close  */
   int HELP_BACK = 10;                  /*< Button help back  */
   int CLICK_FOR_HELP = 11;             /*< Button click for help  */
   int SAFETY_CLOSE = 12;				/*< Button close safe driving mode */
   int ERR_CLOSE = 13;					/*< Button close error  */
   int LIMIT_CLOSE = 14;                /*< Button close limit  */
   int GOT_IT = 15;			            /*< Button got it  */
   int WECHAT_QRCODE_CLOSE = 16;		/*< Button close wechat qrcode  */
   int WECHAT_CANCEL_LOGIN = 17;		/*< Button cancel wechat login  */
   int OFFICIAL_ACCOUNT_SKIP = 18;		/*< Button skip official account  */
   int MAX_NUM = 19;					/*< Button max num, do not care  */

}
