package com.autoai.welink.jni.Enum;
@SuppressWarnings("unused")
public interface WL_JNI_DEVICE_TYPE {
	int NONE = 0;				/*< Device status none */
	int OUT = 1;				/*< Device plug out */
	int ANDROID = 2;			/*< Device (android device normally)*/
	int IPHONE = 3;				/*< Device (ios device normally)*/
	int AOA_UNAUTH = 4;			/*< Device AOA unauth*/
	int UNIDENTIFIED = 5;		/*< Device unidentified (android device normally)*/
	int ADB = 6;				/*< Device ADB */
	int AOA = 7;		        /*< Device AOA */
	int IDB = 8;				/*< Device iphone IDB */
	int WLAN_IPHONE = 9;		/*< Device wlan iphone */
	int WLAN_ANDROID = 10;		/*< Device wlan android */
	int EAP = 11;				/*< Device iphone EAP */
	int ERROR = 12;				/*< Device error */

}
