package com.autoai.welink.param;
/**
 * @brief Define the turn by turn gAplInfo.
 */
@SuppressWarnings("unused")
public class TurnByTurnInfo {
   public int		digitalCode;          // Digital Code
   public int		currentSpeed;		  // Current speed
   public float		nextProgress;		  // Next Progress
   public String    currentRoadName;	  // Current road name
   public int       naviCode;			  // Navi Code
   public String    roadName;			  // Next Road Name
   public int       roadDistance;	      // The Road Distance
   public int       roadDistanceFlag;     // The Road Distance Flag
   public int       remainDistance;		  // The remaining distance
   public int       remainDistanceFlag;   // The remaining distance Flag
   public String    remainTime;			  // The remaining time
}
