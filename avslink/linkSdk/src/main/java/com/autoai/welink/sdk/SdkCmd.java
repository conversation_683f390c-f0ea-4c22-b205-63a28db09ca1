package com.autoai.welink.sdk;

import com.autoai.welink.JniInterface;
import com.autoai.welink.jni.Enum.WL_JNI_CMD_TYPE;
import com.autoai.welink.jni.JniStruct.strJniCmd;

@SuppressWarnings("all")
public class SdkCmd {
    private static SdkCmd instance;
    public static synchronized SdkCmd getInstance(){
        if(instance == null){
            instance = new SdkCmd();
        }
        return instance;
    }
    private JniInterface jniInterface = JniInterface.getInstance();
    public void sourceOnCmd()
    {
        strJniCmd cmd = new strJniCmd();
        cmd.cmd = WL_JNI_CMD_TYPE.SOURCE_ON;
        jniInterface.cmd(cmd);
    }

    public void sourceOffCmd()
    {
        strJniCmd cmd = new strJniCmd();
        cmd.cmd = WL_JNI_CMD_TYPE.SOURCE_OFF;
        jniInterface.cmd(cmd);
    }

    public void videoStart()
    {
        strJniCmd cmd = new strJniCmd();
        cmd.cmd = WL_JNI_CMD_TYPE.RESUME;
        jniInterface.cmd(cmd);
    }

    public void videoStop()
    {
        strJniCmd cmd = new strJniCmd();
        cmd.cmd = WL_JNI_CMD_TYPE.PAUSE;
        jniInterface.cmd(cmd);
    }


    public void startWelink()
    {
        strJniCmd cmd = new strJniCmd();
        cmd.cmd = WL_JNI_CMD_TYPE.START;
        jniInterface.cmd(cmd);
    }

    public  void disconnectWelink()
    {
        strJniCmd cmd = new strJniCmd();
        cmd.cmd = WL_JNI_CMD_TYPE.DISCONNECT;
        jniInterface.cmd(cmd);
    }

    public void launcherExit()
    {
        strJniCmd cmd = new strJniCmd();
        cmd.cmd = WL_JNI_CMD_TYPE.LAUNCHER_EXIT;
        jniInterface.cmd(cmd);
    }

    public void usbReset()
    {
        strJniCmd cmd = new strJniCmd();
        cmd.cmd = WL_JNI_CMD_TYPE.DEVICE_RESET;
        jniInterface.cmd(cmd);
    }


    public void setBtAddr(byte[] addr)
    {
        strJniCmd cmd = new strJniCmd();
        cmd.cmd = WL_JNI_CMD_TYPE.SET_HU_BTADDR;
        cmd.para3 = addr;
        jniInterface.cmd(cmd);
    }

    public void switchDevice()
    {
        strJniCmd cmd = new strJniCmd();
        cmd.cmd = WL_JNI_CMD_TYPE.DEVICE_SWITCH;
        jniInterface.cmd(cmd);
    }

    public void setHUSerialnum(){
        strJniCmd cmd = new strJniCmd();
        cmd.cmd = WL_JNI_CMD_TYPE.SET_HU_SERIALNUM;
        String serialnum = SdkInfo.getInstance().getSerialNum();
        if(null != serialnum){
            cmd.para3 = serialnum.getBytes();
        }

        jniInterface.cmd(cmd);
    }

    public void setJniInterface(JniInterface jni){
        this.jniInterface = jni;
    }
}
