package com.autoai.welink.sdk.Enum;
@SuppressWarnings("unused")
public interface WL_SDK_MSG_STATUS {
	int WELINK_STATUS_START_OK = 1;
	int WELINK_DEVICE_IN = 2;
	int WELINK_DEVICE_OUT = 3;
	int WELINK_STATUS_NONE = 4;
	int WELINK_CONNECTING = 5;
	int WELINK_CONNECTED = 6;
	int WELINK_CONNECT_ERR = 7;
	int WELINK_BT_STATUS = 8;
	int WELINK_REQ_BT_ADDR = 9;
	int CAR_WELINK_FOREGROUND = 12;
	int CAR_WELINK_BACKGROUND = 13;
	int CAR_BT_ADDR = 14;
	int CAR_SWITCH_DEVICE = 15;
	int CAR_SET_LANGUAGE = 16;
	int CAR_SET_SAFETY_DRIVING_STATUS = 17;
	int CAR_AUDIO_EXIT = 18;
	int CAR_AUDIO_RESUME = 19;
	int SEND_POINT = 23;
	int SEND_POINT_SYNC = 24;
	int SEND_BUTTON = 25;
	int SPEECH_RECOGNITION = 26;
	int PREVIOUS_TRACK = 27;
	int NEXT_TRACK = 28;
	int MUSIC_STOP = 29;
	int MUSIC_PLAY = 30;
	int HU_BT_PHONE = 31;
	int HU_LOCAL_VR_STATE = 32;
	int HU_REQ_APP_DATA = 33;
	int HU_CHECK_PHONE_IS_PLAYING_VIDEO = 34;
	int HU_REQ_KEY_FRAME = 35;
	int MAX_NUM = 36;

}
