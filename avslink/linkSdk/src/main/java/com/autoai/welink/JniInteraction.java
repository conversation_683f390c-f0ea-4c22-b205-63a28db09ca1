package com.autoai.welink;

import com.autoai.common.util.LogUtil;
import com.autoai.welink.jni.CallBack.AUDIO_DATA_CALLBACK;
import com.autoai.welink.jni.CallBack.DEVICE_STATUS_CALLBACK;
import com.autoai.welink.jni.CallBack.HU_DATA_CALLBACK;
import com.autoai.welink.jni.CallBack.IMAGE_DATA_CALLBACK;
import com.autoai.welink.jni.CallBack.JNI_MSG_CALLBACK;
import com.autoai.welink.jni.CallBack.JNI_STATUS_CALLBACK;
import com.autoai.welink.jni.CallBack.LAUNCHER_DATA_CALLBACK;
import com.autoai.welink.jni.CallBack.UPDATE_DATA_CALLBACK;
import com.autoai.welink.sdk.SdkJson;
import com.autoai.welink.sdk.SdkPctl;

public class JniInteraction implements AUDIO_DATA_CALLBACK,
        DEVICE_STATUS_CALLBACK,
        HU_DATA_CALLBACK,
        IMAGE_DATA_CALLBACK,
        LAUNCHER_DATA_CALLBACK,
        JNI_MSG_CALLBACK,
        JNI_STATUS_CALLBACK,
        UPDATE_DATA_CALLBACK {

    private static final String TAG = JniInteraction.class.getSimpleName();

    private static final boolean CHECK_JNI_CALLBACK = LogUtil.isFullLog();

    private JniInteraction() {
    }

    private static JniInteraction instance;

    public static JniInteraction getInstance() {
        if (instance == null) {
            instance = new JniInteraction();
        }
        return instance;
    }

    private final SdkPctl mSdkPctl = SdkPctl.getInstance();
    private final SdkJson mAplJson = SdkJson.getInstance();
    private final SdkInteraction sdkInteraction = SdkInteraction.getInstance();

    @Override
    public void audioDataCallback(byte[] data, int len) {
        LogUtil.d(TAG, "audioDataCallback: len=" + len);
        sdkInteraction.audioData(data, len);
    }

    @Override
    public void deviceStatusCallback(int status) {
        LogUtil.d(TAG, "deviceStatusCallback: status=" + status);
        mSdkPctl.deviceStatusCb(status);
    }

    @Override
    public void huDataCallback(byte[] data, int len, int type) {
        LogUtil.d(TAG, "huDataCallback: len=" + len + ", type=" + type);
        sdkInteraction.huData(data, len, type);
    }

    @Override
    public void imageDataCallback(byte[] data, int size, int width, int height, int spspps_data_size, int frame_type) {
        if (CHECK_JNI_CALLBACK)
            LogUtil.v(TAG, "imageDataCallback: size=" + size + ", width=" + width + ", type=" + height + ", spspps_data_size=" + spspps_data_size + ", frame_type=" + frame_type);
        mSdkPctl.imageCb(data, size, width, height, spspps_data_size, frame_type);
    }

    @Override
    public void  launcherDataCallback(byte[] data, int len) {
        LogUtil.d(TAG, "launcherDataCallback: len=" + len);
        int msg = mAplJson.getLauncherMsg(data);
        mSdkPctl.launcherDataCb(msg, data);
    }

    @Override
    public void jniMsgCallback(byte[] data, int size, int type, int para) {
        LogUtil.d(TAG, "jniMsgCallback: size=" + size + ", type" + type + ", para" + para);
        mSdkPctl.jniMsgCb(data, size, type, para);
    }

    @Override
    public void jniStatusCallback(int status, int para1, byte[] para2) {
        LogUtil.d(TAG, "jniStatusCallback: status=" + status + ", para1" + para1);
        mSdkPctl.jniStatusCb(status, para1, para2);
    }

    @Override
    public void updateDataCallback(byte[] data, int len) {
        LogUtil.d(TAG, "jniStatusCallback: len=" + len);
        sdkInteraction.updateData(data, len);
    }
}
