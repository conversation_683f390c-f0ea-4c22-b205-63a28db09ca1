package com.autoai.welink.sdk.Enum;
@SuppressWarnings("unused")
public interface WL_SDK_LAUNCHER_MSG {
    int CUSTOM = 0;
    int LIMIT = 1;//0
    int ON_START_REPLY = 2;
    int ON_MUCHANNEL = 3;
    int RECORD = 4;
    int RESUME = 5;
    int RECEIVE_MSG = 6;
    int SCREEN_OFF = 7;
    int SCREEN_ON = 8;
    int WECHAT_LOGOUT = 9;
    int UNFIND_FRIEND = 10;
    int END_REPLY_BY_USER = 11;
    int BACK_LAUNCHER= 12;
    int EXIT_WELINK = 13;
    int PRESS_HOME_KEY = 14;
    int WECHAT_RESPONSE = 15;
    int BT_STATUS = 16;
    int SET_AUTO_CONNECT = 17;
    int NOTIFY_GUIDE_INFO = 18;
    int STOP_GUIDE_INFO = 19;
    int CALL_BY_NUMBER = 20;
    int SCREEN_CHG = 21;
    int TURN_BY_TURN = 22;
    int MUSIC_PLAY_STATE = 23;
    int MEDIA_STATE = 24;
    int HOME_KEY = 25;
    int APPLY_MIC = 26;
    int RELEASE_MIC = 27;
	int PCM_MUTE_STATE = 28;
    int PCM_STOP_PLAY = 29;
    int REQ_BT_MUSIC_FOCUS = 30;
    int RECORDING_SCREEN_STATE = 31;
	int HID_TOUCH = 32;
    int TRAFFIC_RECORDER_CAPTURE = 33;
    int NAVI_STATE = 34;
    int NUM = 35;

    int AUTH_RESULT = 36;
    int ON_HID_TOUCH_POINT = 40;
    int CAR_BT_CONNECTED = 41;
}
