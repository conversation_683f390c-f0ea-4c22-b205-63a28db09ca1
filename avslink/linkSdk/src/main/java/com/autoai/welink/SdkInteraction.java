package com.autoai.welink;

import com.autoai.welink.macro.WL_API_SDK_MSG_TYPE;
import com.autoai.welink.sdk.SdkPara;
@SuppressWarnings("all")
public class SdkInteraction {
    private SdkListener listener;
    private static SdkInteraction instance;
    public static synchronized SdkInteraction getInstance(){
        if(null == instance){
            instance = new SdkInteraction();
        }
        return instance;
    }
    private final SdkPara mSdkPara = SdkPara.getInstance();
    public void setListener(SdkListener listener){
        this.listener = listener;
    }

    public int updatePage(int page, Object para){
        int ret = -1;
        if(null != listener && mSdkPara.getCurPage() != page ){
            mSdkPara.setCurPage(page);
            ret = listener.onUpdatePage(page,para);
        }
        return ret;
    }

    public int recvMsg(int msg,Object para){
        int ret = -1;
        boolean need_cb = true;
        switch (msg) {
            case WL_API_SDK_MSG_TYPE.START_RECORD:
                if (!mSdkPara.isStartRecord()) {
                    mSdkPara.setStartRecord(true);
                } else {
                    need_cb = false;
                }
                break;
            case WL_API_SDK_MSG_TYPE.STOP_RECORD:
                if (mSdkPara.isStartRecord()) {
                    mSdkPara.setStartRecord(false);
                } else {
                    need_cb = false;
                }
                break;
            default:
                break;

        }
        if(null != listener && need_cb){
            ret = listener.onRecvMsg(msg, para);
        }
        return ret;
    }


    public int audioData(byte[] data, int para){
        int ret = -1;
        if(null != listener){
            ret = listener.onAudioData(data, para);
        }
        return ret;
    }

    public int huData(byte[] data, int para1, int para2){
        int ret = -1;
        if(null != listener){
            ret = listener.onHuData(data, para1, para2);
        }
        return ret;
    }

    public int videoData(byte[] data, int size, int width, int height, int spspps_data_size, int frame_type){
        int ret = -1;
        if(null != listener){
            ret = listener.onVideoData(data, size, width, height, spspps_data_size, frame_type);
        }
        return ret;
    }

    public int updateData(byte[] data, int len){
        int ret = -1;
        if(null!=listener){
            ret = listener.onUpdateData(data,len);
        }
        return ret;
    }
}
