package com.autoai.welink.sdk.BT;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothProfile;
import android.content.Context;
import android.util.Log;
import com.autoai.common.util.LogUtil;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

public class BtMusicFocusImpl implements BtMusicFocusBase {
    private static final String TAG = "BtMusicFocusImpl";
    private Context mContext;

    public BtMusicFocusImpl(Context context) {
        mContext = context.getApplicationContext();
        Log.d(TAG, "BtMusicFocusImpl initialized, A2DP management handled by BtConnectionManager");
    }

    @Override
    public void init() {
        // do init work
    }

    @Override
    public void requestBtMusicFocus(BluetoothDevice device) {
        /*int connState = getA2dpSinkConnState(device);
        if (connState < 2) {
            Log.d(TAG, "not connected");
        }*/
        Log.d(TAG, "requestBtMusicFocus");
        setWifiCastConnState(device, 1);
    }

    @Override
    public void abandonBtMusicFocus(BluetoothDevice device) {
        Log.d(TAG, "abandonBtMusicFocus");
        setWifiCastConnState(device, 0);
    }

    public void setWifiCastConnState(BluetoothDevice device, int state) {
        Log.d(TAG, "setWifiCastConnState " + state);
        try {
            // 使用BtConnectionManager管理的A2DP Profile，带安全检查
            BluetoothProfile a2dpSink = BtConnectionManager.getInstance().getA2dpSink();
            if (a2dpSink == null) {
                Log.d(TAG, "A2dpSink from BtConnectionManager is NULL, A2DP Profile not ready");
                return;
            }
            
            // 使用BtConnectionManager封装的A2DP连接状态检查，防止在没有实际连接时调用系统接口导致崩溃
            if (!BtConnectionManager.getInstance().isA2dpProfileConnected()) {
                Log.d(TAG, "A2DP Profile not connected via BtConnectionManager, skip operation");
                return;
            }
            
            // 额外安全检查：确保设备参数有效
            if (device != null) {
                Log.d(TAG, "setWifiCastConnState for device: " + device.getAddress());
            } else {
                Log.d(TAG, "setWifiCastConnState with null device (XPENG compatible)");
            }
            
            Class<?> cls = a2dpSink.getClass();
            Log.d(TAG, "a2dpSink from BtConnectionManager is " + cls.getSimpleName());
            Method setCastStateFunc = cls.getDeclaredMethod("setWifiCastConnectionState", BluetoothDevice.class, int.class);
            setCastStateFunc.setAccessible(true);
            Boolean result = (Boolean) setCastStateFunc.invoke(a2dpSink, device, state);
            Log.d(TAG, "setWifiCastConnState success ? " + result);
        } catch (NoSuchMethodException e) {
            LogUtil.e(TAG, "setWifiCastConnectionState method not found", e);
        } catch (InvocationTargetException e) {
            LogUtil.e(TAG, "Failed to invoke setWifiCastConnectionState", e);
        } catch (IllegalAccessException e) {
            LogUtil.e(TAG, "Cannot access setWifiCastConnectionState method", e);
        } catch (Exception e) {
            LogUtil.e(TAG, "Unexpected error in setWifiCastConnState", e);
        }
    }

    /*public int getA2dpSinkConnState(BluetoothDevice device) {
        try {
            if (mA2dpSink == null) {
                Log.d(TAG, "A2dpSink is NULL !!!");
                return -1;
            }
            Class<?> cls = mA2dpSink.getClass();
            Method getConnectionState = cls.getDeclaredMethod("getConnectionState", BluetoothDevice.class);
            getConnectionState.setAccessible(true);
            Integer result = (Integer) getConnectionState.invoke(mA2dpSink, device);
            Log.d(TAG, "getConnectionState = " + result);
            return result;
        } catch (NoSuchMethodException e) {
            throw new RuntimeException(e);
        } catch (InvocationTargetException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }*/
}
