package com.autoai.welink.sdk;

import com.autoai.welink.param.ConfigInfo;

@SuppressWarnings("all")
public class SdkInfo {

    private SdkInfo() {
    }

    private static SdkInfo instance;

    public static synchronized SdkInfo getInstance() {
        if (instance == null) {
            instance = new SdkInfo();
        }
        return instance;
    }

    private boolean isWeLinkForeground = false;

    private String mHuVersion;
    private String mSerialNum;
    private String mConfigFilePath;

    private int mFps;
    private int rttNo;
    private int mVideoWidth;
    private int mVideoHeight;
    private int mVideoX;
    private int mVideoY;

    private String mWelinkPath;
    /**
     * < Welink folder path, if strlen(welink_path) is 0, current path default
     */
    private String mLogPath;
    /**
     * < Log path, if strlen(log_path) is 0, printf default
     * 设置this.mLogType = 1;，只打印ERR
     * 设置this.mLogType = 15;，只打印ERR、WARNING 、INFO、DEBUG
     * 设置this.mLogType = 1060895;，都ERR 到 VERBOSE所有都打印
     */
    private int mLogType;
    /**
     * < Log type, value in WL_LOG_TYPE_ERROR ~ WL_LOG_TYPE_ALL
     */
    private int mBtAutoConnect;
    /**
     * < Bluetooth auto connect option, 1 auto connect, 0 not auto connent
     */
    private int mSupportRecord;
    /**
     * < Record option, 1 supported, 0 not supported
     */
    private int mSupportDecoder;
    /**
     * < Supported decoder type: [WL_DECODER_TYPE_JPEG] or [WL_DECODER_TYPE_H264] or [WL_DECODER_TYPE_JPEG_H264]
     */
    private int mDefaultRecord;
    /**
     * < Record option, 0 hu record, 1 phone record, 2 bt record
     */
    private int mSupportHucall;
    /**
     * < HU phone call option, 1 supported, 0 not supported
     */
    private int mDisableDriver;
    /**
     * < Driver starting option, 1 not start, 0 start
     */
    private int mDisableSwitch;
    /**
     * < Device auto switch option, 0 auto, 1 not auto
     */
    private int mDisableDeviceNotice;
    /**
     * < Device notice option, 0 driver notice, 1 APP notice
     */
    private int mAoaPkgSize;
    /**
     * < Aoa package size, 32k or 512k
     */
    private int mSupportHuVerticalfull;
    /**
     * < Vertical display in full screen option, 0 not supported , 1 supported
     */
    private int mSupportHuChannel;
    /**
     * < HU channel(car data), 1 support, 0 not support.
     */
    private int mSupportDataChannel;
    /**
     * < Data channel(mic & update), 1 support, 0 not support.
     */
    private int mSupportAudioChannel;

    /**
     * < Audio channel(pcm) flag, 1 support, 0 not support.
     */

    public void deinit() {
        if (null != this.mHuVersion) {
            this.mHuVersion = null;
        }
        if (null != this.mConfigFilePath) {
            this.mConfigFilePath = null;
        }
        if (null != this.mSerialNum) {
            this.mSerialNum = null;
        }

    }

    public int getScreenWidth() {
        return this.mVideoWidth;
    }

    public int getScreenHeight() {
        return this.mVideoHeight;
    }

    public int getScreenX() {
        return this.mVideoX;
    }

    public int getScreenY() {
        return this.mVideoY;
    }

    public void setVideoWidth(int w) {
        this.mVideoWidth = w;
    }

    public void setVideoHeight(int h) {
        this.mVideoHeight = h;
    }

    public void setVideoX(int x) {
        this.mVideoX = x;
    }

    public void setVideoY(int y) {
        this.mVideoY = y;
    }

    public String getWelinkPath() {
        return this.mWelinkPath;
    }

    public void setWelinkPath(String path) {
        this.mWelinkPath = path;
    }

    public String getLogPath() {
        return this.mLogPath;
    }

    public void setLogPath(String path) {
        this.mLogPath = path;
    }

    public int getLogType() {
        return this.mLogType;
    }

    public void setLogType(int type) {
        if(type == 1){
            this.mLogType = 1060895;
        }else if(type == 5 || type == 4){
            this.mLogType = 3;
        }else{
            this.mLogType = 15;
        }
    }

    public int getBtAutoConnect() {
        return this.mBtAutoConnect;
    }

    public void setBtAutoConnect(int status) {
        this.mBtAutoConnect = status;
    }

    public int getSupportRecord() {
        return this.mSupportRecord;
    }

    public void setSupportRecord(int type) {
        this.mSupportRecord = type;
    }

    public int getSupportDecoder() {
        return this.mSupportDecoder;
    }

    public void setSupportDecoder(int type) {
        this.mSupportDecoder = type;
    }

    public int getDefaultRecord() {
        return this.mDefaultRecord;
    }

    public void setDefaultRecord(int type) {
        this.mDefaultRecord = type;
    }

    public int getSupportHuCall() {
        return this.mSupportHucall;
    }

    public void setSupportHucall(int type) {
        this.mSupportHucall = type;
    }

    public int getDisableDriver() {
        return this.mDisableDriver;
    }

    public void setDisableDriver(int type) {
        this.mDisableDriver = type;
    }

    public int getDisableSwith() {
        return this.mDisableSwitch;
    }

    public void setDisableSwitch(int type) {
        this.mDisableSwitch = type;
    }

    public int getDisableDeviceNotice() {
        return this.mDisableDeviceNotice;
    }

    public void setDisableDeviceNotice(int type) {
        this.mDisableDeviceNotice = type;
    }

    public int getAoaPkgSize() {
        return this.mAoaPkgSize;
    }

    public void setAoaPkgSize(int size) {
        if (0 >= size) {
            this.mAoaPkgSize = 512;
        } else {
            this.mAoaPkgSize = size;
        }
    }

    public int getHuVerticalFull() {
        return this.mSupportHuVerticalfull;
    }

    public void setHuVerticalFull(int para) {
        this.mSupportHuVerticalfull = para;
    }

    public String getHuVersion() {
        return this.mHuVersion;
    }

    public String getSerialNum() {
        return this.mSerialNum;
    }

    public String getConfigFilePath() {
        return this.mConfigFilePath;
    }

    public void setConfig(ConfigInfo config) {
        this.mHuVersion = config.huVersion;
        this.mSerialNum = config.serialNum;
        this.mSupportHuVerticalfull = config.huVerticalFull;
        this.mVideoWidth = config.videoWidth;
        this.mVideoHeight = config.videoHeight;
        this.mVideoX = config.videoX;
        this.mVideoY = config.videoY;
        this.mConfigFilePath = config.configFilePath;
        setFps(config.fps);
        setRTT(config.rtt);
        getConfigFromFile(config.configFilePath);
    }

    public void setWeLinkForeground() {
        isWeLinkForeground = true;
    }

    public void setWeLinkBackground() {
        isWeLinkForeground = false;
    }

    public boolean isWeLinkForeground() {
        return isWeLinkForeground;
    }

    public void setFps(int mFps) {
        if (0 >= mFps) {
            this.mFps = 30;
        } else {
            this.mFps = mFps;
        }
    }

    public void setRTT(int rttNo) {
        this.rttNo = rttNo;
    }
    public int getRTT() {
        return this.rttNo;
    }


    public int getFps() {
        return this.mFps;
    }

    private void getConfigFromFile(String path) {
//        mConfigMgr.readProperties(getWelinkPath());
//        String str = mConfigMgr.getIniKey("WL_AUTO_BT");
//        this.mBtAutoConnect = Integer.parseInt(str);
//
//        str = mConfigMgr.getIniKey("WL_SUPPORT_RECORD");
//        this.mSupportRecord = Integer.parseInt(str);
//
//        str = mConfigMgr.getIniKey("WL_DEFAULT_RECORD");
//        this.mDefaultRecord = Integer.parseInt(str);
//
//        str = mConfigMgr.getIniKey("WL_SUPPORT_DECODER");
//        this.mSupportDecoder = Integer.parseInt(str);
//
//        str = mConfigMgr.getIniKey("WL_DISABLE_SWITCH_AOA");
//        this.mSupportDecoder = Integer.parseInt(str);
//
//        this.mSupportHucall = 0;
//
//        str = mConfigMgr.getIniKey("WL_DISABLE_DRIVER");
//        this.mDisableDriver = Integer.parseInt(str);
//
//        this.mLogPath = mConfigMgr.getIniKey("WL_LOG_PATH");
//        this.mWelinkPath = mConfigMgr.getIniKey("WL_WELINK_PATH");
//
//        str = mConfigMgr.getIniKey("WL_LOG_TYPE");
//        this.mLogType = Integer.parseInt(str);
//
//        str = mConfigMgr.getIniKey("WL_AOA_PACKAGE_SIZE");
//        setAoaPkgSize(Integer.parseInt(str));
//        this.mAoaPkgSize = getAoaPkgSize() ;
//
        this.mBtAutoConnect = 0;

        this.mSupportRecord = 1;

        this.mDefaultRecord = 0;

        this.mSupportDecoder = 3;

        this.mSupportHucall = 1;

        this.mDisableSwitch = 0;

        this.mDisableDriver = 0;
        this.mLogPath = "";
        this.mWelinkPath = "";

//        this.mLogType = 13;

        this.mAoaPkgSize = 512;

    }
}
