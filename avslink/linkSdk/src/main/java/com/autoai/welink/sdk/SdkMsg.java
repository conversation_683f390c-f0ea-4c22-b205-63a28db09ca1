package com.autoai.welink.sdk;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;

import com.autoai.welink.JniInterface;
import com.autoai.welink.sdk.Enum.WL_SDK_MSG_STATUS;
import com.autoai.welink.sdk.Struct.StrPointInfo;
//import com.autoai.welink.macro.WL_API_DEVICE_TYPE;

//import static android.content.ContentValues.TAG;

@SuppressWarnings("unused")
public class SdkMsg {

    private SdkMsg() {
    }

    private static SdkMsg instance;

    public static synchronized SdkMsg getInstance() {
        if (instance == null) {
            instance = new SdkMsg();
        }
        return instance;
    }

    private final SdkStatus mSdkStatus = SdkStatus.getInstance();
    private final JniInterface jniInterface = JniInterface.getInstance();
    private final SdkCmd mSdkCmd = SdkCmd.getInstance();
    private final SdkInfo mSdkInfo = SdkInfo.getInstance();
    private final Handler handler = new Handler(Looper.myLooper()) {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            switch (msg.what) {
                case WL_SDK_MSG_STATUS.CAR_WELINK_FOREGROUND:
                    if (!mSdkInfo.isWeLinkForeground()) {
                        mSdkCmd.sourceOnCmd();
                    }
                    mSdkStatus.setWeLinkForeground();
                    mSdkCmd.videoStart();
                    break;
                case WL_SDK_MSG_STATUS.CAR_WELINK_BACKGROUND:
                    mSdkCmd.sourceOffCmd();
                    mSdkStatus.setWeLinkBackground();
                    mSdkCmd.videoStop();
                    break;
                case WL_SDK_MSG_STATUS.CAR_BT_ADDR:
                    mSdkCmd.setBtAddr((byte[]) msg.obj);
                    break;
                case WL_SDK_MSG_STATUS.CAR_SWITCH_DEVICE:
                    mSdkCmd.switchDevice();
                    break;
                case WL_SDK_MSG_STATUS.CAR_AUDIO_EXIT:
                    mSdkStatus.sendAudioExit();
                    break;
                case WL_SDK_MSG_STATUS.CAR_AUDIO_RESUME:
                    mSdkStatus.sendAudioResume();
                    break;
                case WL_SDK_MSG_STATUS.SEND_BUTTON:
                    mSdkStatus.touchResponse(msg.arg1, msg.arg2);
                    break;
                case WL_SDK_MSG_STATUS.SEND_POINT:
                    StrPointInfo info = (StrPointInfo) msg.obj;
                    mSdkStatus.sendPoint(info.x, info.y, info.presure, info.id, info.type);
                    break;
                case WL_SDK_MSG_STATUS.SEND_POINT_SYNC:
                    jniInterface.sendPointSync();
                    break;
//                case WL_SDK_MSG_STATUS.CAR_SET_LANGUAGE:
//                    SdkPara.getInstance().setLanguageType(msg.arg1);
//                    mSdkStatus.sendOnControlEnAndCh(msg.arg1);
//                    break;
                case WL_SDK_MSG_STATUS.WELINK_BT_STATUS:
                    break;
                case WL_SDK_MSG_STATUS.CAR_SET_SAFETY_DRIVING_STATUS:
                    break;
                case WL_SDK_MSG_STATUS.MUSIC_STOP:
                    mSdkStatus.sendMusicStop();
                    break;
                case WL_SDK_MSG_STATUS.MUSIC_PLAY:
                    mSdkStatus.sendMusicPlay();
                    break;
//                case WL_SDK_MSG_STATUS.AUDIOFOCUS_LOSS:
//                    mSdkStatus.sendAudioFocusLoss(msg.arg1);
//                    break;
//                case WL_SDK_MSG_STATUS.AUDIOFOCUS_GAIN:
//                    mSdkStatus.sendAudioFocusGain(msg.arg1);
//                    break;
                case WL_SDK_MSG_STATUS.HU_BT_PHONE:
                    mSdkStatus.sendBtPhoneState(msg.arg1);
                    break;
                case WL_SDK_MSG_STATUS.HU_LOCAL_VR_STATE:
                    mSdkStatus.sendHuLocalVrState(msg.arg1);
                    break;
                case WL_SDK_MSG_STATUS.HU_REQ_APP_DATA:
                    mSdkStatus.sendHuReqAppData(msg.arg1);
                    break;
                case WL_SDK_MSG_STATUS.HU_CHECK_PHONE_IS_PLAYING_VIDEO:
                    mSdkStatus.sendHuReqPhoneIsPlayVideo(msg.arg1);
                    break;
                case WL_SDK_MSG_STATUS.HU_REQ_KEY_FRAME:
                     mSdkStatus.sendHuReqKeyFrame(msg.arg1, msg.arg2);
                     break;
                default:
                    break;

            }
        }
    };

    public void sendMsg(Message msg) {
        handler.sendMessage(msg);
    }
}