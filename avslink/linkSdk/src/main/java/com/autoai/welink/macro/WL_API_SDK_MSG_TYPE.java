package com.autoai.welink.macro;
@SuppressWarnings("unused")
public interface WL_API_SDK_MSG_TYPE {
    int BT_STATUS = 1;			/** [SDK->APP] Notice bluetooth status  */
    int REQ_BT_ADDR = 2;		/** [SDK->APP] Request bluetooth address  */
    int RECV_MSG = 3;			/** [SDK->APP] Recving message  */
    int NAVI_GUIDE_START = 4;	/** [SDK->APP] Notice navi guide info start  */
    int NAVI_GUIDE_STOP = 5;	/** [SDK->APP] Stop navi guide info  */
    int UPDATE_KEY = 6;			/** [SDK->APP] Update key  */
    int CONNECTING = 7;			/** [SDK->APP] Notice connecting status  */
    int CONNECTED = 8;			/** [SDK->APP] Notice connected status  */
    int CONNECT_ERR = 9;		/** [SDK->APP] Notice connect error status  */
    int DISCONNECT = 10;		/** [SDK->APP] Notice disconnect status  */
    int START_RECORD = 11;		/** [SDK->APP] Start record  */
    int STOP_RECORD = 12;		/** [SDK->APP] Stop record  */
    int WLAN_LIST_UPDATE = 13;	/** [SDK-->APP] Notice wlan list update  */
    int START_OK = 14;			/** [SDK->APP] SDK&DRIVER start ok*/
    int TURN_BY_TURN = 15;		/** [SDK->APP] Turn By Turn info */
    int CALL_NUMBER = 16;    	/** [SDK->APP] Call number */
    int MUSIC_PLAY_STATE = 17;  /** [SDK->APP] Play state */
    int MEDIA_STATE = 18;       /** [SDK->APP] Media state */
    int HOME_KEY = 19;          /** [SDK->APP] Home key */
    int APPLY_MIC = 20;         /** [SDK->APP] Apply mic */
    int RELEASE_MIC = 21;       /** [SDK->APP] Release mic */
	int MUTE_START = 22;        /**< [SDK->APP] Start mute(vr)  */
    int MUTE_STOP = 23;         /**< [SDK->APP] Stop mute(vr)  */
    int PCM_STOP_PLAY = 24;     /**< [SDK->APP] PCM Stop Play  */
    int REQ_BT_MUSIC_FOCUS = 25;    /**< [SDK->APP] Req bt Music Focus  */
    int RECORDING_SCREEN_STATE = 26;    /**< [SDK->APP] Recording Screen State  */
    int HID_TOUCH = 27;         /**< [SDK->APP] Hid Touch*/
    int CAPTURE_STATE = 28;         /**< [SDK->APP] Capture State*/
    int NAVI_STATE = 29;		/** [SDK->APP] Navi State */
    int MAX_NUM = 30;
    int ON_HID_TOUCH_POINT = 40;
    int CAR_BT_CONNECTED = 41;  /** [SDK->APP] Car BT Connected State */
}
