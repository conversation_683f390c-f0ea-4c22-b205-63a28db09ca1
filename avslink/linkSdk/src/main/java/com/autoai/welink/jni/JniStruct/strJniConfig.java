package com.autoai.welink.jni.JniStruct;

public class strJniConfig {
    public byte[] 	welink_path;				/*< SdkApi folder path, if strlen(welinkPath) is 0, current path default. MAX SIZE: 256*/
    public byte[] 	log_path;					/*< Log path, if strlen(log_path) is 0, printf default. MAX SIZE: 256*/
    public int		log_type;					/*< Log type, value in WL_LOG_TYPE_ERROR ~ WL_LOG_TYPE_ALL */
    public int		bt_auto_connect;			/*< Bluetooth auto connect option, 1 auto connect, 0 not auto connent */
    public int		support_record;				/*< Record option, 1 supported, 0 not supported */
    public int		support_decoder;			/*< Supported decoder type: [WL_DECODER_TYPE_JPEG] or [WL_DECODER_TYPE_H264] or [WL_DECODER_TYPE_JPEG_H264]*/
    public int		default_record;				/*< Record option, 0 hu record, 1 phone record, 2 bt record */
    public int		support_hucall;				/*< HU phone call option, 1 supported, 0 not supported */
    public int		disable_driver;				/*< Driver starting option, 1 not start, 0 start */
    public int		disable_switch;				/*< Device auto switch option, 0 auto, 1 not auto */
    public int		disable_device_notice;		/*< Device notice option, 0 driver notice, 1 APP notice */
    public int		aoa_pkg_size;				/*< Aoa z size, 32k or 512k */
    public int		support_HuVerticalfull;		/*< Vertical display in full screen option, 0 not supported , 1 supported */
    public int		support_hu_channel;			/*< HU channel(car data), 1 support, 0 not support. */
    public int		support_data_channel;		/*< Data channel(mic & update), 1 support, 0 not support. */
    public int		support_audio_channel;		/*< Audio channel(pcm) flag, 1 support, 0 not support. */
    public int		support_rtt;		       /**< RTT计算手机端和车机端系统时间gap的flag, 1 support, 0 not support. */
}