package com.autoai.welink.sdk;

/**
 * 车机蓝牙连接信息类
 * 用于封装手机端发送的蓝牙连接状态和MAC地址信息
 *
 * <AUTHOR> SDK
 * @version 1.0
 */
public class CarBTConnectedInfo {

    /**
     * 蓝牙连接状态常量
     */
    public static final int STATE_DISCONNECTED = 0;  // 未连接
    public static final int STATE_CONNECTED = 1;     // 已连接

    /**
     * 蓝牙连接状态 (1: 已连接, 0: 未连接)
     */
    private final int connectedState;

    /**
     * 手机蓝牙MAC地址 (如果为空字符串表示手机端未保存车机MAC地址)
     */
    private final String phoneMac;

    /**
     * 构造函数
     * @param connectedState 蓝牙连接状态 (使用 STATE_CONNECTED 或 STATE_DISCONNECTED 常量)
     * @param phoneMac 手机蓝牙MAC地址，可以为null或空字符串
     */
    public CarBTConnectedInfo(int connectedState, String phoneMac) {
        // 验证连接状态参数
        if (connectedState != STATE_CONNECTED && connectedState != STATE_DISCONNECTED) {
            throw new IllegalArgumentException("Invalid connectedState: " + connectedState +
                    ". Must be STATE_CONNECTED(1) or STATE_DISCONNECTED(0)");
        }

        this.connectedState = connectedState;
        this.phoneMac = phoneMac != null ? phoneMac.trim() : "";
    }

    /**
     * 获取蓝牙连接状态
     * @return 连接状态 (STATE_CONNECTED: 已连接, STATE_DISCONNECTED: 未连接)
     */
    public int getConnectedState() {
        return connectedState;
    }

    /**
     * 获取手机蓝牙MAC地址
     * @return 手机蓝牙MAC地址，不会返回null
     */
    public String getPhoneMac() {
        return phoneMac;
    }

    /**
     * 检查手机MAC地址是否为空
     * @return true表示MAC地址为空或null，false表示不为空
     */
    public boolean isPhoneMacEmpty() {
        return phoneMac == null || phoneMac.isEmpty();
    }

    /**
     * 检查是否已连接
     * @return true表示已连接，false表示未连接
     */
    public boolean isConnected() {
        return connectedState == STATE_CONNECTED;
    }

    /**
     * 检查是否未连接
     * @return true表示未连接，false表示已连接
     */
    public boolean isDisconnected() {
        return connectedState == STATE_DISCONNECTED;
    }

    /**
     * 验证MAC地址格式是否正确
     * 标准MAC地址格式：XX:XX:XX:XX:XX:XX (6组2位十六进制数，用冒号分隔)
     * @return true表示格式正确，false表示格式错误或为空
     */
    public boolean isValidMacFormat() {
        if (isPhoneMacEmpty()) {
            return false;
        }

        // MAC地址正则表达式：6组2位十六进制数，用冒号分隔
        String macPattern = "^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$";
        return phoneMac.matches(macPattern);
    }

    /**
     * 获取连接状态的字符串描述
     * @return 连接状态描述
     */
    public String getConnectedStateDescription() {
        switch (connectedState) {
            case STATE_CONNECTED:
                return "已连接";
            case STATE_DISCONNECTED:
                return "未连接";
            default:
                return "未知状态(" + connectedState + ")";
        }
    }

    /**
     * 创建已连接状态的实例
     * @param phoneMac 手机蓝牙MAC地址
     * @return CarBTConnectedInfo实例
     */
    public static CarBTConnectedInfo createConnected(String phoneMac) {
        return new CarBTConnectedInfo(STATE_CONNECTED, phoneMac);
    }

    /**
     * 创建未连接状态的实例
     * @param phoneMac 手机蓝牙MAC地址
     * @return CarBTConnectedInfo实例
     */
    public static CarBTConnectedInfo createDisconnected(String phoneMac) {
        return new CarBTConnectedInfo(STATE_DISCONNECTED, phoneMac);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;

        CarBTConnectedInfo that = (CarBTConnectedInfo) obj;
        return connectedState == that.connectedState &&
                phoneMac.equals(that.phoneMac);
    }

    @Override
    public int hashCode() {
        int result = connectedState;
        result = 31 * result + phoneMac.hashCode();
        return result;
    }

    @Override
    public String toString() {
        return "CarBTConnectedInfo{" +
                "connectedState=" + connectedState +
                "(" + getConnectedStateDescription() + ")" +
                ", phoneMac='" + (isPhoneMacEmpty() ? "empty" : phoneMac) + '\'' +
                ", validMacFormat=" + isValidMacFormat() +
                '}';
    }
}
