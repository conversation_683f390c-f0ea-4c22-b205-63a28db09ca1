package com.autoai.welink.param;

public class HidConstants {

    /************   iOS 发送给 车机侧 相关命令事件  ***************/
    /**
     * 手机请求校准
     */
    public final static int ON_TOUCH_POINT_READY = 1;
    /**
     * 手机发送点信息
     */
    public final static int ON_TOUCH_SEND_POINT = 2;
    /**
     * 手机结束校准,不在前台
     */
    public final static int ON_TOUCH_END_POINT = 3;
    /**
     * 应用在前台，请求校准
     */
    public final static int ON_TOUCH_APP_FRONT = 4;
    /**
     * 应用在前台，结束预备校准，给车机侧发送校准点
     */
    public final static int ON_TOUCH_SEND_PREPARE_POINT = 5;


    /************  车机侧 发送 给 iOS 相关命令事件  ***************/
    /**
     * 请求校准应答成功：蓝牙已连接，可以校准
     */
    public final static int ON_TOUCH_POINT_REQ_SUCCESS = 1;
    /**
     * 请求校准应答失败：蓝牙连接失败，不可以校准
     */
    public final static int ON_TOUCH_POINT_REQ_FAIL = 2;

    /**
     * 开始校准
     */
    public final static int ON_TOUCH_POINT_BEGIN = 3;
    /**
     * 校准结束
     */
    public final static int ON_TOUCH_POINT_END = 4;
    /**
     * 校准结束成功
     */
    public final static int ON_TOUCH_POINT_END_SUCCESS = 5;
    /**
     * 校准结束失败
     */
    public final static int ON_TOUCH_POINT_END_FAIL = 6;

    /**
     * 预备校准，检查历史参数、蓝牙是否匹配
     */
    public final static int ON_TOUCH_POINT_PREPARE = 7;

    /**
     * 预备校准结束，通知 iOS 侧回传校准点
     */
    public final static int ON_TOUCH_POINT_PREPARE_END = 8;


    /**
     * HID校准成功
     */
    public final static int  ON_HID_SUCCESS = 0;

    /**
     * HID校准失败
     */
    public final static int  ON_HID_FAIL = 1;

    /**
     * HID校准失败，连接失败
     */
    public final static int  ON_HID_CONNECT_FAIL = 2;
    /**
     * HID校准失败，手机APP在后台
     */
    public final static int  ON_HID_APP_BACKGROUND_FAIL = 3;
    /**
     * HID 车机进入 HID 校准条件（蓝牙和互联手机匹配）。
     */
    public final static int ON_HID_BT_PAIRED_WITH_PHONE = 4;
    
    /**
     * HID校准失败，请求校准时手机APP已经在后台
     */
    public final static int ON_HID_APP_NOT_FOREGROUND = 21;


        /**
     * 获取HID触控点状态的字符串描述(iOS 侧的应答)
     * @param state 状态码
     * @return 状态描述
     */
    public static String getHidTouchPointStateFromIOSString(int state) {
        switch (state) {
            case HidConstants.ON_TOUCH_POINT_READY:
                return "ON_TOUCH_POINT_READY";
            case HidConstants.ON_TOUCH_SEND_POINT:
                return "ON_TOUCH_SEND_POINT";
            case HidConstants.ON_TOUCH_END_POINT:
                return "ON_TOUCH_END_POINT";
            case HidConstants.ON_TOUCH_APP_FRONT:
                return "ON_TOUCH_APP_FRONT";
            case HidConstants.ON_TOUCH_SEND_PREPARE_POINT:
                return "ON_TOUCH_SEND_PREPARE_POINT";
            default:
                return "UNKNOWN_STATE_" + state;
        }
    }

        /**
     * 获取HID触控点状态的字符串描述(发送给 iOS 侧的命令)
     * @param state 状态码
     * @return 状态描述
     */
    public static String getHidTouchPointStateToIOSString(int state) {
        switch (state) {
            case HidConstants.ON_TOUCH_POINT_REQ_SUCCESS:
                return "ON_TOUCH_POINT_REQ_SUCCESS";
            case HidConstants.ON_TOUCH_POINT_REQ_FAIL:
                return "ON_TOUCH_POINT_REQ_FAIL";
            case HidConstants.ON_TOUCH_POINT_BEGIN:
                return "ON_TOUCH_POINT_BEGIN";
            case HidConstants.ON_TOUCH_POINT_END:
                return "ON_TOUCH_POINT_END";
            case HidConstants.ON_TOUCH_POINT_END_SUCCESS:
                return "ON_TOUCH_POINT_END_SUCCESS";
            case HidConstants.ON_TOUCH_POINT_END_FAIL:
                return "ON_TOUCH_POINT_END_FAIL";
            case HidConstants.ON_TOUCH_POINT_PREPARE:
                return "ON_TOUCH_POINT_PREPARE";
            case HidConstants.ON_TOUCH_POINT_PREPARE_END:
                return "ON_TOUCH_POINT_PREPARE_END";
            default:
                return "UNKNOWN";
        }
    }

    public static final String TAG_IOS_CALIB = ".iOSCalib";
    public static final String TAG_IOSHID = ".iOSHID";

}
