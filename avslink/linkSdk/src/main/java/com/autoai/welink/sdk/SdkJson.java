package com.autoai.welink.sdk;

import android.util.Log;

import com.autoai.common.util.LogUtil;
import com.autoai.welink.param.HidMessage;
import com.autoai.welink.param.HidTouch;
import com.autoai.welink.param.NaviGuide;
import com.autoai.welink.param.TurnByTurnInfo;
import com.autoai.welink.sdk.Enum.WL_SDK_LAUNCHER_MSG;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@SuppressWarnings("all")
public class SdkJson {
    private static final String TAG = "SdkJson";
    private static final String TAG_BT = "BTPair";

    private SdkJson() {
    }

    private static SdkJson instance;

    public static synchronized SdkJson getInstance() {
        if (instance == null) {
            instance = new SdkJson();
        }
        return instance;
    }

    private static final String WE_METHOD = "method";
    private static final String WE_EXT_DATA = "extData";

    private JSONObject getJson(JSONObject obj) {
        JSONObject json = new JSONObject();
        try {
            json.put("moduleName", "WeLink");
            json.put("version", 0);
            json.put("platform", "android");
            json.put("command", obj);

        } catch (JSONException e) {
            e.printStackTrace();
        }

        return json;
    }

    public JSONObject getOnExitAudio() {
        JSONObject json = new JSONObject();
        try {
            json.put(WE_METHOD, "onExitAudio");
            json.put(WE_EXT_DATA, "");

        } catch (JSONException e) {
            e.printStackTrace();
        }
        return getJson(json);
    }

    public JSONObject getOnResumeAudio() {
        JSONObject json = new JSONObject();
        try {
            json.put(WE_METHOD, "onResumeAudio");
            json.put(WE_EXT_DATA, "");

        } catch (JSONException e) {
            e.printStackTrace();
        }
        return getJson(json);
    }

    public JSONObject getOnMusicStop() {
        JSONObject json = new JSONObject();
        JSONObject extData = new JSONObject();
        try {
            extData.put("audioState", 1);
            json.put(WE_METHOD, "onControlMobileAudio");
            json.put(WE_EXT_DATA, extData);

        } catch (JSONException e) {
            e.printStackTrace();
        }
        return getJson(json);
    }

    public JSONObject getOnMusicPlay() {
        JSONObject json = new JSONObject();
        JSONObject extData = new JSONObject();
        try {
            extData.put("audioState", 2);
            json.put(WE_METHOD, "onControlMobileAudio");
            json.put(WE_EXT_DATA, extData);

        } catch (JSONException e) {
            e.printStackTrace();
        }
        return getJson(json);
    }

//    public JSONObject getOnAudioFocus(int channel, int action){
//        JSONObject json = new JSONObject();
//        JSONObject extData = new JSONObject();
//        try {
//            extData.put("soundChannelName",channel);
//            extData.put("soundChannelAction",action);
//            json.put(WE_METHOD,"onHuSoundChannel");
//            json.put(WE_EXT_DATA,extData);
//
//        } catch (JSONException e) {
//            e.printStackTrace();
//        }
//        return getJson(json);
//    }

    public JSONObject getOnControlEnAndCh(int type)
    {
        JSONObject json = new JSONObject();
        JSONObject extData = new JSONObject();
        try {
            extData.put("controlState",type);
            json.put(WE_METHOD,"onControlEnAndCh");
            json.put(WE_EXT_DATA,extData);

        } catch (JSONException e) {
            e.printStackTrace();
        }
        return getJson(json);
    }

//    public JSONObject getOnResBtMusicFocus(int state)
//    {
//        JSONObject json = new JSONObject();
//        JSONObject extData = new JSONObject();
//        try {
//            extData.put("resBTMusicFocusState",state);
//            json.put(WE_METHOD,"onResBTMusicFocus");
//            json.put(WE_EXT_DATA,extData);
//
//        } catch (JSONException e) {
//            e.printStackTrace();
//        }
//        return getJson(json);
//    }

    public JSONObject getOnHuKeyState(int keyState)
    {
        JSONObject json = new JSONObject();
        JSONObject extData = new JSONObject();
        try {
            extData.put("keyState",keyState);
            json.put(WE_METHOD,"onHuKeyState");
            json.put(WE_EXT_DATA,extData);

        } catch (JSONException e) {
            e.printStackTrace();
        }
        return getJson(json);
    }

    public JSONObject getOnAutoBTConnection(String btMacInfo) {
        JSONObject json = new JSONObject();
        JSONObject extData = new JSONObject();
        try {
            extData.put("btmacInfo", btMacInfo);
            json.put(WE_METHOD, "onAutoBTConnection");
            json.put(WE_EXT_DATA, extData);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return getJson(json);
    }

    public JSONObject getOnHuChannel(int key) {
        JSONObject json = new JSONObject();
        JSONObject extData = new JSONObject();
        try {
            extData.put("pressDownKey", key);
            json.put(WE_METHOD, "onHuChannel");
            json.put(WE_EXT_DATA, extData);

        } catch (JSONException e) {
            e.printStackTrace();
        }
        return getJson(json);
    }

    public JSONObject getOnWeChatResponse(int type) {
        JSONObject json = new JSONObject();
        JSONObject extData = new JSONObject();
        try {
            extData.put("code", type);
            json.put(WE_METHOD, "onWeChatResponse");
            json.put(WE_EXT_DATA, extData);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return getJson(json);
    }

    public JSONObject getOnKeyStateInfo(int hardKey) {
        JSONObject json = new JSONObject();
        JSONObject extData = new JSONObject();
        try {
            extData.put("keyState", hardKey);
            json.put(WE_METHOD, "onKeyStateInfo");
            json.put(WE_EXT_DATA, extData);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return getJson(json);
    }

    public JSONObject getOnHuBTPhone(int btPhoneState) {
        JSONObject json = new JSONObject();
        JSONObject extData = new JSONObject();
        try {
            extData.put("btPhoneState", btPhoneState);
            json.put(WE_METHOD, "onHuBTPhone");
            json.put(WE_EXT_DATA, extData);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return getJson(json);
    }

    public JSONObject getOnHuLocalVrState(int localVrState) {
        JSONObject json = new JSONObject();
        JSONObject extData = new JSONObject();
        try {
            extData.put("localVRState", localVrState);
            json.put(WE_METHOD, "onHuLocalVRState");
            json.put(WE_EXT_DATA, extData);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return getJson(json);
    }

    public JSONObject getOnHuReqAppData(int type) {
        JSONObject json = new JSONObject();
        JSONObject extData = new JSONObject();
        try {
            extData.put("reqAction", type);
            json.put(WE_METHOD, "onReqAppData");
            json.put(WE_EXT_DATA, extData);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return getJson(json);
    }

    public JSONObject getOnHuReqPhoneIsPayingVideo(int type) {
        JSONObject json = new JSONObject();
        JSONObject extData = new JSONObject();
        try {
            extData.put("reqAction", type);
            json.put(WE_METHOD, "OnReqPhoneIsPayingVideo");
            json.put(WE_EXT_DATA, extData);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return getJson(json);
    }

    public JSONObject getOnControlMobileAduio(int hardKey) {
        JSONObject json = new JSONObject();
        JSONObject extData = new JSONObject();
        try {
            extData.put("audioState", hardKey);
            json.put(WE_METHOD, "onControlMobileAudio");
            json.put(WE_EXT_DATA, extData);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return getJson(json);
    }

//    public JSONObject getOnPModeKeyStateInfo(int key)
//    {
//        JSONObject json = new JSONObject();
//        JSONObject extData = new JSONObject();
//        try {
//            extData.put("pmodeKeyState", key);
//            json.put(WE_METHOD,"onHuPModeKeyState");
//            json.put(WE_EXT_DATA,extData);
//        } catch (JSONException e) {
//            e.printStackTrace();
//        }
//        return getJson(json);
//    }

//    public JSONObject getOnHuVehicleInfo(String vehicleBrand, String vehicleModel)
//    {
//        JSONObject json = new JSONObject();
//        JSONObject extData = new JSONObject();
//        try {
//            extData.put("vehicleBrand", vehicleBrand);
//            extData.put("vehicleModel", vehicleModel);
//            json.put(WE_METHOD,"onHuVehicleInfo");
//            json.put(WE_EXT_DATA, extData);
//        } catch (JSONException e) {
//            e.printStackTrace();
//        }
//        return getJson(json);
//    }

    public JSONObject getOnHuRvcState(int rvcState) {
        JSONObject json = new JSONObject();
        JSONObject extData = new JSONObject();
        try {
            extData.put("rvcState", rvcState);
            json.put(WE_METHOD, "onHuRVCState");
            json.put(WE_EXT_DATA, extData);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return getJson(json);
    }

    public JSONObject getOnHuMicState(boolean state) {
        JSONObject json = new JSONObject();
        JSONObject extData = new JSONObject();
        try {
            extData.put("huMicState", state);
            json.put(WE_METHOD, "onHuMicState");
            json.put(WE_EXT_DATA, extData);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return getJson(json);
    }

    public JSONObject getOnHuAppFront(boolean status) {
        JSONObject json = new JSONObject();
        JSONObject extData = new JSONObject();
        try {
            extData.put("state", status);
            json.put(WE_METHOD, "onHuAppFront");
            json.put(WE_EXT_DATA, extData);

        } catch (JSONException e) {
            e.printStackTrace();
        }
        return getJson(json);
    }

    /**
     * 车机端请求关键帧消息
     * @param frame  请求关键帧个数
     * @param time  时间，单位毫秒    time时间内请求frame 个关键帧
     * @return
     */
    public JSONObject getOnHuReqKeyFrame(int frame,int time) {
        JSONObject json = new JSONObject();
        JSONObject extData = new JSONObject();
        try {
            extData.put("frame", frame);
            extData.put("time", time);
            json.put(WE_METHOD, "onHuReqKeyFrame");
            json.put(WE_EXT_DATA, extData);

        } catch (JSONException e) {
            e.printStackTrace();
        }
        return getJson(json);
    }
    public int getLauncherMsg(byte[] json) {
        int msg = 0;
        String jstr = new String(json);
        String method = getMethod(jstr);
        Log.d("SdkJson", jstr);
        if (method.equals("onMsgReceive")) {
            msg = WL_SDK_LAUNCHER_MSG.RECEIVE_MSG;
        } else if (method.equals("onExitWelink")) {
            msg = WL_SDK_LAUNCHER_MSG.EXIT_WELINK;
        } else if (method.equals("onLimitWelink")) {
            msg = WL_SDK_LAUNCHER_MSG.LIMIT;
        } else if (method.equals("onResumeWelink")) {
            msg = WL_SDK_LAUNCHER_MSG.RESUME;
        } else if (method.equals("onMuChannel")) {
            msg = WL_SDK_LAUNCHER_MSG.ON_MUCHANNEL;
        } else if (method.equals("onBluetoothState")) {
            msg = WL_SDK_LAUNCHER_MSG.BT_STATUS;
        } else if (method.equals("screenOff")) {
            msg = WL_SDK_LAUNCHER_MSG.SCREEN_OFF;
        } else if (method.equals("onScreenChange")) {
            msg = WL_SDK_LAUNCHER_MSG.SCREEN_CHG;
        } else if (method.equals("userPresent")) {
            msg = WL_SDK_LAUNCHER_MSG.SCREEN_ON;
        } else if (method.equals("onWeChatResponse")) {
            msg = WL_SDK_LAUNCHER_MSG.WECHAT_RESPONSE;
        } else if (method.equals("setAutoLinkState")) {
            msg = WL_SDK_LAUNCHER_MSG.SET_AUTO_CONNECT;
        } else if (method.equals("notifyGuideNodeInfo")) {
            msg = WL_SDK_LAUNCHER_MSG.NOTIFY_GUIDE_INFO;
        } else if (method.equals("stopGuideNodeInfo")) {
            msg = WL_SDK_LAUNCHER_MSG.STOP_GUIDE_INFO;
        } else if (method.equals("onCallByNumber")) {
            msg = WL_SDK_LAUNCHER_MSG.CALL_BY_NUMBER;
        } else if (method.equals("onMusicPlaySuccess")) {
            msg = WL_SDK_LAUNCHER_MSG.MUSIC_PLAY_STATE;
        } else if (method.equals("onMediaState")) {
            msg = WL_SDK_LAUNCHER_MSG.MEDIA_STATE;
        } else if (method.equals("onHomeKeyState")) {
            msg = WL_SDK_LAUNCHER_MSG.HOME_KEY;
        } else if (method.equals("onApplyMic")) {
            msg = WL_SDK_LAUNCHER_MSG.APPLY_MIC;
        } else if (method.equals("onReleaseMic")) {
            msg = WL_SDK_LAUNCHER_MSG.RELEASE_MIC;
        } else if (method.equals("onTurnbyturn")) {
            msg = WL_SDK_LAUNCHER_MSG.TURN_BY_TURN;
        } else if (method.equals("onMuteState")) {
            msg = WL_SDK_LAUNCHER_MSG.PCM_MUTE_STATE;
        } else if (method.equals("onStopPcmPlay")) {
            msg = WL_SDK_LAUNCHER_MSG.PCM_STOP_PLAY;
        } else if (method.equals("onReqBTMusicFocus")) {
            msg = WL_SDK_LAUNCHER_MSG.REQ_BT_MUSIC_FOCUS;
        } else if (method.equals("onRecordingScreenState")) {
            msg = WL_SDK_LAUNCHER_MSG.RECORDING_SCREEN_STATE;
        } else if (method.equals("hidTouch")) {
            msg = WL_SDK_LAUNCHER_MSG.HID_TOUCH;
        } else if (method.equals("trafficRecorderCapture")) {
            msg = WL_SDK_LAUNCHER_MSG.TRAFFIC_RECORDER_CAPTURE;
        } else if (method.equals("onNaviState")) {
            msg = WL_SDK_LAUNCHER_MSG.NAVI_STATE;
        } else if (method.equals("notifyAuthorityResult")) {
            msg = WL_SDK_LAUNCHER_MSG.AUTH_RESULT;
        }else if (method.equals("onIosHidTouch")) {
            msg = WL_SDK_LAUNCHER_MSG.ON_HID_TOUCH_POINT;
        }else if (method.equals("onCarBTConnected")) {
            msg = WL_SDK_LAUNCHER_MSG.CAR_BT_CONNECTED;
        }

        return msg;
    }

    public int getOnMuChannelExtData(String json) {
        int ret = -1;
        JSONObject extData = getExtData(json, "extData");
        try {
            ret = extData.getInt("pressDownKey");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return ret;
    }

    public int getOnRecvMsgExtData(String json) {
        int ret = -1;
        JSONObject extData = getExtData(json, "extData");
        try {
            ret = extData.getInt("type");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return ret;
    }

    public int getWeChatResponseExtData(String json) {
        int ret = -1;
        JSONObject extData = getExtData(json, "extData");
        try {
            ret = extData.getInt("code");
        } catch (JSONException e) {
            e.printStackTrace();
        }

        return ret;
    }

    public int getBluetoothStatus(String json) {
        int ret = -1;
        JSONObject extData = getExtData(json, "extData");
        try {
            String bString = extData.getString("bluetoothState");
            if ("1".equals(bString) || "true".equals(bString)) {
                ret = 1;
            } else {
                ret = 0;
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return ret;
    }

    public NaviGuide getNaviGuideExtData(String json) {
        NaviGuide info = new NaviGuide();
        JSONObject extData = getExtData(json, "extData");
        try {
            info.name = extData.getString("name");
            info.nextName = extData.getString("nextName");
            info.distanceToCurrPoint = extData.getString("distanceToCurrPoint");
            info.direction = extData.getString("direction");
            info.icon = extData.getInt("icon");
            info.remainDistance = extData.getString("remainDistance");
            info.remainTime = extData.getString("remainTime");
            info.limitSpeed = extData.getInt("limitSpeed");
            info.currSpeed = extData.getString("currSpeed");
            info.percentToCurrPoint = extData.getInt("percentToCurrPoint");
        } catch (JSONException e) {
            e.printStackTrace();
        }

        return info;
    }

    public TurnByTurnInfo getTurnByTurnExtData(String json) {
        TurnByTurnInfo info = new TurnByTurnInfo();
        JSONObject extData = getExtData(json, "extData");
        try {
            info.digitalCode = extData.getInt("digitalCode");
            info.currentSpeed = extData.getInt("currentSpeed");
            info.nextProgress = (float) extData.getDouble("nextProgress");
            info.currentRoadName = extData.getString("currentRoadName");
            info.naviCode = extData.getInt("naviCode");
            info.roadName = extData.getString("roadName");
            info.roadDistance = extData.getInt("roadDistance");
            info.roadDistanceFlag = extData.getInt("roadDistanceFlag");
            info.remainDistance = extData.getInt("remainDistance");
            info.remainDistanceFlag = extData.getInt("remainDistanceFlag");
            info.remainTime = extData.getString("remainTime");
        } catch (JSONException e) {
            e.printStackTrace();
        }

        return info;
    }

    public HidTouch getScreenRotation(String json) {
        LogUtil.v(TAG, "json");
        HidTouch hidTouch = new HidTouch();
        JSONObject extData = getExtData(json, "extData");
        JSONObject jsonHome = getExtData(json, "phoneHome");
        JSONObject jsonBack = getExtData(json, "phoneBack");
        JSONObject jsonHuHome = getExtData(json, "huHome");
        JSONObject jsonPhoneSize = getExtData(json, "phoneSize");
        try {
            JSONObject jsonObject = new JSONObject(json);
            hidTouch.platform = jsonObject.getString("platform");

            hidTouch.isSupportHID = extData.getBoolean("enabled");
            hidTouch.angle = extData.getInt("angle");
            hidTouch.h264X = extData.getInt("x");
            hidTouch.h264Y = extData.getInt("y");
            hidTouch.h264W = extData.getInt("w");
            hidTouch.h264H = extData.getInt("h");

            hidTouch.videoMode = new JSONObject(json).optJSONObject("command").optInt("videoMode");

            hidTouch.home.x = jsonHome.getInt("x");
            hidTouch.home.y = jsonHome.getInt("y");
            hidTouch.home.w = jsonHome.getInt("w");
            hidTouch.home.h = jsonHome.getInt("h");

            hidTouch.back.x = jsonBack.getInt("x");
            hidTouch.back.y = jsonBack.getInt("y");
            hidTouch.back.w = jsonBack.getInt("w");
            hidTouch.back.h = jsonBack.getInt("h");

            hidTouch.huHome.x = jsonHuHome.getInt("x");
            hidTouch.huHome.y = jsonHuHome.getInt("y");
            hidTouch.huHome.w = jsonHuHome.getInt("w");
            hidTouch.huHome.h = jsonHuHome.getInt("h");

            if(jsonPhoneSize != null) {
                hidTouch.phoneSize.x = jsonPhoneSize.getInt("x");
                hidTouch.phoneSize.y = jsonPhoneSize.getInt("y");
                hidTouch.phoneSize.w = jsonPhoneSize.getInt("w");
                hidTouch.phoneSize.h = jsonPhoneSize.getInt("h");
            }


        } catch (JSONException e) {
            e.printStackTrace();
        }

        LogUtil.i(TAG, "isSupportHID:" + hidTouch.isSupportHID + ", angle:" + hidTouch.angle
                + ", h264X:" + hidTouch.h264X + ", h264Y:" + hidTouch.h264Y
                + ", h264W:" + hidTouch.h264W + ", h264H:" + hidTouch.h264H);
        return hidTouch;
    }

    public String getCallNumber(String json) {
        String ret = "";
        JSONObject extData = getExtData(json, "extData");
        try {
            ret = extData.getString("phoneNumber");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return ret;
    }

    public int getMusicPlayState(String json) {
        int ret = -1;
        JSONObject extData = getExtData(json, "extData");
        try {
            ret = extData.getInt("musicPlaySuccessState");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return ret;
    }

    public int getMediaState(String json) {
        int ret = -1;
        JSONObject extData = getExtData(json, "extData");
        if (extData.has("mediaState")) {
            try {
                ret = extData.getInt("mediaState");
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        return ret;
    }

    public int getcaptureState(String json) {
        int ret = -1;
        JSONObject extData = getExtData(json, "extData");
        try {
            ret = extData.getInt("captureState");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return ret;
    }

    public int getNaviState(String json) {
        int ret = -1;
        JSONObject extData = getExtData(json, "extData");
        try {
            ret = extData.getInt("naviState");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return ret;
    }

    public int getMusicFocus(String json) {
        int ret = -1;
        JSONObject extData = getExtData(json, "extData");
        try {
            ret = extData.getInt("reqBTMusicFocusState");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return ret;
    }

    public int getRecordScrState(String json) {
        int ret = -1;
        JSONObject extData = getExtData(json, "extData");
        try {
            ret = extData.getInt("recordingScreenState");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return ret;
    }

    public int getPCMMuteState(String json) {
        int ret = -1;
        JSONObject extData = getExtData(json, "extData");
        try {
            ret = extData.getInt("muteState");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return ret;
    }

    public int getStopPCM(String json) {
        int ret = -1;
        JSONObject extData = getExtData(json, "extData");
        try {
            ret = extData.getInt("pcmInfo");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return ret;
    }

    private String getMethod(String str) {
        String method = "";
        try {
            if (null != str) {
                JSONObject json = new JSONObject(str);
                JSONObject command = json.getJSONObject("command");
                method = command.getString("method");
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        LogUtil.v(TAG, "getMethod:" + method);
        return method;
    }

    private JSONObject getExtData(String str, String key) {
        JSONObject extData = new JSONObject();
        try {
            JSONObject json = new JSONObject(str);
            JSONObject command = json.getJSONObject("command");
            extData = command.getJSONObject(key);

        } catch (JSONException e) {
            e.printStackTrace();
        }
        return extData;
    }

    public JSONObject getCustomExtData(String jstr) {
        JSONObject extData = getExtData(jstr, "extData");
        return extData;
    }

    public HidMessage getTouchSendPoint(String json) {
        int ret = -1;
        List<Double> retList = new ArrayList<>(20);
        int j = 0;
        JSONObject extData = getExtData(json, "extData");
        try {
            int type = extData.getInt("type");
            JSONArray pointsArray = extData.getJSONArray("points");
            // 遍历 points 数组
            for (int i = 0; i < pointsArray.length(); i++) {
                // 获取每个点的数组
                JSONArray point = pointsArray.getJSONArray(i);
                double x = point.getDouble(0);
                double y = point.getDouble(1);
                retList.add(x);
                retList.add(y);

                // 输出坐标
                LogUtil.v(TAG, "getMethod: x = " + x  + ", y = " + y);
            }
            return new HidMessage(type,retList);
        } catch (JSONException e) {
            return null;
        }
    }

    /**
     * 创建iOS HID触控事件的JSON对象
     * 
     * @param type 控制状态类型
     * @param enableMoveEvents 是否启用移动事件，当值 >= 1 时才添加该字段
     * @return 包含iOS HID触控事件信息的JSON对象
     */
    public JSONObject getIosHidTouchPoint(int type, int enableMoveEvents){
        JSONObject json = new JSONObject();
        JSONObject extData = new JSONObject();
        try {
            extData.put("controlState", type);
            // 只有当enableMoveEvents >= 1时才添加该字段
            if (enableMoveEvents >= 1) {
                extData.put("enableMoveEvents", enableMoveEvents);
            }
            json.put(WE_METHOD, "onIosHidTouch");
            json.put(WE_EXT_DATA, extData);

        } catch (JSONException e) {
            e.printStackTrace();
        }
        return getJson(json);
    }

    public int getCarBTConnectedState(String json) {
        int ret = -1;
        JSONObject extData = getExtData(json, "extData");
        try {
            String connectedState = extData.getString("connectedState");
            if ("1".equals(connectedState) || "true".equals(connectedState)) {
                ret = 1;
            } else {
                ret = 0;
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return ret;
    }

    /**
     * 从JSON中获取手机蓝牙MAC地址
     * @param json JSON字符串
     * @return 手机蓝牙MAC地址，如果不存在则返回空字符串
     */
    public String getPhoneMacFromCarBTConnected(String json) {
        String phoneMac = "";
        JSONObject extData = getExtData(json, "extData");
        try {
            if (extData.has("phoneMac")) {
                phoneMac = extData.getString("phoneMac");
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return phoneMac;
    }

    /**
     * 解析车机蓝牙连接信息，包含连接状态和手机MAC地址
     * @param json JSON字符串
     * @return CarBTConnectedInfo对象，包含连接状态和手机MAC地址
     */
    public CarBTConnectedInfo parseCarBTConnectedInfo(String json) {
        LogUtil.d(TAG + TAG_BT, "SdkJson::parseCarBTConnectedInfo <<< parsing JSON: " + json);

        int connectedState = CarBTConnectedInfo.STATE_DISCONNECTED;
        String phoneMac = "";

        JSONObject extData = getExtData(json, "extData");
        try {
            // 解析连接状态
            String connectedStateStr = extData.getString("connectedState");
            if ("1".equals(connectedStateStr) || "true".equals(connectedStateStr)) {
                connectedState = CarBTConnectedInfo.STATE_CONNECTED;
            } else {
                connectedState = CarBTConnectedInfo.STATE_DISCONNECTED;
            }
            // 解析手机MAC地址
            if (extData.has("phoneMac")) {
                phoneMac = extData.getString("phoneMac");
            }

            LogUtil.d(TAG + TAG_BT, "SdkJson::parseCarBTConnectedInfo parsed connectedState=" + connectedState +
                     ", phoneMac=" + (phoneMac.isEmpty() ? "empty" : phoneMac));
        } catch (JSONException e) {
            LogUtil.e(TAG + TAG_BT, "SdkJson::parseCarBTConnectedInfo JSON parsing error", e);
            e.printStackTrace();
            // 异常情况下返回未连接状态
            connectedState = CarBTConnectedInfo.STATE_DISCONNECTED;
        }

        CarBTConnectedInfo result = new CarBTConnectedInfo(connectedState, phoneMac);
        LogUtil.d(TAG + TAG_BT, "SdkJson::parseCarBTConnectedInfo result: " + result.toString());
        return result;
    }

    /**
     * 创建车机蓝牙连接状态的JSON对象
     *
     * @param connectedState 连接状态 ("1"表示已连接, "0"表示未连接)
     * @return 包含车机蓝牙连接状态信息的JSON对象
     */
    public JSONObject getOnCarBTConnected(String connectedState) {
        JSONObject json = new JSONObject();
        JSONObject extData = new JSONObject();
        try {
            extData.put("connectedState", connectedState);
            json.put(WE_METHOD, "onCarBTConnected");
            json.put(WE_EXT_DATA, extData);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return getJson(json);
    }

    /**
     * 创建车机向手机发送当前A2DP连接的手机MAC地址的JSON对象
     *
     * @param macAddress 当前A2DP连接的手机蓝牙MAC地址
     * @return 包含手机蓝牙MAC地址信息的JSON对象
     */
    public JSONObject getOnPhoneBTMacAddress(String macAddress) {
        LogUtil.d(TAG + TAG_BT, "SdkJson::getOnPhoneBTMacAddress >>> creating JSON for macAddress: " + macAddress);

        JSONObject json = new JSONObject();
        JSONObject extData = new JSONObject();
        try {
            extData.put("macAddress", macAddress);
            json.put(WE_METHOD, "onPhoneBTMacAddress");
            json.put(WE_EXT_DATA, extData);
        } catch (JSONException e) {
            LogUtil.e(TAG + TAG_BT, "SdkJson::getOnPhoneBTMacAddress JSON creation error", e);
            e.printStackTrace();
        }

        JSONObject result = getJson(json);
        LogUtil.d(TAG + TAG_BT, "SdkJson::getOnPhoneBTMacAddress >>> generated JSON: " + result.toString());
        return result;
    }
}
