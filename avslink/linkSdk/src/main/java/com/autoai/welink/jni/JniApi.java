/*
 * Copyright (C) 2009 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.autoai.welink.jni;

import com.autoai.welink.jni.CallBack.AUDIO_DATA_CALLBACK;
import com.autoai.welink.jni.CallBack.DEVICE_STATUS_CALLBACK;
import com.autoai.welink.jni.CallBack.HU_DATA_CALLBACK;
import com.autoai.welink.jni.CallBack.IMAGE_DATA_CALLBACK;
import com.autoai.welink.jni.CallBack.JNI_MSG_CALLBACK;
import com.autoai.welink.jni.CallBack.JNI_STATUS_CALLBACK;
import com.autoai.welink.jni.CallBack.LAUNCHER_DATA_CALLBACK;
import com.autoai.welink.jni.CallBack.UPDATE_DATA_CALLBACK;
import com.autoai.welink.jni.JniStruct.strDeviceInfo;
import com.autoai.welink.jni.JniStruct.strJniCmd;
import com.autoai.welink.jni.JniStruct.strJniConfig;
import com.autoai.welink.jni.JniStruct.strJniPara;
@SuppressWarnings("unused")
public class JniApi {
	private AUDIO_DATA_CALLBACK cb_audio_data;
	private DEVICE_STATUS_CALLBACK cb_device_status;
	private HU_DATA_CALLBACK cb_hu_data;
	private IMAGE_DATA_CALLBACK cb_image_data;
	private LAUNCHER_DATA_CALLBACK cb_launcher_data;
	private JNI_MSG_CALLBACK cb_sdk_msg;
	private JNI_STATUS_CALLBACK cb_sdk_status;
	private UPDATE_DATA_CALLBACK cb_update_data;

    /** Called when the activity is first created. */
/*

	public void onCreate(Bundle savedInstanceState)
	{
		*/
/*<*************************************WlSdkExit***************************************//*

		WlSdkExit();

		*/
/*<*************************************WlSdkKeyEvent***************************************//*

		WlSdkKeyEvent(1);

		*/
/*<*************************************WlSdkTouchEvent***************************************//*

		WlSdkTouchEvent(2, 2, 2);

		*/
/*<*************************************WlSdkSendPoint***************************************//*

		WlSdkSendPoint(3, 3, 3, 3, 3);

		*/
/*<*************************************WlSdkSendPointSync***************************************//*

		WlSdkSendPointSync();

		*/
/*<*************************************WlSdkCmd***************************************//*


		*/
/*<*************************************WlSdkSetFps***************************************//*

		//WlSdkSetFps(5);

		*/
/*<*************************************WlSdkSendJstr***************************************//*

		byte[] send_jstr = new byte[256];
		for (int i = 0; i < 6; i++) {
			send_jstr[i] = 'j';
		}
		WlSdkSendJstr(send_jstr, 6);

		*/
/*<*************************************WlSdkSendRecord***************************************//*

		byte[] send_record = new byte[256];
		for (int i = 0; i < 7; i++) {
			send_record[i] = 'r';
		}
		WlSdkSendRecord(send_record, 7);

		*/
/*<*************************************WlSdkSendUpdate***************************************//*

		byte[] send_update = new byte[256];
		for (int i = 0; i < 8; i++) {
			send_update[i] = 'u';
		}
		WlSdkSendUpdate(8, send_update, 8);

		*/
/*<*************************************WlSdkSendAudio***************************************//*

		byte[] send_audio = new byte[256];
		for (int i = 0; i < 9; i++) {
			send_audio[i] = 'a';
		}
		WlSdkSendAudio(9, send_audio, 9);

		*/
/*<*************************************WlSdkSendHuData***************************************//*

		byte[] send_hu = new byte[256];
		for (int i = 0; i < 10; i++) {
			send_hu[i] = 'h';
		}
		WlSdkSendHuData(10, send_hu, 10);

		*/
/*<**************************************WlSdkGetVersion***************************************//*

		byte[] version = new byte[256];
		version = WlSdkGetVersion();
		String str_version = new String(version);
		System.out.println(str_version);

		LogUtil.e("JNI Test", "End");
	}
*/

	public native int WlSdkSetConfig(strJniConfig sdk_config);
	public native int WlSdkInit(strJniPara para);
	public native int WlSdkExit();
	public native int WlSdkKeyEvent(int key_type);
	public native int WlSdkTouchEvent(int x, int y, int touch_type);
	public native int WlSdkSendPoint(int x, int y, int id, int presure, int touch_type);
	public native int WlSdkSendPointSync();
	public native int WlSdkCmd(strJniCmd sdk_cmd);
	public native int WlSdkDeviceChange(int cmd, int para1, int para2, strDeviceInfo info);
    public native int WlSdkSetFps(int fps);
    public native int WlSdkSendJstr(byte[] jstr, int len);
    public native int WlSdkSendRecord(byte[] data, int len);
    public native int WlSdkSendUpdate(int type, byte[] data, int len);
    public native int WlSdkSendAudio(int type, byte[] data, int len);
    public native int WlSdkSendHuData(int type, byte[] data, int len);
    public native byte[] WlSdkGetVersion();
    
    /***************************************Test Callback***************************************/
    public native void NativeInitialize();

	public int SdkInit(strJniPara sdk_para){

  	  this.cb_audio_data = sdk_para.cb_audio_data;
	  this.cb_device_status = sdk_para.cb_device_status;
	  this.cb_hu_data = sdk_para.cb_hu_data;
  	  this.cb_image_data = sdk_para.cb_image_data;
	  this.cb_launcher_data = sdk_para.cb_launcher_data;
	  this.cb_sdk_msg = sdk_para.cb_sdk_msg;
  	  this.cb_sdk_status = sdk_para.cb_sdk_status;
	  this.cb_update_data = sdk_para.cb_update_data;

  	  return WlSdkInit(sdk_para);
  }

	public void _LAUNCHER_DATA_CALLBACK(byte[] data, int len){
		cb_launcher_data.launcherDataCallback(data, len);
		//cb.launcherDataCallback(data, len);
		//cb_launcher_data.doExec(data, len);
	}

	/**
	 * No usages found in All Places
	 * @param data data
	 * @param len len
	 */
	@Deprecated
	public void _AUDIO_DATA_CALLBACK(byte[] data, int len){
		cb_audio_data.audioDataCallback(data, len);
		//cb.audioDataCallback(data, len);
		//cb_audio_data.doExec(data, len);
	}

	/**
	 * No usages found in All Places
	 * @param data  data
	 * @param len len
	 */
	@Deprecated
	public void _UPDATE_DATA_CALLBACK(byte[] data, int len){
		cb_update_data.updateDataCallback(data,len);
		//cb.updateDataCallback(data,len);
		//cb_update_data.doExec(data, len);
	}

	/**
	 *  No usages found in All Places
	 * @param data data
	 * @param len len
	 * @param type type
	 */
	@Deprecated
	public void _HU_DATA_CALLBACK(byte[] data, int len, int type){
		cb_hu_data.huDataCallback(data, len, type);
		//cb.huDataCallback(data, len, type);
		//cb_hu_data.doExec(data, len, type);
	}

	public void _SDK_STATUS_CALLBACK(int status, int para1, byte[] para2){
		cb_sdk_status.jniStatusCallback(status, para1, para2);
		//cb.jniStatusCallback(status, para1, para2);
		//cb_sdk_status.doExec(status, para1, para2);
	}

	public void _DEVICE_STATUS_CALLBACK(int status){
		cb_device_status.deviceStatusCallback(status);
		//cb.deviceStatusCallback(status);
		//cb_device_status.doExec(status);
	}

	public void _SDK_MSG_CALLBACK(byte[] data, int size, int type, int para){
		cb_sdk_msg.jniMsgCallback(data, size, type, para);
		//cb.jniMsgCallback(data, size, type, para);
		//cb_sdk_msg.doExec(data, size, type, para);
	}

	public void _IMAGE_DATA_CALLBACK(byte[] data, int size, int width, int height, int spspps_data_size, int frame_type){
		cb_image_data.imageDataCallback(data, size, width, height, spspps_data_size, frame_type);
		//cb.imageDataCallback(data, size, width, height, spspps_data_size, frame_type);
		//cb_image_data.doExec(data, size, width, height, spspps_data_size, frame_type);
	}
    static {
        System.loadLibrary("wl_sdk_jni");
    }

}
