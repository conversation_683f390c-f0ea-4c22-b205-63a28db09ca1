package com.autoai.welink.sdk.util;

import com.autoai.welink.macro.WL_API_DEVICE_TYPE;
import com.autoai.welink.sdk.SdkInfo;
import com.autoai.welink.sdk.SdkPara;
@SuppressWarnings("all")
public class PointChg {
    private static PointChg instance;
    public static synchronized PointChg getInstance(){
        if(instance == null){
            instance = new PointChg();
        }
        return instance;
    }
    private int x;
    private int y;
    private int phoneWidth;
    private int phoneHeight;
    private SdkPara mAplPara = SdkPara.getInstance();
    private SdkInfo mSdkInfo = SdkInfo.getInstance();

    public int getX(){
        return this.x;
    }
    public int getY(){
        return this.y;
    }

    public int  setPoint(int x,int y,int deviceType){
        int ret = 0;
        phoneWidth = mAplPara.getPhoneWidth();
        phoneHeight = mAplPara.getPhoneHeight();
        if(deviceType == WL_API_DEVICE_TYPE.AOA){
            if(0 > x || x > mSdkInfo.getScreenWidth() || 0 > y || y > mSdkInfo.getScreenHeight())
            {
                return -2;//WL_ERR_PARAM
            }
            if(mSdkInfo.getScreenWidth() > mSdkInfo.getScreenHeight()){
                int h = mSdkInfo.getScreenHeight();
                int tmp = x;
                x = h - y;
                y = tmp;
                ret = _point_transform(x,y);
            }else{
                _pointTransform_vertical(x,y);
            }

        }else{
            if(mSdkInfo.getScreenWidth() > mSdkInfo.getScreenHeight()){
                _point_transform_ios(x,y);
            }else{
                _pointTransform_ios_vertical(x,y);
            }

        }
        return  ret;

    }

    private int _sc_check_ratio()
    {
        int ret = 0;
        if (phoneWidth * 16 < phoneHeight * 9) {
            ret = 1;
        } else if (phoneWidth * 16 > phoneHeight * 9) {
            ret = 2;
        }
        return ret;
    }

    private void  _point_transform_ios(int x, int y)
    {
        int ret = 0;
	    y	= y * phoneWidth / mSdkInfo.getScreenHeight();
	    x	= x * phoneHeight / mSdkInfo.getScreenWidth();

	    this.x = x;
	    this.y = y;
    }
    private int  _point_transform(int x, int y)
    {
        int ret = 0;
        switch (_sc_check_ratio()) {
            case 1:
            {
                float p_w = phoneHeight * 9 / 16;
                float tmp = (p_w -phoneWidth) / 2;

                x = x * (int)(p_w / mSdkInfo.getScreenHeight());
                y = y * phoneHeight / mSdkInfo.getScreenWidth();
                x = x - (int)tmp;
                if (x < 0 || x > phoneWidth) {
                    ret = -1;//WL_ERR_FAILED
                }
            }
                break;
            case 2:
                x	= x * phoneWidth / mSdkInfo.getScreenHeight();
                y	= y * phoneHeight / mSdkInfo.getScreenWidth();
                break;
            default:
                x	= x * phoneWidth /  mSdkInfo.getScreenHeight();
                y	= y * phoneHeight / mSdkInfo.getScreenWidth();
                break;
        }
        this.x = x;
        this.y = y;
        return ret;
    }

    private int _pointTransform_vertical(int x, int y)
    {
        int ret = 0;
        switch (_sc_check_ratio()) {
            case 1:
            {
                float p_w = phoneWidth * 9 / 16;
                float tmp = (p_w - phoneWidth) / 2;
                x	= x * (int)(p_w / mSdkInfo.getScreenWidth());
                y	= y * phoneHeight / mSdkInfo.getScreenHeight();
                x = x - (int)tmp;
                if (x < 0 || x > phoneWidth) {
                    ret = -1;//WL_ERR_FAILED;
                }
            }
            break;
            case 2:
		        x	= x * phoneWidth / mSdkInfo.getScreenWidth();
		        y	= y * phoneHeight / mSdkInfo.getScreenHeight();
                break;
            default:
                x	= x * phoneWidth / mSdkInfo.getScreenWidth();
                y	= y * phoneHeight / mSdkInfo.getScreenHeight();
                break;
        }
        this.x = x;
        this.y = y;
        return ret;
    }

    private void _pointTransform_ios_vertical(int x, int y)
    {
        x	= x * phoneWidth / mSdkInfo.getScreenWidth();
        y	= y * phoneHeight / mSdkInfo.getScreenHeight();
        this.x = x;
        this.y = y;
    }

}