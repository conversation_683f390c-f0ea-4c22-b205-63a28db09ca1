package com.autoai.welink.sdk.BT;

import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.os.Build;
import android.util.Log;
import com.autoai.common.util.LogUtil;

public class BtMusicFocusManager {
    private static BtMusicFocusBase mBtMusicFocusImpl;
    
    //车机与手机是否连接成功
    private static boolean isConnectPhone = false;
    private static boolean isA2dpSinkConnect = false;

    /**
     * context: 单例类中谨慎使用Context
     */
    public static void init(Context context) {
        Log.d("BtMusicFocusManager", "initVehicleType " + Build.MODEL);
        String name = Build.MODEL;
        if (name.startsWith("XPENG")) {
            mBtMusicFocusImpl = new BtMusicFocusImpl(context.getApplicationContext());
            mBtMusicFocusImpl.init();
        } else {
            //
        }
    }

    /**
     * 请求蓝牙音频焦点
     * device: Mona车机可传null
     */
    public static void requestBtMusicFocus(BluetoothDevice device) {
        if (!isConnectPhone) return;
        Log.d("BtMusicFocusManager", "requestBtMusicFocus ");
        if (mBtMusicFocusImpl != null) {
            mBtMusicFocusImpl.requestBtMusicFocus(device);
        }
    }

    /**
     * 放弃蓝牙音频焦点
     * device: Mona车机可传null
     */
    public static void abandonBtMusicFocus(BluetoothDevice device) {
        if (!isConnectPhone) return;
        Log.d("BtMusicFocusManager", "abandonBtMusicFocus");
        if (mBtMusicFocusImpl != null) {
            mBtMusicFocusImpl.abandonBtMusicFocus(device);
        }
    }

    public static boolean isConnectPhone() {
        return isConnectPhone;
    }

    public static void setConnectPhone(boolean connectPhone) {
        Log.d("BtMusicFocusManager", "set isConnectPhone from " + isConnectPhone + " to " + connectPhone);
        isConnectPhone = connectPhone;
    }

    public static boolean isA2dpSinkConnect() {
        return isA2dpSinkConnect;
    }

    public static void setA2dpSinkConnect(boolean a2dpSinkConnect) {
        Log.d("BtMusicFocusManager", "set isA2dpSinkConnect from " + isA2dpSinkConnect + " to " + a2dpSinkConnect);
        isA2dpSinkConnect = a2dpSinkConnect;
    }
}
