package com.autoai.welink.param;

import java.util.List;

/**
 * <AUTHOR>
 */
public class HidMessage {
    private int type;
    private List<Double> pointList;

    public HidMessage(int type, List<Double> pointList) {
        this.type = type;
        this.pointList = pointList;
    }

    public int getType() {
        return type;
    }

    public List<Double> getPointList() {
        return pointList;
    }

    @Override
    public String toString() {
        return "HidMessage{" +
                "type=" + type +
                ", pointList=" + pointList +
                '}';
    }
}
