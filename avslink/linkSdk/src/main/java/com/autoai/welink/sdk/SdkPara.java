package com.autoai.welink.sdk;

import com.autoai.welink.jni.Enum.WL_JNI_STATUS;
import com.autoai.welink.macro.WL_API_PAGE_TYPE;
@SuppressWarnings("all")
public class SdkPara {
    private static SdkPara instance;
    public static synchronized SdkPara getInstance(){
        if(instance == null){
            instance = new SdkPara();
        }
        return instance;
    }
    private static int sAudioRecord     = 0;
    private static int sSdkStatus       = WL_JNI_STATUS.NONE;
    private static int sBeginRunFlag    = 0;
    private static int sConfigFps       = 30;
    private static int sCurPage         = WL_API_PAGE_TYPE.INITIAL_PAGE;
    private static int sPhoneWidth      = 0;
    private static int sPhoneHeight     = 0;
    private static int sLimitFlag       = 0;
    private static boolean sStartRecord = false;
    private static int sLanguageType = 0;


    public void setAudioRecord(int para){
        sAudioRecord = para;
    }

    public int getAudioRecord(){
        return sAudioRecord;
    }

    public void setSdkStatus(int status){
        sSdkStatus = status;
    }

    public int getSdkStatus(){
        return sSdkStatus;
    }

    public void setBeginRunFlag(int flag){
        sBeginRunFlag = flag;
    }

    public int getBeginRunFlag(){
        return sBeginRunFlag;
    }

    public void setConfigFps(int fps){
        sConfigFps = fps;
    }

    public int getConfigFps(){
        return sConfigFps;
    }

    public void setCurPage(int page)
    {
        sCurPage = page;
    }

    public int getCurPage()
    {
        return sCurPage;
    }

    public void setPhoneScreen(int w,int h){
        sPhoneWidth = w;
        sPhoneHeight = h;
    }

    public int getPhoneWidth(){
        return sPhoneWidth;
    }

    public int getPhoneHeight(){
        return sPhoneHeight;
    }

    public void setLimitFlag(int flag){
        sLimitFlag = flag;
    }

    public int getLimitFlag(){
        return sLimitFlag;
    }

    public boolean isStartRecord(){
        return sStartRecord;
    }

    public void setStartRecord(boolean status){
        sStartRecord = status;
    }

    public void setLanguageType(int type){
        sLanguageType = type;
    }

    public int getLanguageType(){
        return sLanguageType;
    }

}

