package com.autoai.welink;

import static com.autoai.welink.param.HidConstants.TAG_IOS_CALIB;

import android.content.Context;
import android.os.Message;

import com.autoai.common.util.LogUtil;
import com.autoai.welink.jni.Enum.WL_JNI_CMD_TYPE;
import com.autoai.welink.jni.JniStruct.strJniCmd;
import com.autoai.welink.jni.JniStruct.strJniConfig;
import com.autoai.welink.jni.JniStruct.strJniPara;
import com.autoai.welink.macro.WL_API_APP_MSG_TYPE;
import com.autoai.welink.macro.WL_API_BUTTON_TYPE;
import com.autoai.welink.macro.WL_API_DEVICE_TYPE;
import com.autoai.welink.macro.WL_API_HARD_KEY;
import com.autoai.welink.macro.WL_API_TOUCH_TYPE;
import com.autoai.welink.param.ConfigInfo;
import com.autoai.welink.param.HidConstants;
import com.autoai.welink.sdk.Enum.WL_SDK_MSG_STATUS;
import com.autoai.welink.sdk.SdkInfo;
import com.autoai.welink.sdk.SdkJson;
import com.autoai.welink.sdk.SdkMsg;
import com.autoai.welink.sdk.Struct.StrPointInfo;

@SuppressWarnings("all")
public class SdkInterface {
    private static final String TAG = "SdkCode";
    private static final String TAG_BT = "BTPair";
    private static int sInitFlag = 0;
    private static int sConfigFlag = 0;
    private static int sLastDeviceChg = WL_API_DEVICE_TYPE.OUT;
    private static int sDeviceType = WL_API_DEVICE_TYPE.OUT;
    private static final SdkInteraction appCB = SdkInteraction.getInstance();
    private static final JniInterface jniInterface = JniInterface.getInstance();
    public static final SdkMsg gSdkMsg = SdkMsg.getInstance();
    private final SdkInfo mSdkInfo = SdkInfo.getInstance();

    private static SdkInterface sdkInterface;

    public static synchronized SdkInterface getInstance() {
        if (null == sdkInterface) {
            sdkInterface = new SdkInterface();
        }
        return sdkInterface;
    }

    public int setConfig(ConfigInfo config) {
        LogUtil.v(TAG, "setConfig \n" +
                "[configFilePath]" + config.configFilePath + "\n" +
                "[huVersion]" + config.huVersion + "\n" +
                "[serialNum]" + config.serialNum + "\n" +
                "[fps]" + config.fps + "\n" +
                "[rtt]" + config.rtt + "\n" +
                "[huVerticalFull]" + config.huVerticalFull + "\n" +
                "[videoWidth]" + config.videoWidth + "\n" +
                "[videoHeight]" + config.videoHeight + "\n" +
                "[videoX]" + config.videoX + "\n" +
                "[videoY]" + config.videoY + "\n");
        int ret = 0;
        if (0 != checkConfigPara(config)) {
            LogUtil.e(TAG, "SDK CONFIG DATA ERROR");
            //Log.v("WeLink","!!!!!SdkCode sendAudioReplyData!!!!!ERR");
            ret = -1;
            return ret;
        }
        jniInterface.nativeInitialize();
        strJniConfig sdk_config = new strJniConfig();
        mSdkInfo.setConfig(config);

        sdk_config.bt_auto_connect = mSdkInfo.getBtAutoConnect();
        sdk_config.support_decoder = mSdkInfo.getSupportDecoder();
        sdk_config.support_record = mSdkInfo.getSupportRecord();
        sdk_config.default_record = mSdkInfo.getDefaultRecord();
        sdk_config.support_hucall = mSdkInfo.getSupportHuCall();
        sdk_config.disable_driver = mSdkInfo.getDisableDriver();
        sdk_config.disable_switch = mSdkInfo.getDisableSwith();
        sdk_config.disable_device_notice = mSdkInfo.getDisableDeviceNotice();

        sdk_config.log_path = mSdkInfo.getLogPath().getBytes();
        sdk_config.aoa_pkg_size = mSdkInfo.getAoaPkgSize();
        sdk_config.welink_path = mSdkInfo.getWelinkPath().getBytes();
        sdk_config.log_type = mSdkInfo.getLogType();
        sdk_config.support_rtt = mSdkInfo.getRTT();

        sdk_config.support_HuVerticalfull = mSdkInfo.getHuVerticalFull();
        sdk_config.support_data_channel = 1;
        sdk_config.support_audio_channel = 1;
        sdk_config.support_hu_channel = 0;
        ret = jniInterface.setConfig(sdk_config);
        if (0 == ret) {
            sConfigFlag = 1;
        }
        return ret;

    }

    public void setContext(Context context) {
        LogUtil.v(TAG, "setContext");
    }

    public int init() {
        LogUtil.v(TAG, "init");
        int ret = 0;
        if (0 == sConfigFlag) {
            LogUtil.e(TAG, "SDK CONFIG DATA IS NOT SETTED");
            return -1;
        }
        strJniPara para = new strJniPara();
        /*
        para.cb_launcher_data = new LauncherDataCallBack();
        para.cb_device_status = new DeviceStatusCallBack();
        para.cb_image_data = new ImageCallBack();
        para.cb_sdk_msg = new SdkMsgCallBack();
        para.cb_sdk_status = new SdkStatusCallBack();
        para.cb_audio_data = new AudioDataCallBack();
*/
        para.cb_audio_data = JniInteraction.getInstance();
        para.cb_launcher_data = JniInteraction.getInstance();
        para.cb_device_status = JniInteraction.getInstance();
        para.cb_image_data = JniInteraction.getInstance();
        para.cb_sdk_msg = JniInteraction.getInstance();
        para.cb_sdk_status = JniInteraction.getInstance();

        para.hu_version = mSdkInfo.getHuVersion().getBytes();
        para.screen_width = mSdkInfo.getScreenWidth();
        para.screen_height = mSdkInfo.getScreenHeight();

        sInitFlag = 1;
        //gSdkUdp.startUdpService();
        jniInterface.init(para);

        return ret;
    }

    public int deinit() {
        LogUtil.v(TAG, "deinit");
        int ret = 0;
        if (1 == sInitFlag) {
            jniInterface.exit();
            mSdkInfo.deinit();
        } else {
            LogUtil.e(TAG, "DEINIT FAILED,SDK IS NOT INIT");
            ret = -1;
        }
        sInitFlag = 0;
        sConfigFlag = 0;
        return 0;
    }

    public int sendButton(int btn, int para) {
        LogUtil.v(TAG, "sendButton:btn=" + btn + " para=" + para);
        int ret = 0;
        if (0 == sInitFlag) {
            LogUtil.e(TAG, "SDK IS NOT INIT");
            return -1;
        }
        if (0 >= btn || WL_API_BUTTON_TYPE.MAX_NUM <= btn) {
            LogUtil.e(TAG, "BUTTON IS NOT EXIST");
            return -1;
        }
        Message msg = new Message();
        msg.arg1 = btn;
        msg.arg2 = para;
        msg.what = WL_SDK_MSG_STATUS.SEND_BUTTON;
        gSdkMsg.sendMsg(msg);
        return 0;
    }

    @Deprecated
    public int sendPoint(int x, int y, int id, int pressure, int type) {
        LogUtil.v(TAG, "sendPoint:x=" + x + " y=" + y + " type=" + type);
        int ret = 0;
        if (0 == sInitFlag) {
            LogUtil.e(TAG, "SDK IS NOT INIT");
            return -1;
        }
        if (0 > x || 0 > y || 0 > id || type == WL_API_TOUCH_TYPE.UNKNOWN) {
            LogUtil.e(TAG, "POINT LOCATION OR TYPE ERROR");
            return -1;
        }
        StrPointInfo pointInfo = new StrPointInfo();
        pointInfo.x = x;
        pointInfo.y = y;
        pointInfo.id = id;
        pointInfo.presure = pressure;
        if (5 >= pointInfo.presure) {
            pointInfo.presure = 50;
        }
        pointInfo.type = type;
        Message msg = new Message();
        msg.obj = pointInfo;
        msg.what = WL_SDK_MSG_STATUS.SEND_POINT;
        gSdkMsg.sendMsg(msg);

        return ret;
    }

    @Deprecated
    public int sendPointSync() {
        LogUtil.v(TAG, "sendPointSync");
        if (0 == sInitFlag) {
            LogUtil.e(TAG, "SDK IS NOT INIT");
            return -1;
        }
        Message msg = new Message();
        msg.what = WL_SDK_MSG_STATUS.SEND_POINT_SYNC;
        gSdkMsg.sendMsg(msg);
        return 0;
    }

    public int sendHardKey(int key, int para) {
        SdkJson json;
        String str;
        LogUtil.i(TAG, "sendHardKey, key = " + key);
        switch (key) {
            case WL_API_HARD_KEY.WL_API_HARD_KEY_RECORD:
                json = SdkJson.getInstance();
                str = json.getOnKeyStateInfo(3).toString();
                if (null != str) {
                    jniInterface.sendJstr(str.getBytes(), str.length());
                }
                break;
            case WL_API_HARD_KEY.WL_API_HARD_KEY_PRE:
                json = SdkJson.getInstance();
                str = json.getOnControlMobileAduio(3).toString();
                if (null != str) {
                    jniInterface.sendJstr(str.getBytes(), str.length());
                }
                break;
            case WL_API_HARD_KEY.WL_API_HARD_KEY_NEXT:
                json = SdkJson.getInstance();
                str = json.getOnControlMobileAduio(4).toString();
                if (null != str) {
                    jniInterface.sendJstr(str.getBytes(), str.length());
                }
                break;
            case WL_API_HARD_KEY.WL_API_HARD_KEY_VR_START:
                json = SdkJson.getInstance();
                str = json.getOnKeyStateInfo(1).toString();
                if (null != str) {
                    jniInterface.sendJstr(str.getBytes(), str.length());
                }
                break;
            case WL_API_HARD_KEY.WL_API_HARD_KEY_VR_END:
                json = SdkJson.getInstance();
                str = json.getOnKeyStateInfo(2).toString();
                if (null != str) {
                    jniInterface.sendJstr(str.getBytes(), str.length());
                }
                break;
        }
        return 0;
    }

//    public int sendAudioStateHardKey(int key, int para){
//        LogUtil.i(TAG,"sendAudioStateHardKey, key = "+key);
//        SdkJson json = SdkJson.getInstance();
//        String str = json.getOnControlMobileAduio(key).toString();
//        if(null != str) {
//            jniInterface.sendJstr(str.getBytes(), str.length());
//        }
//        return 0;
//    }

//    public int sendPModeHardKey(int key, int para){
//        LogUtil.i(TAG,"sendPModeHardKey, key = "+key);
//        SdkJson json = SdkJson.getInstance();
//        String str = json.getOnPModeKeyStateInfo(key).toString();
//        if(null != str) {
//            jniInterface.sendJstr(str.getBytes(), str.length());
//        }
//        return 0;
//    }

//    public int sendVehicleInfo(String vehicleBrand, String vehicleModel){
//        LogUtil.i(TAG,"sendVehicleInfo, vehicleBrand = "+vehicleBrand.toString()+", vehicleModel = "+vehicleModel.toString());
//        SdkJson json = SdkJson.getInstance();
//        String str = json.getOnHuVehicleInfo(vehicleBrand, vehicleModel).toString();
//        if(null != str) {
//            jniInterface.sendJstr(str.getBytes(), str.length());
//        }
//        return 0;
//    }

    public int sendHuMicState(boolean state) {
        LogUtil.i(TAG, "sendHuMicState, state = " + state);
        SdkJson json = SdkJson.getInstance();
        String str = json.getOnHuMicState(state).toString();
        if (null != str) {
            jniInterface.sendJstr(str.getBytes(), str.length());
        }
        return 0;
    }

    @Deprecated
    public int sendHuRvcState(int rvcState) {
        LogUtil.i(TAG, "sendHuRvcState, rvcState = " + rvcState);
        SdkJson json = SdkJson.getInstance();
        String str = json.getOnHuRvcState(rvcState).toString();
        if (null != str) {
            jniInterface.sendJstr(str.getBytes(), str.length());
        }
        return 0;
    }

    public int sendControlEnAndCh(int controlState){
        LogUtil.i(TAG,"sendControlEnAndCh, controlState = "+controlState);
        SdkJson json = SdkJson.getInstance();
        String str = json.getOnControlEnAndCh(controlState).toString();
        if(null != str) {
            jniInterface.sendJstr(str.getBytes(), str.length());
        }
        return 0;
    }

//    public int sendResBtFocusState(int btFocusState){
//        LogUtil.i(TAG,"sendResBtFocusState, btFocusState = "+btFocusState);
//        SdkJson json = SdkJson.getInstance();
//        String str = json.getOnResBtMusicFocus(btFocusState).toString();
//        if(null != str) {
//            jniInterface.sendJstr(str.getBytes(), str.length());
//        }
//        return 0;
//    }

    @Deprecated
    public int sendHuKeyState(int keyState){
        LogUtil.i(TAG,"sendHuKeyState, keyState = "+keyState);
        SdkJson json = SdkJson.getInstance();
        String str = json.getOnHuKeyState(keyState).toString();
        if(null != str) {
            jniInterface.sendJstr(str.getBytes(), str.length());
        }
        return 0;
    }

    public int sendBTMacInfo(String btMacInfo) {
        LogUtil.i(TAG, "sendBTMacInfo, btMacInfo = " + btMacInfo);
        SdkJson json = SdkJson.getInstance();
        String str = json.getOnAutoBTConnection(btMacInfo).toString();
        if (null != str) {
            jniInterface.sendJstr(str.getBytes(), str.length());
        }
        return 0;
    }

    /**
     * 向手机端发送当前A2DP连接的手机蓝牙MAC地址
     * @param macAddress 当前A2DP连接的手机蓝牙MAC地址
     * @return 发送结果
     */
    public int sendPhoneBTMacAddress(String macAddress) {
        LogUtil.i(TAG + TAG_BT, "SdkInterface::sendPhoneBTMacAddress >>> sending A2DP connected phone MAC to phone: " + macAddress);
        SdkJson json = SdkJson.getInstance();
        String str = json.getOnPhoneBTMacAddress(macAddress).toString();
        if (null != str) {
            LogUtil.d(TAG + TAG_BT, "SdkInterface::sendPhoneBTMacAddress >>> sending JSON via JNI: " + str);
            jniInterface.sendJstr(str.getBytes(), str.length());
            LogUtil.d(TAG + TAG_BT, "SdkInterface::sendPhoneBTMacAddress >>> JSON sent successfully");
        } else {
            LogUtil.e(TAG + TAG_BT, "SdkInterface::sendPhoneBTMacAddress failed to generate JSON");
        }
        return 0;
    }

    public int sendMsg(int msg, Object para) {
        LogUtil.v(TAG, "sendMsg:msg =" + msg);
        if (0 == sInitFlag) {
            LogUtil.e(TAG, "SDK IS NOT INIT");
            return -1;
        }
        if (0 >= msg || WL_API_APP_MSG_TYPE.MAX_NUM <= msg) {
            LogUtil.e(TAG, "THIS MSG IS NOT EXIST");
            return -1;
        }
        Message message = new Message();
        switch (msg) {
            case WL_API_APP_MSG_TYPE.FOREGROUND:
                message.what = WL_SDK_MSG_STATUS.CAR_WELINK_FOREGROUND;
                break;
            case WL_API_APP_MSG_TYPE.BACKGROUND:
                message.what = WL_SDK_MSG_STATUS.CAR_WELINK_BACKGROUND;
                break;
            case WL_API_APP_MSG_TYPE.BT_ADDR:
                message.what = WL_SDK_MSG_STATUS.CAR_BT_ADDR;
                if (null != para) {
                    message.obj = para;
                }
                break;
            case WL_API_APP_MSG_TYPE.SET_LANGUAGE:
                if (null != para) {
                    message.arg1 = (int)para;
                }
                message.what = WL_SDK_MSG_STATUS.CAR_SET_LANGUAGE;
                break;
            case WL_API_APP_MSG_TYPE.SET_DRIVE_STAT:
                if (null != para) {
                    message.arg1 = (int) para;
                }
                message.what = WL_SDK_MSG_STATUS.CAR_SET_SAFETY_DRIVING_STATUS;
                break;
            case WL_API_APP_MSG_TYPE.AUDIO_EXIT:
                message.what = WL_SDK_MSG_STATUS.CAR_AUDIO_EXIT;
                break;
            case WL_API_APP_MSG_TYPE.AUDIO_RESUME:
                message.what = WL_SDK_MSG_STATUS.CAR_AUDIO_RESUME;
                break;
            case WL_API_APP_MSG_TYPE.SPEECH_RECOGNITION:
                message.what = WL_SDK_MSG_STATUS.SPEECH_RECOGNITION;
                break;
            case WL_API_APP_MSG_TYPE.PREVIOUS_TRACK:
                message.what = WL_SDK_MSG_STATUS.PREVIOUS_TRACK;
                break;
            case WL_API_APP_MSG_TYPE.NEXT_TRACK:
                message.what = WL_SDK_MSG_STATUS.NEXT_TRACK;
                break;
            case WL_API_APP_MSG_TYPE.MUSIC_STOP:
                message.what = WL_SDK_MSG_STATUS.MUSIC_STOP;
                break;
            case WL_API_APP_MSG_TYPE.MUSIC_PLAY:
                message.what = WL_SDK_MSG_STATUS.MUSIC_PLAY;
                break;
//            case WL_API_APP_MSG_TYPE.AUDIOFOCUS_LOSS:
//                if (null != para) {
//                    message.arg1 = (int)para;
//                }
//                message.what = WL_SDK_MSG_STATUS.AUDIOFOCUS_LOSS;
//                break;
//            case WL_API_APP_MSG_TYPE.AUDIOFOCUS_GAIN:
//                if (null != para) {
//                    message.arg1 = (int)para;
//                }
//                message.what = WL_SDK_MSG_STATUS.AUDIOFOCUS_GAIN;
//                break;
            case WL_API_APP_MSG_TYPE.HU_BT_PHONE:
                if (null != para) {
                    message.arg1 = (int) para;
                }
                message.what = WL_SDK_MSG_STATUS.HU_BT_PHONE;
                break;
            case WL_API_APP_MSG_TYPE.HU_LOCAL_VR_STATE:
                if (null != para) {
                    message.arg1 = (int) para;
                }
                message.what = WL_SDK_MSG_STATUS.HU_LOCAL_VR_STATE;
                break;
            case WL_API_APP_MSG_TYPE.HU_REQ_APP_DATA:
                if (null != para) {
                    message.arg1 = (int) para;
                }
                message.what = WL_SDK_MSG_STATUS.HU_REQ_APP_DATA;
                break;

            case WL_API_APP_MSG_TYPE.CAR_DRIVER_STATUS_CHANGE:
                if (null != para) {
                    message.arg1 = (int) para;
                }
                message.what = WL_SDK_MSG_STATUS.HU_CHECK_PHONE_IS_PLAYING_VIDEO;
                break;
            case WL_API_APP_MSG_TYPE.HU_REQ_KEY_FRAME:
                if (para instanceof Message) {
                    message.arg1 = ((Message) para).arg1;
                    message.arg2 = ((Message) para).arg2;
                }
                message.what = WL_SDK_MSG_STATUS.HU_REQ_KEY_FRAME;
                break;
            default:
                break;

        }
        gSdkMsg.sendMsg(message);
        return 0;
    }

    public int deviceChg(int device, String ip) {
        LogUtil.v(TAG, "deviceChg: device=" + device + " ip=" + ip);
        if (0 == sInitFlag) {
            LogUtil.e(TAG, "SDK IS NOT INIT");
            return -1;
        }
        setDeviceType(device);
        /*
        if(null !=deviceinfo && null != deviceinfo.usb_info && 64 <= deviceinfo.usb_info.length)
        {
            Log.w(TAG,"deviceChg");
            return -1;
        }
        */
        strJniCmd cmd = new strJniCmd();
        cmd.cmd = WL_JNI_CMD_TYPE.DEVICE_CHG;
        cmd.para1 = deviceTypeTransfer(device);
        if (device == WL_API_DEVICE_TYPE.OUT) {
            cmd.para2 = sLastDeviceChg;
        }
        sLastDeviceChg = deviceTypeTransfer(device);
        if (null != ip) {
            cmd.para3 = ip.getBytes();
        }
        jniInterface.cmd(cmd);
        return 0;
    }

    @Deprecated
    public int sendMicData(byte[] data, int size) {
        LogUtil.v(TAG, "sendMicData");
        if (0 == sInitFlag) {
            LogUtil.w(TAG, "sendMicData");
            return -1;
        }
        if (0 >= size || null == data) {
            LogUtil.w(TAG, "sendMicData");
            return -1;
        }
        return jniInterface.sendRecord(data, size);
    }

    @Deprecated
    public int sendAudioReplyData(int type, byte[] data, int size) {
        LogUtil.v(TAG, "sendAudioReplyData: type=" + type + " size=" + size);
        if (0 == sInitFlag) {
            LogUtil.w(TAG, "sendAudioReplyData");
            return -1;
        }
        if (0 >= size || null == data) {
            LogUtil.w(TAG, "sendAudioReplyData");
            return -1;
        }
        return jniInterface.sendAudio(type, data, size);
    }

    @Deprecated
    public int sendHuData(int type, byte[] data, int size) {
        LogUtil.v(TAG, "sendHuData: type=" + type + " size=" + size);
        if (0 == sInitFlag) {
            LogUtil.e(TAG, "SDK IS NOT INIT");
            return -1;
        }
        if (0 >= size || null == data) {
            LogUtil.e(TAG, "HU DATA IS NULL");
            return -1;
        }
        return jniInterface.sendHuData(type, data, size);
    }

    @Deprecated
    public String getVersion() {
        return "V.APL.050";
    }

    public void setCallbackFunc(SdkListener callback) {
        appCB.setListener(callback);
    }

    private int checkConfigPara(ConfigInfo config) {
        LogUtil.v(TAG, "checkConfigPara:");

        int ret = 0;
        if (null == config) {
            LogUtil.e(TAG, "CONFIG DATA IS NULL");
            return -1;
        }
        if (null != config.configFilePath && 256 < config.configFilePath.length()) {
            LogUtil.e(TAG, "CONFIG FILE PATH ERROR");
            return -1;
        }
        if (null != config.huVersion && 64 < config.huVersion.length()) {
            LogUtil.e(TAG, "HU VERSION ERROR");
            return -1;
        }
        if (null != config.serialNum && 64 < config.serialNum.length()) {
            LogUtil.e(TAG, "SERIALNUM ERROR");
            return -1;
        }
        int x = config.videoX;
        int y = config.videoY;
        int w = config.videoWidth;
        int h = config.videoHeight;
        if (0 > x || 0 > y || 0 >= w || 0 >= h) {
            LogUtil.e(TAG, "VIDEO SIZE ERROR");
            return -1;
        }
        return ret;
    }

    private static int bytes2Int(byte[] bytes) {
        int num = bytes[0] & 0xFF;
        num |= ((bytes[1] << 8) & 0xFF);
        num |= ((bytes[2] << 16) & 0xFF);
        num |= ((bytes[3] << 24) & 0xFF);
        return num;
    }

    private int deviceTypeTransfer(int type) {
        int ret = -1;
        switch (type) {
            case WL_API_DEVICE_TYPE.OUT:
                ret = 1;
                break;
            case WL_API_DEVICE_TYPE.AOA:
                ret = 7;
                break;
            case WL_API_DEVICE_TYPE.IDB:
                ret = 8;
                break;
            case WL_API_DEVICE_TYPE.WLAN_IPHONE:
                ret = 9;
                break;
            case WL_API_DEVICE_TYPE.WLAN_ANDROID:
                ret = 10;
                break;
            case WL_API_DEVICE_TYPE.EAP:
                ret = 11;
                break;
            case WL_API_DEVICE_TYPE.WLAN_HARMONY:
                ret = 13;
                break;
            default:
                break;
        }

        return ret;
    }

    public static void setDeviceType(int type) {
        sDeviceType = type;
    }

    public static int getDeviceType() {
        return sDeviceType;
    }


    /**
     * 发送 hid 触控命令给到 iOS
     * @param controlState 控制协议命令  @ON_TOUCH_POINT_END_SUCCESS
     * @param enableMoveEvents 是否收集按下，移动，抬起事件，默认值为0
     * @return 返回0表示发送成功
     */
    public int sendIosHidTouchPoint(int controlState, int enableMoveEvents){
        SdkJson json = SdkJson.getInstance();
        String str = json.getIosHidTouchPoint(controlState, enableMoveEvents).toString();
        String stateStr = HidConstants.getHidTouchPointStateToIOSString(controlState);
        LogUtil.i(TAG + TAG_IOS_CALIB,"SdkInterface:sendIosHidTouchPoint >>> sending to iOS: " + stateStr + "(" + controlState + "), enableMoveEvents=" + enableMoveEvents + " " + str);
        if(null != str) {
            jniInterface.sendJstr(str.getBytes(), str.length());
        }
        return 0;
    }

    /**
     * 发送 hid 触控命令给到 iOS
     * @param controlState 控制协议命令  @ON_TOUCH_POINT_END_SUCCESS
     * @return 返回0表示发送成功
     */
    public int sendIosHidTouchPoint(int controlState){
        return sendIosHidTouchPoint(controlState, 0);
    }

    /**
     * 获取HID触控点状态的字符串描述
     * @param state 状态码
     * @return 状态描述
     */
    private String getHidTouchPointStateString(int state) {
        switch (state) {
            case HidConstants.ON_TOUCH_POINT_REQ_SUCCESS:
                return "ON_TOUCH_POINT_REQ_SUCCESS";
            case HidConstants.ON_TOUCH_POINT_REQ_FAIL:
                return "ON_TOUCH_POINT_REQ_FAIL";
            case HidConstants.ON_TOUCH_POINT_BEGIN:
                return "ON_TOUCH_POINT_BEGIN";
            case HidConstants.ON_TOUCH_POINT_END:
                return "ON_TOUCH_POINT_END";
            case HidConstants.ON_TOUCH_POINT_END_SUCCESS:
                return "ON_TOUCH_POINT_END_SUCCESS";
            case HidConstants.ON_TOUCH_POINT_END_FAIL:
                return "ON_TOUCH_POINT_END_FAIL";
            case HidConstants.ON_TOUCH_POINT_PREPARE:
                return "ON_TOUCH_POINT_PREPARE";
            case HidConstants.ON_TOUCH_POINT_PREPARE_END:
                return "ON_TOUCH_POINT_PREPARE_END";
            default:
                return "UNKNOWN";
        }
    }
}
