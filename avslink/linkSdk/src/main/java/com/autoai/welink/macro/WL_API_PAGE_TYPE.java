package com.autoai.welink.macro;
@SuppressWarnings("unused")
public interface WL_API_PAGE_TYPE {
	int SOURCE_OFF = 0;							/*< Page source off(background)  */
	int INITIAL_PAGE = 1;						/*< Page none  */
	int HELP_PAGE = 2;							/*< Page help */
	int CONNECTING_PAGE_OFFICIAL_ACCOUNT = 3;	/*< Page official account  */
	int CONNECTING_PAGE = 4;					/*< Page connecting  */
	int ERR_PAGE_SDK = 5;						/*< Page sdk error  */
	int ERR_PAGE_CONNECT = 6;					/*< Page connect error  */
	int ERR_PAGE_HEART = 7;						/*< Page heart error  */
	int RUNNING_PAGE_SAFE_DRIVING = 8;			/*< Page safe driving  */
	int RUNNING_PAGE_WECHAT_QRCODE = 9;			/*< Page wechat qrcode  */
	int RUNNING_PAGE_WECHAT_CONFIRM_LOGIN = 10;	/*< Page wechat login confirm  */
	int RUNNING_PAGE_WECHAT_LOGIN_SUCCESS = 11;	/*< Page wechat login success  */
	int RUNNING_PAGE_LIMIT = 12;				/*< Page limit  */
	int RUNNING_PAGE = 13;						/*< Page running  */
	int NUM = 14;								/*< Page max num, do not care  */
}
