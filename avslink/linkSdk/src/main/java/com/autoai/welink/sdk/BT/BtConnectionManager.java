package com.autoai.welink.sdk.BT;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothHidDevice;
import android.bluetooth.BluetoothProfile;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Handler;
import android.os.Looper;
import android.annotation.SuppressLint;

import com.autoai.common.util.LogUtil;

import java.lang.reflect.Method;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * <AUTHOR> system
 * @description : 蓝牙连接管理类单例，统一管理蓝牙相关功能
 * @date : 2024/12/07
 * @version : 1.0
 */
public class BtConnectionManager {
    private static final String TAG = "BtConnectionManager";
    private static final int A2DP_SINK = 11;
    private static volatile BtConnectionManager instance;

    private Context mContext;
    private SystemBluetoothReceiver mSystemBluetoothReceiver;
    private boolean isSystemReceiverRegistered = false;

    // 蓝牙状态管理相关
    private Set<BluetoothDevice> deviceSet = ConcurrentHashMap.newKeySet();
    private Set<BlueToothSysBroadcastReceiver> bluetoothReceivers = new CopyOnWriteArraySet<>();

    // 延迟HID连接任务处理
    private final Handler handler = new Handler(Looper.getMainLooper());
    private BluetoothDevice pendingHidDevice = null;
    private Runnable pendingHidConnection = null;

    // A2DP连接状态变化广播 - 通过反射动态获取
    private static String ACTION_A2DP_CONNECTION_STATE_CHANGED;

    // A2DP Profile管理
    private BluetoothAdapter mBluetoothAdapter;
    private BluetoothProfile mA2dpSink;
    private BluetoothProfile.ServiceListener mA2dpProfileListener;
    private boolean isBtMusicFocusManagerInitialized = false;

    // HID操作回调接口
    private HidOperationCallback hidOperationCallback;
    
    private BtConnectionManager() {
        // 私有构造函数，防止外部实例化
        // 初始化A2DP连接状态变化广播action
        initA2dpConnectionAction();
        // 初始化A2DP Profile ServiceListener
        initA2dpProfileListener();
    }



    /**
     * HID操作回调接口
     */
    public interface HidOperationCallback {
        /**
         * 注册HID服务
         */
        void registerHIDAfterEnable();

        /**
         * 注销HID服务
         */
        void unregisterHIDAfterDisabled();

        /**
         * 连接蓝牙HID设备
         * @param device 蓝牙设备
         */
        void connectBluetoothHIDDevice(BluetoothDevice device);
    }

    /**
     * 初始化A2DP连接状态变化广播action
     */
    private void initA2dpConnectionAction() {
        if (ACTION_A2DP_CONNECTION_STATE_CHANGED == null) {
            try {
                // 通过反射获取BluetoothA2dpSink中的ACTION_CONNECTION_STATE_CHANGED常量
                Class<?> clazz = Class.forName("android.bluetooth.BluetoothA2dpSink");
                java.lang.reflect.Field field = clazz.getDeclaredField("ACTION_CONNECTION_STATE_CHANGED");
                field.setAccessible(true);
                ACTION_A2DP_CONNECTION_STATE_CHANGED = (String) field.get(null);
                LogUtil.d(TAG, "BtConnectionManager::initA2dpConnectionAction: success, ACTION=" + ACTION_A2DP_CONNECTION_STATE_CHANGED);
            } catch (Exception e) {
                // 如果反射失败，使用默认值
                ACTION_A2DP_CONNECTION_STATE_CHANGED = "android.bluetooth.a2dp-sink.profile.action.CONNECTION_STATE_CHANGED";
                LogUtil.e(TAG, "BtConnectionManager::initA2dpConnectionAction: reflection failed, use default, error=" + e.getMessage());
            }
        }
    }

    /**
     * 初始化A2DP Profile ServiceListener
     * 统一管理A2DP Profile连接和设备获取，避免重复逻辑
     */
    private void initA2dpProfileListener() {
        mA2dpProfileListener = new BluetoothProfile.ServiceListener() {
            @Override
            @SuppressLint("MissingPermission")
            public void onServiceConnected(int profile, BluetoothProfile proxy) {
                LogUtil.d(TAG, "A2DP onServiceConnected profile=" + profile);
                if (profile == A2DP_SINK) {
                    mA2dpSink = proxy;
                    LogUtil.d(TAG, "BtConnectionManager::onServiceConnected: mA2dpSink connected");
                    
                    // A2DP Profile连接成功后再初始化BtMusicFocusManager
                    if (!isBtMusicFocusManagerInitialized && mContext != null) {
                        BtMusicFocusManager.init(mContext);
                        isBtMusicFocusManagerInitialized = true;
                        LogUtil.d(TAG, "BtConnectionManager::onServiceConnected: BtMusicFocusManager initialized after A2DP connected");
                    }
                    
                    // A2DP Profile连接后，自动初始化蓝牙状态
                    try {
                        List<BluetoothDevice> connectedDevices = mA2dpSink.getConnectedDevices();
                        if (!connectedDevices.isEmpty()) {
                            deviceSet.addAll(connectedDevices);
                            LogUtil.d(TAG, "BtConnectionManager::onServiceConnected: auto-updated deviceSet, found devices=" 
                                    + connectedDevices.size() + ", total=" + deviceSet.size());
                        }
                        // 确保设备状态完整初始化
                        LogUtil.d(TAG, "BtConnectionManager::onServiceConnected: A2DP Profile ready, device management active");
                    } catch (Exception e) {
                        LogUtil.e(TAG, "BtConnectionManager::onServiceConnected: failed to get connected devices", e);
                    }
                }
            }

            @Override
            public void onServiceDisconnected(int profile) {
                LogUtil.d(TAG, "A2DP onServiceDisconnected profile=" + profile);
                if (profile == A2DP_SINK) {
                    mA2dpSink = null;
                    LogUtil.d(TAG, "BtConnectionManager::onServiceDisconnected: mA2dpSink disconnected");
                }
            }
        };
    }

    /**
     * 初始化蓝牙适配器和A2DP Profile
     */
    private void initBluetoothAdapter() {
        if (mBluetoothAdapter == null) {
            mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        }
        
        if (mBluetoothAdapter != null && mA2dpProfileListener != null) {
            // 获取A2DP Profile代理
            mBluetoothAdapter.getProfileProxy(mContext, mA2dpProfileListener, A2DP_SINK);
            LogUtil.d(TAG, "BtConnectionManager::initBluetoothAdapter: A2DP Profile proxy requested");
        } else {
            LogUtil.w(TAG, "BtConnectionManager::initBluetoothAdapter: BluetoothAdapter or ProfileListener is null");
        }
    }

    /**
     * 获取单例实例
     */
    public static BtConnectionManager getInstance() {
        if (instance == null) {
            synchronized (BtConnectionManager.class) {
                if (instance == null) {
                    instance = new BtConnectionManager();
                    LogUtil.d(TAG, "BtConnectionManager::getInstance: created new instance");
                }
            }
        }
        return instance;
    }
    
    /**
     * 初始化蓝牙管理器
     * @param context 应用上下文
     */
    public void init(Context context) {
        if (context == null) {
            LogUtil.e(TAG, "BtConnectionManager::init: context is null");
            return;
        }

        // 防止重复初始化
        if (mContext != null) {
            LogUtil.w(TAG, "BtConnectionManager::init: already initialized");
            return;
        }

        mContext = context.getApplicationContext();

        // 初始化蓝牙适配器和A2DP Profile
        initBluetoothAdapter();


        // 初始化系统蓝牙接收器
        if (mSystemBluetoothReceiver == null) {
            mSystemBluetoothReceiver = new SystemBluetoothReceiver();
        }

        // 注册系统蓝牙监听
        registerSystemBluetoothReceiver();

        LogUtil.d(TAG, "BtConnectionManager::init: completed");
    }

    /**
     * 初始化蓝牙状态 - 需要在A2DP Profile连接后调用
     * 从外部调用，确保A2DP Profile已就绪
     */
    public void initBtStateWhenReady() {
        if (mA2dpSink != null) {
            initBtState();
            LogUtil.d(TAG, "BtConnectionManager::initBtStateWhenReady: executed with A2DP ready");
        } else {
            LogUtil.d(TAG, "BtConnectionManager::initBtStateWhenReady: A2DP not ready, will auto-init when connected");
        }
    }
    


    /**
     * 注册系统蓝牙状态监听
     */
    @SuppressLint("UnspecifiedRegisterReceiverFlag")
    private void registerSystemBluetoothReceiver() {
        if (mContext == null || mSystemBluetoothReceiver == null) {
            LogUtil.e(TAG, "BtConnectionManager::registerSystemBluetoothReceiver: context or receiver is null");
            return;
        }

        if (isSystemReceiverRegistered) {
            LogUtil.w(TAG, "BtConnectionManager::registerSystemBluetoothReceiver: already registered");
            return;
        }

        try {
            IntentFilter filter = new IntentFilter();
            // 系统蓝牙开关 打开/关闭
            filter.addAction(BluetoothAdapter.ACTION_STATE_CHANGED);
            // 蓝牙适配器连接状态变化 - 用于延迟HID连接逻辑
            filter.addAction(BluetoothAdapter.ACTION_CONNECTION_STATE_CHANGED);
            // A2DP连接状态变化 - 整合原BluetoothReceiver功能
            filter.addAction(ACTION_A2DP_CONNECTION_STATE_CHANGED);
            // 设备连接/断开
            filter.addAction(BluetoothDevice.ACTION_ACL_CONNECTED);
            filter.addAction(BluetoothDevice.ACTION_ACL_DISCONNECTED);
            // HID设备连接状态变化
            filter.addAction(BluetoothHidDevice.ACTION_CONNECTION_STATE_CHANGED);

            mContext.registerReceiver(mSystemBluetoothReceiver, filter);
            isSystemReceiverRegistered = true;
            LogUtil.d(TAG, "BtConnectionManager::registerSystemBluetoothReceiver: success");
        } catch (Exception e) {
            LogUtil.e(TAG, "BtConnectionManager::registerSystemBluetoothReceiver: failed, error=" + e.getMessage());
        }
    }
    


    /**
     * 注销系统蓝牙状态监听
     */
    private void unregisterSystemBluetoothReceiver() {
        if (mContext == null || mSystemBluetoothReceiver == null || !isSystemReceiverRegistered) {
            return;
        }

        try {
            mContext.unregisterReceiver(mSystemBluetoothReceiver);
            isSystemReceiverRegistered = false;
            LogUtil.d(TAG, "BtConnectionManager::unregisterSystemBluetoothReceiver: success");
        } catch (Exception e) {
            LogUtil.e(TAG, "BtConnectionManager::unregisterSystemBluetoothReceiver: failed, error=" + e.getMessage());
        }
    }

    /**
     * 销毁蓝牙管理器
     */
    public void destroy() {
        // 注销蓝牙监听
        unregisterSystemBluetoothReceiver();

        // 取消待处理的HID连接任务
        cancelPendingHidConnection();

        // 清理A2DP Profile资源
        cleanupA2dpProfile();

        // 清理资源
        mSystemBluetoothReceiver = null;
        deviceSet.clear();
        bluetoothReceivers.clear();
        mContext = null;

        // 重置单例实例，确保下次调用getInstance()时重新创建
        synchronized (BtConnectionManager.class) {
            instance = null;
        }

        LogUtil.d(TAG, "BtConnectionManager::destroy: completed");
    }
    
    /**
     * 获取蓝牙连接状态
     */
    public boolean isBluetoothConnected() {
        if (mContext == null) {
            LogUtil.w(TAG, "BtConnectionManager::isBluetoothConnected: not initialized, return false");
            return false;
        }
        boolean result = RequestBTMusicFocusUtil.isBluetoothConnected(mContext);
        LogUtil.d(TAG, "BtConnectionManager::isBluetoothConnected: result=" + result);
        return result;
    }

    /**
     * 检查A2DP Profile是否已连接
     * 用于BtMusicFocusImpl安全调用系统接口
     */
    public boolean isA2dpProfileConnected() {
        if (mContext == null) {
            LogUtil.w(TAG, "BtConnectionManager::isA2dpProfileConnected: not initialized, return false");
            return false;
        }
        boolean result = RequestBTMusicFocusUtil.isBluetoothConnected(mContext);
        LogUtil.d(TAG, "BtConnectionManager::isA2dpProfileConnected: result=" + result);
        return result;
    }

    /**
     * 获取A2DP Profile对象
     * 用于BtMusicFocusImpl调用系统接口
     * 增强安全性检查
     */
    public BluetoothProfile getA2dpSink() {
        if (mA2dpSink == null) {
            LogUtil.w(TAG, "BtConnectionManager::getA2dpSink: mA2dpSink is null, A2DP Profile not ready");
            return null;
        }
        
        // 额外安全检查：确保A2DP Profile连接状态正常
        try {
            if (mBluetoothAdapter != null) {
                int profileState = mBluetoothAdapter.getProfileConnectionState(A2DP_SINK);
                if (profileState != BluetoothProfile.STATE_CONNECTED) {
                    LogUtil.w(TAG, "BtConnectionManager::getA2dpSink: A2DP Profile state=" + profileState + ", not fully connected");
                }
            }
        } catch (Exception e) {
            LogUtil.e(TAG, "BtConnectionManager::getA2dpSink: error checking profile state", e);
        }
        
        return mA2dpSink;
    }

    /**
     * 请求蓝牙音频焦点
     */
    public void requestBtMusicFocus() {
        if (mContext == null) {
            LogUtil.w(TAG, "BtConnectionManager::requestBtMusicFocus: not initialized");
            return;
        }
        BtMusicFocusManager.requestBtMusicFocus(null);
        LogUtil.d(TAG, "BtConnectionManager::requestBtMusicFocus: completed");
    }

    /**
     * 放弃蓝牙音频焦点
     */
    public void abandonBtMusicFocus() {
        if (mContext == null) {
            LogUtil.w(TAG, "BtConnectionManager::abandonBtMusicFocus: not initialized");
            return;
        }
        BtMusicFocusManager.abandonBtMusicFocus(null);
        LogUtil.d(TAG, "BtConnectionManager::abandonBtMusicFocus: completed");
    }

    /**
     * 设置手机连接状态
     */
    public void setConnectPhone(boolean connected) {
        if (mContext == null) {
            LogUtil.w(TAG, "BtConnectionManager::setConnectPhone: not initialized, connected=" + connected);
            return;
        }
        BtMusicFocusManager.setConnectPhone(connected);
        LogUtil.d(TAG, "BtConnectionManager::setConnectPhone: completed, connected=" + connected);
    }

    /**
     * 获取手机连接状态
     */
    public boolean isConnectPhone() {
        boolean result = BtMusicFocusManager.isConnectPhone();
        LogUtil.d(TAG, "BtConnectionManager::isConnectPhone: result=" + result);
        return result;
    }

    // ==================== 新增蓝牙状态管理API ====================

    /**
     * 注册蓝牙系统广播接收器
     * @param receiver 接收器实现
     * @param peekData 是否立即返回当前已连接设备
     */
    public void registerBluetoothReceiver(BlueToothSysBroadcastReceiver receiver, boolean peekData) {
        if (receiver == null) {
            LogUtil.e(TAG, "BtConnectionManager::registerBluetoothReceiver: receiver is null");
            return;
        }

        if (bluetoothReceivers.contains(receiver)) {
            LogUtil.w(TAG, "BtConnectionManager::registerBluetoothReceiver: already registered");
            return;
        }

        bluetoothReceivers.add(receiver);
        LogUtil.d(TAG, "BtConnectionManager::registerBluetoothReceiver: success, peekData=" + peekData + ", total=" + bluetoothReceivers.size());
    }

    /**
     * 注册蓝牙系统广播接收器 - 重载方法，默认不返回当前设备
     * @param receiver 接收器实现
     */
    public void registerBluetoothReceiver(BlueToothSysBroadcastReceiver receiver) {
        registerBluetoothReceiver(receiver, false);
    }

    /**
     * 注销蓝牙系统广播接收器
     * @param receiver 接收器实现
     */
    public void unregisterBluetoothReceiver(BlueToothSysBroadcastReceiver receiver) {
        if (receiver == null) {
            LogUtil.e(TAG, "BtConnectionManager::unregisterBluetoothReceiver: receiver is null");
            return;
        }

        if (!bluetoothReceivers.contains(receiver)) {
            LogUtil.w(TAG, "BtConnectionManager::unregisterBluetoothReceiver: receiver not found");
            return;
        }

        bluetoothReceivers.remove(receiver);
        LogUtil.d(TAG, "BtConnectionManager::unregisterBluetoothReceiver: success, total=" + bluetoothReceivers.size());
    }



    /**
     * 获取已连接的蓝牙设备集合
     * @return 已连接的蓝牙设备集合
     */
    public Set<BluetoothDevice> getConnectedBluetoothDevices() {
        LogUtil.d(TAG, "BtConnectionManager::getConnectedBluetoothDevices: size=" + deviceSet.size());
        return deviceSet;
    }

    /**
     * 初始化蓝牙状态
     */
    @SuppressLint("MissingPermission")
    public void initBtState() {
        // 连接蓝牙-hid 的设备
        getClassicalConnectedDevices(bluetoothDevices -> {
            deviceSet.addAll(bluetoothDevices);
            LogUtil.d(TAG, "BtConnectionManager::initBtState: found devices=" + bluetoothDevices.size() + ", total=" + deviceSet.size());
        });
    }

    /**
     * 设置HID操作回调
     * @param callback HID操作回调
     */
    public void setHidOperationCallback(HidOperationCallback callback) {
        this.hidOperationCallback = callback;
        LogUtil.d(TAG, "BtConnectionManager::setHidOperationCallback: callback=" + (callback != null ? "set" : "null"));
    }

    // ==================== 内部辅助方法 ====================

    /**
     * 获取经典蓝牙已连接设备
     */
    @SuppressLint("MissingPermission")
    private void getClassicalConnectedDevices(ConnectedDevicesCallback callback) {
        // 优先使用已连接的mA2dpSink，避免重复创建Profile连接
        if (mA2dpSink != null) {
            try {
                List<BluetoothDevice> connectedDevices = mA2dpSink.getConnectedDevices();
                callback.onConnectedDevices(connectedDevices);
                LogUtil.d(TAG, "BtConnectionManager::getClassicalConnectedDevices: reused mA2dpSink, devices=" + connectedDevices.size());
                return;
            } catch (Exception e) {
                LogUtil.e(TAG, "BtConnectionManager::getClassicalConnectedDevices: failed to get devices from mA2dpSink", e);
            }
        }
        
        // Fallback: 如果mA2dpSink不可用，检查Profile状态并返回空列表
        BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        if (bluetoothAdapter != null && bluetoothAdapter.getProfileConnectionState(A2DP_SINK) == BluetoothProfile.STATE_CONNECTED) {
            LogUtil.d(TAG, "BtConnectionManager::getClassicalConnectedDevices: A2DP connected but mA2dpSink not ready, will get devices when Profile connected");
        } else {
            LogUtil.d(TAG, "BtConnectionManager::getClassicalConnectedDevices: no A2DP connection");
        }
        
        // 返回空列表，设备会在mA2dpProfileListener.onServiceConnected中更新
        callback.onConnectedDevices(Collections.emptyList());
    }

    /**
     * 连接设备回调接口
     */
    private interface ConnectedDevicesCallback {
        void onConnectedDevices(List<BluetoothDevice> devices);
    }

    /**
     * 取消待处理的HID连接任务
     */
    private void cancelPendingHidConnection() {
        if (pendingHidConnection != null) {
            handler.removeCallbacks(pendingHidConnection);
            LogUtil.d(TAG, "BtConnectionManager::cancelPendingHidConnection: cancelled, device=" + (pendingHidDevice != null ? pendingHidDevice.getName() : "null"));
            pendingHidConnection = null;
            pendingHidDevice = null;
        }
    }

    /**
     * 清理A2DP Profile资源
     */
    private void cleanupA2dpProfile() {
        if (mBluetoothAdapter != null && mA2dpSink != null) {
            try {
                mBluetoothAdapter.closeProfileProxy(A2DP_SINK, mA2dpSink);
                LogUtil.d(TAG, "BtConnectionManager::cleanupA2dpProfile: A2DP profile proxy closed");
            } catch (Exception e) {
                LogUtil.e(TAG, "BtConnectionManager::cleanupA2dpProfile: failed to close profile proxy", e);
            }
        }
        
        mA2dpSink = null;
        mA2dpProfileListener = null;
        mBluetoothAdapter = null;
        isBtMusicFocusManagerInitialized = false;  // 重置初始化状态
        LogUtil.d(TAG, "BtConnectionManager::cleanupA2dpProfile: A2DP resources cleared");
    }

    // ==================== 通知方法 ====================

    /**
     * 通知蓝牙状态变化
     */
    private void notifyBluetoothStateChanged(int state) {
        for (BlueToothSysBroadcastReceiver receiver : bluetoothReceivers) {
            try {
                receiver.onBluetoothStateChanged(state);
            } catch (Exception e) {
                LogUtil.e(TAG, "notifyBluetoothStateChanged error: " + e.getMessage());
            }
        }
    }

    /**
     * 通知设备连接
     */
    private void notifyDeviceConnected(BluetoothDevice device) {
        for (BlueToothSysBroadcastReceiver receiver : bluetoothReceivers) {
            try {
                receiver.onDeviceConnected(device);
            } catch (Exception e) {
                LogUtil.e(TAG, "notifyDeviceConnected error: " + e.getMessage());
            }
        }
    }

    /**
     * 通知设备断开
     */
    private void notifyDeviceDisconnected(BluetoothDevice device) {
        for (BlueToothSysBroadcastReceiver receiver : bluetoothReceivers) {
            try {
                receiver.onDeviceDisconnected(device);
            } catch (Exception e) {
                LogUtil.e(TAG, "notifyDeviceDisconnected error: " + e.getMessage());
            }
        }
    }

    /**
     * 通知设备切换
     */
    private void notifyDeviceSwitch(Set<BluetoothDevice> devices) {
        for (BlueToothSysBroadcastReceiver receiver : bluetoothReceivers) {
            try {
                receiver.onDeviceSwitch(devices);
            } catch (Exception e) {
                LogUtil.e(TAG, "notifyDeviceSwitch error: " + e.getMessage());
            }
        }
    }

    // ==================== 蓝牙状态处理方法 ====================

    /**
     * 处理蓝牙状态变化
     */
    private void handleBluetoothStateChange(int state) {
        LogUtil.d(TAG, "BtConnectionManager::handleBluetoothStateChange: " + state);
        switch (state) {
            case BluetoothAdapter.STATE_TURNING_ON:
                LogUtil.i(TAG, "BtConnectionManager::handleBluetoothStateChange: 经典蓝牙正在打开");
                break;
            case BluetoothAdapter.STATE_ON:
                LogUtil.i(TAG, "BtConnectionManager::handleBluetoothStateChange: 经典蓝牙已经打开");
                // 如果 SDP 注册后，重启开启蓝牙，需要重新注册
                if (hidOperationCallback != null) {
                    hidOperationCallback.registerHIDAfterEnable();
                }
                // 通知监听器蓝牙已打开
                for (BlueToothSysBroadcastReceiver receiver : bluetoothReceivers) {
                    try {
                        receiver.onBluetoothOn();
                    } catch (Exception e) {
                        LogUtil.e(TAG, "BtConnectionManager::handleBluetoothStateChange: onBluetoothOn error=" + e.getMessage());
                    }
                }
                break;
            case BluetoothAdapter.STATE_TURNING_OFF:
                LogUtil.i(TAG, "BtConnectionManager::handleBluetoothStateChange: 经典蓝牙正在关闭");
                // 蓝牙关闭时取消所有待处理的HID连接任务
                cancelPendingHidConnection();
                break;
            case BluetoothAdapter.STATE_OFF:
                LogUtil.i(TAG, "BtConnectionManager::handleBluetoothStateChange: 经典蓝牙已经关闭，清空设备集合");
                // 清空设备集合
                deviceSet.clear();
                // 注销HID服务
                if (hidOperationCallback != null) {
                    hidOperationCallback.unregisterHIDAfterDisabled();
                }
                // 通知监听器蓝牙已关闭
                for (BlueToothSysBroadcastReceiver receiver : bluetoothReceivers) {
                    try {
                        receiver.onBluetoothOff();
                    } catch (Exception e) {
                        LogUtil.e(TAG, "BtConnectionManager::handleBluetoothStateChange: onBluetoothOff error=" + e.getMessage());
                    }
                }
                // 蓝牙关闭时取消所有待处理的HID连接任务
                cancelPendingHidConnection();
                break;
        }

        notifyBluetoothStateChanged(state);
    }

    /**
     * 处理A2DP连接状态变化 - 恢复原HidBtStateReceiver逻辑
     */
    @SuppressLint("MissingPermission")
    private void handleA2DPConnectionStateChange(Intent intent) {
        int prevState = intent.getIntExtra(BluetoothProfile.EXTRA_PREVIOUS_STATE, 0);
        int nowState = intent.getIntExtra(BluetoothProfile.EXTRA_STATE, 0);
        BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);

        // 打印设备信息和状态变化
        String deviceInfo = (device != null) ?
            "device=" + device.getName() + "(" + device.getAddress() + ")" : "device=null";
        LogUtil.d(TAG, "BtConnectionManager::handleA2DPConnectionStateChange:蓝牙连接状态变化: " + deviceInfo +
                  ", prevState=" + getConnectionStateName(prevState) + ", nowState=" + getConnectionStateName(nowState)  + " bluetoothReceivers:" + bluetoothReceivers.size());

        // 设备连接
        if (prevState != BluetoothProfile.STATE_CONNECTED && nowState == BluetoothProfile.STATE_CONNECTED) {
            if (device != null) {
                deviceSet.add(device);
                // 通知监听器
                Set<BluetoothDevice> deviceSetForCallback = new HashSet<>();
                deviceSetForCallback.add(device);
                for (BlueToothSysBroadcastReceiver receiver : bluetoothReceivers) {
                    try {
                        receiver.onDeviceConnected(device);
                        receiver.onDeviceSwitch(deviceSetForCallback);
                    } catch (Exception e) {
                        LogUtil.e(TAG, "BtConnectionManager::handleA2DPConnectionStateChange: onConnected error=" + e.getMessage());
                    }
                }
                LogUtil.i(TAG, "BtConnectionManager::handleA2DPConnectionStateChange: 经典蓝牙设备已连接, device=" + device.getName());

                // 预约一个 500ms 的定时执行任务连接HID设备
                // 先取消之前可能存在的任务
                cancelPendingHidConnection();

                // 创建新的延迟任务
                pendingHidDevice = device;
                pendingHidConnection = () -> {
                    if (hidOperationCallback != null) {
                        hidOperationCallback.connectBluetoothHIDDevice(device);
                    }
                    pendingHidConnection = null;
                    pendingHidDevice = null;
                };

                // 延迟500ms执行
                handler.postDelayed(pendingHidConnection, 500);
                LogUtil.d(TAG, "BtConnectionManager::handleA2DPConnectionStateChange: 延迟连接HID设备, device=" + device.getName());
            }
        }
        // 设备断开
        else if (prevState != BluetoothProfile.STATE_DISCONNECTED && nowState == BluetoothProfile.STATE_DISCONNECTED) {
            if (device != null) {
                // 如果断开的设备是待处理HID连接的设备，取消任务
                if (device.equals(pendingHidDevice)) {
                    cancelPendingHidConnection();
                }
                LogUtil.i(TAG, "BtConnectionManager::handleA2DPConnectionStateChange: 经典蓝牙设备已断开, device=" + device.getName());
            }
            checkRemainingConnections(device);
        }
    }

    /**
     * 处理设备连接事件
     */
    @SuppressLint("MissingPermission")
    private void handleDeviceConnected(BluetoothDevice device) {
        if (device != null && device.getName() != null) {
            // 通知监听器
            Set<BluetoothDevice> deviceSetForCallback = new HashSet<>();
            deviceSetForCallback.add(device);
            for (BlueToothSysBroadcastReceiver receiver : bluetoothReceivers) {
                try {
                    receiver.onDeviceSwitch(deviceSetForCallback);
                } catch (Exception e) {
                    LogUtil.e(TAG, "BtConnectionManager::handleDeviceConnected: receiver error=" + e.getMessage());
                }
            }
            deviceSet.add(device);
            notifyDeviceConnected(device);
            LogUtil.i(TAG, "BtConnectionManager::handleDeviceConnected: 当前连接的蓝牙设备=" + device.getName() + ", total=" + deviceSet.size());
        }
    }

    /**
     * 处理设备断开事件
     */
    private void handleDeviceDisconnected(BluetoothDevice device) {
        LogUtil.d(TAG, "BtConnectionManager::handleDeviceDisconnected 经典蓝牙设备已断开: device=" + device);

        // 如果断开的设备是待处理HID连接的设备，取消任务
        if (device != null && device.equals(pendingHidDevice)) {
            cancelPendingHidConnection();
        }

        checkRemainingConnections(device);
        if (device != null) {
            notifyDeviceDisconnected(device);
        }
    }

    /**
     * 检查剩余连接
     */
    @SuppressLint("MissingPermission")
    private void checkRemainingConnections(BluetoothDevice disconnectedDevice) {
        if (disconnectedDevice != null) {
            deviceSet.remove(disconnectedDevice);
        }

        // 通知监听器设备断开
        if (disconnectedDevice != null) {
            for (BlueToothSysBroadcastReceiver receiver : bluetoothReceivers) {
                try {
                    receiver.onDeviceDisconnected(disconnectedDevice);
                } catch (Exception e) {
                    LogUtil.e(TAG, "BtConnectionManager::checkRemainingConnections: receiver error=" + e.getMessage());
                }
            }
        }

        LogUtil.d(TAG, "BtConnectionManager::checkRemainingConnections: remaining devices=" + deviceSet.size());
    }

    /**
     * 处理蓝牙HID状态变化 - 恢复原HidBtStateReceiver逻辑
     */
    private void handleBluetoothHidStateChanged(Intent intent) {
        int currentState = intent.getIntExtra(BluetoothProfile.EXTRA_STATE, BluetoothProfile.STATE_DISCONNECTED);
        int previousState = intent.getIntExtra(BluetoothProfile.EXTRA_PREVIOUS_STATE, BluetoothProfile.STATE_DISCONNECTED);
        BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);

        // 打印状态信息
        LogUtil.d(TAG, "BtConnectionManager::handleBluetoothHidStateChanged HID连接状态变化: device=" + (device != null ? device.getName() + "(" + device.getAddress() + ")" : "null") +
                ", previousState=" + getConnectionStateName(previousState) +
                ", currentState=" + getConnectionStateName(currentState));

        // 根据状态变化处理连接或断开逻辑
        if (previousState != BluetoothProfile.STATE_CONNECTED &&
                currentState == BluetoothProfile.STATE_CONNECTED) {
            // HID设备已连接
            if (device != null) {
                // 如果HID设备已连接，取消待处理的连接任务
                if (device.equals(pendingHidDevice)) {
                    cancelPendingHidConnection();
                }
                LogUtil.i(TAG, "BtConnectionManager::handleBluetoothHidStateChanged: HID设备已连接, device=" + device.getName());
            }
        } else if (previousState != BluetoothProfile.STATE_DISCONNECTED &&
                currentState == BluetoothProfile.STATE_DISCONNECTED) {
            // HID设备已断开
            if (device != null) {
                LogUtil.i(TAG, "BtConnectionManager::handleBluetoothHidStateChanged: HID设备已断开, device=" + device.getName());
            }
        }
    }

    /**
     * 处理蓝牙连接状态变化 - 仅记录日志
     */
    private void handleBluetoothConnectionStateChange(Intent intent) {
        int currentState = intent.getIntExtra(BluetoothAdapter.EXTRA_CONNECTION_STATE, BluetoothAdapter.STATE_DISCONNECTED);
        int previousState = intent.getIntExtra(BluetoothAdapter.EXTRA_PREVIOUS_CONNECTION_STATE, BluetoothAdapter.STATE_DISCONNECTED);
        BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);

        LogUtil.d(TAG, "BtConnectionManager::handleBluetoothConnectionStateChange Adapter Connection State Changed: device=" + (device != null ? device.getName() + "(" + device.getAddress() + ")" : "null") +
                ", previousState=" + getConnectionStateName(previousState) +
                ", currentState=" + getConnectionStateName(currentState));
    }

    /**
     * 获取连接状态名称 - 用于日志输出
     */
    private String getConnectionStateName(int state) {
        switch (state) {
            case BluetoothProfile.STATE_DISCONNECTED:
                return "DISCONNECTED";
            case BluetoothProfile.STATE_CONNECTING:
                return "CONNECTING";
            case BluetoothProfile.STATE_CONNECTED:
                return "CONNECTED";
            case BluetoothProfile.STATE_DISCONNECTING:
                return "DISCONNECTING";
            default:
                return "UNKNOWN(" + state + ")";
        }
    }

    /**
     * 处理蓝牙音频焦点状态变化 - 原BluetoothReceiver逻辑
     */
    private void handleBluetoothMusicFocusStateChange(Intent intent) {
        int prevState = intent.getIntExtra(BluetoothProfile.EXTRA_PREVIOUS_STATE, 0);
        int nowState = intent.getIntExtra(BluetoothProfile.EXTRA_STATE, 0);

        LogUtil.d(TAG, "BtConnectionManager::handleBluetoothMusicFocusStateChange: prevState=" + getConnectionStateName(prevState) + ", nowState=" + getConnectionStateName(nowState));

        if (prevState != BluetoothProfile.STATE_CONNECTED && nowState == BluetoothProfile.STATE_CONNECTED) {
            // 蓝牙连接，请求音频焦点
            BtMusicFocusManager.requestBtMusicFocus(null);
            LogUtil.d(TAG, "BtConnectionManager::handleBluetoothMusicFocusStateChange: 请求音频焦点");
        } else if (prevState != BluetoothProfile.STATE_DISCONNECTED && nowState == BluetoothProfile.STATE_DISCONNECTED) {
            // 蓝牙断开，放弃音频焦点
            BtMusicFocusManager.abandonBtMusicFocus(null);
            LogUtil.d(TAG, "BtConnectionManager::handleBluetoothMusicFocusStateChange: 放弃音频焦点");
        }
    }

    // ==================== 系统蓝牙接收器 ====================

    /**
     * 系统蓝牙广播接收器
     */
    private class SystemBluetoothReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (action != null) {
                LogUtil.d(TAG, "SystemBluetoothReceiver::onReceive 经典蓝牙状态变化 action=" + action);
                switch (action) {
                    // 系统蓝牙开关 打开/关闭
                    case BluetoothAdapter.ACTION_STATE_CHANGED:
                        handleBluetoothStateChange(intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, 0));
                        break;

                    // 蓝牙适配器连接状态变化 - 有的时候收不到状态变化
                    case BluetoothAdapter.ACTION_CONNECTION_STATE_CHANGED:
                        handleBluetoothConnectionStateChange(intent);
                        break;

                    case BluetoothDevice.ACTION_ACL_CONNECTED:
                        handleDeviceConnected(intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE));
                        break;

                    case BluetoothDevice.ACTION_ACL_DISCONNECTED:
                        handleDeviceDisconnected(intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE));
                        break;

                    case BluetoothHidDevice.ACTION_CONNECTION_STATE_CHANGED:
                        handleBluetoothHidStateChanged(intent);
                        break;

                    default:
                        // 处理A2DP连接状态变化 - 整合了原BluetoothReceiver的逻辑
                        if (ACTION_A2DP_CONNECTION_STATE_CHANGED != null && ACTION_A2DP_CONNECTION_STATE_CHANGED.equals(action)) {
                            // HID延迟注册

                            // 同时处理A2DP连接状态变化和音频焦点管理
                            handleA2DPConnectionStateChange(intent);
                            handleBluetoothMusicFocusStateChange(intent);
                        }
                        break;
                }
            }
        }
    }
}
