package com.autoai.welink.sdk.BT;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothManager;
import android.bluetooth.BluetoothProfile;
import android.content.Context;
import android.content.Intent;
import com.autoai.common.util.LogUtil;
import java.lang.reflect.Field;

/**
 * <AUTHOR> zhanggc
 * @description : 德赛蓝牙焦点请求工具类
 * @date : 2024/4/12 15:00
 * @version : 1.0
 */
public class RequestBTMusicFocusUtil {
    private static final String TAG = "RequestBTMusicFocusUtil";
    public static final String DESAYSV_ACTION_START_SOURCE = "com.desaysv.mediaapp.action.startSource";
    public static final String KEY_START_SOURCE = "key_start_source";
    public static final String KEY_IS_FOREGROUND = "key_is_foreground";
    public static final String KEY_IS_REQUEST_FOCUS = "key_is_request_focus";
    public static final String KEY_OPEN_REASON = "key_open_reason";
    public static final String MUSIC_APP_PACKAGE = "com.desaysv.audioapp";
    public static final String BT_MUSIC_SOURCE = "bt_music";
    public static final String REASON_VALUE = "we_link";

    //车机与手机是否连接成功
    public static boolean isConnectPhone = false;
    //蓝牙状态变化广播
    public static String ACTION_CONNECTION_STATE_CHANGED = "android.bluetooth.a2dp-sink.profile.action.CONNECTION_STATE_CHANGED";

    /**
     * 德赛请求蓝牙焦点广播
     */
    public static void requestBTMusicFocus(Context context) {
        LogUtil.d(TAG, "requestBTMusicFocus--------context:" + context + ",isConnectPhone:" + isConnectPhone);
        if (context == null || !isConnectPhone) return;
        Intent intent = new Intent(DESAYSV_ACTION_START_SOURCE);
        intent.putExtra(KEY_START_SOURCE, BT_MUSIC_SOURCE);
        intent.putExtra(KEY_IS_FOREGROUND, false);
        intent.putExtra(KEY_IS_REQUEST_FOCUS, true);
        intent.putExtra(KEY_OPEN_REASON, REASON_VALUE);
        intent.setPackage(MUSIC_APP_PACKAGE);
        context.startService(intent);
    }

    /**
     * 判断蓝牙是否连接
     */
    @SuppressLint("MissingPermission")
    public static boolean isBluetoothConnected(Context context) {
        // 获取蓝牙适配器
        BluetoothAdapter bluetoothAdapter = null;
        if (context != null) {
            BluetoothManager bluetoothManager = (BluetoothManager) context.getSystemService(Context.BLUETOOTH_SERVICE);
            if (bluetoothManager != null) {
                bluetoothAdapter = bluetoothManager.getAdapter();
            }
        }
        if (bluetoothAdapter != null && bluetoothAdapter.isEnabled()) {
//            int headset = bluetoothAdapter.getProfileConnectionState(getProperty("HEADSET_CLIENT", 16));
            int a2dp = bluetoothAdapter.getProfileConnectionState(getProperty("A2DP_SINK", 11));
            LogUtil.d(TAG, "bluetoothAdapter a2dp:" + a2dp);
            return a2dp == BluetoothProfile.STATE_CONNECTED;
        } else {
            LogUtil.d(TAG, "bluetoothAdapter:" + bluetoothAdapter + ",或 isEnabled 为false");
        }
        return false;
    }

    public static int getProperty(String key, int defaultValue) {
        //获取属性
        int value = defaultValue;
        try {
            //获取对应的属性类 SystemProperties
            Class<?> clazz = Class.forName("android.bluetooth.BluetoothProfile");
            //通过属性名获取Field对象
            Field field = clazz.getDeclaredField(key);
            // 如果属性是私有的，则需要调用此方法
            field.setAccessible(true);
            value = (Integer) field.get(null);
            LogUtil.d(TAG, "bluetoothState    value:" + value);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return value;
    }

    public static String getReceiveProperty(String key) {
        //获取属性
        try {
            //获取对应的属性类 SystemProperties
            Class<?> clazz = Class.forName("android.bluetooth.BluetoothA2dpSink");
            //通过属性名获取Field对象
            Field field = clazz.getDeclaredField(key);
            // 如果属性是私有的，则需要调用此方法
            field.setAccessible(true);
            ACTION_CONNECTION_STATE_CHANGED = (String) field.get(null);
            LogUtil.d(TAG, "bluetoothState    value:" + ACTION_CONNECTION_STATE_CHANGED);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ACTION_CONNECTION_STATE_CHANGED;
    }
}
