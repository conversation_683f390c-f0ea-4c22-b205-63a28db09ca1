package com.autoai.welink.jni.Enum;
@SuppressWarnings("unused")
public interface WL_JNI_ERR_CODE {
    int E001 = 0xE001;	/*< No device insertion */
    int E002 = 0xE002;	/*< Start connection error */
    int E003 = 0xE003;	/*< Android SDK version not support */
    int E004 = 0xE004;	/*< wlCmd start failed */
    int E005 = 0xE005;	/*< EAP node open failed */
    int E006 = 0xE006;	/*< AOA driver error */
    int E007 = 0xE007;	/*< IDB driver error */
    int E008 = 0xE008;	/*< Check device time out */
    int E101 = 0xE101;	/*< Install error */
    int E102 = 0xE102;	/*< Start app error */
    int E103 = 0xE103;	/*< App not installed */
    int E201 = 0xE201;	/*< Connect error */
    int E301 = 0xE301;	/*< Unsupported codec */
    int E302 = 0xE302;	/*< Authorization failed */
    int E303 = 0xE303;	/*< Authorization expired */
    int E304 = 0xE304;	/*< Need to update mobile app */
    int E305 = 0xE305;	/*< Device not authorized */
    int E306 = 0xE306;	/*< No network */
    int E307 = 0xE307;	/*< Auth server error */
    int E308 = 0xE308;	/*< Auth parameter error */
    int E309 = 0xE309;	/*< Illegal json string */
    int E310 = 0xE310;	/*< Data format not supported */
    int E311 = 0xE311;	/*< Auth timeout error */
    int E312 = 0xE312;	/*< Error with displayCode */
    int E401 = 0xE401;	/*< Heart Timer error */
    int E402 = 0xE402;	/*< Decoder error */
    int E501 = 0xE501;	/*< AOA capturer error */
    int E601 = 0xE601;	/*< Socket error */

}
