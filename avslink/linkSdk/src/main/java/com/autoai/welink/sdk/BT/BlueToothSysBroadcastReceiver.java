package com.autoai.welink.sdk.BT;

import android.bluetooth.BluetoothDevice;
import java.util.Set;

/**
 * 蓝牙系统广播接收器接口 - 统一的蓝牙状态监听接口
 * 供上层模块实现，用于接收蓝牙状态变化通知
 * 整合了原 BluetoothStateListener 的功能
 *
 * <AUTHOR>
 * @date 2024/12/07
 * @version 1.0
 */
public interface BlueToothSysBroadcastReceiver {

    /**
     * 蓝牙状态变化 - 原始状态值
     * @param state 蓝牙状态（BluetoothAdapter.STATE_*）
     */
    void onBluetoothStateChanged(int state);

    /**
     * 蓝牙打开 - 整合自BluetoothStateListener
     */
    void onBluetoothOn();

    /**
     * 蓝牙关闭 - 整合自BluetoothStateListener
     */
    void onBluetoothOff();

    /**
     * 设备连接 - 单个设备
     * @param device 连接的蓝牙设备
     */
    void onDeviceConnected(BluetoothDevice device);

    /**
     * 设备断开 - 单个设备
     * @param device 断开的蓝牙设备
     */
    void onDeviceDisconnected(BluetoothDevice device);

    /**
     * 设备切换
     * @param devices 切换的蓝牙设备集合
     */
    void onDeviceSwitch(Set<BluetoothDevice> devices);
}
