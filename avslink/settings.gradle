pluginManagement {
    repositories {
        gradlePluginPortal()
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/central' }
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        maven { url 'https://wdnexus.autoai.com/content/repositories/autoai-AVS/' }
        maven { url "https://wdnexus.autoai.com/content/repositories/releases/" }
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/central' }
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        maven { url 'https://wdnexus.autoai.com/content/repositories/autoai-AVS/' }
        maven { url "https://wdnexus.autoai.com/content/repositories/releases/" }
    }
}
rootProject.name = 'AvsLinkHost'

include ':testapp'
if (file("./linkHost").exists() && SDK_TEST != "true") {
    include ':linkHost'
    include ':linkSdk'
    include ':wlhardware'
    include ':linkCommon'
// submodule 工作方式
    if (file("./wlhardware/wlhardware").exists()) {
        project(':wlhardware').projectDir = file("./wlhardware/wlhardware")
    } else {
        project(':wlhardware').projectDir = file("./localLibs/wlhardware")
    }
}
