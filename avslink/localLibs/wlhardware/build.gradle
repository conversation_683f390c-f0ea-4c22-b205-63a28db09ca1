configurations.maybeCreate("default")
artifacts.add("default", file('wlhardware.aar'))

apply plugin: 'maven'

uploadArchives {
    repositories {
        mavenDeployer {
            repository(url: "https://wdnexus.autoai.com/content/repositories/autoai-AVS/") {
                authentication(userName: "autoai-AVS", password: "@pIbl9kE")
            }
            pom.version = '1.0.2'
            pom.groupId = 'com.autoai.avs.link'
            pom.artifactId = 'wlhardware'
        }
    }
}