package com.autoai.wdlink.hu.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;


/**
 *
 */

public class AOADialog {
    private final FrameLayout mFrameLayout;
    private View mBgView;
    private View mDialogView;
    private boolean isCancelableOnTouchOutSide;
    private boolean isCancelable = true;

    private final Context mAppActivity;

    private boolean isShowing;// 对话框是否在显示

    public AOADialog(Context aAppActivity,FrameLayout frameLayout) {
        mAppActivity = aAppActivity;
        mFrameLayout = frameLayout;
        if (mFrameLayout == null) {
            return;
        }
        initBgView(mAppActivity);
    }

    /**
     * 初始化背景view
     *
     * @param context Context
     */
    private void initBgView(Context context) {
        mBgView = new LinearLayout(context);
        mBgView.setBackgroundColor(Color.parseColor("#CC000000"));
        mBgView.setOnClickListener(v -> {
            if (!isCancelableOnTouchOutSide) {
                return;
            }
            cancel();
        });
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT);
        mBgView.setLayoutParams(params);
    }

    /**
     * 设置背景颜色
     *
     * @param color 颜色值
     */
    public void setBackgroundColor(int color) {
        if (mBgView != null) {
            mBgView.setBackgroundColor(color);
        }
    }

    /**
     * 取消对话框
     */
    private void cancel() {
        if (mOnCancelListener != null) {
            mOnCancelListener.onCancel();
        }
        dismiss();
    }

    /**
     * 取消对话框
     */
    public void dismiss() {
        if (mFrameLayout == null || isViewNull()) {
            return;
        }
        isShowing = false;
        mFrameLayout.removeView(mBgView);
        mFrameLayout.removeView(mDialogView);
    }

    private long mCurrentTime;

    /**
     * 显示对话框
     */
    public void show() {
        if (isShowing || mFrameLayout == null || isViewNull()) {
            return;
        }
        isShowing = true;
        mFrameLayout.addView(mBgView);
        mFrameLayout.addView(mDialogView);
        mCurrentTime = System.currentTimeMillis();
    }

    /**
     * 点击对话框外是否取消对话框
     *
     * @param b true：取消；false：不取消
     */
    public void setCanceledOnTouchOutside(boolean b) {
        isCancelableOnTouchOutSide = b;
    }

    /**
     * 点击返回按键是否取消对话框
     *
     * @param b true：取消；false；不取消
     */
    public void setCancelable(boolean b) {
        isCancelable = b;
    }

    /**
     * 设置对话框的View
     *
     * @param v 要设置的View
     */
    public void setContentView(View v) {
        if (isShowing || v == null) {
            return;
        }
        mDialogView = v;
        initListener();
    }


    /**
     * 监听返回按键
     */
    @SuppressLint("ClickableViewAccessibility")
    private void initListener() {
        mDialogView.setOnTouchListener((v, event) -> true);

        mDialogView.setOnKeyListener((v, keyCode, event) -> {
            onKeyDown(v, keyCode, event);
            if (System.currentTimeMillis() - mCurrentTime < 200) {
                return false;
            }
            if (!isCancelable) {
                return true;
            } else {
                if (keyCode == KeyEvent.KEYCODE_BACK) {
                    cancel();
                    return true;
                }
                return false;
            }
        });
        mDialogView.setFocusable(true);
        mDialogView.setFocusableInTouchMode(true);
        mDialogView.requestFocus();
    }

    public void onKeyDown(View v, int keyCode, KeyEvent event) {

    }

    private boolean isViewNull() {
        return (mBgView == null || mDialogView == null);
    }

    /**
     * 设置显示View的布局参数
     *
     * @param params 布局参数
     */
    public void setLayoutParams(ViewGroup.LayoutParams params) {
        if (mDialogView != null) {
            mDialogView.setLayoutParams(params);
        }
    }

    /**
     * 获取View的布局参数
     *
     * @return 布局参数
     */
    public ViewGroup.LayoutParams getParams() {
        return mDialogView.getLayoutParams();
    }

    /**
     * 获取View布局中的控件
     *
     * @param id 控件的id
     * @return 获取的View
     */
    public View findViewById(int id) {
        if (id < 0) {
            return null;
        }
        return mDialogView.findViewById(id);
    }

    /**
     * 点击取消按钮的监听
     */
    public interface OnCancelListener {
        void onCancel();
    }

    private OnCancelListener mOnCancelListener;

    /**
     * 设置取消按钮的监听器
     *
     * @param listener 监听器
     */
    public void setOnCancelListener(OnCancelListener listener) {
        mOnCancelListener = listener;
    }

    /**
     * 对话框是否在显示
     *
     * @return true:显示；false:没显示
     */
    public boolean isShowing() {
        return isShowing;
    }
}
