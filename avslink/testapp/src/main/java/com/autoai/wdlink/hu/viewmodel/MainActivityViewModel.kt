package com.autoai.wdlink.hu.viewmodel

import android.app.Activity
import android.content.Context
import android.view.MotionEvent
import android.widget.FrameLayout
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.autoai.wdlink.hu.Logger
import com.autoai.wdlink.hu.frame.BaseViewModel
import com.autoai.wdlink.hu.model.PageModel
import com.autoai.wdlink.hu.sdk.HidRegionHelper
import com.autoai.wdlink.hu.sdk.LinkSdkModel
import com.autoai.wdlink.hu.sdk.SurfaceHelper
import com.autoai.wdlink.hu.view.ExitFragment
import com.autoai.wdlink.hu.view.HelpFragment
import com.autoai.wdlink.hu.view.InitFragment
import com.autoai.wdlink.hu.view.RunningFragment
import com.autoai.welinkapp.model.ButtonType
import com.autoai.welinkapp.model.MessageEvent
import com.autoai.welinkapp.model.OnMessageListener

class MainActivityViewModel : BaseViewModel() {

    var currentFragment: String = ""
        set(value) {
            Logger.v("currentFragment = $value")
            field = value
        }


    val pageEvent: LiveData<PageModel>
        get() = LinkSdkModel.exteriorPageEvent

    val bgVisibility: MutableLiveData<Int> = MutableLiveData()

//    val fullScreenLiveData = MutableLiveData<Boolean>()
//    var fullScreen = false
//    var windowHasFocus = false

//    fun fullScreen() {
//        Logger.i("fullScreen：windowHasFocus = $windowHasFocus, fullScreen = $fullScreen")
//        if (windowHasFocus) {
//            fullScreenLiveData.postValue(fullScreen)
//        } else {
//            fullScreenLiveData.postValue(false)
//        }
//    }

    /**
     * 接收手机端发过来的消息
     * */


    fun sendButton(type: Int, param: Int) {
        LinkSdkModel.sendButton(type, param)
    }

    /**
     * 触屏传输
     * */


//    fun setHidRegionCallback(phoneSurfaceParent: FrameLayout) {
//        LinkHost.setHidRegionCallback(HidRegionHelper.getHidCallback(phoneSurfaceParent))
//    }

    /**
     * 返回事件拦截处理
     * */
    fun onBackPressed(back: () -> Unit) {
        Logger.d("MainActivityViewModel：onBackPressed --> currentFragment = $currentFragment")
        when (currentFragment) {
            RunningFragment::class.java.simpleName -> {
                back.invoke()
                sendButton(ButtonType.LAUNCHER, 0)
            }
            ExitFragment::class.java.simpleName -> {
                back.invoke()
                sendButton(ButtonType.GOT_IT, 1)
            }
            HelpFragment::class.java.simpleName -> {
                sendButton(ButtonType.HELP_BACK, 0)
            }
            InitFragment::class.java.simpleName -> {
                sendButton(ButtonType.INIT_CLOSE, 0)
                back.invoke()
            }
            else -> {
                back.invoke()
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        LinkSdkModel.destroy()
    }

}