package com.autoai.wdlink.hu.frame.utils;

import android.annotation.SuppressLint;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.Outline;
import android.graphics.PixelFormat;
import android.graphics.Rect;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.Message;
import android.util.DisplayMetrics;
import android.util.Log;
import android.util.Pair;
import android.view.Display;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.TextureView;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewOutlineProvider;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.SeekBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;

import com.autoai.common.util.LogUtil;
import com.autoai.wdlink.hu.R;
import com.autoai.wdlink.hu.dialog.BasePopDialog;
import com.autoai.wdlink.hu.sdk.LinkUtil;
import com.autoai.welinkapp.checkconnect.CommonData;
import com.autoai.welinkapp.datatransfer.BluetoothHidManager;
import com.autoai.welinkapp.util.TouchUtil;
import com.autoai.welinkapp.util.WindowUtil;

import java.io.FileOutputStream;

import java.util.Date;

public class PopWinService extends Service implements View.OnTouchListener {

    private static final String TAG = "PopWinService";

    private WindowManager.LayoutParams layoutParams;
    private View popView;
    private WindowManager windowManager;
    private Context context;
    private WindowUtil mInstance;
    //    private SdkViewModel mSdkViewModel;
    private Handler handler = new Handler(Looper.getMainLooper());
    private int videoHeight;
    /**
     * 最多宽高
     */
    private Pair<Integer, Integer> maxSize;
    /**
     * 要设置宽高
     */
    private Pair<Integer, Integer> videoSize;
    private int scalevideoWidth;
    private int scalevideoHeight;
    private float hsscale = 1f;
    private int maxHight = 1080;
    private int maxWidth = 1920;

    private BluetoothHidManager bluetoothHidManager = null;

    /**
     * 竖屏最初缩放显示为最大尺寸倍数
     */
    private float portraitScale = 0.8f;
    @Override
    public void onCreate() {
        super.onCreate();
        context = this;
        windowManager = (WindowManager) getSystemService(WINDOW_SERVICE);

        //PlatformAdaptor.getInstance(getApplicationContext()).registerReceiver(getApplicationContext());
        WindowManager wm = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        Display display = wm.getDefaultDisplay();
        DisplayMetrics dm = new DisplayMetrics();
        display.getRealMetrics(dm);

        maxHight = dm.heightPixels;
        maxWidth = dm.widthPixels;
        mInstance = WindowUtil.getInstance(this);

        Log.d(TAG,"start BluetoothHidManager");
        bluetoothHidManager = new BluetoothHidManager(context);
        bluetoothHidManager.callBluetooth();

//        MainActivityViewModel.initLink(MainActivity.Companion.getInstance());
//        mSdkViewModel = new ViewModelProvider(this).get(SdkViewModel.class);
       /* mSdkViewModel = SdkViewModel.getInstance(getApplication());
        mSdkViewModel.onCreate(new NavController(this) {
            @Override
            public void navigate(int resId) {

            }
        });
        mSdkViewModel.platformadaptor.setSdkViewModel(mSdkViewModel);*/
        //已在mainactivity 设置高度 不用再减去top
        videoHeight = mInstance.getScreenHeight();
        maxSize = new Pair<>(mInstance.getScreenWidth(), videoHeight);
        LogUtil.d(TAG, "getStatusBarHeight:" + getStatusBarHeight() + ",videoHeight:" + videoHeight);
        LogUtil.d(TAG, "maxHight:" + maxHight + ",maxWidth:" + maxWidth);
    }

    public int getStatusBarHeight() {
        int result = 0;
        int resourceId = getResources().getIdentifier("status_bar_height", "dimen", "android");
        if (resourceId > 0) {
            result = getResources().getDimensionPixelSize(resourceId);
        }
        return result;
    }

    boolean isShow = false;
    boolean isNotification = true;

    @RequiresApi(api = Build.VERSION_CODES.O)
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent == null) {
            LogUtil.d(TAG, "onStartCommand------>intent is null  isShow:" + isShow);
            return super.onStartCommand(intent, flags, startId);
        }
        String action = intent.getAction();
        LogUtil.d(TAG, "onStartCommand------>action：" + action + ",isShow:" + isShow);
        if ("start".equals(action)) {
            if (isNotification) {
                createNotificationChannel();
                isNotification = false;
            }
            writeFile();
        } else if ("stop".equals(action) && isShow) {
            destroyThumbWindow();
        } else if ("show".equals(action) && !isShow) {
            showThumbWindow();
        } else if ("showDialog".equals(action)) {
            showDialog();
        } else if ("dismissDialog".equals(action)) {
            dismissDialog();
        } else if ("destory".equals(action)) {

        }
        return super.onStartCommand(intent, flags, startId);

    }

    @RequiresApi(api = Build.VERSION_CODES.O)
    private void createNotificationChannel() {
        NotificationManager mNotificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        // 通知渠道的id
        String id = "my_channel_01";
        // 用户可以看到的通知渠道的名字.
        CharSequence name = "Welink";
//         用户可以看到的通知渠道的描述
        String description = "Welink Demo";
        int importance = NotificationManager.IMPORTANCE_HIGH;
        NotificationChannel mChannel = new NotificationChannel(id, name, importance);
//         配置通知渠道的属性
        mChannel.setDescription(description);
//         设置通知出现时的闪灯（如果 android 设备支持的话）
        mChannel.enableLights(true);
        mChannel.setLightColor(Color.RED);
//         设置通知出现时的震动（如果 android 设备支持的话）
        mChannel.enableVibration(true);
        mChannel.setVibrationPattern(new long[]{100, 200, 300, 400, 500, 400, 300, 200, 400});
//         最后在notificationmanager中创建该通知渠道 //
        mNotificationManager.createNotificationChannel(mChannel);

        // 为该通知设置一个id
        int notifyID = 1;
        // 通知渠道的id
        String CHANNEL_ID = "my_channel_01";
        // Create a notification and set the notification channel.
        Notification notification = new Notification.Builder(this)
                .setContentTitle("New Message").setContentText("You've received new messages.")
                .setSmallIcon(R.mipmap.welink)
                .setChannelId(CHANNEL_ID)
                .build();
        startForeground(1, notification);
    }


    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }


    /**
     * 模拟文件写入
     */
    private void writeFile() {
        if (popView == null) {
            popView = createView();
            Log.e(TAG, "writeFile ---1width:" + layoutParams.width + ",1height:" + layoutParams.height +
                    ",SurfaceHeight:" + mInstance.getSurfaceHeight() + ",ScreenWidth:" + mInstance.getScreenWidth());
       /*     popView.setOutlineProvider(new ViewOutlineProvider() {
                @Override
                public void getOutline(View view, Outline outline) {
                    outline.setRoundRect(0, 0, view.getWidth(), view.getHeight(), 50);
                }
            });
            // 设置圆角
            popView.setClipToOutline(true);*/
        }
    }

    ViewGroup vp;
    TextureView sv;
    TextView sentFile;
    FrameLayout dialogView,vp_framelayout,sv_framelayout;
    int width = 400;
    boolean isSwitch = false;
    @SuppressLint("ClickableViewAccessibility")
    private View createView() {

        layoutParams = new WindowManager.LayoutParams(
                mInstance.getScreenWidth(), mInstance.getScreenHeight(), 0, 0, PixelFormat.TRANSPARENT);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            //26及以上必须使用TYPE_APPLICATION_OVERLAY   @deprecated TYPE_PHONE
            layoutParams.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
        } else {
            layoutParams.type = WindowManager.LayoutParams.TYPE_PHONE;
        }

        layoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
        layoutParams.gravity = Gravity.CENTER;
//        layoutParams.gravity = Gravity.LEFT | Gravity.TOP;
//        layoutParams.x = 300;
//        layoutParams.y = 0;

        vp = (ViewGroup) LayoutInflater.from(this).inflate(R.layout.my_videoview, null);
        dialogView = vp.findViewById(R.id.dialog_view);
        sv_framelayout = vp.findViewById(R.id.sv_framelayout);
        vp_framelayout = vp.findViewById(R.id.vp_framelayout);
        sv = vp.findViewById(R.id.phoneData_sv);
//        sv.setOutlineProvider(new TextureVideoViewOutlineProvider(20));
//        sv.setClipToOutline(true);
        sv.setOnTouchListener(this);
        createSurface(sv);


        vp.findViewById(R.id.close).setOnClickListener(view -> {
            destroyThumbWindow();
            CommonData.sendMsg(this, CommonData.CONNECT_STATUS_OUT, CommonData.iCurrentConnectType, null);
          /*  mSdkViewModel.platformadaptor.sendMsg(
                    PlatformAdaptor.MSG_TYPE.AP_MSG_APP_STATE,
                    -1,
                    false);
            mSdkViewModel.onPause();*/
        });
        vp.findViewById(R.id.vertical).setOnClickListener(view -> {
            LogUtil.d(TAG, "layoutParams.height:" + layoutParams.height + ",layoutParams.width:" + layoutParams.width);
            switchView();
        });
        vp.findViewById(R.id.horizontal).setOnClickListener(view -> {
            updateLayout(mInstance.getScreenWidth(), videoHeight);
            if (mInstance.getSurfaceWidth() != 640) {
                mInstance.setSurfaceHeight(layoutParams.height);
                mInstance.setSurfaceWidth(layoutParams.width);
                ViewGroup.LayoutParams params = sv.getLayoutParams();
                params.width = mInstance.getSurfaceWidth();
                params.height = mInstance.getSurfaceHeight();
                LogUtil.d(TAG, ".width:" + params.width + ",.params.height" + params.height);
                sv.setLayoutParams(params);
            }
        });
        vp.findViewById(R.id.move).setOnTouchListener((view, event) -> {

//            if (event.getAction() == MotionEvent.ACTION_UP) {
//                view.performClick();
//                return false;
//            }
            int mInScreenX = (int) event.getRawX();
            int mInScreenY = (int) event.getRawY();
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    mLastX = (int) event.getRawX();
                    mLastY = (int) event.getRawY();
                    break;
                case MotionEvent.ACTION_MOVE:
                    layoutParams.x += mInScreenX - mLastX;
                    layoutParams.y += mInScreenY - mLastY;
                    mLastX = mInScreenX;
                    mLastY = mInScreenY;
                    updateWindowLayout(false);
                    break;
                case MotionEvent.ACTION_UP:
                    break;
            }
            return false;

        });


        vp.findViewById(R.id.resize).setOnTouchListener((view, event) -> {

            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    mStartX = event.getX();
                    mStartY = event.getY();
                    break;
                case MotionEvent.ACTION_MOVE:
                    mMoveX = event.getX();
                    mMoveY = event.getY();

                    layoutParams.width += mMoveX - mStartX;
                    layoutParams.height += mMoveY - mStartY;

                    // surfaceWidth += mMoveX - mStartX;
                    // surfaceHeight += mMoveY - mStartY;


                    Log.e("scaleView", "scaleView:  qqqqqq==layoutParams.width=" + layoutParams.width + "===layoutParams.height=" + layoutParams.height);

                    ///updateLayout(layoutParams.width, layoutParams.height);
                    if (Math.abs(mMoveX - mStartX) > 1 || Math.abs(mMoveY - mStartY) > 1) {
                        scaleView(sv_framelayout, layoutParams.width, layoutParams.height);
                    }
                    /*ViewGroup.LayoutParams params = sv.getLayoutParams();
                    params.width = mInstance.getSurfaceWidth();
                    params.height = mInstance.getSurfaceHeight();

                    sv.setLayoutParams(params);*/

                 /*   layoutParams.width += mInScreenX - mLastX;
                    layoutParams.height += mInScreenY - mLastY;
                    mLastX = mInScreenX;
                    mLastY = mInScreenY;
                    LogUtil.d("DASFAS", "width00000:==" + mInScreenX + "===,params.height:" + mInScreenY);
                    LogUtil.d("DASFAS", "width11111:==" + mLastX + "===,params.height:" + mLastY);
                    LogUtil.d("DASFAS", "width22222:==" + layoutParams.width + "==,params.height:" + layoutParams.height);
*/
                   /* int surfaceSize = Math.max( layoutParams.width, layoutParams.height);
                    mInstance.setSurfaceHeight(surfaceSize);
                    mInstance.setSurfaceWidth(surfaceSize);*/
                  /*  ViewGroup.LayoutParams params = sv.getLayoutParams();
                    params.width = mInstance.getSurfaceWidth();
                    params.height = mInstance.getSurfaceHeight();
                    LogUtil.d("DASFAS", ".width33333:" + params.width + ",.params.height" + params.height);
                    sv.setLayoutParams(params);


                    TouchUtil.getInstance().setScreenSize(mInstance.getSurfaceWidth(), mInstance.getSurfaceHeight());

                    updateWindowLayout();*/
                    break;
                case MotionEvent.ACTION_UP:
                    //设置无 线坐标跳转

                    break;
            }
            return false;

        });


        TouchUtil.getInstance().setHidRegionCallback((width, height, angle, mode) -> {

            LogUtil.i(TAG, "Rotation:  width:" + width + ",height:" + height + ",angle:" + angle + ",mode:" + mode
                    + ",isSupportHid:" + TouchUtil.getInstance().isSupportHid());

         /*   if (rotation == null) {
                return;
            }*/

            if (width == 0 || height == 0) {
                return;
            }

           /* if (!TouchUtil.getInstance().isSupportHid()) {
                return;
            }*/
            if (popView != null && layoutParams != null) {
                handler.post(() -> {
                        if(angle == 90 || angle == 270){
                            switcMax();
                        }else{
                            if(mInstance.getSurfaceWidth() == maxWidth || mInstance.getScreenHeight() == maxHight){
                                switcNornal();
                            }
                            int w = (int) ((float) mInstance.getSurfaceWidth() / (float) mInstance.getScreenWidth() * width);
                            int h = (int) ((float) mInstance.getSurfaceWidth() / (float) mInstance.getScreenWidth() * height);
                            updateLayout(w, h);
                        }
                        // int roundWidth = Math.round(hsscale * width);
                        // int roundHeight = Math.round(hsscale * height);

                });
            }
        });


        return vp;
    }

    public void switchView() {
        LogUtil.d(TAG, "width3:" + mInstance.getSurfaceWidth() + ",height3:" + mInstance.getSurfaceHeight() + ",layoutParams.height:" + layoutParams.height + ",layoutParams.width:" + layoutParams.width);
        if (layoutParams.height == videoHeight && layoutParams.width != mInstance.getScreenWidth()) {
            updateLayout(layoutParams.width / 2, layoutParams.height / 2);
            mInstance.setSurfaceHeight(videoHeight / 2);
            mInstance.setSurfaceWidth(mInstance.getScreenWidth() / 2);
        } else if (layoutParams.height == videoHeight / 2 && layoutParams.width != mInstance.getScreenWidth() / 2) {
            updateLayout(layoutParams.width * 2, videoHeight);
            mInstance.setSurfaceHeight(videoHeight);
            mInstance.setSurfaceWidth(mInstance.getScreenWidth());
        } else if (layoutParams.height == videoHeight && layoutParams.width == mInstance.getScreenWidth()) {
            updateLayout(mInstance.getScreenWidth() / 2, videoHeight / 2);
            mInstance.setSurfaceHeight(videoHeight / 2);
            mInstance.setSurfaceWidth(mInstance.getScreenWidth() / 2);
        } else {
            updateLayout(mInstance.getScreenWidth(), videoHeight);
            mInstance.setSurfaceHeight(videoHeight);
            mInstance.setSurfaceWidth(mInstance.getScreenWidth());
        }
        isSwitch = true;
        ViewGroup.LayoutParams params = sv.getLayoutParams();
        LogUtil.d(TAG, "width3:" + params.width + ",height3" + params.height + ",layoutParams.height:" + layoutParams.height + ",layoutParams.width:" + layoutParams.width);
        params.width = mInstance.getSurfaceWidth();
        params.height = mInstance.getSurfaceHeight();

        sv.setLayoutParams(params);
        TouchUtil.getInstance().setScaleScreen(mInstance.getScreenHeight()*1f/mInstance.getSurfaceHeight());
        TouchUtil.getInstance().setScreenSize(mInstance.getSurfaceWidth(), mInstance.getSurfaceHeight());
    }
    public void switcMax(){
        if(maxHight == 0 || maxWidth == 0){
            maxHight = 1080;
            maxWidth = 1920;
        }
//        updateLayout(maxWidth, maxHight);
        mInstance.setSurfaceHeight(maxHight);
        mInstance.setSurfaceWidth(maxWidth);

        ViewGroup.LayoutParams params = sv.getLayoutParams();
        LogUtil.d(TAG, "width3:" + params.width + ",height3" + params.height + ",layoutParams.height:" + layoutParams.height + ",layoutParams.width:" + layoutParams.width);
        params.width = mInstance.getSurfaceWidth();
        params.height = mInstance.getSurfaceHeight();

        sv.setLayoutParams(params);
        updateLayout(maxWidth, maxHight);
        TouchUtil.getInstance().setScreenSize(mInstance.getSurfaceWidth(), mInstance.getSurfaceHeight());
        TouchUtil.getInstance().setScaleScreen(1f);
    }
    public void switcNornal(){
//        updateLayout(width, height);
        mInstance.setSurfaceHeight((int) (mInstance.getScreenHeight()*0.8f));
        mInstance.setSurfaceWidth((int) (mInstance.getScreenWidth()*0.8f));

        ViewGroup.LayoutParams params = sv.getLayoutParams();
        LogUtil.d(TAG, "width3:" + params.width + ",height3" + params.height + ",layoutParams.height:" + layoutParams.height + ",layoutParams.width:" + layoutParams.width);
        params.width = mInstance.getSurfaceWidth();
        params.height = mInstance.getSurfaceHeight();

        sv.setLayoutParams(params);
        float scale = mInstance.getSurfaceHeight()*1f/mInstance.getScreenHeight();
        LogUtil.d(TAG, "scale----:" + scale);
        TouchUtil.getInstance().setScaleScreen(mInstance.getScreenHeight()*1f/mInstance.getSurfaceHeight());
        TouchUtil.getInstance().setScreenSize(mInstance.getSurfaceWidth(), mInstance.getSurfaceHeight());
    }
    // 重新计算TextureView大小
    private void reCalculateTextureViewSize(Pair<Integer, Integer> videoSize) {
        if (maxSize == null || videoSize == null) {
            return;
        }
        // 根据原画面大小videoSize计算在maxSize空间内的最大缩放大小
        int tmp1 = videoSize.second * maxSize.first / videoSize.first;
        Pair<Integer, Integer> surfaceSize;
        // 横向最大不会超出
        if (maxSize.second > tmp1) {
            surfaceSize = new Pair<>(maxSize.first, tmp1);
            // 竖向最大不会超出
        } else {
            surfaceSize = new Pair<>(videoSize.first * maxSize.second / videoSize.second, maxSize.second);
        }
        mInstance.setSurfaceHeight(surfaceSize.first);
        mInstance.setSurfaceWidth(surfaceSize.second);
        updateLayout(surfaceSize.first, surfaceSize.second);
        // 更新大小
        ViewGroup.LayoutParams layoutParams = sv.getLayoutParams();
        layoutParams.width = surfaceSize.first;
        layoutParams.height = surfaceSize.second;
        sv.setLayoutParams(layoutParams);
        LogUtil.d(TAG, "surfaceSize.width:" + surfaceSize.first + ",height" + surfaceSize.second);
        TouchUtil.getInstance().setScreenSize(mInstance.getSurfaceWidth(), mInstance.getSurfaceHeight());
    }

    private void createSurface(TextureView surfaceView) {
        ViewGroup.LayoutParams params = surfaceView.getLayoutParams();
        params.width = mInstance.getSurfaceWidth();
        params.height = mInstance.getSurfaceHeight();
        Log.e(TAG, "params.width:" + params.width + ",params.height:" + params.height);
        surfaceView.setLayoutParams(params);
//        surfaceView.setSurfaceTextureListener(mSdkViewModel);
        LinkUtil.readyForLink(this, (FrameLayout) vp);
        TouchUtil.getInstance().setScreenSize(mInstance.getSurfaceWidth(), mInstance.getSurfaceHeight());
        LogUtil.i(TAG, "createSurface end  width:" + params.width + ",height:" + params.height);
      /*  mSdkViewModel.onResume();
        mSdkViewModel.platformadaptor.sendMsg(
                PlatformAdaptor.MSG_TYPE.AP_MSG_APP_STATE,
                -1,
                true);*/
    }


    private void showThumbWindow() {
        if (popView != null) {
            Log.e(TAG, "1width:" + layoutParams.width + ",1height:" + layoutParams.height +
                    ",SurfaceHeight:" + mInstance.getSurfaceHeight() + ",ScreenWidth:" + mInstance.getScreenWidth());
            windowManager.addView(popView, layoutParams);
            isShow = true;
        }
    }


    private int mLastY, mLastX;
    private float mStartX, mStartY;
    private float mMoveX, mMoveY;

    @Override
    public boolean onTouch(View v, MotionEvent motionEvent) {
        if(bluetoothHidManager != null && bluetoothHidManager.isBluetoothReady()){
            bluetoothHidManager.hidSendPointEvent(motionEvent);
        }

        LogUtil.i(TAG, "onTouch" + motionEvent.toString());
//        motionEvent.setLocation(motionEvent.getX()-960f, motionEvent.getY());
//        TouchUtil.getInstance().MulitTouch(mSdkViewModel, motionEvent);
        LinkUtil.motionEventTrans(motionEvent);

        return true;

    }

    /**
     * 更新window
     */
    public void updateWindowLayout(boolean isShow) {
        if (isShow){
            if (windowManager != null && layoutParams != null) {
                ViewGroup.LayoutParams layoutParams1 = sv_framelayout.getLayoutParams();
                layoutParams1.width = layoutParams.width;
                layoutParams1.height = layoutParams.height;
                sv_framelayout.setLayoutParams(layoutParams1);

                ViewGroup.LayoutParams layoutParams2 = vp_framelayout.getLayoutParams();
                layoutParams2.width = layoutParams.width+  30;
                layoutParams2.height = layoutParams.height + 30;
                vp_framelayout.setLayoutParams(layoutParams2);
                windowManager.updateViewLayout(popView, layoutParams2);
            }
        }else {
            if (windowManager != null && layoutParams != null) {
                windowManager.updateViewLayout(popView, layoutParams);
            }
        }

    }

    public void destroyThumbWindow() {
        if (windowManager != null && popView != null) {
            windowManager.removeView(popView);
            popView = null;
            LinkUtil.stopForLink(this, (FrameLayout) vp);
            mInstance.setSurfaceHeight(mInstance.getScreenHeight());
            mInstance.setSurfaceWidth(mInstance.getScreenWidth());
            isShow = false;
        }
    }

    private void updateLayout(int width, int height) {
        layoutParams.width = width;
        layoutParams.height = height;
        updateWindowLayout(true);
    }

    /* public class TextureVideoViewOutlineProvider extends ViewOutlineProvider {
     private float mRadius;
     public TextureVideoViewOutlineProvider(float radius) {
         this.mRadius = radius;
     }
         @Override
         public void getOutline(View view, Outline outline) {
             Rect rect = new Rect();
             view.getGlobalVisibleRect(rect);
             int leftMargin = 0;
             int topMargin = 0;
             LogUtil.d(TAG,"rect:"+rect);
             if(rect.bottom > mInstance.getScreenHeight()/2 ){
                 rect.bottom =  mInstance.getScreenHeight();
             }
             LogUtil.d(TAG,"rect2:"+rect);
             Rect selfRect = new Rect(leftMargin, topMargin,
                     rect.right - rect.left - leftMargin, rect.bottom- rect.top - topMargin);
             outline.setRoundRect(selfRect, mRadius);

          }

      }
  */
    BasePopDialog basePopDialog = new BasePopDialog();

    public void showDialog() {
        if (dialogView != null && !basePopDialog.isShow()) {
            basePopDialog.showTwoButtonDialog(this, dialogView);
        }
    }

    public void refreshDialogSize(int width, int height) {
        if (dialogView != null && layoutParams != null) {
            basePopDialog.refreshDialogSize(layoutParams.width, layoutParams.height);
        }
    }

    public void dismissDialog() {
        if (dialogView != null) {
            basePopDialog.closeDialog();
        }
    }


    @Override
    public void onDestroy() {
        isNotification = false;
        destroyThumbWindow();
        stopForeground(true);
        super.onDestroy();
    }

    public void scaleView(View view, int newWidth, int newHeight) {

        //获取外部布局宽高
        int originalWidth = view.getWidth();
        int originalHeight = view.getHeight();
        //获取内部显示区域宽高
        ViewGroup.LayoutParams params = view.getLayoutParams();
        int surfaceWidth = mInstance.getSurfaceWidth();
        int surfaceHeight = mInstance.getSurfaceHeight();
        float scaleNewWidth = (newWidth / (float) originalWidth);
        float scaleNewHeight = (newHeight / (float) originalHeight);

        float scale = Math.min(scaleNewWidth, scaleNewHeight);

        params.width = Math.round((originalWidth * scale));
        params.height = Math.round(originalHeight * scale);

        scalevideoWidth = Math.round(surfaceWidth * scale);
        scalevideoHeight = Math.round(surfaceHeight * scale);

        mInstance.setSurfaceWidth(scalevideoWidth);
        mInstance.setSurfaceHeight(scalevideoHeight);
        ViewGroup.LayoutParams svparams = sv.getLayoutParams();
        svparams.width = scalevideoWidth;
        svparams.height = scalevideoHeight;
        // 应用新的参数
        sv.setLayoutParams(svparams);
        view.setLayoutParams(params);

        hsscale = scale;
        updateLayout(params.width, params.height);
        TouchUtil.getInstance().setScaleScreen(mInstance.getScreenHeight()*1f/mInstance.getSurfaceHeight());
        TouchUtil.getInstance().setScreenSize(mInstance.getSurfaceWidth(), mInstance.getSurfaceHeight());

    }

}
