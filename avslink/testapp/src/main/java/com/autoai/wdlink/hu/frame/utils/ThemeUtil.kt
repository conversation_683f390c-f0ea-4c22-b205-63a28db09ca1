package com.autoai.wdlink.hu.frame.utils

import android.app.UiModeManager
import android.content.Context
import android.content.res.Configuration

object ThemeUtil {

    fun isNightMode(context: Context): Boolean {
        val uiModelManager = context.getSystemService(Context.UI_MODE_SERVICE) as UiModeManager
        return UiModeManager.MODE_NIGHT_YES == uiModelManager.nightMode
    }

    fun nightTheme(context: Context) {
        val uiModelManager = context.getSystemService(Context.UI_MODE_SERVICE) as UiModeManager
        if (Configuration.UI_MODE_TYPE_CAR == uiModelManager.currentModeType) {
            uiModelManager.enableCarMode(0)
            uiModelManager.nightMode = UiModeManager.MODE_NIGHT_YES
        }
    }

    fun dayTheme(context: Context) {
        val uiModelManager = context.getSystemService(Context.UI_MODE_SERVICE) as UiModeManager
        if (Configuration.UI_MODE_TYPE_CAR == uiModelManager.currentModeType) {
            uiModelManager.enableCarMode(0)
            uiModelManager.nightMode = UiModeManager.MODE_NIGHT_NO
        }
    }
}