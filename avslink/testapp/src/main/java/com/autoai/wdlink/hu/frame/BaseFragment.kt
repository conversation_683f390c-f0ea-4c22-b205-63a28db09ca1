package com.autoai.wdlink.hu.frame

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.RestrictTo
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.viewbinding.ViewBinding
import com.autoai.wdlink.hu.Logger
import com.autoai.wdlink.hu.viewmodel.MainActivityViewModel
import kotlin.reflect.KClass
import kotlin.reflect.KFunction

abstract class BaseFragment<VM : BaseViewModel, VB : ViewBinding> : Fragment() {
    /**
     * xml ui资源绑定，不需要编写findViewById
     * */
    private lateinit var _binding: VB
    protected val binding get() = _binding

    /**
     * onGlobalBack
     * 逻辑层
     * */
    protected lateinit var viewModel: VM
    protected lateinit var activityViewModel: MainActivityViewModel
    override fun onAttach(context: Context) {
        Logger.d("生命周期：onAttach : $this")
        super.onAttach(context)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        Logger.d("生命周期：onCreate : $this")
        super.onCreate(savedInstanceState)
        //绑定ViewModel
        viewModel = ViewModelProvider(this)[getViewModelClass().java]
        activityViewModel = ViewModelProvider(requireActivity())[MainActivityViewModel::class.java]
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        activityViewModel.currentFragment = this.javaClass.simpleName
        Logger.d("生命周期：onCreateView : $this")
        val classVb: KClass<VB> = getViewBindingClass()
        val functionThis2: Collection<KFunction<VB>> =
            classVb.members.filterIsInstance<KFunction<VB>>()
        val functionToInvoke: KFunction<VB>? =
            functionThis2.find { it.name == "inflate" && it.parameters.size == 3 }
        _binding = functionToInvoke?.call(inflater, container, false)!!
        return _binding.root
    }

    /**
     * 获取ViewModel
     * */
    @RestrictTo(RestrictTo.Scope.SUBCLASSES)
    abstract fun getViewModelClass(): KClass<VM>

    /**
     * 获取ViewBinding
     * */
    @RestrictTo(RestrictTo.Scope.SUBCLASSES)
    abstract fun getViewBindingClass(): KClass<VB>

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        Logger.d("生命周期：onViewCreated : $this")
        super.onViewCreated(view, savedInstanceState)

    }


    override fun onStart() {
        Logger.d("生命周期：onStart : $this")
        super.onStart()
    }

    override fun onResume() {
        Logger.d("生命周期：onResume : $this")
        super.onResume()
    }

    override fun onPause() {
        Logger.d("生命周期：onPause : $this")
        super.onPause()
    }

    override fun onStop() {
        Logger.d("生命周期：onStop : $this")
        super.onStop()
    }

    override fun onDestroyView() {
        Logger.d("生命周期：onDestroyView : $this")
        super.onDestroyView()
    }

    override fun onDestroy() {
        Logger.d("生命周期：onDestroy : $this")
        super.onDestroy()
    }

    override fun onDetach() {
        Logger.d("生命周期：onDetach : $this")
        super.onDetach()
    }

}