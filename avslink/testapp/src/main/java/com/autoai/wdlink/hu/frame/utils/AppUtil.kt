package com.autoai.wdlink.hu.frame.utils

import android.content.ComponentName
import android.content.Context
import android.content.Intent


object AppUtil {
    /**
     * 打开车机系统蓝牙界面
     * */
    fun startBluetoothCard(context: Context) {
        startSettingCard(context, "com.desay_sv.show_window.Bluetooth")
    }

    /**
     * 打开车机系统WIFI界面
     * */
    fun startWifiCard(context: Context) {
        startSettingCard(context, "com.desay_sv.show_window.WiFi");
    }

    /**
     * 打开车机系统NET界面
     * */
    fun startNetCard(context: Context) {
        startSettingCard(context, "com.desay_sv.show_window.Net");
    }

    private fun startSettingCard(context: Context, action: String) {
        val componentName = ComponentName(
            "com.desay.setting",
            "com.desay.setting.activity.DialogActivity"
        )
        val intent = Intent()
        intent.action = action
        intent.component = componentName
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        context.startActivity(intent)
    }
}