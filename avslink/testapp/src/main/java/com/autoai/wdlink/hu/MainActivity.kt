package com.autoai.wdlink.hu

import android.Manifest
import android.content.Intent
import android.content.res.Configuration
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.view.View
import android.view.WindowManager
import android.widget.Toast
import androidx.core.app.ActivityCompat
import androidx.lifecycle.Observer
import androidx.navigation.NavController
import androidx.navigation.Navigation
import com.autoai.wdlink.hu.databinding.ActivityMainBinding
import com.autoai.wdlink.hu.frame.BaseActivity
import com.autoai.wdlink.hu.frame.utils.AppUtil
import com.autoai.wdlink.hu.frame.utils.PopWinServiceUtils
import com.autoai.welink.sdk.BT.RequestBTMusicFocusUtil
import com.autoai.wdlink.hu.model.PageModel
import com.autoai.wdlink.hu.sdk.LinkUtil
import com.autoai.wdlink.hu.view.InitFragment
import com.autoai.wdlink.hu.view.RunningFragment
import com.autoai.wdlink.hu.viewmodel.MainActivityViewModel
import com.autoai.welinkapp.checkconnect.CommonData
import com.autoai.welinkapp.model.MessageEvent
import com.autoai.welinkapp.model.UIResponder
import org.json.JSONException
import org.json.JSONObject


/**
 * 首页
 * */
class MainActivity : BaseActivity<MainActivityViewModel, ActivityMainBinding>() {
    companion object {
        var instance : MainActivity? = null;
    }
    private val navController: NavController by lazy {
        Navigation.findNavController(this, R.id.nav_host)
    }

  /*  private val bluetoothReceiver by lazy {
        BluetoothReceiver()
    }*/
   /* private val surfaceHelper by lazy {
        SurfaceHelper()
    }*/
//    private var rootView by lazy{
    private val pageObserver by lazy {
        Observer<PageModel> {
            when (it.page) {
                UIResponder.INITIAL_PAGE -> {
                    viewModel.bgVisibility.value = View.VISIBLE
                    if (null == it.param) {
                        if (viewModel.currentFragment != InitFragment::class.java.simpleName) {
                            Logger.v("uiResponder: popBackStack - > initFragment")
                            navController.popBackStack(R.id.initFragment, false)
                            PopWinServiceUtils.stopPopWinService(this)
                            binding.bgView.postDelayed({
                                PopWinServiceUtils.startPopWinService(this)
                            }, 500)

                        }
                    }
                }

                UIResponder.HELP_PAGE -> {
                    Logger.v("uiResponder: navigate - > helpFragment")
                    viewModel.bgVisibility.value = View.VISIBLE
                    val bundle = Bundle()
                    if (null != it.param) {
                        bundle.putInt(CommonData.MSG_KEY_TYPE, it.param as Int)
                    }
                    navController.navigate(R.id.helpFragment, bundle)
                }

                UIResponder.CONNECTING_PAGE -> {
                    Logger.v("uiResponder: navigate - > connectingFragment")
                    viewModel.bgVisibility.value = View.VISIBLE
                    navController.navigate(R.id.connectingFragment)
                }

                UIResponder.ERR_PAGE_SDK, UIResponder.ERR_PAGE_CONNECT -> {
                    Logger.v("uiResponder: navigate - > connectTimeoutFragment")
                    viewModel.bgVisibility.value = View.VISIBLE
                    navController.navigate(R.id.connectTimeoutFragment)
                    PopWinServiceUtils.stopPopWinService(this)
                }

                UIResponder.ERR_PAGE_HEART -> {
                    Logger.v("uiResponder: navigate - > heartbeatErrorFragment")
                    viewModel.bgVisibility.value = View.VISIBLE
                    navController.navigate(R.id.heartbeatErrorFragment)
                    PopWinServiceUtils.stopPopWinService(this)

                }

                UIResponder.RUNNING_PAGE -> {
                    Logger.v("uiResponder: navigate - > runningFragment")
                    if (viewModel.currentFragment != RunningFragment::class.java.simpleName) {
                        PopWinServiceUtils.showPopWin(this)
                        navController.navigate(R.id.runningFragment)
                        binding.bgView.postDelayed({
                            viewModel.bgVisibility.value = View.GONE
                        }, 500)
                    }
                }

                else -> {
                    viewModel.bgVisibility.value = View.VISIBLE
                }
            }
        }
    }

    private val msgObserver by lazy {
        Observer<MessageEvent> {
            when (it.what) {
                MessageEvent.RECEIVE_MESSAGE -> {
                    Logger.v("MsgCallback Type = MessageEvent.RECEIVE_MESSAGE")
                    var key = ""
                    try {
                        val obj = it.obj.toString()
                        val json = JSONObject(obj)
                        key = json.get("key").toString()
                    } catch (e: JSONException) {
                        Logger.e("消息解析错误", e)
                    }
                    Logger.v("MsgCallback key = $key")
                    if ("actionCarHome" == key) {
                        //回到车机主页
                        moveTaskToBack(false)
                    } else if ("onOpenHuBTSetting" == key) {
                        //前往车机蓝牙设置界面
                        AppUtil.startBluetoothCard(this)
                    }
                }

                MessageEvent.GO_TO_MENU -> {
                    Logger.v("MsgCallback Type = MessageEvent.GO_TO_MENU")
//                    moveTaskToBack(false)
                }

                else -> {
                    Logger.v("MsgCallback Type = others --> ${it.what}")
                }
            }
        }
    }

    override fun getViewModelClass() = MainActivityViewModel::class

    override fun getViewBindingClass() = ActivityMainBinding::class
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
       /* viewModel.bgVisibility.observe(this) { t ->
            t?.let {
                binding.bgView.visibility = it
                binding.touchView.visibility = if (it == View.GONE) {
                    binding.touchView.setOnTouchListener { toucheView, motionEvent ->
                        viewModel.motionEventTrans(motionEvent)
                        if (motionEvent.action == MotionEvent.ACTION_UP) {
                            toucheView.performClick()
                        }
                        true
                    }
                    View.VISIBLE
                } else {
                    binding.touchView.setOnTouchListener(null)
                    View.GONE
                }
            }
        }*/
        instance = this
        //页面切换
        viewModel.pageEvent.observe(this,pageObserver)
        //接受手机端发送的消息
        LinkUtil.interiorMsgEvent.observe(this, msgObserver)
        //
//        viewModel.fullScreenLiveData.observe(this) {
//            binding.root.postDelayed({
//                if (it) {
////                    SystemBarUtil.hideSystemUI(window)
//                    WindowUtil.hideSystemUI(window)
//                } else {
////                    SystemBarUtil.showSystemUI(window, this@MainActivity)
//                    WindowUtil.showSystemUI(window)
//                }
//            }, 200)
//        }
        //
//        viewModel.initLink(this)
//        viewModel.setHidRegionCallback(binding.phoneSurfaceParent)

//        if (ThemeUtil.isNightMode(this@MainActivity)) {
//            binding.modeBtn.text = "当前为黑夜模式"
//        } else {
//            binding.modeBtn.text = "当前为白天模式"
//        }
        binding.ivBackhome.setOnClickListener {
            moveTaskToBack(false)
        }
//        viewModel.fullScreenLiveData.value = true
        //PlatformAdaptor.getInstance(getApplicationContext()).registerReceiver(getApplicationContext());
        /*var windowUtil: WindowUtil = WindowUtil.getInstance(this)
        windowUtil.setScreenWH(windowUtil.screenWidth,windowUtil.screenHeight - getStatusBarHeight())*/

        var windowUtil: com.autoai.welinkapp.util.WindowUtil = com.autoai.welinkapp.util.WindowUtil.getInstance(this)
        Logger.i("111----width: ${windowUtil.getScreenWidth()}, height: ${windowUtil.getScreenHeight()}")
//        windowUtil.setScreenWH(windowUtil.screenWidth*4/5,windowUtil.screenHeight*4/5)
//        windowUtil.setScreenWH(windowUtil.screenWidth,windowUtil.screenHeight -getStatusBarHeight())
//        TouchUtil.getInstance().setScaleScreen(0.8f)
        Logger.i("222----width: ${windowUtil.getScreenWidth()}, height: ${windowUtil.getScreenHeight()}")

        LinkUtil.initLink(this)

//        registerBlueTooth();
        binding.rootContainer.post {

            Logger.d("第四种方式2：width_3="+binding.rootContainer.width+",,,,,,,height_3:"+binding.rootContainer.height)
        }

        Logger.d("权限检查:" + checkSelfPermission(Manifest.permission.ACCESS_FINE_LOCATION)+","
                +checkSelfPermission(Manifest.permission.BLUETOOTH_ADMIN)+","+
                checkSelfPermission(Manifest.permission.BLUETOOTH)+","
                +checkSelfPermission(Manifest.permission.BLUETOOTH_CONNECT) + ","
                +checkSelfPermission(Manifest.permission.BLUETOOTH_ADVERTISE) + ","
                +checkSelfPermission(Manifest.permission.ACCESS_COARSE_LOCATION))
        ActivityCompat.requestPermissions(this,arrayOf(Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.BLUETOOTH_CONNECT,Manifest.permission.BLUETOOTH_ADMIN,Manifest.permission.BLUETOOTH,
            Manifest.permission.BLUETOOTH_ADVERTISE,
            Manifest.permission.ACCESS_COARSE_LOCATION),0X101)
//       requestPermissions(arrayOf(Manifest.permission.ACCESS_FINE_LOCATION,
//            Manifest.permission.BLUETOOTH_CONNECT,Manifest.permission.BLUETOOTH_ADMIN,Manifest.permission.BLUETOOTH,
//            Manifest.permission.BLUETOOTH_ADVERTISE,
//            Manifest.permission.ACCESS_COARSE_LOCATION),101)
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        Logger.d("onNewIntent-----------")
    }
    fun getStatusBarHeight(): Int {
        var result = 0
        val resourceId = resources.getIdentifier("status_bar_height", "dimen", "android")
        if (resourceId > 0) {
            result = resources.getDimensionPixelSize(resourceId)
        }
        return result
    }
  /*  fun registerBlueTooth(){
        //监听蓝牙连接状态
        var filter = IntentFilter()
        filter.addAction(RequestBTMusicFocusUtil.getReceiveProperty("ACTION_CONNECTION_STATE_CHANGED"))
        registerReceiver(bluetoothReceiver, filter);
    }*/

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        Logger.d("onWindowFocusChanged ：hasFocus = $hasFocus")
//        viewModel.windowHasFocus = hasFocus
//        viewModel.fullScreen()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        Logger.d("onConfigurationChanged ：$this")
    }

    /**
     * 切换 Surface
     */
 /*   fun resetSurface() {
        surfaceHelper.resetSurface(rootView!!)
    }

    fun clearSurface() {
        surfaceHelper.clearSurface(rootView!!.findViewById(R.id.phone_surface_parent))
    }*/

    override fun onResume() {
        super.onResume()
//        WindowUtil2s.hideSystemUI(window)
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
//        viewModel.readyForLink(this)
//        RequestBTMusicFocusUtil.requestBTMusicFocus(this)
        startPopWinService()
        Logger.d("iCurrentConnectType:"+CommonData.iCurrentConnectType)
    }
    fun startPopWinService(){

        Logger.e("PopWinService-------start")
        if (!Settings.canDrawOverlays(this)) {
            val intent = Intent(
                Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                Uri.parse("package:$packageName")
            )
            startActivityForResult(intent, 10)
        }else{
//            PopWinService.setVP(rootView)
            PopWinServiceUtils.startPopWinService(this)
            Logger.d("isConnectPhone:"+RequestBTMusicFocusUtil.isConnectPhone)
          /*  if(RequestBTMusicFocusUtil.isConnectPhone) {
                PopWinServiceUtils.showPopWin(this)
            }*/
        }
    }

    override fun onPause() {
        super.onPause()
        window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
//        SystemBarUtil.showSystemUI(window, this)
//        WindowUtil.showSystemUI(window)
//        viewModel.stopForLink(this)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 10) {
            if (Build.VERSION.SDK_INT >= 23) {
                if (!Settings.canDrawOverlays(this)) {
                    // SYSTEM_ALERT_WINDOW permission not granted...
                    Toast.makeText(this, "not granted", Toast.LENGTH_SHORT)
                } else {
                    startPopWinService()
                }
            }
        }
    }
    override fun onBackPressed() {
        viewModel.onBackPressed {
            moveTaskToBack(true)
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        Logger.d("onRequestPermissionsResult code = $requestCode  permission = $permissions grantResults = $grantResults")
        for (permission in permissions) {
            Logger.d("permission = $permission")
        }

        for(result in grantResults){
            Logger.d("permission result = $result")
        }
    }

    override fun onDestroy() {
        instance = null
        PopWinServiceUtils.stopPopWinService(this)
        PopWinServiceUtils.destroyPopWin(this)
        viewModel.pageEvent.removeObserver(pageObserver)
        super.onDestroy()
//        stopService(Intent(this, PopWinService::class.java))
      /*  if(bluetoothReceiver != null) {
            unregisterReceiver(bluetoothReceiver)
        }*/
        LinkUtil.clearMessage()
        android.os.Process.killProcess(android.os.Process.myPid());
        System.exit(0);
    }
}