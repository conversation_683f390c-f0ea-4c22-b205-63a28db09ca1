package com.autoai.wdlink.hu

import android.content.Context
import com.autoai.link.baselog.WeLinkLog

/**
 * 日志工具类
 *
 * <AUTHOR> */
object Logger {
    private val LOG: WeLinkLog = object : WeLinkLog() {
        override fun configTag(): String {
            return "A55日志"
        }

        override fun configLogName(): String {
            return Logger::class.java.simpleName + ".java"
        }
    }

    fun isIsLoggable(): Boolean {
        return LOG.isFileLoggable
    }

    fun setIsLoggable(isLoggable: Boolean) {
        LOG.isIsLoggable = isLoggable
    }

    fun isIsFileLoggable(): Boolean {
        return LOG.isFileLoggable
    }

    fun setIsFileLoggable(isFileLoggable: Boolean) {
        LOG.isFileLoggable = isFileLoggable
    }

    fun v(msg: String?) {
        LOG.v(msg)
    }

    fun v(msg: String?, t: Throwable?) {
        LOG.v(msg, t)
    }

    fun d(msg: String?) {
        LOG.d(msg)
    }

    fun d(msg: String?, t: Throwable?) {
        LOG.d(msg, t)
    }

    fun i(msg: String?) {
        LOG.i(msg)
    }

    fun i(msg: String?, t: Throwable?) {
        LOG.i(msg, t)
    }

    fun w(msg: String?) {
        LOG.w(msg)
    }

    fun w(msg: String?, t: Throwable?) {
        LOG.w(msg, t)
    }

    fun e(msg: String?) {
        LOG.e(msg)
    }

    fun e(msg: String?, t: Throwable?) {
        LOG.e(msg, t)
    }

    fun printStackTrace(msg: String?) {
        LOG.printStackTrace(msg)
    }

    fun registerUncaughtExceptionHandler(context: Context?) {
        LOG.registerUncaughtExceptionHandler(context, null)
    }


}