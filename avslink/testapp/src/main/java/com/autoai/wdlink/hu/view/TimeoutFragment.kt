package com.autoai.wdlink.hu.view

import android.content.Intent
import android.os.Bundle
import android.view.View
import com.autoai.wdlink.hu.databinding.FragmentTimeoutBinding
import com.autoai.wdlink.hu.frame.BaseFragment
import com.autoai.wdlink.hu.viewmodel.TimeoutViewModel
import com.autoai.welinkapp.util.WindowUtil

class TimeoutFragment :
    BaseFragment<TimeoutViewModel, FragmentTimeoutBinding>() {

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        WindowUtil.expandTouchArea(binding.btnConnectTimeoutGotIt, 10)
        binding.tvConnectTimeoutClickHere.setOnClickListener {
            viewModel.help()
        }
        binding.btnConnectTimeoutGotIt.setOnClickListener {
            viewModel.iKnow()
        }
        binding.closeIcon.setOnClickListener {
            activity?.apply {
                finish()
            }
        }
    }

    override fun onResume() {
        super.onResume()
//        activityViewModel.fullScreen = false
//        activityViewModel.fullScreen()
//        activityViewModel.stopForLink(requireActivity())
//        PopWinServiceUtils.stopPopWinService(requireContext())
    }

    override fun getViewModelClass() = TimeoutViewModel::class

    override fun getViewBindingClass() = FragmentTimeoutBinding::class
}