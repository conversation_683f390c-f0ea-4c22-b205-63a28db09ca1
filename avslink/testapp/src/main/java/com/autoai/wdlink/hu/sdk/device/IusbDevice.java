package com.autoai.wdlink.hu.sdk.device;

import static java.lang.Thread.sleep;

import android.text.TextUtils;

import com.autoai.common.util.LogUtil;
import com.autoai.common.util.ThreadUtils;
import com.autoai.welinkapp.eap.OnLinkEapListener;
import com.cczhr.otglocation.jni.LibUsbApi;
import com.cczhr.otglocation.jni.UsbApiInterface;

import java.util.ArrayList;

public class IusbDevice implements com.autoai.welinkapp.eap.EapDevice{
    private static final String TAG = "IusbDevice";
    private static String deviceName = "";
    private static long devicePtr = 0;
    private static long connectPtr = 0;
    private static final int PORT = 2345;
    OnLinkEapListener linkEapListener;

    private static  boolean  CONNECT_START = false;

    @Override
    public void initMfiI2cPath() {

    }

    @Override
    public void eapInit() {

    }

    @Override
    public boolean setUsbMode(boolean usbMode) {
        return false;
    }

    @Override
    public void eapDeinit() {

    }

    @Override
    public void activeEap() {
        //激活
    }

    @Override
    public void deactiveEap() {
       //去激活
    }

    @Override
    public void eapLaunchApp() {
        //拉起app
//        LogUtil.i(TAG, "ideviceConnect connectPtr = " + devicePtr);
//        if(devicePtr > 0) {
//            connectPtr = UsbApiInterface.getInstance().ideviceConnect(devicePtr, PORT);
//            LogUtil.i(TAG, "ideviceConnect connectPtr = " + connectPtr);
//            if (connectPtr > 0) {
//               //return true;
//            }
//        }
    }

    @Override
    public void linkDeviceCallbackRegister(OnLinkEapListener linkEapListener) {
        //连接状态
        this.linkEapListener = linkEapListener;
        LogUtil.i(TAG,"linkDeviceCallbackRegister begin");
        UsbApiInterface.getInstance().registerOnLinkEapListener(new LibUsbApi.IdeviceLinkEapListener() {
            @Override
            public void OnLinkEapListener(int i) {
                LogUtil.i(TAG,"linkDeviceCallbackRegister OnLinkEapListener i = " + i);
                if(!CONNECT_START) {
                    CONNECT_START = true;
                    iusbInitDevice(i);
                }
            }
        });
    }

    private void iusbInitDevice(int i){
        ThreadUtils.postOnBackgroundThread(new Runnable() {
            @Override
            public void run() {
                while(true) {
                    deviceName = UsbApiInterface.getInstance().ideviceGetDeviceName();
                    LogUtil.d(TAG, "linkDeviceCallbackRegister ideviceGetDeviceName deviceName = " + deviceName);
                    if(deviceName == null){
                        try {
                            Thread.sleep(1000);
                            continue;
                        } catch (InterruptedException e) {
                            LogUtil.d(TAG,"InterruptedException e = " + e);
                        }
                    }

                    if (!TextUtils.isEmpty(deviceName)) {
                        devicePtr = UsbApiInterface.getInstance().ideviceNew(deviceName);
                        if(devicePtr > 0){
                            break;
                        }else{
                            try {
                                Thread.sleep(1000);
                                LogUtil.d(TAG,"ideviceNew devicePtr = " + devicePtr);
                            } catch (InterruptedException e) {
                                LogUtil.d(TAG,"InterruptedException e = " + e);
                            }

                        }
                    }
                }
                if (linkEapListener != null) {
                    linkEapListener.onLinkDeviceCallbackType(i);
                }
            }
        });
    }

    @Override
    public void unLinkDeviceCallbackRegister() {

    }

    @Override
    public boolean eapBulkOpen() {
        LogUtil.i(TAG, "ideviceConnect devicePtr = " + devicePtr);
        if(devicePtr > 0) {
            while(true) {
                connectPtr = UsbApiInterface.getInstance().ideviceConnect(devicePtr, PORT);
                LogUtil.i(TAG, "ideviceConnect connectPtr = " + connectPtr);
                if (connectPtr > 0) {
                     break;
                }
                try {
                    sleep(500);
                } catch (Exception e) {
                    LogUtil.e(TAG, "eapBulkOpen e = " + e);
                }
            }
        }
        return true;
    }

    @Override
    public void eapBulkClose() {
        if(connectPtr > 0) {
            UsbApiInterface.getInstance().ideviceFree(connectPtr);
        }
        connectPtr = 0;
        CONNECT_START = false;

        LogUtil.i(TAG,"ideviceConnect disconnect");
    }


    public static String byteArrayToHexString(byte[] bytes, int length) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(String.format("%02X ", bytes[i]));
        }
        return sb.toString().trim();
    }

    @Override
    public byte[] eapBulkRead(int len) {
        if(connectPtr > 0) {
            byte[] out =UsbApiInterface.getInstance().ideviceConnectionReceive(connectPtr);
            if(out != null) {
                LogUtil.i(TAG, "ideviceConnectionReceive  len = " + out.length);
                return out;
            }
        }

        return new byte[0];
    }

    @Override
    public int eapBulkWrite(byte[] buffer, int len) {
        if(connectPtr > 0 && buffer != null){
           int ret = UsbApiInterface.getInstance().ideviceConnectionSend(connectPtr,buffer,len);
           if(ret < 0){
               return ret;
           }
        }
        return len;
    }

    @Override
    public int getEapStatus() {
        return 0;
    }

    @Override
    public boolean resetUsb() {
        return false;
    }
}
