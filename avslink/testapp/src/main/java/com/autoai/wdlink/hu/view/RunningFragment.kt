package com.autoai.wdlink.hu.view

import com.autoai.wdlink.hu.databinding.FragmentRunningBinding
import com.autoai.wdlink.hu.frame.BaseFragment
import com.autoai.wdlink.hu.viewmodel.RunningViewModel

/**
 * 投屏运行
 * */
class RunningFragment : BaseFragment<RunningViewModel, FragmentRunningBinding>() {

    override fun onResume() {
        super.onResume()
//        activityViewModel.fullScreen = true
//        activityViewModel.fullScreen()
//        PopWinServiceUtils.showPopWin(requireActivity())

    }

    override fun getViewModelClass() = RunningViewModel::class

    override fun getViewBindingClass() = FragmentRunningBinding::class
}