package com.autoai.wdlink.hu

import android.app.Application
import com.autoai.wdlink.hu.sdk.LinkSdkModel


class MyApplication : Application() {
    companion object {
        lateinit var application: MyApplication
    }

    override fun onCreate() {
        super.onCreate()
        application = this@MyApplication
        Logger.setIsLoggable(true)
        //logDebug:打开logcat日志输出。fileDebug:打开文件日志输出。
        LinkSdkModel.logAble(true, fileDebug = false)
        Logger.setIsFileLoggable(false)
        Logger.registerUncaughtExceptionHandler(this)
    }
}