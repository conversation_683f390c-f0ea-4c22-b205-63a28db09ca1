package com.autoai.wdlink.hu.frame.utils;

import android.content.Context;
import android.graphics.PixelFormat;
import android.view.Gravity;
import android.view.SurfaceView;
import android.view.TextureView;
import android.view.View;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.VideoView;

/**
 * <AUTHOR> zhanggc
 * @version : 1.0
 * @description : java类作用描述
 * @date : 2024/6/5 16:46
 */
public class FloatingVideoManager {
   private WindowManager windowManager;
   private FrameLayout videoView;
   private WindowManager.LayoutParams params;

   public FloatingVideoManager(Context context) {
      windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);

       params = new WindowManager.LayoutParams();
      params.type = WindowManager.LayoutParams.TYPE_APPLICATION;
      params.format = PixelFormat.RGBA_8888;
      params.gravity = Gravity.START | Gravity.TOP;
      params.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN;
      params.x = 0;
      params.y = 0;
      params.alpha = 1;
      params.width = 360;
      params.height = 760;


      params.x = 0;
      params.y = 0;
   }

   public void showFloatingVideo(FrameLayout view) {
      this.videoView = view;
      windowManager.addView(videoView, params);
      // 设置视频源和播放等操作
   }

   public void hideFloatingVideo() {
      if (videoView != null) {
         windowManager.removeView(videoView);
      }
   }
}
