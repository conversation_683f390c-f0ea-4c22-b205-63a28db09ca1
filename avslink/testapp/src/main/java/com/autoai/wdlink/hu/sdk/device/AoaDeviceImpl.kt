package com.autoai.wdlink.hu.sdk.device

import com.autoai.welinkapp.aoa.AoaDevice

class AoaDeviceImpl: AoaDevice {
    override fun getManufacturer(): String {
        return "AutoAi, Inc"
    }

    override fun getModel(): String {
        return "GAZ Link"
    }

    override fun getDescription(): String {
        return "GAZ Link"
    }

    override fun getUri(): String {
        return "https://play.google.com/store/apps/details?id=com.gazra9k.android.launcher"
    }

    override fun getSerial(): String {
        return "mySerial"
    }

    override fun getVersion(): String {
        return "1.0"
    }
}