package com.autoai.wdlink.hu.sdk

import android.app.Activity
import android.content.Context
import android.view.MotionEvent
import android.widget.FrameLayout
import androidx.lifecycle.MutableLiveData
import com.autoai.wdlink.hu.Logger
import com.autoai.welinkapp.model.MessageEvent
import com.autoai.welinkapp.model.OnMessageListener

/**
 * <AUTHOR> zhanggc
 * @description : java类作用描述
 * @date : 2024/9/2 15:09
 * @version : 1.0
 */
object LinkUtil {
    private val surfaceHelper: SurfaceHelper = SurfaceHelper()
     val interiorMsgEvent by lazy {
        MutableLiveData<MessageEvent>()
    }
    private var inited = false
    private var isReady: Boolean = false

    fun initLink(activity: Activity) {
        if (!inited) {
            LinkSdkModel.initLink(activity)
            LinkSdkModel.requestMultiPermissions(activity)
            LinkSdkModel.initUiResponder()
            inited = true
        }
    }

    @JvmStatic
    fun readyForLink(activity: Context, frameLayout: FrameLayout) {
        if (!isReady && inited) {
            isReady = true
            LinkSdkModel.readyForLink(activity)
            //
            /* if (activity is MainActivity) {
                 activity.resetSurface()
             }*/
            //
            surfaceHelper.resetSurface(frameLayout)
            LinkSdkModel.sendSystemMsg(activity)
            LinkSdkModel.addMessageListener(onMessageListener)
        }
    }

    @JvmStatic
    fun stopForLink(activity: Context, frameLayout: FrameLayout) {
        if (isReady && inited) {
            LinkSdkModel.stopForLink(activity)
            LinkSdkModel.removeMessageListener(onMessageListener)
            //
            /*  if (activity is MainActivity) {
                  activity.clearSurface()
              }*/
            surfaceHelper.clearSurface(frameLayout)
            //
            isReady = false
        }
    }

    private val onMessageListener: OnMessageListener by lazy {
        OnMessageListener { messageEvent ->
            Logger.e("messageEvent what = ${messageEvent.what}, arg1 = ${messageEvent.arg1}, obj = ${messageEvent.obj}")
            interiorMsgEvent.postValue(messageEvent)
        }
    }

    @JvmStatic
    fun motionEventTrans(motionEvent: MotionEvent) {
        HidRegionHelper.motionEventTrans(motionEvent)
    }
    fun clearMessage() {
        inited = false
        interiorMsgEvent.value = MessageEvent(0)
    }
}
