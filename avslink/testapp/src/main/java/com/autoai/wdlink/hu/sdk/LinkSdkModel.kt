package com.autoai.wdlink.hu.sdk

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.view.MotionEvent
import android.view.Surface
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.autoai.common.util.LogUtil
import com.autoai.wdlink.hu.Logger
import com.autoai.wdlink.hu.frame.utils.PopWinServiceUtils
import com.autoai.welink.sdk.BT.RequestBTMusicFocusUtil
import com.autoai.wdlink.hu.model.PageModel
import com.autoai.wdlink.hu.sdk.device.AoaDeviceImpl
import com.autoai.wdlink.hu.sdk.device.EapDeviceImpl
import com.autoai.wdlink.hu.sdk.device.IusbDevice
import com.autoai.welinkapp.LinkHost
import com.autoai.welinkapp.audio.AudioPlayer
import com.autoai.welinkapp.checkconnect.CommonData
import com.autoai.welinkapp.model.*
import com.autoai.welinkapp.platform.LinkPlatform
import com.autoai.welinkapp.util.PermissionUtils
import java.lang.ref.WeakReference

object LinkSdkModel {
    private const val ACT_CLOSE_QUICK_PANEL = "action_close_quick_panel"
    private const val ACT_CLOSE_QS_PANEL = "action_close_quicksetting_panel"
    private const val ACT_CLOSE_HVAC_PANEL = "com.desaysv.hvac.CLOSE_HVAC"
    private lateinit var weakReference: WeakReference<Context>
    /**
     * 页面
     * */
    private val interiorPageEvent by lazy {
        MutableLiveData<PageModel>()
    }
    val exteriorPageEvent: LiveData<PageModel>
        get() = interiorPageEvent

    /**
     * 连接状态
     * */
    private val statusChangeListener =
        OnLinkStatusChangeListener { status, devicesType ->
            RequestBTMusicFocusUtil.isConnectPhone = false
            when (status) {
                CommonData.LINK_STATUS_NONE -> {
                    Logger.d("USB连接状态：未连接 status = $status, devicesType = $devicesType")
                }
                CommonData.LINK_STATUS_FAIL -> {
                    Logger.d("USB连接状态：连接失败 status = $status, devicesType = $devicesType")
                }
                CommonData.LINK_STATUS_CONNECTING -> {
                    Logger.d("USB连接状态：连接中 status = $status, devicesType = $devicesType")
                }
                CommonData.LINK_STATUS_CONNECTED -> {
                    Logger.d("USB连接状态：已连接 status = $status, devicesType = $devicesType")
                    RequestBTMusicFocusUtil.isConnectPhone = true
                   /* if(RequestBTMusicFocusUtil.isBluetoothConnecte(weakReference.get())) {
                        RequestBTMusicFocusUtil.requestBTMusicFocus(weakReference.get())
                    }*/
                }
                else -> {
                    Logger.d("USB连接状态：未知 status = $status, devicesType = $devicesType")
                }
            }
        }

    fun logAble(logDebug: Boolean, fileDebug: Boolean) {
        Logger.i("logAble: logDebug = $logDebug, fileDebug = $fileDebug")
        LinkHost.applyDebug(logDebug, fileDebug)
        LogUtil.setIsFullLog(logDebug)
    }

    fun initLink(
        context: Context,
        linkPlatform: LinkPlatform? = null,
        audioPlayer: AudioPlayer? = null
    ) {
        Logger.i("initLink")
        val sdkConfig = SdkConfig.Builder()
            .setAoaDevice(AoaDeviceImpl())
            .setEapDevice(IusbDevice())
            .setPlatform(linkPlatform)
            .setAudioPlayer(audioPlayer)
            .setFps(30)
            .setSerialNum("HPDESAYLHADRCA57001")
            .build()
        weakReference = WeakReference(context)
        LinkHost.init(context, sdkConfig)
        LinkHost.addLinkStateChangeListener(statusChangeListener)
    }

    fun requestMultiPermissions(activity: Activity) {
        PermissionUtils.requestMultiPermissions(activity) { requestCode: Int ->
            when (requestCode) {
                PermissionUtils.CODE_RECORD_AUDIO -> {}
                PermissionUtils.CODE_ACCESS_FINE_LOCATION -> {}
                PermissionUtils.CODE_READ_PHONE_STATE -> {}
                else -> {}
            }
        }
    }

    fun initUiResponder() {
        LinkHost.registerUiResponder { page, param ->
            interiorPageEvent.postValue(PageModel(page, param))
            Logger.d("initUiResponder: page = $page, param = $param")
         when (page) {
              /*  UIResponder.INITIAL_PAGE -> {
                    PopWinServiceUtils.stopPopWinService(weakReference.get()!!.applicationContext)

                    PopWinServiceUtils.startPopWinService(weakReference.get()!!.applicationContext)
                    if(RequestBTMusicFocusUtil.isConnectPhone) {
                        PopWinServiceUtils.showPopWin(weakReference.get()!!.applicationContext)
                    }
                }
                UIResponder.HELP_PAGE -> {
                }
                UIResponder.CONNECTING_PAGE  -> {
                    sendButton(ButtonType.INIT_CLOSE, 1)
                }
                UIResponder.ERR_PAGE_SDK, UIResponder.ERR_PAGE_CONNECT -> {
                    PopWinServiceUtils.stopPopWinService(weakReference.get()!!.applicationContext)
                }
                UIResponder.ERR_PAGE_HEART -> {
                    PopWinServiceUtils.stopPopWinService(weakReference.get()!!.applicationContext)
                }
                UIResponder.RUNNING_PAGE -> {
                    PopWinServiceUtils.showPopWin(weakReference.get()!!.applicationContext)
                }*/
                UIResponder.SHOW_USB_INTERFACE_DIALOG -> {
                    PopWinServiceUtils.showUSBPopWin(weakReference.get()!!.applicationContext)
                }
                UIResponder.DISMISS_USB_INTERFACE_DIALOG -> {
                    PopWinServiceUtils.dismissUSBPopWin(weakReference.get()!!.applicationContext)
                }
            }
        }
    }

    fun readyForLink(activity: Context) {
        Logger.i("readyForLink")
        LinkHost.readyForLink(activity)
    }

    fun addMessageListener(listener: OnMessageListener) {
        Logger.i("addMessageListener")
        LinkHost.addMessageListener(listener)
    }

    fun sendSystemMsg(context: Context) {
        //发送系统广播
        Logger.i("sendSystemMsg")
        context.sendBroadcast(Intent(ACT_CLOSE_QUICK_PANEL))
        context.sendBroadcast(Intent(ACT_CLOSE_QS_PANEL))
        context.sendBroadcast(Intent(ACT_CLOSE_HVAC_PANEL))
    }

    fun registerLinkSurface(surface: Surface, width: Int, height: Int) {
        LinkHost.registerLinkSurface(surface, width, height)
    }

    fun unRegisterLinkSurface(surface: Surface) {
        LinkHost.unRegisterLinkSurface(surface)
    }

    fun stopForLink(context: Context) {
        Logger.i("stopForLink")
        LinkHost.stopForLink(context, false)
    }

    fun removeMessageListener(listener: OnMessageListener) {
        Logger.i("removeMessageListener")
        LinkHost.removeMessageListener(listener)
    }

    fun sendButton(type: Int, param: Int) {
        Logger.i("sendButton type = $type, param = $param")
        LinkHost.sendButton(type, param)
    }

    fun help() {
        Logger.i("sendButton CLICK_FOR_HELP")
        sendButton(ButtonType.CLICK_FOR_HELP, 1)
    }

    fun iKnow() {
        Logger.i("sendButton GOT_IT")
        sendButton(ButtonType.GOT_IT, 1)
    }

    fun screenTouch(motionEvent: MotionEvent) {
//        Logger.i("screenTouch motionEvent = $motionEvent")
        LinkHost.screenTouch(motionEvent)
    }

    fun setHidRegionCallback(hidCallback: HidCallback) {
        LinkHost.setHidRegionCallback(hidCallback)
    }

    fun destroy() {
        weakReference.clear()
        Logger.i("destroy")
        LinkHost.removeLinkStateChangeListener(statusChangeListener)
        LinkHost.destroy()
    }
}