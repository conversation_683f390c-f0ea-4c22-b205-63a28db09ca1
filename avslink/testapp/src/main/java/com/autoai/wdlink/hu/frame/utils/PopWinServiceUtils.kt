package com.autoai.wdlink.hu.frame.utils

import android.app.Activity
import android.content.Context
import android.content.Intent
import com.autoai.wdlink.hu.MainActivity
import com.autoai.wdlink.hu.sdk.LinkSdkModel

/**
 * <AUTHOR> zhanggc
 * @description : java类作用描述
 * @date : 2024/6/26 19:33
 * @version : 1.0
 */
 object  PopWinServiceUtils {
  fun startPopWinService(activity: Context) {
      sendPopWinService(activity,"start")
  }
    fun stopPopWinService(activity: Context) {
        sendPopWinService(activity,"stop")
    }
    fun showPopWin(activity: Context) {
        sendPopWinService(activity,"show")
    }
    fun showUSBPopWin(activity: Context) {
        sendPopWinService(activity,"showDialog")
    }
    fun dismissUSBPopWin(activity: Context) {
        sendPopWinService(activity,"dismissDialog")
    }
    fun destroyPopWin(activity: Context) {
        sendPopWinService(activity,"destroy")
    }
    fun sendPopWinService(activity: Context,action: String) {
        var intent = Intent()
        intent.setClassName(activity.packageName, PopWinService::class.java.name)
        intent.setAction(action)
        activity.startForegroundService(intent)
    }
}