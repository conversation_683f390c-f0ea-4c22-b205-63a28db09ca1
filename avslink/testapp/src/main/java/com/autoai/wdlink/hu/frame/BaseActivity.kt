package com.autoai.wdlink.hu.frame

import android.content.res.Configuration
import android.os.Bundle
import androidx.annotation.RestrictTo
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import androidx.viewbinding.ViewBinding
import com.autoai.wdlink.hu.Logger
import kotlin.reflect.KClass
import kotlin.reflect.KFunction

abstract class BaseActivity<VM : BaseViewModel, VB : ViewBinding> : AppCompatActivity() {
    /**
     * 逻辑层
     * */
    protected lateinit var viewModel: VM

    /**
     * xml ui资源绑定，不需要编写findViewById
     * */
    private var _binding: VB? = null
    protected val binding get() = _binding!!
    override fun onAttachedToWindow() {
        Logger.d("生命周期：onAttachedToWindow ：$this")
        super.onAttachedToWindow()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        Logger.d("生命周期：onCreate ：$this")
        super.onCreate(savedInstanceState)
        //绑定ViewModel
        viewModel = ViewModelProvider(this)[getViewModelClass().java]
        //获取对应ViewBinding
        val classVb: KClass<VB> = getViewBindingClass()
        val functionThis2: Collection<KFunction<VB>> =
            classVb.members.filterIsInstance<KFunction<VB>>()
        val functionToInvoke: KFunction<VB>? =
            functionThis2.find { it.name == "inflate" && it.parameters.size == 3 }
        _binding = functionToInvoke?.call(layoutInflater, null, false)
        setContentView(binding.root)
    }

    /**
     * fix：多语言和夜间白天模式冲突
     * */
    override fun applyOverrideConfiguration(overrideConfiguration: Configuration?) {
        if (overrideConfiguration != null) {
            val uiMode = overrideConfiguration.uiMode
            overrideConfiguration.setTo(baseContext.resources.configuration)
            overrideConfiguration.uiMode = uiMode
        }
        super.applyOverrideConfiguration(overrideConfiguration)
    }

    /**
     * 获取ViewModel
     * */
    @RestrictTo(RestrictTo.Scope.SUBCLASSES)
    abstract fun getViewModelClass(): KClass<VM>

    /**
     * 获取ViewBinding
     * */
    @RestrictTo(RestrictTo.Scope.SUBCLASSES)
    abstract fun getViewBindingClass(): KClass<VB>

    override fun onRestart() {
        Logger.d("生命周期：onRestart ：$this")
        super.onRestart()
    }

    override fun onStart() {
        Logger.d("生命周期：onStart ：$this")
        super.onStart()
    }


    override fun onResume() {
        Logger.d("生命周期：onResume ：$this")
        super.onResume()
    }

//    override fun onWindowFocusChanged(hasFocus: Boolean) {
//        super.onWindowFocusChanged(hasFocus)
//        Logger.d("onWindowFocusChanged : $hasFocus")
//    }

    override fun onPause() {
        Logger.d("生命周期：onPause ：$this")
        super.onPause()
    }

    override fun onStop() {
        Logger.d("生命周期：onStop ：$this")
        super.onStop()
    }

    override fun onDestroy() {
        Logger.d("生命周期：onDestroy ：$this")
        super.onDestroy()
    }

    override fun onDetachedFromWindow() {
        Logger.d("生命周期：onDetachedFromWindow ：$this")
        super.onDetachedFromWindow()
    }
}