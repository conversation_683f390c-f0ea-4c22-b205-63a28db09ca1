package com.autoai.wdlink.hu.sdk

import android.graphics.SurfaceTexture
import android.view.Surface
import android.view.TextureView
import android.widget.FrameLayout
import com.autoai.wdlink.hu.Logger
import com.autoai.wdlink.hu.R

class SurfaceHelper {
    private var surfaceView: TextureView? = null
    private var currSurface: Surface? = null

    /**
     * 切换 Surface
     */
    fun resetSurface(phoneSurfaceParent: FrameLayout) {
        //
//        surfaceView = TextureView(phoneSurfaceParent.context)
        surfaceView = phoneSurfaceParent.findViewById(R.id.phoneData_sv)
    /*    surfaceView!!.layoutParams = FrameLayout.LayoutParams(
            FrameLayout.LayoutParams.MATCH_PARENT,
            FrameLayout.LayoutParams.MATCH_PARENT
        )*/
        //
        Logger.i("resetSurface")
        unRegisterLinkSurface()
        if (surfaceView!!.surfaceTexture != null) {
            registerLinkSurface(surfaceView!!)
        } else {
            surfaceView!!.surfaceTextureListener = object : TextureView.SurfaceTextureListener {
                override fun onSurfaceTextureAvailable(
                    surface: SurfaceTexture,
                    width: Int,
                    height: Int
                ) {
                    registerLinkSurface(surfaceView!!)
                }

                override fun onSurfaceTextureSizeChanged(
                    surface: SurfaceTexture,
                    width: Int,
                    height: Int
                ) {
                    Logger.d("onSurfaceTextureSizeChanged: width：$width,heigth:$height")
                    currSurface = Surface(surface)
                    LinkSdkModel.registerLinkSurface(currSurface!!, width, height)
                }

                override fun onSurfaceTextureDestroyed(surface: SurfaceTexture): Boolean {
                    unRegisterLinkSurface()
                    return true
                }

                override fun onSurfaceTextureUpdated(surface: SurfaceTexture) {}
            }
        }
        //
//        phoneSurfaceParent.removeAllViews()
//        phoneSurfaceParent.addView(surfaceView!!)
    }

    fun clearSurface(phoneSurfaceParent: FrameLayout) {
        Logger.i("clearSurface")
//        phoneSurfaceParent.removeAllViews()
        surfaceView?.surfaceTextureListener = null
        unRegisterLinkSurface()
    }

    private fun registerLinkSurface(surfaceView: TextureView) {
        Logger.i("registerLinkSurface")
        currSurface = Surface(surfaceView.surfaceTexture)
        LinkSdkModel.registerLinkSurface(currSurface!!, surfaceView.width, surfaceView.height)
    }

    private fun unRegisterLinkSurface() {
        if (currSurface != null) {
            Logger.i("unRegisterLinkSurface")
            LinkSdkModel.unRegisterLinkSurface(currSurface!!)
            currSurface = null
        }
    }
}