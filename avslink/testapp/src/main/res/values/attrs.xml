<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="ScalableTextureView">
        <attr name="scalableType">
            <enum name="none" value="0" />

            <enum name="fitXY" value="1" />
            <enum name="fitStart" value="2" />
            <enum name="fitCenter" value="3" />
            <enum name="fitEnd" value="4" />

            <enum name="leftTop" value="5" />
            <enum name="leftCenter" value="6" />
            <enum name="leftBottom" value="7" />
            <enum name="centerTop" value="8" />
            <enum name="center" value="9" />
            <enum name="centerBottom" value="10" />
            <enum name="rightTop" value="11" />
            <enum name="rightCenter" value="12" />
            <enum name="rightBottom" value="13" />

            <enum name="leftTopCrop" value="14" />
            <enum name="leftCenterCrop" value="15" />
            <enum name="leftBottomCrop" value="16" />
            <enum name="centerTopCrop" value="17" />
            <enum name="centerCrop" value="18" />
            <enum name="centerBottomCrop" value="19" />
            <enum name="rightTopCrop" value="20" />
            <enum name="rightCenterCrop" value="21" />
            <enum name="rightBottomCrop" value="22" />

            <enum name="startInside" value="23" />
            <enum name="centerInside" value="24" />
            <enum name="endInside" value="25" />
        </attr>
    </declare-styleable>
</resources>