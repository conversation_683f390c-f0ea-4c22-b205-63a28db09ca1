<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:layout_marginLeft="@dimen/margin_title_left"
        android:layout_marginRight="@dimen/margin_title_right"
        tools:background="@drawable/bg_color">

        <TextView
            android:id="@+id/tv_connect_timeout_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/connect_time_out"
            android:textColor="@color/text_color"
            android:textStyle="bold"
            android:textSize="@dimen/title_text_size" />

        <TextView
            android:id="@+id/tv_connect_timeout_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_title_top"
            android:gravity="center"
            android:text="@string/connect_time_out_tips"
            android:textColor="@color/text_color"
            android:textSize="@dimen/tips_text_size" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/textView2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingStart="@dimen/click_here_text_space"
                android:paddingTop="@dimen/click_here_padding"
                android:paddingEnd="@dimen/click_here_padding"
                android:paddingBottom="@dimen/click_here_padding"
                android:text="@string/if_need_help"
                android:textColor="@color/text_color"
                android:textSize="@dimen/tips_text_size" />

            <TextView
                android:id="@+id/tv_connect_timeout_click_here"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingStart="@dimen/click_here_text_space"
                android:paddingTop="@dimen/click_here_padding"
                android:paddingEnd="@dimen/click_here_padding"
                android:paddingBottom="@dimen/click_here_padding"
                android:text="@string/click_here"
                android:textColor="@color/text_can_click"
                android:textSize="@dimen/tips_text_size" />
        </LinearLayout>

        <Button
            android:id="@+id/btn_connect_timeout_got_it"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_btn_top"
            android:background="@drawable/btn_bg_selector"
            android:paddingStart="@dimen/btn_start_end"
            android:paddingTop="@dimen/btn_top_bottom"
            android:paddingEnd="@dimen/btn_start_end"
            android:paddingBottom="@dimen/btn_top_bottom"
            android:text="@string/got_it"
            android:textAllCaps="false"
            android:textColor="@color/text_i_know"
            android:textSize="@dimen/tips_text_size" />

    </LinearLayout>

    <ImageView
        android:id="@+id/close_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end"
        android:layout_margin="20dp"
        android:padding="30dp"
        android:src="@drawable/ic_outline_close_24"
        tools:ignore="ContentDescription" />

</FrameLayout>