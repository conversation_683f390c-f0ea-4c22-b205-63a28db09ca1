<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_color">

    <FrameLayout
        android:id="@+id/phone_surface_parent"
        android:layout_width="match_parent"
        android:layout_height="match_parent" >
       <!-- <TextureView
            android:id="@+id/phoneData_sv"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center" />-->
    </FrameLayout>

    <View
        android:id="@+id/bg_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_color"/>

    <View
        android:id="@+id/touch_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />

    <fragment
        android:id="@+id/nav_host"
        android:name="androidx.navigation.fragment.NavHostFragment"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:defaultNavHost="true"
        app:navGraph="@navigation/link_navigation"
        tools:ignore="FragmentTagUsage"
        tools:layout="@layout/fragment_init" />

    <!--        <Button-->
    <!--            android:id="@+id/modeBtn"-->
    <!--            android:layout_width="wrap_content"-->
    <!--            android:layout_height="wrap_content"-->
    <!--            android:layout_gravity="bottom|center_horizontal"-->
    <!--            android:padding="50dp"-->
    <!--            android:text="白天黑夜模式切换" />-->

    <!--        <View-->
    <!--            android:layout_width="wrap_content"-->
    <!--            android:layout_height="4dp"-->
    <!--            android:layout_gravity="bottom"-->
    <!--            android:background="@android:color/holo_red_light" />-->
            <ImageView
                android:id="@+id/iv_backhome"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="30dp"
                android:src="@mipmap/haiwai_launcher_home_overlay_icon"
                />
    <TextView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:text="测试demo 请勿外传"
        android:textColor="@android:color/holo_red_dark"
        android:gravity="center_horizontal"
        android:textSize="28sp"
        android:clickable="false"
        />
</FrameLayout>