<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical"
    android:layout_marginLeft="@dimen/margin_title_left"
    android:layout_marginRight="@dimen/margin_title_right"
    tools:background="@drawable/bg_color">

    <ImageView
        android:id="@+id/ic_init_logo"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:contentDescription="@string/app_name"
        android:src="@mipmap/ic_launcher" />

    <TextView
        android:id="@+id/textView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:contentDescription="@string/connect_success_1"
        android:text="@string/connect_success_1"
        android:textColor="@color/text_color"
        android:textStyle="bold"
        android:textSize="@dimen/title_text_size" />

    <TextView
        android:id="@+id/textView2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:text="@string/connect_success_2"
        android:textColor="@color/text_color"
        android:textSize="@dimen/tips_text_size" />
</LinearLayout>