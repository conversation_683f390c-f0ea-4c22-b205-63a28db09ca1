<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_color"
        android:gravity="center"
        android:layout_marginLeft="@dimen/margin_title_left"
        android:layout_marginRight="@dimen/margin_title_right"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/ic_init_logo"
            android:layout_width="188dp"
            android:layout_height="85dp"
            android:contentDescription="@string/app_name"
            android:src="@mipmap/logo" />

        <TextView
            android:id="@+id/textView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:contentDescription="@string/init_title"
            android:text="@string/init_title"
            android:textColor="@color/text_color"
            android:gravity="center"
            android:textSize="@dimen/title_text_size" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="50dp"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/textView2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingStart="@dimen/click_here_text_space"
                android:paddingTop="@dimen/click_here_padding"
                android:paddingEnd="@dimen/click_here_padding"
                android:paddingBottom="@dimen/click_here_padding"
                android:text="@string/if_need_help"
                android:textColor="@color/text_color"
                android:textSize="@dimen/title_text_size" />

            <TextView
                android:id="@+id/tv_init_click_here"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingStart="@dimen/click_here_text_space"
                android:paddingTop="@dimen/click_here_padding"
                android:paddingEnd="@dimen/click_here_padding"
                android:paddingBottom="@dimen/click_here_padding"
                android:text="@string/click_here"
                android:textColor="@color/text_can_click"
                android:textSize="@dimen/title_text_size" />
        </LinearLayout>
    </LinearLayout>

    <ImageView
        android:id="@+id/close_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end"
        android:layout_margin="20dp"
        android:padding="30dp"
        android:src="@drawable/ic_outline_close_24"
        tools:ignore="ContentDescription" />
</FrameLayout>

