<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal"
    android:layout_gravity="center"
    android:background="@drawable/phone_bg"
    android:id="@+id/vp_framelayout">
    <FrameLayout
        android:id="@+id/sv_framelayout"
        android:layout_width="match_parent"
        android:layout_gravity="center"
        android:layout_height="match_parent">

    <TextureView
        android:id="@+id/phoneData_sv"
        android:layout_gravity="center"
        android:layout_width="360dp"
        android:layout_height="360dp"
     />

    <ImageView
        android:id="@+id/image"
        android:layout_gravity="right"
        android:layout_width="130dp"
        android:layout_height="130dp"
        android:visibility="gone"
        />

    <ImageView
        android:id="@+id/close"
        android:layout_width="45dp"
        android:layout_height="45dp"
        android:layout_gravity="right|top"
        android:src="@mipmap/a7_btn_close_p"
        android:text="close"/>

    <Button
        android:id="@+id/vertical"
        android:layout_width="35dp"
        android:layout_height="35dp"
        android:layout_gravity="top|left"
        android:visibility="gone"
        android:background="@mipmap/shangqi_music_ximalaya_big"
        android:text=""/>
    <ImageView
        android:layout_width="60dp"
        android:layout_height="10dp"
        android:layout_marginTop="25dp"
        android:layout_gravity="top|center_horizontal"
        android:background="@drawable/btn_move_bg"/>
    <Button
        android:id="@+id/move"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_gravity="top|center_horizontal"
        android:background="@android:color/transparent"
       />

    <Button
        android:id="@+id/horizontal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|center_horizontal"
        android:visibility="gone"
        android:text="horizontal"/>

 <!--   <Button
        android:id="@+id/resize"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:layout_gravity="bottom|right"
        android:background="@android:color/transparent"
        android:visibility="visible"
      />-->

    <Button
        android:id="@+id/capture"
        android:layout_width="59dp"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|right"
        android:visibility="gone"
        android:text="CAPTURE"/>


    <FrameLayout
        android:id="@+id/dialog_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
    </FrameLayout>
    <Button
        android:id="@+id/resize"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_gravity="bottom|right"
        android:background="@drawable/btn_move_bg"
        android:visibility="visible"
        android:text="resize"/>

</FrameLayout>