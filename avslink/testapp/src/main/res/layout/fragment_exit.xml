<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:layout_marginLeft="@dimen/margin_title_left"
        android:layout_marginRight="@dimen/margin_title_right"
        tools:background="@drawable/bg_color">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/heart_beat_err_title"
            android:textColor="@color/text_color"
            android:textStyle="bold"
            android:textSize="@dimen/title_text_size" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:text="@string/heart_beat_err_tip"
            android:textColor="@color/text_color"
            android:gravity="center"
            android:textSize="@dimen/tips_text_size" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="60dp"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_heartbeat_err_need_help"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingStart="@dimen/click_here_text_space"
                android:paddingTop="@dimen/click_here_padding"
                android:paddingEnd="@dimen/click_here_padding"
                android:paddingBottom="@dimen/click_here_padding"
                android:text="@string/if_need_help"
                android:textColor="@color/text_color"
                android:textSize="@dimen/tips_text_size" />

            <TextView
                android:id="@+id/tv_heartbeat_err_click_here"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingStart="@dimen/click_here_text_space"
                android:paddingTop="@dimen/click_here_padding"
                android:paddingEnd="@dimen/click_here_padding"
                android:paddingBottom="@dimen/click_here_padding"
                android:text="@string/click_here"
                android:textColor="@color/text_can_click"
                android:textSize="@dimen/tips_text_size" />

        </LinearLayout>

    </LinearLayout>

    <ImageView
        android:id="@+id/close_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end"
        android:layout_margin="20dp"
        android:padding="30dp"
        android:src="@drawable/ic_outline_close_24"
        tools:ignore="ContentDescription" />
</FrameLayout>