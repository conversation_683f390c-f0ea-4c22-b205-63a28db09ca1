plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
}

apply from: "${project.rootDir}/config.gradle"

android {
    compileSdkVersion rootProject.linkCompileSdkVersion

    defaultConfig {
        applicationId "com.gn2023oversea.android.launcher"
        minSdkVersion rootProject.linkMinSdkVersion
        targetSdkVersion rootProject.linkTargetSdkVersion
        versionCode rootProject.linkVersionCode
        versionName rootProject.linkVersionName

        buildFeatures.viewBinding true

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    signingConfigs {
        a20 {
            storeFile file("../storeFile/a20/toyota.keystore")
            storePassword "android"
            keyAlias "androiddebugkey"
            keyPassword "android"
           /* storeFile file("../storeFile/a20/platform.keystore")
            storePassword "000000"
            keyAlias "desay"
            keyPassword "000000"*/
        }
        sibalu {
            storeFile file("../storeFile/sibalu/platform.keystore")
            storePassword "android"
            keyAlias "platform"
            keyPassword "android"
        }
    }

    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.a20
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.a20
        }
      /*  release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }*/
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    buildFeatures {
        viewBinding true
    }
}

dependencies {
//    implementation files('libs/dsv-extlink.jar')
//    implementation files('libs/EapHidl-ds04r-ds04rSign.aar');
    implementation fileTree(dir: 'libs', include: ['*.jar', '*.aar'])
    if (file("../linkHost").exists() && SDK_TEST != "true") {
        implementation project(path: ':linkHost')
    } else {
        implementation "com.autoai.avs.link:linkhost:$LINK_HOST_VERSION"
    }
    implementation "com.autoai.link.baselog:baselog:0.0.11"

    implementation "com.autoai.link.baselog:baselog:0.0.11"
    implementation 'androidx.core:core:1.7.0'

    implementation 'androidx.appcompat:appcompat:1.4.0'

    implementation 'com.ashokvarma.android:bottom-navigation-bar:1.4.1'
    implementation 'androidx.navigation:navigation-fragment:2.4.1'
    implementation 'androidx.navigation:navigation-ui:2.4.1'
    implementation 'com.google.android.material:material:1.4.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.1'
    //
    implementation "org.jetbrains.kotlin:kotlin-reflect:1.6.21"

    //
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'
}
task clearDemo {
    delete 'D:\\Desktop\\avslink'
}
task buildDemo(type: Copy) {
    from rootDir
    into 'D:\\Desktop\\avslink'
    println("copy start")
    exclude '.gradle'
    exclude 'build'
    exclude '.git'
    exclude '.idea'
    exclude 'linkCommon'
    exclude 'linkHost'
    exclude 'linkSdk'
    exclude 'localLibs'
    exclude 'wlhardware'
    exclude '.gitmodules'
    exclude 'logcat.txt'
    exclude 'maven_push.gradle'

}