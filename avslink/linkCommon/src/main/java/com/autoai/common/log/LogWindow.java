package com.autoai.common.log;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.PixelFormat;
import android.text.method.ScrollingMovementMethod;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.TextView;

import com.autoai.common.R;
import com.autoai.common.util.AppUtil;
import com.autoai.common.util.ThreadUtils;

/**
 * <AUTHOR>
 */
public class LogWindow {

    /**
     * <AUTHOR>
     */
    private static final class InstanceHolder {
        @SuppressLint("StaticFieldLeak")
        static final LogWindow INSTANCE = new LogWindow();
    }

    public static LogWindow getInstance() {
        return InstanceHolder.INSTANCE;
    }

    private TextView logView;

    private LogWindow() {
        ThreadUtils.postOnMainThread(new Runnable() {
            @Override
            public void run() {
                createFloatView(AppUtil.getContext());
                logView.setTag(this);
            }
        });
    }

    public void log(String message) {
        ThreadUtils.postOnMainThread(() -> logView.setText(String.valueOf(message)));
    }

    @SuppressLint("InflateParams")
    private void createFloatView(Context context) {
        WindowManager mWindowManager;
        mWindowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        WindowManager.LayoutParams wmParams;
        wmParams = new WindowManager.LayoutParams();
        wmParams.type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT;
        wmParams.format = PixelFormat.RGBA_8888;
        wmParams.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
        wmParams.gravity = Gravity.CENTER | Gravity.BOTTOM;
        wmParams.x = 0;
        wmParams.y = 0;
        wmParams.width = WindowManager.LayoutParams.MATCH_PARENT;
        wmParams.height = 60;
        LayoutInflater inflater = LayoutInflater.from(context);
        View floatView;
        floatView = inflater.inflate(R.layout.log_view, null);
        logView = floatView.findViewById(R.id.message);
        logView.setMovementMethod(ScrollingMovementMethod.getInstance());
        logView.setMarqueeRepeatLimit(6);
        mWindowManager.addView(floatView, wmParams);

    }

}
