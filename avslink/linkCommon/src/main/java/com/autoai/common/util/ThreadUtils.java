package com.autoai.common.util;

import android.os.Handler;
import android.os.Looper;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@SuppressWarnings("unused")
public class ThreadUtils {

    private static volatile Thread sMainThread;
    private static volatile Handler sMainThreadHandler;
    private static volatile ExecutorService backgroundThreadExecutor;

    private static volatile ExecutorService SingleThreadExecutor;

    /**
     * Returns true if the current thread is the UI thread.
     */
    public static boolean isMainThread() {
        if (sMainThread == null) {
            sMainThread = Looper.getMainLooper().getThread();
        }
        return Thread.currentThread() == sMainThread;
    }

    /**
     * Returns a shared UI thread handler.
     */
    public static Handler getUiThreadHandler() {
        if (sMainThreadHandler == null) {
            sMainThreadHandler = new Handler(Looper.getMainLooper());
        }

        return sMainThreadHandler;
    }

    /**
     * Checks that the current thread is the UI thread. Otherwise throws an exception.
     */
    public static void ensureMainThread() {
        if (!isMainThread()) {
            throw new RuntimeException("Must be called on the UI thread");
        }
    }

    /**
     * Posts runnable in background using shared background thread pool.
     */
    public static void postOnBackgroundThread(Runnable runnable) {
        if (backgroundThreadExecutor == null) {
            backgroundThreadExecutor = new ThreadPoolExecutor(8, 20, 1000, TimeUnit.MILLISECONDS, new SynchronousQueue<>(), new ThreadFactory() {
                private int threadCount;

                @Override
                public Thread newThread(Runnable r) {
                    return new Thread(r, "BackgroundThread" + threadCount++);
                }
            }, new ThreadPoolExecutor.DiscardPolicy());
        }
        backgroundThreadExecutor.execute(runnable);
    }

    /**
     * Posts the runnable on the main thread.
     */
    public static void postOnMainThread(Runnable runnable) {
        getUiThreadHandler().post(runnable);
    }

    /**
     * Posts the runnable on the main thread daily.
     */
    public static void postOnMainThread(Runnable runnable, long delayMillis) {
        getUiThreadHandler().postDelayed(runnable, delayMillis);
    }

    public static void postOnBackgroundSingleThread(Runnable runnable){
        if(SingleThreadExecutor == null){
            SingleThreadExecutor = Executors.newSingleThreadExecutor();
        }
        SingleThreadExecutor.execute(runnable);
    }
}
