package com.autoai.common.util;

import android.annotation.SuppressLint;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/**
 * <AUTHOR>
 */
public class ProcessUtil {

    private static final String PROCESS_SPLIT = ":";
    private static final String PROCESS_MAIN = "main";

    @SuppressLint({"PrivateApi", "DiscouragedPrivateApi"})
    public static String getCurrentProcessName() {

//1. 通过ActivityThread中的currentActivityThread()方法得到ActivityThread的实例对象

        Class<?> activityThreadClass;
        try {
            activityThreadClass = Class.forName("android.app.ActivityThread");

            Method method = activityThreadClass.getDeclaredMethod("currentActivityThread");

            Object activityThread = method.invoke(null);

//2. 通过activityThread的getProcessName() 方法获取进程名

            Method getProcessNameMethod = activityThreadClass.getMethod("getProcessName");

            Object processName = getProcessNameMethod.invoke(activityThread);

            if (processName != null) {
                return processName.toString();
            }
        } catch (ClassNotFoundException | NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            e.printStackTrace();
        }


        return null;
    }

    public static String getSimpleProcessName() {
        String name = getCurrentProcessName();
        if (name != null && name.contains(PROCESS_SPLIT)) {
            name = name.split(PROCESS_SPLIT)[1];
        } else {
            name = PROCESS_MAIN;
        }
        return name;
    }

    public static void printProcess(Class<?> c) {
        LogUtil.e(c.getSimpleName(), "process:" + ProcessUtil.getCurrentProcessName());
    }
}
