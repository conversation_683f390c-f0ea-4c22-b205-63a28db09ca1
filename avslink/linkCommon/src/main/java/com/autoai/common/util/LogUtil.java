package com.autoai.common.util;

import android.os.SystemClock;
import android.text.TextUtils;
import android.util.Log;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.Locale;
import java.util.Map;
import java.util.Queue;
import java.util.TimeZone;
import java.util.concurrent.atomic.AtomicBoolean;

public class LogUtil {
    public static final int VERBOSE = 1;
    public static final int DEBUG = 2;
    public static final int INFO = 3;
    public static final int WARN = 4;
    public static final int ERROR = 5;
    public static final int NOTHING = 6;

    private static final int THREAD_SLEEP_TIME = 1;         // Time of thread sleep.

    private static final String UNIQUE_IDENTIFIER = "[WL_LOG]"; // Custom prefix.

    // Whether to output log.
    private static boolean isDebug = false;
    // Whether to output full log.
    private static boolean isFullLog = false;
    // Default log lever.
    private static int logLevel = WARN;
    // Whether to output class name, function name and function name.
    private static boolean isFuncInfo = false;
    // File save path. If it is empty, log will be output directly.
    private static String logPath = null;

    // Mic recording data cache.
    private static final Queue<String> queue = new LinkedList<>();
    private static final AtomicBoolean isRunning = new AtomicBoolean(false);
    private static FileOutputStream fos = null;

    private LogUtil() {
    }


    /**
     * WL log system init.
     *
     * @param debug    Whether to output log.
     * @param level    Output log level.
     * @param funcInfo Whether to output Func info(class name, function name and line number).
     * @param path     File save path. If it is empty, log will be output directly.
     */
    public static synchronized void init(boolean debug, int level, boolean funcInfo, String path) {
        if (!isRunning.get()) {
            isRunning.compareAndSet(false, true);
            isDebug = debug;
            logLevel = level;
            isFuncInfo = funcInfo;
            logPath = path;

            if (null != logPath) {
                ThreadUtils.postOnBackgroundThread(LogUtil::saveLog);
            }
        } else {
            Log.w(UNIQUE_IDENTIFIER, UNIQUE_IDENTIFIER + "LogUtil already initialized!");
            com.tencent.mars.xlog.Log.w(UNIQUE_IDENTIFIER, UNIQUE_IDENTIFIER + "LogUtil already initialized!");
        }
    }

    /**
     * WL log system exit.
     */
    public static void exit() {
        isRunning.compareAndSet(true, false);
        if (null != fos) {
            try {
                fos.close();
                fos = null;
            } catch (IOException e) {
                Log.w(UNIQUE_IDENTIFIER, UNIQUE_IDENTIFIER + "an error occured while close file...", e);
                com.tencent.mars.xlog.Log.w(UNIQUE_IDENTIFIER, UNIQUE_IDENTIFIER + "an error occured while close file...", e);
            }
        }
    }

    /**
     * Send a {@link #VERBOSE} log message.
     *
     * @param tag Used to identify the source of a log message.  It usually identifies
     *            the class or activity where the log call occurs.
     * @param msg The message you would like logged.
     */
    public static void v(String tag, String msg) {
        if (isDebug) {
            if (logLevel <= VERBOSE) {
                // Add class name and line number to msg.
                StackTraceElement caller = getCallerStackTraceElement();
                String customTag = customTagPrefix(caller, tag);
                logOutput(customTag, msg, VERBOSE, null);
            }
        }
    }

    /**
     * Send a {@link #VERBOSE} log message and log the exception.
     *
     * @param tag       Used to identify the source of a log message.  It usually identifies
     *                  the class or activity where the log call occurs.
     * @param msg       The message you would like logged.
     * @param throwable An exception to log
     */
    public static void v(String tag, String msg, Throwable throwable) {
        if (isDebug) {
            if (logLevel <= VERBOSE) {
                // Add class name and line number to msg.
                StackTraceElement caller = getCallerStackTraceElement();
                String customTag = customTagPrefix(caller, tag);
                logOutput(customTag, msg, VERBOSE, throwable);
            }
        }
    }

    /**
     * Send a {@link #DEBUG} log message.
     *
     * @param tag Used to identify the source of a log message.  It usually identifies
     *            the class or activity where the log call occurs.
     * @param msg The message you would like logged.
     */
    public static void d(String tag, String msg) {
        if (isDebug) {
            if (logLevel <= DEBUG) {
                // Add class name and line number to msg.
                StackTraceElement caller = getCallerStackTraceElement();
                String customTag = customTagPrefix(caller, tag);
                logOutput(customTag, msg, DEBUG, null);
            }
        }

//        Log.i(tag,msg);
    }

    /**
     * Send a {@link #DEBUG} log message and log the exception.
     *
     * @param tag       Used to identify the source of a log message.  It usually identifies
     *                  the class or activity where the log call occurs.
     * @param msg       The message you would like logged.
     * @param throwable An exception to log
     */
    public static void d(String tag, String msg, Throwable throwable) {
        if (isDebug) {
            if (logLevel <= DEBUG) {
                // Add class name and line number to msg.
                StackTraceElement caller = getCallerStackTraceElement();
                String customTag = customTagPrefix(caller, tag);
                logOutput(customTag, msg, DEBUG, throwable);
            }
        }
    }

    /**
     * Send an {@link #INFO} log message.
     *
     * @param tag Used to identify the source of a log message.  It usually identifies
     *            the class or activity where the log call occurs.
     * @param msg The message you would like logged.
     */
    public static void i(String tag, String msg) {
        if (isDebug) {
            if (logLevel <= INFO) {
                // Add class name and line number to msg.
                StackTraceElement caller = getCallerStackTraceElement();
                String customTag = customTagPrefix(caller, tag);
                logOutput(customTag, msg, INFO, null);
            }
        }
    }

    /**
     * Send a {@link #INFO} log message and log the exception.
     *
     * @param tag       Used to identify the source of a log message.  It usually identifies
     *                  the class or activity where the log call occurs.
     * @param msg       The message you would like logged.
     * @param throwable An exception to log
     */
    public static void i(String tag, String msg, Throwable throwable) {
        if (isDebug) {
            if (logLevel <= INFO) {
                // Add class name and line number to msg.
                StackTraceElement caller = getCallerStackTraceElement();
                String customTag = customTagPrefix(caller, tag);
                logOutput(customTag, msg, INFO, throwable);
            }
        }
    }

    /**
     * Send a {@link #WARN} log message.
     *
     * @param tag Used to identify the source of a log message.  It usually identifies
     *            the class or activity where the log call occurs.
     * @param msg The message you would like logged.
     */
    public static void w(String tag, String msg) {
        if (isDebug) {
            if (logLevel <= WARN) {
                // Add class name and line number to msg.
                StackTraceElement caller = getCallerStackTraceElement();
                String customTag = customTagPrefix(caller, tag);
                logOutput(customTag, msg, WARN, null);
            }
        }

//        Log.w(tag,msg);
    }

    /**
     * Send a {@link #WARN} log message and log the exception.
     *
     * @param tag       Used to identify the source of a log message.  It usually identifies
     *                  the class or activity where the log call occurs.
     * @param msg       The message you would like logged.
     * @param throwable An exception to log
     */
    public static void w(String tag, String msg, Throwable throwable) {
        if (isDebug) {
            if (logLevel <= WARN) {
                // Add class name and line number to msg.
                StackTraceElement caller = getCallerStackTraceElement();
                String customTag = customTagPrefix(caller, tag);
                logOutput(customTag, msg, WARN, throwable);
            }
        }

//        Log.i(tag,msg);
    }

    /**
     * Send an {@link #ERROR} log message.
     *
     * @param tag Used to identify the source of a log message.  It usually identifies
     *            the class or activity where the log call occurs.
     * @param msg The message you would like logged.
     */
    public static void e(String tag, String msg) {
        if (isDebug) {
            if (logLevel <= ERROR) {
                // Add class name and line number to msg.
                StackTraceElement caller = getCallerStackTraceElement();
                String customTag = customTagPrefix(caller, tag);
                logOutput(customTag, msg, ERROR, null);
            }
        }
     //   Log.e(tag,msg);
    }

    /**
     * Send a {@link #ERROR} log message and log the exception.
     *
     * @param tag       Used to identify the source of a log message.  It usually identifies
     *                  the class or activity where the log call occurs.
     * @param msg       The message you would like logged.
     * @param throwable An exception to log
     */
    public static void e(String tag, String msg, Throwable throwable) {
        if (isDebug) {
            if (logLevel <= ERROR) {
                // Add class name and line number to msg.
                StackTraceElement caller = getCallerStackTraceElement();
                String customTag = customTagPrefix(caller, tag);
                logOutput(customTag, msg, ERROR, throwable);
            }
        }
    }

    /**
     * @return The debug state.
     */
    public static boolean getDebugStates() {
        return isDebug;
    }

    /**
     * 是否输出全部日志
     *
     * @return true 输出全部日志；false 输出简易log,过滤刷屏log
     */
    public static boolean isFullLog() {
        return isFullLog;
    }

    public static void setIsFullLog(boolean isFullLog) {
        LogUtil.isFullLog = isFullLog;
    }

    /**
     * @return The log level. eg.:LogUtil.VERBOSE/DEBUG/INFO/WARN/ERROR/NOTHING.
     */
    public static int getLogLevel() {
        return logLevel;
    }

    /**
     * @param customTag Custom tag.
     * @param msg       Log msg.
     * @param type      Log type: VERBOSE/DEBUG/INFO/WARN/ERROR.
     * @param throwable Throwable msg.
     */
    private static void logOutput(String customTag, String msg, int type, Throwable throwable) {
        if (null != logPath) {
            StringBuilder sb = new StringBuilder();
            sb.append(customTag);
            if (null != throwable) {
                msg = msg + "\n  " + throwable;
                sb.append(" ");
                sb.append(msg);

                // Add stack trace.
                StackTraceElement[] ste = throwable.getStackTrace();
                for (StackTraceElement aa : ste) {
                    sb.append("\n    ");
                    sb.append(aa.toString());
                }
            } else {
                sb.append(" ");
                sb.append(msg);
            }
            if (TextUtils.isEmpty(sb.toString())) {
                return;
            }
            queue.offer(sb.toString());
        } else {
            customTag = UNIQUE_IDENTIFIER + customTag;
            switch (type) {
                case VERBOSE:
                    Log.v(customTag, msg, throwable);
                    com.tencent.mars.xlog.Log.v(customTag, msg, throwable);
                    break;
                case DEBUG:
                    Log.d(customTag, msg, throwable);
                    com.tencent.mars.xlog.Log.d(customTag, msg, throwable);
                    break;
                case INFO:
                    Log.i(customTag, msg, throwable);
                    com.tencent.mars.xlog.Log.i(customTag, msg, throwable);
                    break;
                case WARN:
                    Log.w(customTag, msg, throwable);
                    com.tencent.mars.xlog.Log.w(customTag, msg, throwable);
                    break;
                case ERROR:
                    Log.e(customTag, msg, throwable);
                    com.tencent.mars.xlog.Log.e(customTag, msg, throwable);
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * @param caller Stack trace elements.
     * @param tag    Custom ag string.
     * @return The custom log string.
     */
    private static String customTagPrefix(StackTraceElement caller, String tag) {
        String customStr = null;
        if (null != caller) {
            String info = generateTag(caller);
            customStr = "[" + tag + "]" + info;
        }
        return customStr;
    }

    /**
     * @return An array of stack trace elements representing the stack dump
     * of this thread.
     */
    private static StackTraceElement getCallerStackTraceElement() {
        return Thread.currentThread().getStackTrace()[4];
    }

    /**
     * @param caller An array of stack trace elements representing the stack dump
     *               of this thread.
     * @return String containing class name, function name and line number.
     */
    private static String generateTag(StackTraceElement caller) {
        String tag = "";
        if ((null != caller) && isFuncInfo) {
            tag = "[%s.%s][Line:%d]";   // The formatter.
            String callerClazzName = caller.getClassName(); // Get class name.
            callerClazzName = callerClazzName.substring(callerClazzName.lastIndexOf(".") + 1);
            tag = String.format(Locale.CHINA, tag, callerClazzName, caller.getMethodName(), caller.getLineNumber());
        }
        return tag;
    }

    private static void saveLog() {
        while (isRunning.get()) {
            if (queue.size() > 0) {
                while (queue.size() > 0) {
                    String str = queue.poll();
                    saveLogInfo2File(str);
                }
            } else {
                try {
                    Thread.sleep(THREAD_SLEEP_TIME);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * @param msg Log msg to save.
     */
    private static void saveLogInfo2File(String msg) {
        // Save device information and exception information
        Map<String, String> info = new HashMap<>();

        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : info.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            //sb.append(key + "=" + value + "\n");
            sb.append(key);
            sb.append("=");
            sb.append(value);
            sb.append("\n");
        }

        sb.append(new Date());
        sb.append(":");
        sb.append(msg);
        sb.append("\n");
        try {
            if (null == fos) {
                String fileName = logPath.substring(logPath.lastIndexOf("/") + 1);
                String[] path = logPath.split(fileName);
                File dir = new File(path[0]);
                if (!dir.exists()) {
                    boolean mkdirs = dir.mkdirs();
                    if (!mkdirs) {
                        return;
                    }
                }
                fos = new FileOutputStream(logPath, true);
            }
            fos.write(sb.toString().getBytes());
        } catch (Exception e) {
            Log.w(UNIQUE_IDENTIFIER, UNIQUE_IDENTIFIER + "an error occured while writing file...", e);
            com.tencent.mars.xlog.Log.w(UNIQUE_IDENTIFIER, UNIQUE_IDENTIFIER + "an error occured while writing file...", e);
        }

        info.clear();
    }

    private static String getTimeString() {
        long millisecond = SystemClock.elapsedRealtime();
        SimpleDateFormat dateFormat = new SimpleDateFormat("HH:mm:ss:SSS", Locale.CHINA);
        dateFormat.setTimeZone(TimeZone.getTimeZone("GMT+00:00"));
        return "[" + dateFormat.format(new Date(millisecond)) + "]";
    }

    public static String getLogPath(String fileName) {
        return AppUtil.getContext().getExternalFilesDir("log") + "/" + ProcessUtil.getSimpleProcessName() + "/" + fileName;
    }

    public static void printStackTraceString(String tag, String msg) {
        String stackTraceString =
                Log.getStackTraceString(new Throwable(msg));
        Log.e(
                tag,
                " \n" +
                        "  调用栈： ==》 " + stackTraceString
        );
        com.tencent.mars.xlog.Log.e(tag,
                " \n" +
                        "  调用栈： ==》 " + stackTraceString);
    }
}