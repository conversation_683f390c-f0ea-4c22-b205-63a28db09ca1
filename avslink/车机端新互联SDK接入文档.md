# 车机端新互联SDK接入文档

### SDK版本

最新版本:

0.1.35 新增FPSUtils。

历史版本：

0.1.34 调整本地化协议。

0.1.33 解决EAP回到桌面黑屏问题。

0.1.32 新增三种外语适配。

0.1.31 修改IDBBUG，去掉语言适配。

0.1.30 新增三种外语适配。

0.1.29 修改EAP互联断开连接灰屏问题。

0.1.28 新增idb驱动互联方式。

0.1.27 修改横屏状态下，hid因整形溢出而导致触点不准确的BUG。

0.1.26 修改断线重连无法互联的BUG。

0.1.25 修改hid回控断线重连以后无效的BUG。

0.1.24 修改退出时销毁广播奔溃的BUG。

0.1.23 监听车机端语言变更，通知手机。

0.1.22 修改斯巴鲁车机视频花屏问题。

0.1.21 新增自定义消息解析接口。

0.1.20 增加车机端语言版本控制

0.1.19 修改拔线重连无法播放视频的BUG

0.1.18 适配Android12

0.1.17 新增车机平台定制SN过滤

0.1.16 修改发版BUG。

0.1.15 新增车机sn设置，手机端fps设置。

0.1.14 新增平台定制接口，音频定制接口。

0.1.13 重构代码，修改SDK回收的BUG。

0.1.12 调整EAP工作流程。

0.1.11 新增手机回控更新车机tab按钮状态。

0.1.10 解决SDK的json解析Boolean兼容性问题。

0.1.9 修复打包aar缺少依赖的问题。

0.1.8 添加AppUtil,提供全局context,重构代码。

0.1.7 支持带AOA驱动的初始化；优化日志系统。

0.1.6 支持带EAP驱动的初始化；新增common模块，内置日志工具类，线程工具类，进程工具类。

0.1.5 支持矩形视频流。

0.1.4 优化解码器。

0.1.3 基础版本

### 接入SDK

maven方式接入

1. project的build.gradle中添加

   ```
   buildscript {
       repositories {
           maven {
               url 'https://wdnexus.autoai.com/content/repositories/autoai-AVS/'
           }
   ...
       }
   }

   allprojects {
       repositories {
           maven {
               url 'https://wdnexus.autoai.com/content/repositories/autoai-AVS/'
           }
   ...
       }
   }
   ```

   ​

2. module的build.gradle中添加

   ```
   dependencies {
   ...
   	implementation 'com.autoai.avs.link:linkhost:TAG'
   ...
   }
   ```

   ​


### 配置日志系统

在Application的onCreate中配置日志开关。

e.g.

```
public class MyApplication extends Application {

    @Override
    public void onCreate() {
        super.onCreate();
        //logDebug:打开logcat日志输出。fileDebug:打开文件日志输出。
        LinkHost.applyDebug(this, true, false);
    }
}
```

可以使用com.autoai.common.util.LogUtil来进行日志输出。日志输出文件路径为"App路径下/file/log/"文件夹。支持跨进程日志输出。

###初始化SDK

在首页activity.onCreate中调用

```` LinkHost.init(this,serialNum, fps,aoaDevice,eapDevice,platform,audioPlayer)````

或

```
LinkHost.init(activity, new SdkConfig.Builder()
                .setEapDevice(new EapDevice())
                .setPlatform(linkPlatform)
                .setAudioPlayer(audioPlayer)
                .build());
```

初始化Sdk。参数列表：

serialNum：车机SN，默认值：123445

fps：手机端fps，默认值：30

aoaDevice：AOA驱动，为空，不支持AOA，默认值：

```
new AoaDevice() {
    @NonNull
    @Override
    public String getManufacturer() {
        return "Mapbar, Inc";
    }

    @NonNull
    @Override
    public String getModel() {
        return "WeLink";
    }

    @NonNull
    @Override
    public String getDescription() {
        return "WeLink";
    }

    @NonNull
    @Override
    public String getUri() {
        return "https://welink-rs.autoai.com/saic/ap32";
    }

    @NonNull
    @Override
    public String getSerial() {
        return "mySerial";
    }

    @NonNull
    @Override
    public String getVersion() {
        return "1.0";
    }
}
```

eapDevice：EAP驱动，为空，不支持EAP，默认值为空

linkPlatform：平台定制，为空，不支持平台定制，默认值为空

audioPlayer：音频定制，为空，不支持音频定制，默认值为空

e.g.

```
...
public class MainActivity extends AppCompatActivity {
    
...
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        LinkHost.init(context, "123445", 30, aoaDevice, eapDevice, platform, null);
    }
...

}

```

###（可选）实现接口

1. 在首页activity onCreate中调用```` LinkHost.registerUiResponder````设置页面跳转监听。

   e.g.

   ```
   ...
   public class MainActivity extends AppCompatActivity  implements UIResponder {
       private NavController mNavController;
   ...
       @Override
       protected void onCreate(@Nullable Bundle savedInstanceState) {
           super.onCreate(savedInstanceState);
           setContentView(R.layout.activity_main);
           mNavController = Navigation.findNavController(this, R.id.nav_host);
           LinkHost.init(this);
           LinkHost.registerUiResponder(this);

       }
   ...

   	@Override
       public void onUpdatePage(int page, Object para) {
           if (mNavController == null) {
               return;
           }

           switch (page) {
               case UIResponder.INITIAL_PAGE:
                   if (null == para) {
                       mNavController.popBackStack(R.id.initFragment, false);
                   }
                   break;
               case UIResponder.HELP_PAGE:
                   Bundle bundle = new Bundle();
                   if (null != para) {
                       bundle.putInt(MSG_KEY_TYPE, (int) para);
                   }
                   mNavController.navigate(R.id.helpFragment, bundle);
                   break;
               case UIResponder.CONNECTING_PAGE:
                   mNavController.navigate(R.id.connectingFragment);
                   break;
               case UIResponder.ERR_PAGE_SDK:
               case UIResponder.ERR_PAGE_CONNECT:
                   mNavController.navigate(R.id.connectErrorFragment);
                   break;
               case UIResponder.ERR_PAGE_HEART:
                   mNavController.navigate(R.id.heartbeatErrorFragment);
                   break;
               case UIResponder.RUNNING_PAGE:
                   mNavController.navigate(R.id.runningFragment);
                   break;
               default:
                   break;
           }
       }

   }

   ```

   ​

2. 在首页activity onCreate中调用````LinkHost.setHidRegionCallback```` 设置屏幕旋转的监听。

   e.g.

   ```
   ...
   public class MainActivity extends AppCompatActivity  implements UIResponder {
       private int phoneWidth, phoneHeight, phoneAngle, videoMode = -1;

   ...
       @Override
       protected void onCreate(@Nullable Bundle savedInstanceState) {
           super.onCreate(savedInstanceState);
           setContentView(R.layout.activity_main);
           mNavController = Navigation.findNavController(this, R.id.nav_host);
           LinkHost.init(this);
           LinkHost.registerUiResponder(this);
           LinkHost.setHidRegionCallback((width, height, angle, mode) -> {
               if (width != phoneWidth || height != phoneHeight || phoneAngle != angle || videoMode != mode) {
                   phoneWidth = width;
                   phoneHeight = height;
                   phoneAngle = angle;
                   videoMode = mode;

                   new Handler(Looper.getMainLooper()).post(this::fitSurface);
               }
           });
       }
       
       /**
        * 适配不同的视频尺寸模式
        *
        * videoMode 0 表示固定的方形视频, 1 手机实际的宽高比尺寸
        */
       private void fitSurface() {
           DisplayMetrics dm = getResources().getDisplayMetrics();
           float containerW, containerH;
           // 计算出当前播放器所需的屏幕宽高,
           if (!isFullScreen) {
               containerW = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP,
                       320, dm);
               containerH = dm.heightPixels - TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP,
                       20, dm);
           } else {
               containerW = dm.widthPixels;
               containerH = dm.heightPixels;
           }

           Log.d(TAG, "fitSurface, containerW: " + containerW + ", containerH: " + containerH);

           float base = Math.min(containerW / phoneWidth, containerH / phoneHeight);

           int videoW = (int) (phoneWidth * base);
           int videoH = (int) (phoneHeight * base);

           int txW;
           int txH;
           if (videoMode == 0) {
               // 方形的视频, 需要固定 SurfaceView 尺寸为方形, 通过 hid 传入的实际缩放 parent 以阻止黑色边缘渲染
               txW = txH = Math.max(videoH, videoW) / 2 * 2;
           } else {
               txW = Math.min(videoH, videoW) / 2 * 2;
               txH = Math.max(videoH, videoW) / 2 * 2;
           }

           TextureView tx = isFullScreen && !FULLSCREEN_SWITCH_INNER ? (TextureView) outerFullScreenView.getChildAt(0) : surfaceView;
           
           FrameLayout.LayoutParams lp = (FrameLayout.LayoutParams) tx.getLayoutParams();
           if (lp == null) {
               lp = new FrameLayout.LayoutParams(txW, txH);
           } else {
               lp.width = txW;
               lp.height = txH;
           }
           tx.setLayoutParams(lp);

           FrameLayout parent = isFullScreen && !FULLSCREEN_SWITCH_INNER ? outerFullScreenView : surfaceParent;

           ConstraintLayout.LayoutParams plp = (ConstraintLayout.LayoutParams) parent.getLayoutParams();
           if (plp == null) {
               plp = new ConstraintLayout.LayoutParams(videoW, videoH);
               plp.topToTop = 0;
               plp.bottomToBottom = 0;
               plp.leftToLeft = 0;
               plp.rightToRight = 0;
           } else {
               plp.width = videoW;
               plp.height = videoH;
           }
           Log.d(TAG, "fitSurface, videoW: " + videoW + ", videoH: " + videoH);
           parent.setLayoutParams(plp);
           if (videoMode == 1) {
               switch (phoneAngle) {
                   case Surface.ROTATION_90:
                       tx.setRotation(270);
                       break;
                   case Surface.ROTATION_270:
                       tx.setRotation(90);
                       break;
                   default:
                       tx.setRotation(0);
                       break;
               }
           }
           parent.invalidate();
           tx.invalidate();
       }
   ...
   }

   ```

   ​

3. 在首页activity onCreate中调用````LinkHost.addLinkStateChangeListener````

   添加状态改变监听。首页activity的onDestroy中调用````LinkHost.removeLinkStateChangeListener````移除状态改变监听。

   e.g.

   ```
   ...
   public class MainActivity extends AppCompatActivity  implements UIResponder,OnLinkStatusChangeListener {
       private int phoneWidth, phoneHeight, phoneAngle, videoMode = -1;

   ...
       @Override
       protected void onCreate(@Nullable Bundle savedInstanceState) {
           super.onCreate(savedInstanceState);
           setContentView(R.layout.activity_main);
           mNavController = Navigation.findNavController(this, R.id.nav_host);
           LinkHost.init(this);
           LinkHost.registerUiResponder(this);
           LinkHost.setHidRegionCallback((width, height, angle, mode) -> {
               if (width != phoneWidth || height != phoneHeight || phoneAngle != angle || videoMode != mode) {
                   phoneWidth = width;
                   phoneHeight = height;
                   phoneAngle = angle;
                   videoMode = mode;

                   new Handler(Looper.getMainLooper()).post(this::fitSurface);
               }
           });
       	LinkHost.addLinkStateChangeListener(this);
       }
       
       @Override
       public void onChange(int status, int type) {
           mHandler.post(() -> {
               switch (status) {
                   case CommonData.LINK_STATUS_NONE:
                   ...
                       break;
                   case CommonData.LINK_STATUS_CONNECTING:
                   ...
                       break;
                   case CommonData.LINK_STATUS_CONNECTED:
                   ...
                       break;
                   case CommonData.LINK_STATUS_FAIL:
                   ...
                       break;
               }
           });
       }
       
        @Override
       public void onDestroy() {
           super.onDestroy();
           LinkHost.removeLinkStateChangeListener(this);
       }
   ...
   }

   ```

   ​

4. 首页activity的onResume中调用````LinkHost.addMessageListener(this)````设置消息监听。首页activity的onPause中调用````LinkHost.removeMessageListener(this)````移除消息监听。MessageEvent.getWhat()为事件类型。

   e.g.

   ```
   ...
   public class MainActivity extends AppCompatActivity  implements UIResponder,OnLinkStatusChangeListener,OnMessageListener {

   ...
      
    	@Override
       protected void onResume() {
           super.onResume();
           LinkHost.addMessageListener(this);
       }
       
       @Override
       protected void onPause() {
           super.onPause();
           LinkHost.removeMessageListener(this);
       }
    
   ...
   }

   ```

   ​

###请求权限

1. 在首页activity onCreate中调用````PermissionUtils.requestMultiPermissions(this, mPermissionGrant);```` 

e.g.

```
...
public class MainActivity extends AppCompatActivity  implements UIResponder,OnLinkStatusChangeListener {
    private int phoneWidth, phoneHeight, phoneAngle, videoMode = -1;

...
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        mNavController = Navigation.findNavController(this, R.id.nav_host);
        LinkHost.init(this);
        LinkHost.registerUiResponder(this);
        LinkHost.setHidRegionCallback((width, height, angle, mode) -> {
            if (width != phoneWidth || height != phoneHeight || phoneAngle != angle || videoMode != mode) {
                phoneWidth = width;
                phoneHeight = height;
                phoneAngle = angle;
                videoMode = mode;

                new Handler(Looper.getMainLooper()).post(this::fitSurface);
            }
        });
    	LinkHost.addLinkStateChangeListener(this);
    	PermissionUtils.requestMultiPermissions(this, mPermissionGrant);
    }
    
     /**
     * 获取权限提示
     */
    private final PermissionUtils.PermissionGrant mPermissionGrant = requestCode -> {
        switch (requestCode) {
            case PermissionUtils.CODE_RECORD_AUDIO:
                break;
            case PermissionUtils.CODE_ACCESS_FINE_LOCATION:
                break;
            case PermissionUtils.CODE_READ_PHONE_STATE:
                break;
            default:
                break;
        }
    };
    
...
}

```



###设置Link画面容器

1. 调用````LinkHost.registerLinkSurface````设置Link画面容器。

2. 调用````LinkHost.unRegisterLinkSurface````取消设置Link画面容器。

   e.g.

   ```
   ...
   public class MainActivity extends AppCompatActivity  implements UIResponder,OnLinkStatusChangeListener,OnMessageListener {
   	/**
        * 当前正在播放的 Surface
        */
       private Surface currSurface = null;
       private TextureView surfaceView;
       private FrameLayout surfaceParent;
       private FrameLayout outerFullScreenView;
       private ViewGroup rootView;
   ...

   	public void switchFullScreen(boolean isfull) {
           isFullScreen = isfull;

           if (!FULLSCREEN_SWITCH_INNER) {
               if (isFullScreen) {
                   outerFullScreenView = new FrameLayout(this);
                   outerFullScreenView.setBackgroundColor(0x0fff0000);
                   TextureView tv = new TextureView(this);
                   outerFullScreenView.addView(tv, new FrameLayout.LayoutParams(-1, -1, Gravity.CENTER));

                   ConstraintLayout.LayoutParams plp = new ConstraintLayout.LayoutParams(-1, -1);
                   plp.topToTop = 0;
                   plp.bottomToBottom = 0;
                   plp.leftToLeft = 0;
                   plp.rightToRight = 0;
                   rootView.addView(outerFullScreenView, 1, plp);
                   resetSurface(tv);
               } else {
                   rootView.removeView(outerFullScreenView);
                   resetSurface(surfaceView);
               }
           }
           fitSurface();
       }
      
    	@Override
       protected void onResume() {
           super.onResume();
           resetSurface(surfaceView);
           LinkHost.addMessageListener(this);       
       }
       
    	/**
        * 切换 Surface
        */
       private void resetSurface(TextureView surfaceView) {
           if (currSurface != null) {
               LinkHost.unRegisterLinkSurface(currSurface);
           }
           Consumer<SurfaceTexture> consumer = v -> {
               currSurface = new Surface(surfaceView.getSurfaceTexture());
               LinkHost.registerLinkSurface(currSurface, surfaceView.getWidth(), surfaceView.getHeight());
           };

           if (surfaceView.getSurfaceTexture() != null) {
               consumer.accept(surfaceView.getSurfaceTexture());
           } else {
               surfaceView.setSurfaceTextureListener(new TextureView.SurfaceTextureListener() {
                   @Override
                   public void onSurfaceTextureAvailable(SurfaceTexture surface, int width, int height) {
                       consumer.accept(surface);
                   }

                   @Override
                   public void onSurfaceTextureSizeChanged(SurfaceTexture surface, int width, int height) {

                   }

                   @Override
                   public boolean onSurfaceTextureDestroyed(SurfaceTexture surface) {
                       if (currSurface != null) {
                           LinkHost.unRegisterLinkSurface(currSurface);
                       }
                       return true;
                   }

                   @Override
                   public void onSurfaceTextureUpdated(SurfaceTexture surface) {
                   }
               });
           }
       }
    
   ...
   }

   ```

   ​

###处理SDK生命周期

1. 在首页activity的onResume中调用LinkHost.readyForLink(this, false)启动自动互联。

2. 在首页activity的onPause中调用LinkHost.stopForLink(this, false)停止自动互联。

   e.g.

   ```
   ...
   public class MainActivity extends AppCompatActivity  implements UIResponder,OnLinkStatusChangeListener,OnMessageListener {

   ...
      
    	@Override
       protected void onResume() {
           super.onResume();
           resetSurface(surfaceView);
           LinkHost.addMessageListener(this);       
           LinkHost.readyForLink(this, false);
       }
       
       @Override
       protected void onPause() {
           super.onPause();
           LinkHost.removeMessageListener(this);
           LinkHost.stopForLink(this, false);
       }
    
   ...
   }

   ```

   ​

###（可选）释放资源。

1. 在首页activity的onDestroy中调用LinkHost.destroy()释放资源。该方法在程序即将退出时调用。

   e.g.

   ```
   ...
   public class MainActivity extends AppCompatActivity  implements UIResponder,OnLinkStatusChangeListener {
   ...
       
        @Override
       public void onDestroy() {
           super.onDestroy();
           LinkHost.removeLinkStateChangeListener(this);   
           LinkHost.destroy();
       }
   ...
   }

   ```

###（可选）处理点击事件

1. 调用````LinkHost.sendButton(ButtonType.xxx, 0)```` 发送点击事件。

   e.g.

   ```
   ...
   public class HeartbeatErrorFragment extends Fragment implements View.OnClickListener {
   ...
       @Override
       public void onClick(View view) {
           int id = view.getId();
           LogUtil.i(TAG, "onClick, id = " + id);
           if (id == R.id.iv_heartbeat_error_close) {
               FragmentActivity fragmentActivity = getActivity();
               if (null != fragmentActivity) {
                   getActivity().moveTaskToBack(true);
               }
               LinkHost.sendButton(ButtonType.GOT_IT, 0);
           } else if (id == R.id.tv_heartbeat_err_click_here) {
               LinkHost.sendButton(ButtonType.CLICK_FOR_HELP, 0);
           }
       }
   }

   ```

### 

###处理触摸事件

1. 调用````LinkHost.screenTouch(motionEvent)```` 发送屏幕触摸事件。

   e.g.

   ```
   ...

   public class RunningFragment extends Fragment implements OnMessageListener {
       private FragmentRunningBinding mBinding;
   ...
       @Nullable
       @Override
       public View onCreateView(@NonNull LayoutInflater inflater,
                                @Nullable ViewGroup container,
                                @Nullable Bundle savedInstanceState) {
           mBinding = DataBindingUtil.inflate(inflater,
                   R.layout.fragment_running,
                   container,
                   false);
           setTouchEvent();
           ...
           return mBinding.getRoot();
       }
   ...

       private void setTouchEvent() {
           mBinding.touchView.setOnTouchListener((view, motionEvent) -> {
               LinkHost.screenTouch(motionEvent);
               if (motionEvent.getAction() == MotionEvent.ACTION_UP) {
                   view.performClick();
               }
               return true;
           });
       }
   }
   ```

### 处理平台定制（可选）

1. 创建LinkPlatform的实现类

   ```
   package com.autoai.link.testapp.platform;

   import android.content.ComponentName;
   import android.content.Intent;
   import android.os.Bundle;
   import android.os.RemoteException;
   import android.util.Log;

   import com.autoai.common.util.AppUtil;
   import com.autoai.common.util.LogUtil;
   import com.autoai.welinkapp.checkconnect.CommonData;
   import com.autoai.welinkapp.model.PlatformAdaptor;
   import com.autoai.welinkapp.platform.LinkPlatform;
   import com.desaysv.ivi.platformadapter.app.phonelink.ISvPhoneLinkStateDevice;
   import com.desaysv.ivi.platformadapter.app.phonelink.SvPhoneLinkCommonType;
   import com.desaysv.ivi.platformadapter.app.phonelink.SvPhoneLinkDeviceType;
   import com.desaysv.ivi.platformadapter.app.phonelink.SvPhoneLinkStateManager;
   import com.desaysv.ivi.platformadapter.app.rvc.SvRvcManager;

   import org.json.JSONException;
   import org.json.JSONObject;

   public class DesayLinkPlatform implements LinkPlatform {
       public static final String TAG = DesayLinkPlatform.class.getSimpleName();

       private static SvPhoneLinkStateManager mPhoneLinkStateManager;
       private int connectState = SvPhoneLinkCommonType.LinkConnectStatus.LINK_NORMAL_DISCONNECTED;
       private int deviceType = SvPhoneLinkDeviceType.LinkDeviceType.DEVICE_TYPE_NONE;

       private static final String WELINK_PACKAGE_NAME = "com.autoai.link.testapp";
       private static final String WELINK_ACTIVITY_NAME = "com.autoai.link.testapp.MainActivity";

       ISvPhoneLinkStateDevice.Stub mPhoneLinkBinder = new ISvPhoneLinkStateDevice.Stub() {

           @Override
           public Bundle getState(String s) throws RemoteException {
               return getPhonelinkInfo(s);
           }

           @Override
           public boolean setCtlAction(String s, Bundle bundle) throws RemoteException {
               return setCarPlayCtlAction(s, bundle);
           }
       };

       private PlatformAdaptor adaptor;

       public PlatformAdaptor getAdaptor() {
           return adaptor;
       }

       @Override
       public void init(PlatformAdaptor adaptor) {
           this.adaptor = adaptor;
           mPhoneLinkStateManager = SvPhoneLinkStateManager.get(AppUtil.getContext());
           mPhoneLinkStateManager.registerPhoneLinkToService(SvPhoneLinkStateManager.PHONE_LINK_TYPE_EXTERNAL_LINK, mPhoneLinkBinder);
           final SvVrOthersManager.OthersTool mOtherTool = new SvVrRunnable();
           SvVrConfigManager.getInstance().initSvVr(AppUtil.getContext(), new InitListener() {

               @Override
               public void onConnectedToRemote() {
                   SvVrOthersManager.getInstance().init();
                   SvVrOthersManager.getInstance().setOthersTool(mOtherTool);
                   Log.i(TAG, "onConnectedToRemote---");
               }

               @Override
               public void onDisconnectedToRemote() {
                   Log.i(TAG, "onDisconnectedToRemote---");
                   SvVrOthersManager.getInstance().releaseOthersTool(mOtherTool);
               }
           });

           SvRvcManager.RvcStatusListener rvcStatusListener = new SvRvcManager.RvcStatusListener() {
               @Override
               public void onRvcStatusChange(int state) {
                   LogUtil.i(TAG, "onRvcStatusChange(BACKING_UP):" + state);
                   switch (state) {
                       case PlatformAdaptor.BACKING_UP_STATE.START:
                           adaptor.startBackingUp();
                           break;
                       case PlatformAdaptor.BACKING_UP_STATE.END:
                           adaptor.endBackingUp();
                           break;
                       default:
                           break;
                   }
               }
           };
           SvRvcManager.getInstance().setRvcStatusListener(rvcStatusListener);
       }

       @Override
       public void noticeMediaState(int state) {
           LogUtil.d(TAG, "Send Media State = " + state);
           Bundle bundle = new Bundle();
           bundle.putInt(SvPhoneLinkStateManager.PHONELINK_Status_MediaPlay, state);
           mPhoneLinkStateManager.noticeChanged(SvPhoneLinkStateManager.PHONE_LINK_TYPE_EXTERNAL_LINK,
                   SvPhoneLinkStateManager.PHONELINK_KEY_MEDIA_STATUS, bundle);
       }

       @Override
       public void noticeAppState(boolean bShow) {
           LogUtil.d(TAG, "Send App State = " + bShow);
           Bundle bundle = new Bundle();
           bundle.putBoolean(SvPhoneLinkStateManager.PHONELINK_STATUS_SCREEN_SHOW, bShow);
           mPhoneLinkStateManager.noticeChanged(SvPhoneLinkStateManager.PHONE_LINK_TYPE_EXTERNAL_LINK,
                   SvPhoneLinkStateManager.PHONELINK_KEY_SCREEN_SHOW_STATUS, bundle);
       }

       @Override
       public void noticeConnectState(int state) {
           LogUtil.d(TAG, "Send Connect State = " + state);
           this.connectState = transform_connect_state(state);
           Bundle bundle = new Bundle();
           bundle.putInt(SvPhoneLinkStateManager.PHONELINK_ConnectStatus, this.connectState);
           mPhoneLinkStateManager.noticeChanged(SvPhoneLinkStateManager.PHONE_LINK_TYPE_EXTERNAL_LINK,
                   SvPhoneLinkStateManager.PHONELINK_KEY_CONNECT, bundle);
       }

       @Override
       public void noticeDeviceType(int type, int state) {
           LogUtil.d(TAG, "Send Device = " + type + " State = " + state);
           this.deviceType = transformDeviceType(type);
           Bundle bundle = new Bundle();
           bundle.putInt(SvPhoneLinkStateManager.PHONELINK_DEVICESTYPE, this.deviceType);
           bundle.putInt(SvPhoneLinkStateManager.PHONELINK_DEVICESTATUS, state);

           mPhoneLinkStateManager.noticeChanged(SvPhoneLinkStateManager.PHONE_LINK_TYPE_EXTERNAL_LINK,
                   SvPhoneLinkStateManager.PHONELINK_KEY_DEVICE_INFO, bundle);
       }

       @Override
       public void noticeTrackInfo(Object o) {
           ID3 id3 = (ID3) o;
           String singerName = id3.singerName;
           String songName = id3.songName;
           String albumName = id3.albumName;
           LogUtil.d(TAG, "Send TrackInfo SingerName = " + singerName);
           LogUtil.d(TAG, "Send TrackInfo SongName = " + songName);
           LogUtil.d(TAG, "Send TrackInfo albumName = " + albumName);
           Bundle bundle = new Bundle();
           bundle.putString(SvPhoneLinkStateManager.PHONELINK_TrackArtID3_strTitle, songName);
           bundle.putString(SvPhoneLinkStateManager.PHONELINK_TrackArtID3_strArtist, singerName);
           bundle.putString(SvPhoneLinkStateManager.PHONELINK_TrackArtID3_strAlbum, albumName);

           mPhoneLinkStateManager.noticeChanged(SvPhoneLinkStateManager.PHONE_LINK_TYPE_EXTERNAL_LINK,
                   SvPhoneLinkStateManager.PHONELINK_KEY_MEDIA_TRACK_INFO, bundle);
       }

       @Override
       public void noticeNaviInfo(String segInfo, String totalDistance) {
           LogUtil.d(TAG, "Send NaviInfo segInfo = " + segInfo);
           LogUtil.d(TAG, "Send NaviInfo totalDistance = " + totalDistance);
           Bundle bundle = new Bundle();
           bundle.putString(SvPhoneLinkStateManager.PHONELINK_ROUTEGUIDANCE_SEG_REMAIN_DIS, segInfo);
           bundle.putString(SvPhoneLinkStateManager.PHONELINK_ROUTEGUIDANCE_TOTAL_DISTANCE, totalDistance);

           mPhoneLinkStateManager.noticeChanged(SvPhoneLinkStateManager.PHONE_LINK_TYPE_EXTERNAL_LINK,
                   SvPhoneLinkStateManager.PHONELINK_KEY_ROUTE_GUIDANCE, bundle);
       }

       @Override
       public void noticeNaviState(int state) {
           LogUtil.d(TAG, "Send Navi State = " + state);
           int naviState = transformNaviState(state);
           Bundle bundle = new Bundle();
           bundle.putInt(SvPhoneLinkStateManager.PHONELINK_Status_Navi, naviState);
           mPhoneLinkStateManager.noticeChanged(SvPhoneLinkStateManager.PHONE_LINK_TYPE_EXTERNAL_LINK,
                   SvPhoneLinkStateManager.PHONELINK_KEY_NAVI, bundle);
       }

       private Bundle getPhonelinkInfo(String key) {
           Bundle bundle = new Bundle();

           if (null == key) {
               LogUtil.e(TAG, "getPhonelinkInfo key is null ");
               return bundle;
           }

           LogUtil.d(TAG, "getPhonelinkInfo key = " + key);
           switch (key) {
               case SvPhoneLinkStateManager.PHONELINK_KEY_SCREEN_SHOW_STATUS:
                   bundle.putBoolean(SvPhoneLinkStateManager.PHONELINK_STATUS_SCREEN_SHOW, adaptor.isShowState());
                   break;
               case SvPhoneLinkStateManager.PHONELINK_KEY_CONNECT:
                   bundle.putInt(SvPhoneLinkStateManager.PHONELINK_ConnectStatus, this.connectState);
                   break;
               case SvPhoneLinkStateManager.PHONELINK_KEY_DEVICE_INFO:
                   bundle.putInt(SvPhoneLinkStateManager.PHONELINK_DEVICESTYPE, this.deviceType);
                   break;
               case SvPhoneLinkStateManager.PHONELINK_KEY_MEDIA_TRACK_INFO:
                   adaptor.sendHuReqAppData(1);
                   break;
               case SvPhoneLinkStateManager.PHONELINK_KEY_MEDIA_STATUS:
                   adaptor.sendHuReqAppData(2);
                   break;
               case SvPhoneLinkStateManager.PHONELINK_KEY_ROUTE_GUIDANCE:
                   adaptor.sendHuReqAppData(3);
                   break;
               case SvPhoneLinkStateManager.PHONELINK_KEY_NAVI:
                   adaptor.sendHuReqAppData(4);
                   break;
               default:
                   break;
           }

           return bundle;
       }

       private boolean setCarPlayCtlAction(String key, Bundle bundle) {
           boolean bRet = true;
           if (key.equals(SvPhoneLinkStateManager.PHONELINK_KEY_MEDIA_OP_CTRL)) {
               String option = bundle.getString(SvPhoneLinkStateManager.PHONELINK_MEDIA_OP);
               String keyEvent = bundle.getString(SvPhoneLinkStateManager.PHONELINK_VKEY_EVENT);
               if (option == null || keyEvent == null) {
                   return false;
               }
               if (keyEvent.equals(SvPhoneLinkStateManager.PHONELINK_VKEY_RELEASE)) {
                   switch (option) {
                       case SvPhoneLinkStateManager.PHONELINK_MEDIA_OP_PLAY: // play
                           LogUtil.d(TAG, "Get OP Play ---");
                           adaptor.musicPlay();
                           break;
                       case SvPhoneLinkStateManager.PHONELINK_MEDIA_OP_PAUSE: // pause
                           LogUtil.d(TAG, "Get OP Pause ---");
                           adaptor.musicStop();
                           break;
                       case SvPhoneLinkStateManager.PHONELINK_MEDIA_OP_NEXT: // next
                           LogUtil.d(TAG, "Get OP Next ---");
                           adaptor.shortKeyUpNext();
                           break;
                       case SvPhoneLinkStateManager.PHONELINK_MEDIA_OP_PREVIOUS: // previous
                           LogUtil.d(TAG, "Get OP Previous ---");
                           adaptor.shortkeyUpPre();
                           break;
                       default:
                           break;
                   }
               }
           } else if (key.equals(SvPhoneLinkStateManager.PHONELINK_KEY_LAUNCH_LINK_UI)) {
               LogUtil.d(TAG, "Get Show Req ---");
               String url = bundle.getString(SvPhoneLinkStateManager.PHONELINK_KEY_LAUNCH_LINK_UI);

               if (url == null) {
                   return false;
               }
               showWelink();
           }
           return bRet;
       }

       public class SvVrRunnable implements SvVrOthersManager.OthersTool {

           @Override
           public int onRequest(String s, String s1) {
               String vrState = "";
               Log.d(TAG, "onRequest: s = " + s + "  s1 = " + s1);

               if ("notifyAsrStatus".equals(s)) {
                   try {
                       JSONObject json = new JSONObject(s1);
                       JSONObject command = json.getJSONObject("semantic");
                       vrState = command.getString("value");
                   } catch (JSONException e) {
                       e.printStackTrace();
                   }

                   if ("START".equals(vrState)) {
                       //mSdkViewModel.getSdkApi().sendMsg(WL_API_APP_MSG_TYPE.HU_LOCAL_VR_STATE, 1);
                       adaptor.NativeVrStart();
                       Log.i(TAG, "Native VR Start");
                   } else if ("END".equals(vrState)) {
                       adaptor.NativeVrEnd();
                       //mSdkViewModel.getSdkApi().sendMsg(WL_API_APP_MSG_TYPE.HU_LOCAL_VR_STATE, 2);
                       Log.i(TAG, "Native VR Stop");
                   }
               }

               return 0;
           }
       }

       private int transform_connect_state(int connectState) {
           int state = -1;

           if (PlatformAdaptor.CONNECT_STATE.DISCONNECT == connectState) {
               state = SvPhoneLinkCommonType.LinkConnectStatus.LINK_NORMAL_DISCONNECTED;
           } else if (PlatformAdaptor.CONNECT_STATE.CONNECTED == connectState) {
               state = SvPhoneLinkCommonType.LinkConnectStatus.LINK_CONNECTED;
           } else if (PlatformAdaptor.CONNECT_STATE.CONNECTING == connectState) {
               state = SvPhoneLinkCommonType.LinkConnectStatus.LINK_CONNECTING;
           } else if (PlatformAdaptor.CONNECT_STATE.CONNECTERR == connectState) {
               state = SvPhoneLinkCommonType.LinkConnectStatus.LINK_CONNECTFAIL;
           }

           return state;
       }

       private int transformDeviceType(int type) {
           int deviceType = -1;

           if (CommonData.CONNECT_STATUS_OUT == type) {
               deviceType = SvPhoneLinkDeviceType.LinkDeviceType.DEVICE_TYPE_NONE;
           } else if (CommonData.DEVICE_TYPE_AOA == type) {
               deviceType = SvPhoneLinkDeviceType.LinkDeviceType.DEVICE_TYPE_ANDROID_EXTERNAL_LINK;
           } else if (CommonData.DEVICE_TYPE_EAP == type) {
               deviceType = SvPhoneLinkDeviceType.LinkDeviceType.DEVICE_TYPE_EXTERNAL_LINK;
           } else if ((CommonData.DEVICE_TYPE_WIFI_IOS == type) || (CommonData.DEVICE_TYPE_WIFI_ANDROID == type)) {
               deviceType = SvPhoneLinkDeviceType.LinkDeviceType.DEVICE_TYPE_WIRELESS_EXTERNAL_LINK;
           }

           return deviceType;
       }

       private int transformNaviState(int state) {
           int naviState = -1;

           if (1 == state) {
               naviState = SvPhoneLinkCommonType.LinkNaviStatus.LINK_NAVI_START;
           } else if (2 == state) {
               naviState = SvPhoneLinkCommonType.LinkNaviStatus.LINK_NAVI_STOP;
           }

           return naviState;
       }

       @Override
       public void showWelink() {
           LogUtil.d(TAG, "show welink -----------");
           Intent intent = new Intent();
           ComponentName cName = new ComponentName(WELINK_PACKAGE_NAME, WELINK_ACTIVITY_NAME);
           intent.setComponent(cName);
           intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
           AppUtil.getContext().startActivity(intent);
       }

   }
   ```

2. 使用带linkPlatform参数的初始化方法进行初始化。

   ```
   LinkHost.init(activity, new AoaDevice(), new EapDevice(), new DesayLinkPlatform());
   ```


3. 处理定制平台的其他工作

   ```
   public boolean registerKeyInfo(Context contex) {
           boolean ret = false;
           if (null == mKeyPolicyManager) {
               mKeyPolicyManager = SvKeyPolicyManager.get(contex);
               mKeyPolicyManager.registerKeyCallBack(mKeyCallBackListener, contex.getClass().getName(), contex.getPackageName());
               ret = true;
           }
           return ret;
       }

       public void removeKeyInfo(Context contex) {
           if (null != mKeyPolicyManager) {
               mKeyPolicyManager.unRegisterKeyCallBack(mKeyCallBackListener, contex.getClass().getName(), contex.getPackageName());
               mKeyPolicyManager = null;
           }
       }

       private final SvKeyPolicyManager.OnKeyCallBackListener mKeyCallBackListener = new SvKeyPolicyManager.OnKeyCallBackListener() {
           @Override
           public void onKeyEventCallBack(KeyEvent keyEvent) {

               if (keyEvent.getAction() == KeyEvent.ACTION_DOWN) {
                   LogUtil.d(TAG, "KeyCode Down----------: \n" + keyEvent.getKeyCode());
                   LogUtil.d(TAG, "KeyCode Down:" + keyEvent.getKeyCode());
                   if (keyEvent.getKeyCode() == KEYCODE_VR) {
                       if (keyEvent.isLongPress()) {
                           downTime = keyEvent.getDownTime();
                           LogUtil.d(TAG, "Long press=>KEYCODE_VOICE_ASSIST!");
                           platform.getAdaptor().LongKeyPressVrHandle();
                       }
                   }
               } else if (keyEvent.getAction() == KeyEvent.ACTION_UP) {
                   LogUtil.d(TAG, "KeyCode Up-------:  \n" + keyEvent.getKeyCode());
                   if (keyEvent.getKeyCode() == KEYCODE_VR) {
                       if (downTime != keyEvent.getDownTime()) {
                           platform.getAdaptor().keyPressVrHandle();
                       }
                   } else if (keyEvent.getKeyCode() == KEYCODE_PRE) {
                       if (1 == LinkHost.getMusicAudioFoucs()) {
                           platform.getAdaptor().shortkeyUpPre();
                       }
                   } else if (keyEvent.getKeyCode() == KEYCODE_NEXT) {
                       if (1 == LinkHost.getMusicAudioFoucs()) {
                           platform.getAdaptor().shortKeyUpNext();
                       }
                   }
               }

           }
       };
   ```

### 处理音频定制（可选）

1. 创建AudioPlayer的实现类

   ```
   package com.autoai.link.testapp.audio;

   import android.content.Context;
   import android.media.AudioFormat;

   import com.autoai.common.util.LogUtil;
   import com.autoai.welinkapp.audio.AudioPlayer;

   public class AudioPlayerImpl implements AudioPlayer {
       private static final int SAMPLE_RATE = 16000;
       private static final int CHANNEL_IN = AudioFormat.CHANNEL_IN_MONO;  // CHANNEL_IN_MONO / CHANNEL_IN_STEREO
       private static final int ENCODING_PCM = AudioFormat.ENCODING_PCM_16BIT;
       private static final int PCM_BUFFER_SIZE = 8192;    //MONO:8192(8k), STEREO:16384(16k)

       private static final String TAG = AudioPlayerImpl.class.getSimpleName();
       private AudioAnalyze audioManager;

       @Override
       public void init() {
           MicRecord.getInstance().wl_audio_mic_set_arguments(SAMPLE_RATE, CHANNEL_IN, ENCODING_PCM, PCM_BUFFER_SIZE);
           audioManager = new AudioAnalyze();
       }

       @Override
       public void onCreate(Context mApplication) {
           audioManager.wlAudioInit(mApplication);
       }

       @Override
       public int getMusicAudioFoucs() {
           return audioManager.getMusicAudioFoucs();
       }

       @Override
       public void onAudioData(byte[] bytes, int i) {
           audioManager.wl_audio_input_data(bytes, i);
       }

       @Override
       public void pcmStopPlay(int type) {
           if (type == 0) {
               audioManager.audioReset(AudioAssist.AUDIO_STREAM_TTS);
           } else if (type == 1) {
               audioManager.audioReset(AudioAssist.AUDIO_STREAM_MUSIC);
           } else if (type == 2) {
               audioManager.audioReset(AudioAssist.AUDIO_STREAM_TTS);
           } else if (type == 3) {
               audioManager.audioReset(AudioAssist.AUDIO_STREAM_TTS);
           } else if (type == 4) {
               audioManager.audioReset(AudioAssist.AUDIO_STREAM_VR);
           } else if (type == 5) {
               audioManager.audioReset(AudioAssist.AUDIO_STREAM_TTS);
           }
           LogUtil.i(TAG, "PCM_STOP_PLAY type = " + type);
       }

       @Override
       public void muteStop() {
           MicRecord.getInstance().wl_audio_mic_stop_record();
           audioManager.audioSetVREnd();
       }

       @Override
       public void audioSetVRStart() {
           audioManager.audioSetVRStart();
       }

       @Override
       public boolean muteStart() {
           return MicRecord.MIC_RECORD_RET_SUCCESS == MicRecord.getInstance().wl_audio_mic_start_record();
       }

       @Override
       public void audioStop() {
           MicRecord.getInstance().wl_audio_mic_stop_record();
           audioManager.audioStop(AudioAssist.AUDIO_STREAM_ALL);
       }

       @Override
       public int getMusicState() {
           return audioManager.getMusicState();
       }

       @Override
       public void audioResume() {
           audioManager.audioResume();
       }
   }

   ```

   ​

2. 使用带AudioPlayer参数的方法初始化SDK

   ```
   LinkHost.init(activity, new AoaDevice(), new EapDevice(), new DesayLinkPlatform(), new AudioPlayerImpl());
   ```

3. 处理音频

   * 获取音频焦点

     ```
     LinkHost.getMusicAudioFoucs();
     ```

   * 发送音频数据

     ```
     LinkHost.sendAudioReplyData(0, byteProtocol, byteProtocol.length);
     ```

   * 发送Mic数据

     ```
     LinkHost.sendMicData(micData, micData.length);
     ```

   * 发送ID3数据

     ```
     LinkHost.sendID3Data(this);
     ```

   * 发送音乐停止指令

     ```
     LinkHost.sendMusicStop();
     ```

   * 发送音乐播放指令

     ```
     LinkHost.sendMusicPlay();
     ```

   * 发送Vr结束指令

     ```
     LinkHost.sendVrEnd();
     ```

     ​