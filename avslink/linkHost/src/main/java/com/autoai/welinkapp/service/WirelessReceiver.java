package com.autoai.welinkapp.service;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.autoai.common.util.AppUtil;
import com.autoai.common.util.LogUtil;
import com.autoai.common.util.ProcessUtil;
import com.autoai.common.util.ThreadUtils;
import com.autoai.welink.wireless.WLHardware;
import com.autoai.welink.wireless.WLHardwareListener;
import com.autoai.welinkapp.checkconnect.CommonData;

/**
 * Todo 广播来来回回不知道干啥呢, 后续合并到 ConnectManager 去; 或者抽出一个无线的操作类, 直接调用即可
 */
public class WirelessReceiver extends BroadcastReceiver {
    private static final String TAG = "WL_DRIVER";

    private static int deviceType = -1;

    private static final String DEVICE_BRAND = "WeLink";
    private static final int FREQUENCY = 5745;

    private static final String BROADCAST_DEVICE_TYPE = "deviceType";
    private static final String BROADCAST_DEVICE_IP = "deviceIp";

    private static boolean isWirelessOpen = false;

    @SuppressWarnings("unused")
    public WirelessReceiver() {
    }

    private void sendBroadcast(String msg, int type, String ip) {
        ThreadUtils.postOnMainThread(() -> {
            Intent intent = new Intent(msg);
            intent.putExtra(BROADCAST_DEVICE_TYPE, type);
            if (ip != null) {
                intent.putExtra(BROADCAST_DEVICE_IP, ip);
            }
            AppUtil.getContext().sendBroadcast(intent);
        });
    }

//    private void startWeLink() {
//        Intent intent = new Intent();
//        ComponentName cName = new ComponentName(WELINK_PACKAGE_NAME, WELINK_ACTIVITY_NAME);
//        intent.setComponent(cName);
//        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//        mContext.startActivity(intent);
//    }

    private void openWireless() {
        if (!isWirelessOpen) {
            LogUtil.i(TAG, "openWireless");
            WLHardware.getInstance(AppUtil.getContext()).open(AppUtil.getContext().getPackageName(), 0, FREQUENCY, DEVICE_BRAND, wifiListener);
            isWirelessOpen = true;
        }
    }

    private void closeWireless() {
        if (isWirelessOpen) {
            LogUtil.i(TAG, "closeWireless");
            WLHardware.getInstance(AppUtil.getContext()).close();
            isWirelessOpen = false;
        }
    }

    private void resetWireless() {
        LogUtil.i(TAG, "resetWireless");
        WLHardware.getInstance(AppUtil.getContext()).reset();
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        ProcessUtil.printProcess(getClass());

        if (CommonData.START_CHECK_MSG.equals(action)) {
            LogUtil.i(TAG, "WirelessReceiver::onReceive ServiceReceiver START_CHECK_MSG");
            openWireless();
        } else if (CommonData.STOP_CHECK_MSG.equals(action)) {
            LogUtil.i(TAG, "WirelessReceiver::onReceive ServiceReceiver STOP_CHECK_MSG");
            closeWireless();
        } else if (CommonData.STOP_CONNECT_MSG.equals(action)) {
            LogUtil.i(TAG, "WirelessReceiver::onReceive ServiceReceiver STOP_CONNECT_MSG");
            resetWireless();
        } else {
            LogUtil.i(TAG, "WirelessReceiver::onReceive WirelessReceiver, onReceive other msg.");
        }
    }

    private final WLHardwareListener wifiListener = new WLHardwareListener() {
        @Override
        public void onDocking(int type) {
            // connecting
            // type: 0 android, 1 iOS 2 Harmony
            LogUtil.i(TAG, "WirelessReceiver::WLHardwareListener onDocking, type: " + type);
            if (type == 0) {
                deviceType = CommonData.DEVICE_TYPE_WIFI_ANDROID;
            }else if(type == 2){
                deviceType = CommonData.DEVICE_TYPE_WIFI_HARMONY;
            }else{
                deviceType = CommonData.DEVICE_TYPE_WIFI_IOS;
            }
            sendBroadcast(CommonData.DEVICE_CONNECTING, deviceType, null);
        }

        @Override
        public void onDocked(String ip) {
            // connect success
            LogUtil.i(TAG, "WirelessReceiver::WLHardwareListener onDocked, ip: " + ip);
            if (null == ip) {
                LogUtil.i(TAG, "onDocked ip is null");
                ip = "NULL";
            }
            if (deviceType < 0) {
                // TODO: 2022/1/22 iOS 互联权限修复之前的测试代码
                deviceType = CommonData.DEVICE_TYPE_WIFI_IOS;
            }
            sendBroadcast(CommonData.DEVICE_IN_MSG, deviceType, ip);
        }

        @Override
        public void onDisdock() {
            LogUtil.i(TAG, "WirelessReceiver::WLHardwareListener onDisdock.");
            if (deviceType < 0) {
                LogUtil.i(TAG, "onDisdock unknown type.");
                return;
            }
            sendBroadcast(CommonData.DEVICE_OUT_MSG, deviceType, null);
        }

        @Override
        public void onError(int reason) {
            // reason - 0: Wi-Fi没有打开；1：BLE没有打开
            LogUtil.i(TAG, "WirelessReceiver::WLHardwareListener onError, reason: " + reason);
        }
    };

}
