package com.autoai.welinkapp.checkconnect;

import static com.autoai.welinkapp.checkconnect.CommonData.TAG_AOA;
import static com.autoai.welinkapp.checkconnect.CommonData.TAG_EAP;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbManager;

import com.autoai.avslinkhid.api.HidUtil;
import com.autoai.common.util.LogUtil;
import com.autoai.common.util.ProcessUtil;
import com.autoai.welinkapp.idb.IdbLink;
import com.autoai.welinkapp.model.SdkViewModel;

/**
 * 监听系统 USB 连接状态
 */
public class SystemUsbReceiver extends BroadcastReceiver {
    private static final String TAG = "WL_DRIVER";
    private final Context mContext;
    private static final String ACTION_USB_ATTACH = "com.wldriver.checkconnect.USB_ATTACH";
    private static final String ACTION_USB_DETACH = "com.wldriver.checkconnect.USB_DETACH";
    public static final String ACTION_USB_PERMISSION = "com.wldriver.checkconnect.USB_PERMISSION";

    public SystemUsbReceiver(Context context) {
        mContext = context;
    }

    /**
     * 注册系统广播
     */
    @SuppressLint("UnspecifiedRegisterReceiverFlag")
    public void registerReceiver() {
        LogUtil.d(TAG, "SystemUsbReceiver::registerReceiver()");
        IntentFilter filter = new IntentFilter();
        filter.addAction(ACTION_USB_ATTACH);
        filter.addAction(ACTION_USB_DETACH);
        filter.addAction(UsbManager.ACTION_USB_ACCESSORY_ATTACHED);
        filter.addAction(UsbManager.ACTION_USB_ACCESSORY_DETACHED);
        filter.addAction(UsbManager.ACTION_USB_DEVICE_ATTACHED);
        filter.addAction(UsbManager.ACTION_USB_DEVICE_DETACHED);
        filter.addAction(ACTION_USB_DETACH);
        filter.addAction(ACTION_USB_PERMISSION);
        mContext.registerReceiver(this, filter,Context.RECEIVER_NOT_EXPORTED);
    }


    /**
     * 移除广播监听
     */
    public void unregisterReceiver() {
        LogUtil.d(TAG, "SystemUsbReceiver unregisterReceiver");
        mContext.unregisterReceiver(this);
    }

    /**
     * 系统USB状态广播监听
     */
    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        ProcessUtil.printProcess(getClass());
        LogUtil.i(TAG + TAG_AOA + TAG_EAP, "SystemUsbReceiver::onReceive action --> " + action);
        if (ACTION_USB_ATTACH.equals(action) || UsbManager.ACTION_USB_DEVICE_ATTACHED.equals(action)) {
            LogUtil.i(TAG, "SystemUsbReceiver::onReceive  --> USB 设备接入" );
            CommonData.strDeviceSerialNum = SdkViewModel.getInstance().platformadaptor.getDeviceSerialNum();
            LogUtil.i(TAG, "Android SerialNum: " + CommonData.strDeviceSerialNum);
            try {
                //Desay: sleep 250ms
                Thread.sleep(250);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            //-->检查，并发起usb连接
            boolean ret = AOACheckDevice.getInstance().checkDevices();
            LogUtil.d(TAG, "SystemUsbReceiver::onReceive  --> checkAoaDevices end, ret:" + ret);
            if (IdbLink.isIdbModel()) {
                ret = IDBCheckDevice.getInstance().checkDevices();
                LogUtil.d(TAG, "SystemUsbReceiver::onReceive  --> checkIdbDevices end, ret:" + ret);
            }
        } else if (ACTION_USB_DETACH.equals(action) || UsbManager.ACTION_USB_DEVICE_DETACHED.equals(action)) {
            LogUtil.i(TAG, "SystemUsbReceiver::onReceive  --> USB 移除" );

            AOACheckDevice.getInstance().dismissUsbDialog();
            if (CommonData.DEVICE_TYPE_AOA == CommonData.iNeedConnectType) {
                CommonData.iNeedConnectType = CommonData.DEVICE_TYPE_NONE;
            }
            if (CommonData.DEVICE_TYPE_AOA == CommonData.iCurrentConnectType) {
                CommonData.sendMsg(mContext, CommonData.CONNECT_STATUS_OUT, CommonData.DEVICE_TYPE_AOA, null);
                AOACheckDevice.getInstance().deinitUsbDevice();
                HidUtil.unregisterHID();
            }else if(CommonData.DEVICE_TYPE_EAP == CommonData.iCurrentConnectType){
                CommonData.sendMsg(mContext, CommonData.CONNECT_STATUS_OUT, CommonData.DEVICE_TYPE_EAP, null);
            }
            if (CommonData.DEVICE_TYPE_IDB == CommonData.iNeedConnectType) {
                CommonData.iNeedConnectType = CommonData.DEVICE_TYPE_NONE;
            }
            if (CommonData.DEVICE_TYPE_IDB == CommonData.iCurrentConnectType) {
                CommonData.sendMsg(mContext, CommonData.CONNECT_STATUS_OUT, CommonData.DEVICE_TYPE_IDB, null);
                IDBCheckDevice.getInstance().deinitUsbDevice();
            } else if (IDBCheckDevice.getInstance().isCheckDeviceRunning()) {
                IDBCheckDevice.getInstance().deinitUsbDevice();
            }
        } else if (ACTION_USB_PERMISSION.equals(action)) {
            LogUtil.i(TAG, "SystemUsbReceiver::onReceive  -->  ACTION_USB_PERMISSION 申请权限" );
            synchronized (this) {
                UsbDevice device = intent.getParcelableExtra(UsbManager.EXTRA_DEVICE);
                if (device == null) {
                    LogUtil.e(TAG, "SystemUsbReceiver::onReceive  --> device is null");
                    return;
                }
                LogUtil.d(TAG, device.toString());
                if (intent.getBooleanExtra(UsbManager.EXTRA_PERMISSION_GRANTED, false)) {
                    LogUtil.i(TAG, "SystemUsbReceiver::onReceive  --> permission success");
                    AOACheckDevice.getInstance().initUsbDevice(device);
                    if (IdbLink.isIdbModel()) {
                        IDBCheckDevice.getInstance().initUsbDevice(device);
                    }
                } else {
                    LogUtil.e(TAG, "SystemUsbReceiver::onReceive --> permission denied");
                }
            }
        } else {
            LogUtil.e(TAG, "SystemUsbReceiver::onReceive --> Unknown system broadcast!!");
        }
    }

}
