package com.autoai.welinkapp.platform;

import com.autoai.welinkapp.model.PlatformAdaptor;

/**
 * <AUTHOR>
 */
public interface LinkPlatform {
    /**
     * 初始化
     * @param adaptor 平台对接adaptor
     */
    void init(PlatformAdaptor adaptor);

    /**
     * 音频播放状态改变
     * @param state 音频播放状态
     */
    void noticeMediaState(int state);

    /**
     * App展示状态改变
     * @param bShow App是否展示
     */
    void noticeAppState(boolean bShow);

    /**
     * 连接状态改变
     * @param state 连接状态
     */
    void noticeConnectState(int state);

    /**
     * @param type  设备类型.参考 {@link com.autoai.welinkapp.checkconnect.CommonData}
     * @param state 0
     */
    void noticeDeviceType(int type, int state);

    /**
     * @param segInfo segInfo
     * @param totalDistance totalDistance
     */
    void noticeNaviInfo(String segInfo, String totalDistance);

    /**
     * @param state state
     */
    void noticeNaviState(int state);

    /**
     * 启动WeLink
     */
    void showWelink();

    void noticeTrackInfo(Object id3);

    String getDeviceSerialNum();
}
