package com.autoai.welinkapp.model;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import java.util.UUID;

import top.eiyooooo.easycontrol.app.client.Client;
import top.eiyooooo.easycontrol.app.entity.AppData;
import top.eiyooooo.easycontrol.app.entity.Device;
import top.eiyooooo.easycontrol.app.entity.Setting;
import top.eiyooooo.easycontrol.app.helper.ConnectHelper;

public class AppClientManager {
    private static volatile AppClientManager mInstance;

    private AppClientManager() {
    }

    public static AppClientManager getInstance() {
        if (null == mInstance) {
            synchronized (AppClientManager.class) {
                if (null == mInstance) {
                    mInstance = new AppClientManager();
                }
            }
        }
        return mInstance;
    }

    private Client client;
    private String ipAddress;

    /**
     * 启动应用投屏
     *
     * @param x、y：出现的位置 angle：横竖屏状态
     */
    public void projectApp(Context context, int x, int y, int angle) {
        ipAddress = SdkViewModel.getInstance().getIpAddress();
        Log.i("TTTTT", "AppClientManager::projectApp ipAddress = " + ipAddress);
        if (TextUtils.isEmpty(ipAddress)) {
            return;
        }
        AppData.setting = new Setting(context.getSharedPreferences("setting", Context.MODE_PRIVATE));
        Device device = Device.getDefaultDevice(UUID.randomUUID().toString(), Device.TYPE_NORMAL);
        device.address = ipAddress + ":5555";

        device.small_l_p_x = x;
        device.small_l_p_y = y;
        Log.i("TTTTT", "1 SdkViewModel::angle = " + angle);
        if (angle == 90 || angle == 270) {
            // 横屏
            Log.i("TTTTT", "2 SdkViewModel::angle = " + angle);
            client = new Client(device, null, 1, 1);
        } else {
            // 竖屏
            Log.i("TTTTT", "3 SdkViewModel::angle = " + angle);
            client = new Client(device, null, 1, 0);
        }
        client.setShowToastListener(new Client.ShowToastListener() {
            @Override
            public void onShow(int status) {
                mShowToastListener.onShow(status);
            }
        });
        ConnectHelper.setShowToastListener(new Client.ShowToastListener() {
            @Override
            public void onShow(int status) {
                mShowToastListener.onShow(status);
            }
        });
    }


    private ShowToastListener mShowToastListener;
    public interface ShowToastListener {
        void onShow(int status);
    }

    public void setShowToastListener(ShowToastListener showToastListener) {
        this.mShowToastListener = showToastListener;
    }

    /**
     * 应用投屏个数
     */
    public int getClientSize() {
        if(null == client){
            return 0;
        } else {
            return client.getClientSize();
        }
    }

    /**
     * 关闭应用投屏窗口
     */
    public void closeClient() {
        if (client != null) {
            client.clientView.updateDevice();
            client.clientView.onClose.run();
        }
    }

}
