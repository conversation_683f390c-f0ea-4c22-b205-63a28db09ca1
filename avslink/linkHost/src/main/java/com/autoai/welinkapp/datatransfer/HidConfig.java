package com.autoai.welinkapp.datatransfer;

public class HidConfig {
    public final static String MOUSE_NAME = "VV Mouse";

    public final static String DESCRIPTION = "VV for you";

    public final static String PROVIDER = "VV";

    public static final byte[] MOUSE_COMBO = {


            //键盘HID注册
//            0x05, 0x0C, 0x09, 0x01, (byte) 0xA1, 0x01, (byte) 0x85, 0x01, 0x05, 0x0C,
//            0x15, 0x00, 0x25, 0x01, 0x75, 0x01, (byte) 0x95, 0x1C, 0x09, 0x40,
//            0x09, 0x42, 0x09, 0x43, 0x09, 0x44, 0x09, 0x45, 0x09, (byte) 0x8C,
//            0x09, (byte) 0xE2, 0x09, (byte) 0xB0, 0x09, (byte) 0xB5, 0x09, (byte) 0xB6,
//            0x09, (byte) 0xB7, 0x09, (byte) 0xCD, 0x09, (byte) 0xEA, 0x09, (byte) 0xE9,
//            0x0A, 0x23, 0x02, 0x0A, 0x24, 0x02, (byte) 0x81, 0x02, (byte) 0xC0,


            (byte)0x05,(byte)0x01,(byte)0x09,(byte)0x06,(byte)0xa1,(byte)0x01,(byte)0x85,(byte)0x01,(byte)0x05,(byte)0x07,
            (byte)0x19,(byte)0xe0,(byte)0x29,(byte)0xe7,(byte)0x15,(byte)0x00,(byte)0x25,(byte)0x01,
            (byte)0x75,(byte)0x01,(byte)0x95,(byte)0x08,(byte)0x81,(byte)0x02,(byte)0x95,(byte)0x01,
            (byte)0x75,(byte)0x08,(byte)0x81,(byte)0x01,(byte)0x95,(byte)0x05,(byte)0x75,(byte)0x01,
            (byte)0x05,(byte)0x08,(byte)0x19,(byte)0x01,(byte)0x29,(byte)0x05,(byte)0x91,(byte)0x02,
            (byte)0x95,(byte)0x01,(byte)0x75,(byte)0x03,(byte)0x91,(byte)0x01,(byte)0x95,(byte)0x06,
            (byte)0x75,(byte)0x08,(byte)0x15,(byte)0x00,(byte)0x26,(byte)0xff,(byte)0x00,(byte)0x05,
            (byte)0x07,(byte)0x19,(byte)0x00,(byte)0x29,(byte)0xff,(byte)0x81,(byte)0x00,(byte)0x05,
            (byte)0x0c,(byte)0x75,(byte)0x01,(byte)0x95,(byte)0x01,(byte)0x09,(byte)0xb8,(byte)0x15,
            (byte)0x00,(byte)0x25,(byte)0x01,(byte)0x81,(byte)0x02,(byte)0x05,(byte)0xff,(byte)0x09,
            (byte)0x03,(byte)0x75,(byte)0x07,(byte)0x95,(byte)0x01,(byte)0x81,(byte)0x02,(byte)0xc0,

            (byte) 0x05, (byte) 0x01,              // USAGE_PAGE (Generic Desktop)
            (byte) 0x09, (byte) 0x02,              // USAGE (Mouse)
            (byte) 0xa1, (byte) 0x01,              // COLLECTION (Application)
            (byte) 0x85, (byte) 0x04,              // REPORT_ID (4)
            (byte) 0x09, (byte) 0x01,              //  USAGE (Pointer)
            (byte) 0xa1, (byte) 0x00,              //  COLLECTION (Physical)
            (byte) 0x05, (byte) 0x09,              //   USAGE_PAGE (Button)
            (byte) 0x19, (byte) 0x01,              //   USAGE_MINIMUM (Button 1)
            (byte) 0x29, (byte) 0x03,              //   USAGE_MAXIMUM (Button 2)
            (byte) 0x15, (byte) 0x00,              //   LOGICAL_MINIMUM (0)
            (byte) 0x25, (byte) 0x01,              //   LOGICAL_MAXIMUM (1)
            (byte) 0x95, (byte) 0x03,              //   REPORT_COUNT (3)
            (byte) 0x75, (byte) 0x01,              //   REPORT_SIZE (1)
            (byte) 0x81, (byte) 0x02,              //   INPUT (Data,Var,Abs)
            (byte) 0x95, (byte) 0x01,              //   REPORT_COUNT (1)
            (byte) 0x75, (byte) 0x05,              //   REPORT_SIZE (5)
            (byte) 0x81, (byte) 0x03,              //   INPUT (Cnst,Var,Abs)
            (byte) 0x05, (byte) 0x01,              //   USAGE_PAGE (Generic Desktop)
            (byte) 0x09, (byte) 0x30,              //   USAGE (X)
            (byte) 0x09, (byte) 0x31,              //   USAGE (Y)
            (byte) 0x09, (byte) 0x38,              //   USAGE (Wheel)
            (byte) 0x15, (byte) 0x81,              //   LOGICAL_MINIMUM (-127)
            (byte) 0x25, (byte) 0x7F,              //   LOGICAL_MAXIMUM (127)
            (byte) 0x75, (byte) 0x08,              //   REPORT_SIZE (8)
            (byte) 0x95, (byte) 0x03,              //   REPORT_COUNT (3)
            (byte) 0x81, (byte) 0x06,              //   INPUT (Data,Var,Rel)
            //水平滚轮
            (byte) 0x05, (byte) 0x0c,              //   USAGE_PAGE (Consumer Devices)
            (byte) 0x0a, (byte) 0x38, (byte) 0x02, //   USAGE (AC Pan)
            (byte) 0x15, (byte) 0x81,              //   LOGICAL_MINIMUM (-127)
            (byte) 0x25, (byte) 0x7f,              //   LOGICAL_MAXIMUM (127)
            (byte) 0x75, (byte) 0x08,              //   REPORT_SIZE (8)
            (byte) 0x95, (byte) 0x01,              //   REPORT_COUNT (1)
            (byte) 0x81, (byte) 0x06,              //   INPUT (Data,Var,Rel)

            (byte) 0xc0,                           //  END_COLLECTION
            (byte) 0xc0,                           // END_COLLECTION

    };



}
