package com.autoai.welinkapp.model;

import androidx.annotation.NonNull;

import com.autoai.welinkapp.aoa.AoaDevice;
import com.autoai.welinkapp.audio.AudioPlayer;
import com.autoai.welinkapp.eap.EapDevice;
import com.autoai.welinkapp.idb.IdbDevice;
import com.autoai.welinkapp.platform.LinkPlatform;

public class SdkConfig {
    private String serialNum = "123445";
    private int fps = 30;
    private int rtt  = 0;
    private AoaDevice aoaDevice = new AoaDevice() {
        @NonNull
        @Override
        public String getManufacturer() {
            return "Mapbar, Inc";
        }

        @NonNull
        @Override
        public String getModel() {
            return "WeLink";
        }

        @NonNull
        @Override
        public String getDescription() {
            return "WeLink";
        }

        @NonNull
        @Override
        public String getUri() {
            return "https://welink-rs.autoai.com/saic/ap32";
        }

        @NonNull
        @Override
        public String getSerial() {
            return "mySerial";
        }

        @NonNull
        @Override
        public String getVersion() {
            return "1.0";
        }
    };
    private EapDevice eapDevice;
    private LinkPlatform platform;
    private AudioPlayer audioPlayer;
    private IdbDevice idbDevice;

    private SdkConfig() {
    }

    public String getSerialNum() {
        return serialNum;
    }


    public int getFps() {
        return fps;
    }
    public int getRtt() {
        return rtt;
    }


    public AoaDevice getAoaDevice() {
        return aoaDevice;
    }


    public EapDevice getEapDevice() {
        return eapDevice;
    }


    public LinkPlatform getPlatform() {
        return platform;
    }

    public AudioPlayer getAudioPlayer() {
        return audioPlayer;
    }

    public IdbDevice getIdbDevice() {
        return idbDevice;
    }


    public static class Builder {

        private final SdkConfig sdkConfig;

        public Builder() {
            sdkConfig = new SdkConfig();
        }

        public Builder setSerialNum(String serialNum) {
            sdkConfig.serialNum = serialNum;
            return this;
        }


        public Builder setFps(int fps) {
            sdkConfig.fps = fps;
            return this;
        }
        public Builder setRtt(int rtt) {
            sdkConfig.rtt = rtt;
            return this;
        }

        public Builder setAoaDevice(AoaDevice aoaDevice) {
            sdkConfig.aoaDevice = aoaDevice;
            return this;
        }


        public Builder setEapDevice(EapDevice eapDevice) {
            sdkConfig.eapDevice = eapDevice;
            if (eapDevice != null)
                sdkConfig.idbDevice = null;
            return this;
        }

        public Builder setIdbDevice(IdbDevice idbDevice) {
            sdkConfig.idbDevice = idbDevice;
            if (idbDevice != null)
                sdkConfig.eapDevice = null;
            return this;
        }

        public Builder setPlatform(LinkPlatform platform) {
            sdkConfig.platform = platform;
            return this;
        }

        public Builder setAudioPlayer(AudioPlayer audioPlayer) {
            sdkConfig.audioPlayer = audioPlayer;
            return this;
        }

        public SdkConfig build() {
            return sdkConfig;
        }
    }
}
