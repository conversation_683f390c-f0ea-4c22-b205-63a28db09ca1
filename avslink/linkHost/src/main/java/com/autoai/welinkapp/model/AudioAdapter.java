package com.autoai.welinkapp.model;

import android.content.Context;

import com.autoai.welinkapp.audio.AudioPlayer;

import top.eiyooooo.easycontrol.app.client.Client;


public class AudioAdapter {

    private AudioPlayer audioPlayer;


    public void init(AudioPlayer audioPlayer) {
        this.audioPlayer = audioPlayer;
        if (audioPlayer != null) {
            audioPlayer.init();
        }
    }

    public void onDisconnect() {
        if (audioPlayer != null) {
            audioPlayer.audioStop();
        }
    }

    public void onConnecting() {
        if (audioPlayer != null) {
            audioPlayer.audioResume();
        }
    }

    public int getMusicState() {
        if (audioPlayer != null) {
            return audioPlayer.getMusicState();
        }
        return 0;
    }


    public void onConnectError() {
        if (audioPlayer != null) {
            audioPlayer.audioStop();
        }
    }

    public boolean muteStart() {
        if (audioPlayer != null) {
            return audioPlayer.muteStart();
        }
        return false;
    }

    public void audioSetVRStart() {
        if (audioPlayer != null) {
            audioPlayer.audioSetVRStart();
        }
    }

    public void muteStop() {
        if (audioPlayer != null) {
            audioPlayer.muteStop();
        }
    }

    public void pcmStopPlay(int type) {
        if (audioPlayer != null) {
            audioPlayer.pcmStopPlay(type);
        }
    }

    public void onAudioData(byte[] bytes, int i) {
        if (audioPlayer != null) {
            audioPlayer.onAudioData(bytes, i);
        }
    }

    public int getMusicAudioFoucs() {
        if (audioPlayer != null) {
            return audioPlayer.getMusicAudioFoucs();
        }
        return 0;
    }

    public void onCreate(Context mApplication) {
        if (audioPlayer != null) {
            audioPlayer.onCreate(mApplication);
        }
    }
}
