package com.autoai.welinkapp.util;

import com.autoai.common.util.LogUtil;

import java.util.Locale;

/**
 * <AUTHOR>
 */
public class LocalUtil {
    private static final String LANGUAGE_ENGLISH = "en";
    private static final String LANGUAGE_SPAIN = "es";
    private static final String LANGUAGE_PORTUGAL = "pt";
    private static final String LANGUAGE_ARAB = "ar";
    private static final String LANGUAGE_INDIA = "in";
    private static final String LANGUAGE_VIETNAM = "vi";
    private static final String LANGUAGE_THAILAND = "th";
    private static final String LANGUAGE_MALAYSIA = "ms";
    private static final String LANGUAGE_RUSSIA = "ru";

    public static int getLocal() {
        Locale locale = Locale.getDefault();
        LogUtil.e("LocalUtil", "LocalUtil : " + locale);
        int localType;  //0：繁体中文  1:中文  2:英文   3:西班牙语  4：印尼  5：阿拉伯语  6:葡萄牙语 7：越南   8：泰语    9：马来语 10:俄罗斯语
        if (LANGUAGE_SPAIN.equals(locale.getLanguage())) {
            localType = 3;
        } else if (LANGUAGE_PORTUGAL.equals(locale.getLanguage())) {
            localType = 6;
        } else if (LANGUAGE_ARAB.equals(locale.getLanguage())) {
            localType = 5;
        } else if (LANGUAGE_ENGLISH.equals(locale.getLanguage())) {
            localType = 2;
        } else if (LANGUAGE_INDIA.equals(locale.getLanguage())) {
            localType = 4;
        } else if (Locale.TRADITIONAL_CHINESE.getCountry().equals(locale.getCountry())) {
            localType = 0;
        } else if (LANGUAGE_VIETNAM.equals(locale.getLanguage())) {
            localType = 7;
        } else if (LANGUAGE_THAILAND.equals(locale.getLanguage())) {
            localType = 8;
        } else if (LANGUAGE_MALAYSIA.equals(locale.getLanguage())) {
            localType = 9;
        } else if (LANGUAGE_RUSSIA.equals(locale.getLanguage())) {
            localType = 10;
        }else {
            localType = 1;
        }

        return localType;
    }
}


