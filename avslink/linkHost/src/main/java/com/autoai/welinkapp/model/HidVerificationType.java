package com.autoai.welinkapp.model;

/**
 * <AUTHOR>
 */
public class HidVerificationType {
    /**
     * 未校准
     */
    public static final int HID_VERIFY_TYPE_NONE = 0;
    /**
     * HID未连接，不可校准
     */
    public static final int HID_VERIFY_TYPE_UN_CONNECT = 1;
    /**
     * HID已连接，可校准
     */
    public static final int HID_VERIFY_TYPE_CONNECT = 2;
    /**
     * 校准中
     */
    public static final int HID_VERIFY_TYPE_VERIFICATION_ING = 3;
    /**
     * 校准完成
     */
    public static final int HID_VERIFY_TYPE_VERIFICATION_SUCCESS = 4;
    /**
     * 校准失败,HID已连接
     */
    public static final int HID_VERIFY_TYPE_VERIFICATION_FAIL = 5;

    /**
     * 校准失败,HID未连接
     */
    public static final int HID_VERIFY_TYPE_HID_CONNECT_VERIFICATION_FAIL = 6;
}
