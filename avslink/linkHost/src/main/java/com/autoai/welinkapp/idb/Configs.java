package com.autoai.welinkapp.idb;

/**
 * 配置类,方便自动打包平台统一替换
 * 
 * <AUTHOR>
 * 
 */
public class Configs
{
	
	/**true :先锋,false 普通*/
	public static boolean isUseSU = false;

	/**true :支持IOS_MFI认证,false 不支持*/
	public static boolean isIOS_MFI = true;

	/**普通级别的日志,可以暴露给使用者*/
	public static boolean isDebug = true;
	/**SDK级别的日志,不能暴露给使用者*/
	public static boolean isSdkDebug = false;


	/**
	 * 车机启动手机Launcher的应用包名
	 */
	public static String startAppPackageName = "";

	/**
	 * 用来记录welink是否有焦点(互联控制使用)
	 * 默认true如果不设置此变量,welink切后台插入手机会自动互联
	 */
	public static boolean isResume = true;

	/**
	 * 支持手机当车机使用IOS WIFI模式使用
	 */
	public static boolean isIOSWifiTest = false;


	public static String systemPath = "/system/bin/";


	public static String adbName = "adb";


	/**
	 * 是否检测adb进程，进行兼容百度亿联
	 */
	public static boolean isCompatibilityAdb = false;

	/**
	 * 是否需要判断USB插入设备的节点文件
	 */
	public static boolean USB_DEVICE_NODE = false;
	/**
	 * 外设的VendorId数组
	 */
	public static int[] arrayVendorId ={0x2c7c};

	/**
	 * 外设的ProductId数组
	 */
	public static int[] arrayProductId = {0x125};

}
