package com.autoai.welinkapp.eap;

import java.util.ArrayList;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 */
public class EapLink {
    private static final AtomicReference<EapDevice> EAP_DEVICE = new AtomicReference<>();


    private static boolean isEapOpen = false;
    private static boolean isEapActive = false;

    private EapLink() {
    }

    public static void initMfiI2cPath() {
        EapDevice device = EAP_DEVICE.get();
        if (device != null) {
            device.initMfiI2cPath();
        }
    }
    public static void eapInit() {
        EapDevice device = EAP_DEVICE.get();
        if (device != null) {
            device.eapInit();
        }
    }

    public static boolean setUsbMode(boolean usbMode) {
        EapDevice device = EAP_DEVICE.get();
        if (device != null) {
            return device.setUsbMode(usbMode);
        }
        return false;
    }

    public static void eapDeinit() {
        EapDevice device = EAP_DEVICE.get();
        if (device != null) {
            deactiveEap();
            eapBulkClose();
            device.eapDeinit();
        }
        EAP_DEVICE.set(null);
    }

    public static void activeEap() {
        EapDevice device = EAP_DEVICE.get();
        if (device != null && !isEapActive) {
            device.activeEap();
            isEapActive = true;
        }
    }

    public static void deactiveEap() {
        EapDevice device = EAP_DEVICE.get();
        if (device != null && isEapActive) {
            device.deactiveEap();
            isEapActive = false;
        }
    }

    public static void eapLaunchApp() {
        EapDevice device = EAP_DEVICE.get();
        if (device != null) {
            device.eapLaunchApp();
        }
    }

    public static void linkDeviceCallbackRegister(OnLinkEapListener linkEapListener) {
        EapDevice device = EAP_DEVICE.get();
        if (device != null) {
            device.linkDeviceCallbackRegister(linkEapListener);
        }
    }

    public static boolean eapBulkOpen() {
        EapDevice device = EAP_DEVICE.get();
        if (device != null) {
            if (!isEapOpen) {
                isEapOpen = true;
                return device.eapBulkOpen();
            }
        }
        return false;
    }

    public static void eapBulkClose() {
        EapDevice device = EAP_DEVICE.get();
        if (device != null) {
            if (isEapOpen) {
                device.eapBulkClose();
                isEapOpen = false;
            }
        }
    }

    public static byte[] eapBulkRead(int len) {
        EapDevice device = EAP_DEVICE.get();
        if (device != null) {
            return device.eapBulkRead(len);
        }
        return new byte[0];
    }

    public static int eapBulkWrite(byte[] buffer, int len) {
        EapDevice device = EAP_DEVICE.get();
        if (device != null) {
            return device.eapBulkWrite(buffer, len);
        }
        return -10;
    }
    public static int getEapStatus() {
        EapDevice device = EAP_DEVICE.get();
        if (device != null) {
            return device.getEapStatus();
        }
        return -10;
    }

    public static void unLinkDeviceCallbackRegister() {
        EapDevice device = EAP_DEVICE.get();
        if (device != null) {
           device.unLinkDeviceCallbackRegister();
        }
    }
    public static boolean resetUsb() {
        EapDevice device = EAP_DEVICE.get();
        if (device != null) {
            return device.resetUsb();
        }
        return false;
    }
    public static void init(EapDevice device) {
        EAP_DEVICE.set(device);
    }

    public static boolean isEapModel() {
        return EAP_DEVICE.get() != null;
    }
}
