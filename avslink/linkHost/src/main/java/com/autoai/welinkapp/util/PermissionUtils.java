package com.autoai.welinkapp.util;

import android.Manifest;
import android.app.Activity;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.provider.Settings;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;
import androidx.core.app.ActivityCompat;

import com.autoai.welinkapp.R;
import com.autoai.common.util.LogUtil;
import java.util.ArrayList;
import java.util.List;

public class PermissionUtils {

    private static final String TAG = PermissionUtils.class.getSimpleName();
    public static final int CODE_RECORD_AUDIO = 0;
    public static final int CODE_ACCESS_FINE_LOCATION = 1;
    public static final int CODE_READ_PHONE_STATE = 2;
    public static final int CODE_MULTI_PERMISSION = 100;

    public static final String PERMISSION_RECORD_AUDIO = Manifest.permission.RECORD_AUDIO;
    public static final String PERMISSION_ACCESS_FINE_LOCATION = Manifest.permission.ACCESS_FINE_LOCATION;
    public static final String PERMISSION_READ_PHONE_STATE = Manifest.permission.READ_PHONE_STATE;
    public static final String PERMISSION_READ_EXTERNAL_STORAGE = Manifest.permission.READ_EXTERNAL_STORAGE;
    public static final String PERMISSION_WRITE_EXTERNAL_STORAGE = Manifest.permission.WRITE_EXTERNAL_STORAGE;

    private static final String[] requestPermissions = {
            PERMISSION_RECORD_AUDIO,
            PERMISSION_ACCESS_FINE_LOCATION,
            PERMISSION_READ_PHONE_STATE,
            //PERMISSION_READ_EXTERNAL_STORAGE,
            //PERMISSION_WRITE_EXTERNAL_STORAGE
    };

    public interface PermissionGrant {
        void onPermissionGranted(int requestCode);
    }

    /**
     * Requests permission.
     *
     * @param activity    Activity of the caller.
     * @param requestCode Request code, e.g. if you need request CAMERA permission,parameters is PermissionUtils.CODE_CAMERA
     */
    public static void requestPermission(final Activity activity, final int requestCode, PermissionGrant permissionGrant) {
        if (activity == null) {
            return;
        }

        LogUtil.i(TAG, "requestPermission requestCode:" + requestCode);
        if (requestCode < 0 || requestCode >= requestPermissions.length) {
            LogUtil.w(TAG, "requestPermission illegal requestCode:" + requestCode);
            return;
        }

        final String requestPermission = requestPermissions[requestCode];
        int checkSelfPermission;
        try {
            checkSelfPermission = ActivityCompat.checkSelfPermission(activity, requestPermission);
        } catch (RuntimeException e) {
            Toast.makeText(activity, "please open this permission", Toast.LENGTH_SHORT)
                    .show();
            LogUtil.e(TAG, "RuntimeException:" + e.getMessage());
            return;
        }

        if (checkSelfPermission != PackageManager.PERMISSION_GRANTED) {
            LogUtil.i(TAG, "ActivityCompat.checkSelfPermission != PackageManager.PERMISSION_GRANTED");


            if (ActivityCompat.shouldShowRequestPermissionRationale(activity, requestPermission)) {
                LogUtil.i(TAG, "requestPermission shouldShowRequestPermissionRationale");
                shouldShowRationale(activity, requestCode, requestPermission);

            } else {
                LogUtil.d(TAG, "requestCameraPermission else");
                ActivityCompat.requestPermissions(activity, new String[]{requestPermission}, requestCode);
            }

        } else {
            LogUtil.d(TAG, "ActivityCompat.checkSelfPermission ==== PackageManager.PERMISSION_GRANTED");
            Toast.makeText(activity, "opened:" + requestPermissions[requestCode], Toast.LENGTH_SHORT).show();
            permissionGrant.onPermissionGranted(requestCode);
        }
    }

//    private static void requestMultiResult(Activity activity, String[] permissions, int[] grantResults, PermissionGrant permissionGrant) {
//
//        if (activity == null) {
//            return;
//        }
//
//        LogUtil.d(TAG, "onRequestPermissionsResult permissions length:" + permissions.length);
//        Map<String, Integer> perms = new HashMap<>();
//
//        ArrayList<String> notGranted = new ArrayList<>();
//        for (int i = 0; i < permissions.length; i++) {
//            LogUtil.d(TAG, "permissions: [i]:" + i + ", permissions[i]" + permissions[i] + ",grantResults[i]:" + grantResults[i]);
//            perms.put(permissions[i], grantResults[i]);
//            if (grantResults[i] != PackageManager.PERMISSION_GRANTED) {
//                notGranted.add(permissions[i]);
//            }
//        }
//
//        if (notGranted.size() == 0) {
//            Toast.makeText(activity, "all permission success" + notGranted, Toast.LENGTH_SHORT)
//                    .show();
//            permissionGrant.onPermissionGranted(CODE_MULTI_PERMISSION);
//        } else {
//            openSettingActivity(activity, "those permission need granted!");
//        }
//    }

    /**
     * 一次申请多个权限
     */
    public static void requestMultiPermissions(final Activity activity, PermissionGrant grant) {

        final List<String> permissionsList = getNoGrantedPermission(activity, false);
        final List<String> shouldRationalePermissionsList = getNoGrantedPermission(activity, true);

        //TODO checkSelfPermission
        if (permissionsList == null || shouldRationalePermissionsList == null) {
            return;
        }
        LogUtil.d(TAG, "requestMultiPermissions permissionsList:" + permissionsList.size() + ",shouldRationalePermissionsList:" + shouldRationalePermissionsList.size());

        if (permissionsList.size() > 0) {
            ActivityCompat.requestPermissions(activity, permissionsList.toArray(new String[0/*permissionsList.size()*/]),
                    CODE_MULTI_PERMISSION);
            LogUtil.d(TAG, "showMessageOKCancel requestPermissions");

        } else if (shouldRationalePermissionsList.size() > 0) {
            showMessageOKCancel(activity, "should open those permission",
                    (dialog, which) -> {
                        ActivityCompat.requestPermissions(activity, shouldRationalePermissionsList.toArray(new String[0/*shouldRationalePermissionsList.size()*/]),
                                CODE_MULTI_PERMISSION);
                        LogUtil.d(TAG, "showMessageOKCancel requestPermissions");
                    });
        } else {
            grant.onPermissionGranted(CODE_MULTI_PERMISSION);
        }

    }

    private static void shouldShowRationale(final Activity activity, final int requestCode, final String requestPermission) {
        //TODO
        String[] permissionsHint = activity.getResources().getStringArray(R.array.permissions);
        showMessageOKCancel(activity, "Rationale: " + permissionsHint[requestCode], (dialog, which) -> {
            ActivityCompat.requestPermissions(activity,
                    new String[]{requestPermission},
                    requestCode);
            LogUtil.d(TAG, "showMessageOKCancel requestPermissions:" + requestPermission);
        });
    }

    private static void showMessageOKCancel(final Activity context, String message, DialogInterface.OnClickListener okListener) {
        new AlertDialog.Builder(context)
                .setMessage(message)
                .setPositiveButton("OK", okListener)
                .setNegativeButton("Cancel", null)
                .create()
                .show();

    }

//    /**
//     * @param activity Activity
//     * @param requestCode Need consistent with request permission
//     * @param permissions
//     * @param grantResults
//     */
//    public static void requestPermissionsResult(final Activity activity, final int requestCode, @NonNull String[] permissions,
//                                                @NonNull int[] grantResults, PermissionGrant permissionGrant) {
//
//        if (activity == null) {
//            return;
//        }
//        LogUtil.d(TAG, "requestPermissionsResult requestCode:" + requestCode);
//
//        if (requestCode == CODE_MULTI_PERMISSION) {
//            requestMultiResult(activity, permissions, grantResults, permissionGrant);
//            return;
//        }
//
//        if (requestCode < 0 || requestCode >= requestPermissions.length) {
//            LogUtil.w(TAG, "requestPermissionsResult illegal requestCode:" + requestCode);
//            Toast.makeText(activity, "illegal requestCode:" + requestCode, Toast.LENGTH_SHORT).show();
//            return;
//        }
//
////        LogUtil.i(TAG, "onRequestPermissionsResult requestCode:" + requestCode + ",permissions:" + Arrays.toString(permissions)
////                + ",grantResults:" + Arrays.toString(grantResults) + ",length:" + grantResults.length);
//
//        if (grantResults.length == 1 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
//            LogUtil.i(TAG, "onRequestPermissionsResult PERMISSION_GRANTED");
//            permissionGrant.onPermissionGranted(requestCode);
//
//        } else {
//            LogUtil.i(TAG, "onRequestPermissionsResult PERMISSION NOT GRANTED");
//            String[] permissionsHint = activity.getResources().getStringArray(R.array.permissions);
//            openSettingActivity(activity, "Result111" + permissionsHint[requestCode]);
//        }
//
//    }

    private static void openSettingActivity(final Activity activity, String message) {

        showMessageOKCancel(activity, message, (dialog, which) -> {
            Intent intent = new Intent();
            intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
            LogUtil.d(TAG, "getPackageName(): " + activity.getPackageName());
            Uri uri = Uri.fromParts("package", activity.getPackageName(), null);
            intent.setData(uri);
            activity.startActivity(intent);
        });
    }


    /**
     * @param activity          Activity of the caller.
     * @param isShouldRationale true: return no granted and shouldShowRequestPermissionRationale permissions, false:return no granted and !shouldShowRequestPermissionRationale
     * @return permission list
     */
    public static ArrayList<String> getNoGrantedPermission(Activity activity, boolean isShouldRationale) {

        ArrayList<String> permissions = new ArrayList<>();

        for (String requestPermission : requestPermissions) {
            // Check self permission
            int checkSelfPermission;
            try {
                checkSelfPermission = ActivityCompat.checkSelfPermission(activity, requestPermission);
            } catch (RuntimeException e) {
                Toast.makeText(activity, "please open those permission", Toast.LENGTH_SHORT)
                        .show();
                LogUtil.e(TAG, "RuntimeException:" + e.getMessage());
                return null;
            }

            if (checkSelfPermission != PackageManager.PERMISSION_GRANTED) {
                LogUtil.i(TAG, "getNoGrantedPermission ActivityCompat.checkSelfPermission != PackageManager.PERMISSION_GRANTED:" + requestPermission);

                if (ActivityCompat.shouldShowRequestPermissionRationale(activity, requestPermission)) {
                    LogUtil.d(TAG, "shouldShowRequestPermissionRationale if");
                    if (isShouldRationale) {
                        permissions.add(requestPermission);
                    }

                } else {

                    if (!isShouldRationale) {
                        permissions.add(requestPermission);
                    }
                    LogUtil.d(TAG, "shouldShowRequestPermissionRationale else");
                }

            }
        }

        return permissions;
    }

}
