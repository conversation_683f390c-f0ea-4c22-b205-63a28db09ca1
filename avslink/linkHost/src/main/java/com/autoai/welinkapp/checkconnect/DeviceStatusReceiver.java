package com.autoai.welinkapp.checkconnect;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.util.Log;

import com.autoai.common.util.LogUtil;
import com.autoai.common.util.ProcessUtil;
import com.autoai.welinkapp.datatransfer.DataTransfer;

/**
 * 该广播Receiver是针对于连接状态的一个消息中转组件，监听的消息
 */
public class DeviceStatusReceiver extends BroadcastReceiver {
    private static final String TAG = "WL_DRIVER";

    /**
     * 所有 AOA 相关的操作日志
     */
    private static final String TAG_AOA = ".WL_AOA";

    /**
     * 所有 EAP 相关的操作日志
     */
    private static final String TAG_EAP = ".WL_EAP";

    private final Context mContext;
    private final DataTransfer mDataTransfer;
    private final DeviceStatusListener mListener;

    public DeviceStatusReceiver(Context context, DeviceStatusListener listener) {
        LogUtil.d(TAG, "DeviceStatusReceiver::init");
        mContext = context;
        mListener = listener;
        //--> fixme: 是否重复
        mDataTransfer = new DataTransfer();
    }


    /**
     * 开启互联状态监听
     */
    @SuppressLint("UnspecifiedRegisterReceiverFlag")
    public void registerReceiver() {
        LogUtil.d(TAG, "DeviceStatusReceiver::registerReceiver start");
        IntentFilter filter = new IntentFilter();
        filter.addAction(CommonData.DEVICE_IN_MSG);
        filter.addAction(CommonData.DEVICE_OUT_MSG);
        filter.addAction(CommonData.DEVICE_CONNECTING);
        mContext.registerReceiver(this, filter,Context.RECEIVER_NOT_EXPORTED);
        LogUtil.d(TAG, "DeviceStatusReceiver::registerReceiver complete");
    }

    public void unregisterReceiver() {
        LogUtil.d(TAG, "DeviceStatusReceiver::unregisterReceiver");
        mContext.unregisterReceiver(this);
    }

    private void filterDeviceInMsg(int type, String ip) {
        String deviceTypeStr = getDeviceTypeString(type);
        LogUtil.d(TAG, "DeviceStatusReceiver::filterDeviceInMsg <--- type=" + type + "(" + deviceTypeStr + "), ip=" + ip);
        if (CommonData.iCurrentConnectStatus != CommonData.CONNECT_STATUS_IN) {
            if (CommonData.DEVICE_TYPE_AOA == type) {
                LogUtil.d(TAG + TAG_AOA, "DeviceStatusReceiver::filterDeviceInMsg AOA device in, start connect");
                mDataTransfer.startConnect(ConnectManager.DEVICE_TYPE_AOA);
            } else if (CommonData.DEVICE_TYPE_EAP == type) {
                LogUtil.d(TAG + TAG_EAP, "DeviceStatusReceiver::filterDeviceInMsg EAP device in, start connect");
                mDataTransfer.startConnect(ConnectManager.DEVICE_TYPE_EAP);
            } else if (CommonData.DEVICE_TYPE_IDB == type) {
                LogUtil.d(TAG, "DeviceStatusReceiver::filterDeviceInMsg IDB device in, start connect");
                mDataTransfer.startConnect(ConnectManager.DEVICE_TYPE_IDB);
            } else {
                LogUtil.d(TAG, "DeviceStatusReceiver::filterDeviceInMsg Wireless device in, start connect");
            }
            if (mListener != null) {
                LogUtil.i(TAG, "DeviceStatusReceiver::filterDeviceInMsg notify listener onDeviceIn, type=" + type);
                mListener.onDeviceIn(type, ip);
            }
            CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_IN;
            CommonData.iCurrentConnectType = type;
            LogUtil.d(TAG, "DeviceStatusReceiver::filterDeviceInMsg status updated: currentType=" + type + ", status=IN");
        } else if (CommonData.iCurrentConnectType != type) {
            LogUtil.d(TAG, "DeviceStatusReceiver::filterDeviceInMsg device already connected, new type=" + type + ", current=" + CommonData.iCurrentConnectType);
            //--> fixme：@zhenggc 留意
//            CommonData.iNeedConnectType = type;
//            CommonData.strNeedConnectIp = ip;
        } else {
            LogUtil.d(TAG, "DeviceStatusReceiver::filterDeviceInMsg device in again, type=" + type);
        }
    }

    private void filterDeviceOutMsg(int type) {
        String deviceTypeStr = getDeviceTypeString(type);
        LogUtil.i(TAG,"DeviceStatusReceiver::filterDeviceOutMsg <--- type=" + type + "(" + deviceTypeStr + ")");
        if (CommonData.iCurrentConnectStatus != CommonData.CONNECT_STATUS_OUT) {
            if (CommonData.iCurrentConnectType == type) {
                if (CommonData.DEVICE_TYPE_AOA == type) {
                    LogUtil.d(TAG + TAG_AOA, "DeviceStatusReceiver::filterDeviceOutMsg AOA device out, stop connect");
                    mDataTransfer.stopConnect(ConnectManager.DEVICE_TYPE_AOA);
                } else if (CommonData.DEVICE_TYPE_EAP == type) {
                    LogUtil.d(TAG + TAG_EAP, "DeviceStatusReceiver::filterDeviceOutMsg EAP device out, stop connect");
                    mDataTransfer.stopConnect(ConnectManager.DEVICE_TYPE_EAP);
                } else if (CommonData.DEVICE_TYPE_IDB == type) {
                    LogUtil.d(TAG, "DeviceStatusReceiver::filterDeviceOutMsg IDB device out, stop connect");
                    mDataTransfer.stopConnect(ConnectManager.DEVICE_TYPE_IDB);
                } else {
                    LogUtil.d(TAG, "DeviceStatusReceiver::filterDeviceOutMsg Wireless device out, stop connect");
                }
                if (mListener != null) {
                    LogUtil.i(TAG, "DeviceStatusReceiver::filterDeviceOutMsg notify listener onDeviceOut, type=" + type);
                    mListener.onDeviceOut(type);
                }
                LogUtil.d(TAG, "DeviceStatusReceiver::filterDeviceOutMsg reset connect status");
                CommonData.resetConnectStatus();
              /*  if (CommonData.iNeedConnectType != CommonData.DEVICE_TYPE_NONE) {
                    checkOtherConnect(CommonData.iNeedConnectType);
                }*/
            } else {
                LogUtil.e(TAG, "DeviceStatusReceiver::filterDeviceOutMsg type mismatch: current=" + CommonData.iCurrentConnectType + ", received=" + type);
                CommonData.iNeedConnectType = CommonData.DEVICE_TYPE_NONE;
                CommonData.strNeedConnectIp = null;
            }
        } else {
            LogUtil.e(TAG, "DeviceStatusReceiver::filterDeviceOutMsg already disconnected, type=" + type);
        }
    }

//    private void checkOtherConnect(int type) {
//        if (mListener == null) {
//            LogUtil.e(TAG, "checkOtherConnect, mListener is null.");
//            return;
//        }
//        if (CommonData.DEVICE_TYPE_AOA == type) {
//            AOACheckDevice.getInstance().checkDevices();
//        } else if (CommonData.DEVICE_TYPE_IDB == type) {
//            IDBCheckDevice.getInstance().checkDevices();
//        } else if (CommonData.DEVICE_TYPE_EAP == type) {
//            LogUtil.d(TAG, "need connect EAP");
//            mDataTransfer.startConnect(ConnectManager.DEVICE_TYPE_EAP);
//            mListener.onDeviceIn(type, CommonData.strNeedConnectIp);
//            CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_IN;
//            CommonData.iCurrentConnectType = type;
//        } else {
//            LogUtil.i(TAG, "checkOtherConnect onDeviceIn, type: " + type);
//            mListener.onDeviceIn(type, CommonData.strNeedConnectIp);
//            CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_IN;
//            CommonData.iCurrentConnectType = type;
//        }
//        CommonData.iNeedConnectType = CommonData.DEVICE_TYPE_NONE;
//        CommonData.strNeedConnectIp = null;
//    }

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        ProcessUtil.printProcess(getClass());
        LogUtil.d(TAG, "DeviceStatusReceiver::onReceive <--- broadcast action=" + action);

        if (CommonData.DEVICE_IN_MSG.equals(action)) {
            int type = intent.getIntExtra(CommonData.MSG_KEY_TYPE, -1);
            String ip = intent.getStringExtra(CommonData.MSG_KEY_IP);
            String deviceTypeStr = getDeviceTypeString(type);
            LogUtil.d(TAG, "DeviceStatusReceiver::onReceive <--- DEVICE_IN_MSG: type=" + type + "(" + deviceTypeStr + "), ip=" + ip);
            filterDeviceInMsg(type, ip);
        } else if (CommonData.DEVICE_OUT_MSG.equals(action)) {
            int type = intent.getIntExtra(CommonData.MSG_KEY_TYPE, -1);
            String deviceTypeStr = getDeviceTypeString(type);
            LogUtil.d(TAG, "DeviceStatusReceiver::onReceive <--- DEVICE_OUT_MSG: type=" + type + "(" + deviceTypeStr + ")");
            filterDeviceOutMsg(type);
        } else if (CommonData.DEVICE_CONNECTING.equals(action)) {
            if (mListener != null) {
                int type = intent.getIntExtra(CommonData.MSG_KEY_TYPE, -1);
                String deviceTypeStr = getDeviceTypeString(type);
                LogUtil.i(TAG, "DeviceStatusReceiver::onReceive <--- DEVICE_CONNECTING: type=" + type + "(" + deviceTypeStr + ")");
                mListener.onDevicesConnecting(type);
            }
        } else {
            LogUtil.e(TAG, "DeviceStatusReceiver::onReceive <--- unknown broadcast: " + action);
        }
    }

    private String getDeviceTypeString(int type) {
        switch (type) {
            case CommonData.DEVICE_TYPE_AOA: return "AOA";
            case CommonData.DEVICE_TYPE_EAP: return "EAP";
            case CommonData.DEVICE_TYPE_IDB: return "IDB";
            case CommonData.DEVICE_TYPE_WIFI_ANDROID: return "WIFI_ANDROID";
            case CommonData.DEVICE_TYPE_WIFI_IOS: return "WIFI_IOS";
            case CommonData.DEVICE_TYPE_WIFI_HARMONY: return "WIFI_HARMONY";
            default: return "UNKNOWN";
        }
    }

}
