package com.autoai.welinkapp.checkconnect;

import android.content.Context;
import android.content.Intent;

import androidx.annotation.IntDef;

import com.autoai.common.util.LogUtil;
import com.autoai.common.util.ThreadUtils;
import com.autoai.welink.macro.WL_API_DEVICE_TYPE;

/**
 * Common data && common function class.
 */
@SuppressWarnings("unused")
public class CommonData {
    private static final String TAG = "WL_DRIVER";

    /**
     * 所有 AOA 相关的操作日志
     */
    public static final String TAG_AOA = ".WL_AOA";

    /**
     * 所有 EAP 相关的操作日志
     */
    public static final String TAG_EAP = ".WL_EAP";

    /**
     * Device connection status
     */
    public static final int CONNECT_STATUS_IN = 0;
    /**
     * 当前没有设别接入
     */
    public static final int CONNECT_STATUS_OUT = 1;
    /**
     * Wireless definition
     */
    public static final int WIRELESS_START_CHECK = 2;
    public static final int WIRELESS_STOP_CHECK = 3;
    public static final int WIRELESS_STOP_CONNECT = 4;

    public static final String START_CHECK_MSG = "com.autoai.welinkapp.service.START_CHECK";
    public static final String STOP_CHECK_MSG = "com.autoai.welinkapp.service.STOP_CHECK";
    public static final String STOP_CONNECT_MSG = "com.autoai.welinkapp.service.STOP_CONNECT";

    /**
     * Device type (AOA/EAP/WIFI_ANDROID/WIFI_IOS)
     */
    public static final int DEVICE_TYPE_NONE = WL_API_DEVICE_TYPE.OUT;

    public static final int DEVICE_TYPE_AOA = WL_API_DEVICE_TYPE.AOA;
    public static final int DEVICE_TYPE_IDB = WL_API_DEVICE_TYPE.IDB;
    public static final int DEVICE_TYPE_EAP = WL_API_DEVICE_TYPE.EAP;
    public static final int DEVICE_TYPE_WIFI_ANDROID = WL_API_DEVICE_TYPE.WLAN_ANDROID;
    public static final int DEVICE_TYPE_WIFI_IOS = WL_API_DEVICE_TYPE.WLAN_IPHONE;

    public static final int DEVICE_TYPE_WIFI_HARMONY  = WL_API_DEVICE_TYPE.WLAN_HARMONY;

    @IntDef({
            DEVICE_TYPE_NONE,
            DEVICE_TYPE_AOA,
            DEVICE_TYPE_IDB,
            DEVICE_TYPE_EAP,
            DEVICE_TYPE_WIFI_ANDROID,
            DEVICE_TYPE_WIFI_IOS,
            DEVICE_TYPE_WIFI_HARMONY
    })
    public @interface DeviceType {
        public static class Print{
            public static String toString(@DeviceType int status) {
                String str = "NA";
                switch (status){
                    case DEVICE_TYPE_NONE: str = "DEVICE_TYPE_NONE"; break;
                    case DEVICE_TYPE_AOA: str = "DEVICE_TYPE_AOA"; break;
                    case DEVICE_TYPE_IDB: str = "DEVICE_TYPE_IDB"; break;
                    case DEVICE_TYPE_EAP: str = "DEVICE_TYPE_EAP"; break;
                    case DEVICE_TYPE_WIFI_ANDROID: str = "DEVICE_TYPE_WIFI_ANDROID"; break;
                    case DEVICE_TYPE_WIFI_IOS: str = "DEVICE_TYPE_WIFI_IOS"; break;
                    case DEVICE_TYPE_WIFI_HARMONY: str = "DEVICE_TYPE_WIFI_HARMONY"; break;
                }
                return status + " : " + str;
            }
        }
    }

    public static final int LINK_STATUS_NONE = 0;            //未连接
    public static final int LINK_STATUS_FAIL = 3;           //连接失败
    public static final int LINK_STATUS_CONNECTING = 1;      //连接中
    public static final int LINK_STATUS_CONNECTED = 2;       //已连接

    public static final int LINK_STATUS_SCREEN_MIRRORING = 4;         //同意录屏权限 ，开始投屏

    @IntDef({
            LINK_STATUS_NONE,
            LINK_STATUS_FAIL,
            LINK_STATUS_CONNECTING,
            LINK_STATUS_CONNECTED,
            LINK_STATUS_SCREEN_MIRRORING
    })
    public @interface LinkStatus {
        class Print{
            public static String toString(@LinkStatus int status) {
                String str = "NA";
                switch (status){
                    case LINK_STATUS_NONE: str = "LINK_STATUS_NONE: 未连接"; break;
                    case LINK_STATUS_FAIL: str = "LINK_STATUS_FAIL: 连接失败"; break;
                    case LINK_STATUS_CONNECTING: str = "LINK_STATUS_CONNECTING: 连接中"; break;
                    case LINK_STATUS_CONNECTED: str = "LINK_STATUS_CONNECTED: 已连接"; break;
                    case LINK_STATUS_SCREEN_MIRRORING: str = "LINK_STATUS_SCREEN_MIRRORING: 同意录屏权限，开始投屏"; break;
                }
                return status + " : " + str;
            }
        }
    }


    /**
     * Broadcast string definition
     */

    /**
     * 设备接入
     */
    public static final String DEVICE_IN_MSG = "com.wldriver.checkconnect.DEVICE_IN_MSG";

    /**
     * 设备断开
     */
    public static final String DEVICE_OUT_MSG = "com.wldriver.checkconnect.DEVICE_OUT_MSG";

    public static final String DEVICE_CONNECTING = "com.autoai.checkconnect.DEVICE_CONNECTING";
    /**
     * Lookup key for broadcast
     */
    public static final String MSG_KEY_TYPE = "deviceType";
    public static final String MSG_KEY_IP = "deviceIp";

    /**
     * VenderID ProductID of iPhone
     */
    public static final int PID_IPHONE = 0x12A8;
    public static final int VID_IPHONE = 0x05AC;
    /**
     * 当前连接设备 状态 CONNECT_STATUS_IN | CONNECT_STATUS_OUT
     *
     */
    public static int iCurrentConnectStatus = CONNECT_STATUS_OUT;  // device_in, device_out

    /**
     * she tips：
     *  这个标记位注意与 {@link #iCurrentConnectStatus}进行区分：这个状态主要在如下场景会赋值
     *  举例：
     *  1.当前正在通过无线投屏，这时候有线（usb）接入，弹出，对该标记位标记
     *  2.当前应用层还没有触发开启互联{@link #isCheckingStart},但是通过系统广播识别到当前有usb
     *  此刻进行标记位记录，等状态位{@link #isCheckingStart}开启后，进行互联
     */
    public static int iNeedConnectType = DEVICE_TYPE_NONE;
    /**
     * 连接的类型
     */
    public static int iCurrentConnectType = DEVICE_TYPE_NONE;      // aoa, eap, wifi_android, wifi_ios

    /**
     * 是否开启usb设备的识别
     * Checking flag, don't check device when stopCheckConnect
     */
    public static boolean isCheckingStart = false;
    public static String strNeedConnectIp = null;
    /**
     * Usb device serial number
     */
    public static String strDeviceSerialNum = "";
    /**
     * Broadcast receiver package name
     */
    private static final String PACKAGE_NAME = "com.autoai.welinkapp";

    /**
     * Reset connect type and status flag
     */
    public static void resetConnectStatus() {
        LogUtil.e(TAG, "CommonData::resetConnectStatus");
        iCurrentConnectStatus = CONNECT_STATUS_OUT;
        iCurrentConnectType = DEVICE_TYPE_NONE;
    }

    /**
     * Send broadcast
     */
    public static void sendMsg(Context context, int msgId, int type, String ip) {
        ThreadUtils.postOnMainThread(() -> {
            switch (msgId) {
                case CONNECT_STATUS_IN:
                    LogUtil.d(TAG, "CommonData::sendMsg -->  device_in broadcast to app, type: " + type + ", ip: " + ip);
                    Intent intentIn = new Intent(DEVICE_IN_MSG);
//                intentIn.setPackage(PACKAGE_NAME);
                    intentIn.putExtra(MSG_KEY_TYPE, type);
                    intentIn.putExtra(MSG_KEY_IP, ip);
                    context.sendBroadcast(intentIn);
                    break;
                case CONNECT_STATUS_OUT:
                    LogUtil.d(TAG, "CommonData::sendMsg --> send device_out broadcast to app, type: " + type);
                    Intent intentOut = new Intent(DEVICE_OUT_MSG);
//                intentOut.setPackage(PACKAGE_NAME);
                    intentOut.putExtra(MSG_KEY_TYPE, type);
                    context.sendBroadcast(intentOut);
                    break;
                case WIRELESS_START_CHECK:
                    LogUtil.d(TAG, "CommonData::sendMsg --> CommonData::WIRELESS_START_CHECK");
                    Intent intentStart = new Intent(START_CHECK_MSG);
//                intentStart.setPackage(PACKAGE_NAME);
                    context.sendBroadcast(intentStart);
                    break;
                case WIRELESS_STOP_CHECK:
                    LogUtil.d(TAG, "CommonData::sendMsg --> WIRELESS_STOP_CHECK");
                    Intent intentStop = new Intent(STOP_CHECK_MSG);
                    context.sendBroadcast(intentStop);
                    break;
                case WIRELESS_STOP_CONNECT:
                    LogUtil.d(TAG, "CommonData::sendMsg --> WIRELESS_STOP_CONNECT");
                    Intent intentConnect = new Intent(STOP_CONNECT_MSG);
//                intentConnect.setPackage(PACKAGE_NAME);
                    context.sendBroadcast(intentConnect);
                    break;
                default:
                    LogUtil.e(TAG, "CommonData::sendMsg --> sendMsg unknown broadcast: " + msgId);
                    break;
            }
        });
    }
}
