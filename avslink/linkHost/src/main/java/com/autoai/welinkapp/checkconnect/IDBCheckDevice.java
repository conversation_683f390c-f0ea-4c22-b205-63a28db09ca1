package com.autoai.welinkapp.checkconnect;

import android.annotation.SuppressLint;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.hardware.usb.UsbConfiguration;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbInterface;
import android.hardware.usb.UsbManager;
import android.os.Build;

import com.autoai.common.util.LogUtil;
import com.autoai.common.util.ThreadUtils;
import com.autoai.welinkapp.eap.EapLink;
import com.autoai.welinkapp.idb.ADBUtil;
import com.autoai.welinkapp.idb.Configs;
import com.autoai.welinkapp.idb.IdbLink;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;

/**
 * Check for AOA device.
 */
@SuppressWarnings("WeakerAccess")
public class IDBCheckDevice {
    private static final String TAG = "WL_DRIVER";

    @SuppressLint("StaticFieldLeak")
    private static IDBCheckDevice mInstance = null;
    private Context mContext = null;
    private UsbManager mUsbManager = null;
    private UsbDevice mUsbDevice = null;

    private static final int STORAGE_INTERFACE_CLASS = 0x08;
    private static final int STORAGE_INTERFACE_SUBCLASS = 0x06;
    private static final int STORAGE_INTERFACE_PROTOCOL = 0x50;
    private static final int STORAGE_CONFIG_INTERFACE_COUNT = 1;

    private final static int VID_APPLE = 0x5ac;
    private final static int PID_RANGE_LOW = 0x1290;
    private final static int PID_RANGE_MAX = 0x12af;

    private final static String IDB_FORWARD_TCP = Configs.systemPath + "idb forward tcp:6806 tcp:6806 &";
    private final static String IDB_FORWARD_MSG_TCP = Configs.systemPath + "idb forward tcp:6804 tcp:6804 &";
    private final static String IDB_FORWARD_DATA_TCP = Configs.systemPath + "idb forward tcp:6819 tcp:6819 &";

    private final static String WL_PS_IDB = "ps -e| grep idb";

    private final static String WL_FILE_IDB = "idb";

    private final static String ADB_KILL_WELINK_SERVER = "kill -9 ";

    private IDBCheckDevice() {
    }

    public static IDBCheckDevice getInstance() {
        if (null == mInstance) {
            synchronized (IDBCheckDevice.class) {
                if (null == mInstance) {
                    mInstance = new IDBCheckDevice();
                }
            }
        }
        return mInstance;
    }

    public void init(Context context) {
        LogUtil.d(TAG, "AOA CheckDevice init");
        mContext = context;
        mUsbManager = (UsbManager) mContext.getSystemService(Context.USB_SERVICE);

    }

    public void deinit() {
        LogUtil.d(TAG, "AOA CheckDevice deinit");
        deinitUsbDevice();
    }

    /**
     *
     * @param device
     * @return
     *   true --> 表示当前 device 不符合条件，被滤掉
     *   false --> 没问题的device
     */
    private boolean filterDevice(UsbDevice device) {
        LogUtil.i(TAG, "IDBCheckDevice::filterDevice() --> device = " + device);
        if (null == device) {
            return true;
        }
        // serial number
        // she review tips:历史项目德赛对SerialNum进行过身份识别，目前无用，而且涉及到系统权限，android.permission.READ_PHONE_STATE
        String sn = device.getSerialNumber();
        if (sn == null) {
            LogUtil.e(TAG, "IDBCheckDevice::filterDevice() --> getSerialNumber null");
            return true;
        }
        LogUtil.d(TAG, "IDBCheckDevice::filterDevice() -->sn: " + sn);
        LogUtil.d(TAG, "IDBCheckDevice::filterDevice() -->CommonData sn: " + CommonData.strDeviceSerialNum);
        if (!"".equals(CommonData.strDeviceSerialNum) && !sn.equals(CommonData.strDeviceSerialNum)) {
            LogUtil.i(TAG, "IDBCheckDevice::filterDevice() -->Serial number is not same.");
            return true;
        }
        // usb storage device
        UsbInterface usbInterface = device.getInterface(0);
        UsbConfiguration usbConfig = device.getConfiguration(0);
        if ((STORAGE_INTERFACE_CLASS == usbInterface.getInterfaceClass()) &&
                (STORAGE_INTERFACE_SUBCLASS == usbInterface.getInterfaceSubclass()) &&
                (STORAGE_INTERFACE_PROTOCOL == usbInterface.getInterfaceProtocol()) &&
                (STORAGE_CONFIG_INTERFACE_COUNT == usbConfig.getInterfaceCount())) {
            LogUtil.i(TAG, "IDBCheckDevice::filterDevice() --> This device is usb storage device");
            return true;
        }
        return false;
    }

    public boolean checkDevices() {
        if (mContext == null || mUsbManager == null || !IdbLink.isIdbModel()) {
            LogUtil.e(TAG, "IDBCheckDevice::checkDevices() fail");
            return false;
        }

        if (!CommonData.isCheckingStart) {
            LogUtil.i(TAG, "IDBCheckDevice::checkDevices() --> Checking is not start, don't check usb device !!");
            CommonData.iNeedConnectType = CommonData.DEVICE_TYPE_IDB;
            return false;
        }

        if (CommonData.iCurrentConnectStatus != CommonData.CONNECT_STATUS_OUT) {
            LogUtil.i(TAG, "IDBCheckDevice::checkDevices() --> is connected now.");
            CommonData.iNeedConnectType = CommonData.DEVICE_TYPE_IDB;
            return false;
        }

        HashMap<String, UsbDevice> deviceList = mUsbManager.getDeviceList();
        LogUtil.i(TAG, "IDBCheckDevice::checkDevices() --> device count = " + deviceList.size());
        for (UsbDevice device : deviceList.values()) {
            if (device == null) {
                continue;
            }
            if (filterDevice(device)) {
                LogUtil.d(TAG, "IDBCheckDevice::checkDevices() --> ignore other device");
                continue;
            }
            if (mUsbManager.hasPermission(device)) {
                LogUtil.d(TAG, "IDBCheckDevice::checkDevices() --> device has Permission");
                if (initUsbDevice(device)) {
                    LogUtil.d(TAG, "init device success");
                    return true;
                }
            } else {
                LogUtil.d(TAG, "device requestPermission");
                PendingIntent mPendingIntent = PendingIntent.getBroadcast(mContext, 0,
                        new Intent(SystemUsbReceiver.ACTION_USB_PERMISSION),
                        Build.VERSION.SDK_INT > Build.VERSION_CODES.R ? PendingIntent.FLAG_IMMUTABLE :
                                0);
                mUsbManager.requestPermission(device, mPendingIntent);
            }
        }
        return false;
    }

    public boolean initUsbDevice(UsbDevice device) {
        deinitUsbDevice();
        if (device == null || mUsbManager == null) {
            LogUtil.e(TAG, "initUsbDevice fail1");
            return false;
        }
        if (!isIosDevice(device)) {
            LogUtil.i(TAG, "device is in not iPhone");
            return false;
        }
        mUsbDevice = device;
        ThreadUtils.postOnBackgroundThread(this::checkIosDevice);
        return true;
    }

    public boolean isCheckDeviceRunning() {
        return mUsbDevice != null;
    }

    public void deinitUsbDevice() {
        try {
            mUsbDevice = null;
        } catch (Exception ex) {
            LogUtil.e(TAG, "deinitUsbDevice fail");
        }
    }

    private boolean isIosDevice(UsbDevice device) {
        int vendorId = device.getVendorId();
        if (vendorId == VID_APPLE) {
            int productId = device.getProductId();
            return PID_RANGE_LOW <= productId && productId <= PID_RANGE_MAX;
        }
        return false;
    }

    private void checkIosDevice() {
        try {
            while (mUsbDevice != null) {
                killIDBService();
                boolean isSuccess = startIDBService();
                if (isSuccess) {
                    LogUtil.i(TAG,"checkIosDevice success");
                    CommonData.sendMsg(mContext, CommonData.CONNECT_STATUS_IN, CommonData.DEVICE_TYPE_IDB, null);
                    break;
                } else {
                    Thread.sleep(1000);
                }
            }
        } catch (Exception e) {
        }
    }

    private boolean startIDBService() {
        ArrayList<String> commonds = new ArrayList<>();
        commonds.add(IDB_FORWARD_MSG_TCP);
        commonds.add(IDB_FORWARD_TCP);
        commonds.add(IDB_FORWARD_DATA_TCP);

        try {
            ADBUtil.execute2(commonds);
        } catch (IOException e) {
            e.printStackTrace();
        }
        try {
            Thread.sleep(100);
        } catch (Exception e) {

        }
        commonds = new ArrayList<>();
        commonds.add(WL_PS_IDB);
        ArrayList<String> lines = ADBUtil.execute(commonds, false);
        LogUtil.i(TAG, "ps -e| grep idb = " + lines);
        return lines.size() >= 3;
    }


    private void killIDBService() {
        ArrayList<String> commonds = new ArrayList<>();
        commonds.add(WL_PS_IDB);
        ArrayList<String> lines = ADBUtil.execute(commonds, false);
        int size = lines.size();
        for (int i = 0; i < size; i++) {
            String str = lines.get(i);
            if (str.endsWith(WL_FILE_IDB)) {
                str = (str.trim().substring(str.indexOf(" "))).trim();
                String[] arrs = str.split(" ");
                try {
                    int pid = Integer.parseInt(arrs[0]);
                    ArrayList<String> killCmds = new ArrayList<>();
                    killCmds.add(ADB_KILL_WELINK_SERVER + pid);
                    ADBUtil.execute(killCmds, "", false);
                } catch (Exception e) {
                }
            }
        }
    }
}
