package com.autoai.welinkapp.eap;

import java.util.ArrayList;

/**
 * <AUTHOR>
 */
public interface EapDevice {
    void initMfiI2cPath();
    /**
     * eao初始化
     */
    void eapInit();

    /**
     * 设置usb模式
     * @param usbMode
     */
    boolean setUsbMode (boolean usbMode);
    /**
     * eap回收资源
     */
    void eapDeinit();

    /**
     * USB Attach
     *
     */
    void activeEap();

    /**
     * EAP_DETACH
     */
    void deactiveEap();

    /**
     * 启动eap弹窗app
     */
    void eapLaunchApp();

    /**
     * 注册eap连接状态回调
     *
     * @param linkEapListener eap连接状态回调
     */
    void linkDeviceCallbackRegister(OnLinkEapListener linkEapListener);

    /**
     * 移除回调
     */
    void unLinkDeviceCallbackRegister();

    /**
     * 打开eap数据通道
     *
     * @return 打开成功返回true，否则false
     */
    boolean eapBulkOpen();

    /**
     * 关闭eap数据通道
     */
    void eapBulkClose();

    /**
     * 读取eap数据
     *
     * buffer 接收数据的数据buffer
     * @param len    数据长度
     * @return 返回读取到的数据
     */
    byte[] eapBulkRead(int len);

    /**
     * 写入eap数据
     *
     * @param buffer 被写入的数据buffer
     * @param len    数据长度
     * @return 返回写入的数据长度
     */
    int eapBulkWrite(byte[] buffer, int len);

    /**
     *
     *  获取当前eap 连接状态
     * @return
     */
    int getEapStatus();

    /**
     * 重置翻转 为host模式
     * @return
     */
    boolean resetUsb();
}
