package com.autoai.welinkapp.idb;

import android.content.Context;
import android.os.Environment;


public final class VarHelper
{
	private  static String ADB = Configs.adbName+" -s ";
	private final static String ADB_SU = "su";
	private final static String ADB_CHMOD = "chmod";
	private final static String ADB_ID = "id";
	private final static String ADB_EXIT = "exit";
	//普通命令
//	private final static String ADB_DEVICES = "/data/local/tmp/adb-wd devices";
//	private final static String ADB_KILL_SERVER = "/data/local/tmp/adb-wd kill-server";
//	private final static String ADB_START_SERVER = "/data/local/tmp/adb-wd start-server";
	//大众命令
	private  static String ADB_DEVICES = Configs.adbName+" devices";
	private  static String ADB_KILL_SERVER = Configs.adbName+" kill-server";
	private  static String ADB_START_SERVER = Configs.adbName+" start-server";
	private final static String ADB_BUSYBOX_IFCONFIG = "busybox ifconfig";
	//aoa命令
//	private final static String AOA_DEVICES = "/system/bin/odb devices";
	private final static String AOA_DEVICES = "/system/bin/wlClient devices";
//	private final static String AOA_VERSION = "odb version";
	private final static String AOA_VERSION = "wlClient version";
//	private final static String AOA_NAME_AOA = "odb ";
	private static String AOA_NAME_AOA = "wlClient ";
	private final static String AOA_PACKET_SIZE = "setpacketsize ";
	private final static String AOA_SWITCH = "switch ";
	
	private final static String AOA_DEVICES_ON_MARK = "QLXBBBAOA123456	device";
	private final static String AOA_DEVICES_OFF_MARK = "QLXBBBAOA123456	unauthorized";
	private final static String AOA_DEVICES_MARK = "QLXBBBAOA123456";

	private final static String ADB_SHELL = "shell";
	private final static String ADB_PUSH = "push";
	private final static String ADB_PULL = "pull";
	private final static String ADB_REMOVE = "rm";
	private final static String ADB_GETEVENT_TOUCH = "shell getevent | grep 0035";
	private final static String ADB_INPUT_KEYEVENT = "shell input keyevent ";
	private final static String ADB_INPUT_TAP = "shell input tap ";
	private final static String ADB_SENDEVENT = "shell sendevent ";
	private final static String ADB_FORWARD_TCP = "forward tcp:";
	private final static String ADB_FORWARD_REMOVE = "forward --remove-all";
	private final static String ADB_PS = " shell ps";
	private final static String ADB_PS_GREP_WELINK = "shell ps | grep welink";
	private final static String ADB_START_SERVICE = "shell /data/local/tmp/welink &";
	private final static String ADB_AM_START = "shell am start -n ";
//	private final static String ADB_AM_START = "shell am start -n com.wedrive.welink.gw.launcher/com.mapbar.wedrive.launcher.MainActivity";
	private final static String ADB_GETPROP_SDK = "shell getprop ro.build.version.sdk";
	private final static String ADB_GET_VERSION = "shell getprop ro.build.version.release";
	private final static String ADB_DUMPSYS_ACTIVITY = "shell dumpsys activity | grep mFocusedActivity";
	private final static String ADB_CD_PATH = "cd /data/local/tmp";
	private final static String ADB_INSTALL = "install -r";
	private final static String ADB_UNINSTALL = "uninstall";

	private final static String ADB_PS_GREP = "ps | grep ";
	private final static String ADB_NAME_EC= "adb-ec";
	private final static String ADB_NAME_BDCL = "bdcl";
	private final static String ADB_NAME_ADB= "adb";
	private final static String ADB_NAME_WELINK= "welink";
	private final static String ADB_NAME_ADB_WL= "adb-wl";

	private final static String ADB_SYSTEM_BIN= "/system/bin/";
	private final static String ADB_SYSTEM_XBIN= "/system/xbin/";
	private final static String ADB_SYSTEM_SBIN= "/sbin/";

//	private final static String ADB_CHMOD_SERVICE = "chmod 777 /data/local/tmp/welink";
	private final static String ADB_CHMOD_SERVICE = "chmod 777 /data/local/tmp/wlCmd";
	private final static String ADB_GET_SERIALNO = "get-serialno";
	private final static String ADB_SENDMSG_FOR_MM = "shell am start -n com.tencent.mm/com.tencent.mm.ui.LauncherUI";
	private final static String ADB_INPUT_TEXT = "shell input text ";
//	private final static String ADB_FIND = "shell busybox find /data/local/tmp -name welink";
	private final static String ADB_FIND = "shell ls /data/local/tmp/wlCmd";
	private final static String ADB_BACK = "shell input keyevent 4 ";
	private final static String ADB_WELINK_SERVER = "ps | grep welink";
	private final static String ADB_KILL_WELINK_SERVER = "kill -9 ";

	private final static String ADB_DIANCHI = "shell dumpsys battery | grep level";
	private final static String ADB_DIANCHIVOLTAGE = "shell dumpsys battery | grep voltage";
	private final static String ADB_PHONE_MODEL = " shell getprop ro.product.model";

	private final static String COMMOND_ROOT = "/system/bin/sh";
	private final static String COMMOND_EXIT = "\nexit\n";
//	private final static String MTK_JPEG_CHECKFILE = "cat myfile\n";
//	private final static String MTK_MODEL1 = "model1";
//	private final static String MTK_MODEL2 = "model2";
	private final static String MTK_JPEG_CHECKFILE = "cat /sys/chipid/chip\n";
//	private final static String MTK_MODEL1 = "AC832X-MXFI";
//	private final static String MTK_MODEL2 = "AC822X-MXFI";
	private final static String MTK_MODEL="AC";
	private final static String WELINK_UNAVAIL = "WeLink could not be used in this device.";

	private final static String CHMOD_IDB = "chmod 777 "+Configs.systemPath+"idb";
	private final static String CHMOD_IOSHELPER = "chmod 777 "+Configs.systemPath+"iosHelper";
//	private final static String ADB_CHECK_VIEWSERVER = "shell service call window 3";
//	private final static String ADB_START_VIEWSERVER = "shell service call window 1 i32 4939";
//	private final static String ADB_STOP_VIEWSERVER = "shell service call window 2 i32 4939";

	private final static String IDB_SERVICE = Configs.systemPath+"iosHelper";
	private final static String IDB_FORWARD_TCP = Configs.systemPath+"idb forward tcp:6806 tcp:6806 &";
	private final static String IDB_FORWARD_MSG_TCP = Configs.systemPath+"idb forward tcp:6804 tcp:6804 &";
	private final static String IDB_FORWARD_DATA_TCP = Configs.systemPath+"idb forward tcp:6819 tcp:6819 &";
//	private final static String IDB_SERVICE = "./iosHelper &";
//	private final static String IDB_FORWARD_TCP = "./idb forward tcp:6806 tcp:6806 &";

	private  static String WL_MARK_ADB = Configs.adbName;
	private final static String WL_MARK_TCP = "tcp:";
	private final static String WL_MARK_PATH = "/data/local/tmp";
//	private final static String WL_MARK_SERVICE = "/data/local/tmp/welink";
	private final static String WL_MARK_SERVICE = "/data/local/tmp/wlCmd";
	private final static String WL_MARK_DEVICE = "device";
	private final static String WL_MARK_UID = "uid=0";
//	private final static String WL_MARK_SERVICE_STARTED = "PORT";
	private final static String WL_MARK_SERVICE_STARTED = "CmdServer start!";
	private final static String WL_MARK_SERVICE_VERSION = "WeLink";
	private final static String WL_MARK_SERVICE_SCREEN = "SCREEN";
	private final static String WL_MARK_ACTIVITY_ERROR = "Error: Activity";
	private final static String WL_MARK_SUCCESS = "Success";
	private final static String WL_MARK_TOUCH_EVENTID = "0035";
	private final static String WL_MARK_TOUCH_X = " 3 53 ";
	private final static String WL_MARK_TOUCH_Y = " 3 54 ";
	private final static String WL_MARK_TOUCH_DOWN = " 1 330 1";
	private final static String WL_MARK_TOUCH_UP = " 1 330 0";
	private final static String WL_MARK_TOUCH_FLUSH = " 0 0 0";
	private final static String WL_MARK_EMULATOR = "emulator";
	private final static String WL_MARK_CHECK = "CHECK 6806";
	private final static String WL_MARK_DEVICEINFO = "DEVICEINFO 6806";
	private final static String WL_MARK_AVAIL_ID = "Coolpad8297-0e15e457,Coolpad7295-9b4057a1";
	private final static String WL_MARK_WIFI_ADB_PORT = ":5555";
	private final static String WL_MARK_SDK19 = "4.4.2";
	private final static String WL_MARK_SDK19_STR = "19_4_4_2";
	private final static String WL_MARK_ERROR = "Error";
	private final static String WL_MARK_SYSTEM_START = "the system running";
	private final static String WL_MARK_USER = "u0_";
	private final static String WL_MARK_IAP2_END = "/64Scope:Link";
	private final static String WL_MARK_IAP2_ADDRESS = "inet6addr:";
	private final static String WL_MARK_IAP2_WLAN = "wlan0";
	private final static String WL_MARK_IAP2_USB = "usb0";

	private String WL_SERVER_IP = "localhost";
//	private final static String WL_FILE_PATH = "welink.msz";
	private final static String WL_FILE_PATH = "wlCmd";
	private final static String WD_FILE_PATH = "WeDrive.apk";
	private final static String WD_APPSTORE_FILE_PATH = "WeDrive_AppStore.apk";


	private final static String WL_FILE_IDB_PATH = "idb";
	private final static String WL_FILE_IOSHelper_PATH = "iosHelper";
	private final static String WL_DIR_CONFIG_PATH = "config";

	private final static String WL_PS_IOS_Helper = "ps | grep "+Configs.systemPath+"iosHelper";
	private final static String WL_PS_IDB = "ps -e| grep idb";
//	private final static String WL_PS_IDB = "ps | grep "+Configs.systemPath+"idb";
	//海马车机9.0
//	private final static String WL_PS_IDB = "ps -ef | grep idb";
//	private final static String WL_FILE_IDB = Configs.systemPath+"idb";
	private final static String WL_FILE_IDB = "idb";

	private final static String  ACTIVATION_CODES_PATH=Environment.getExternalStorageDirectory()+ "/mapbar/welink";
	private final static String  ACTIVATION_CODES_NAME="/activation_codes.dat";
	
	private final static String ADB_GET_APP_PKGNAME="shell dumpsys activity top | grep ACTIVITY";
	private String device;
	private int aoaChannelPort = 6822;
	private int channelPort = 6805;
	private int phonePort = 6802;
//	private int dataChannelPort = 6821;
	private int dataChannelPort = 6819;
	private int touchPort = 6820;
	private int voicePort=6821;
	private int carDataPort=6831;
	private int pcmDataPort=6833;
	private int phoneWidth = 1280;
	private int phoneHeight = 720;
//	private int viewServerPort =4939;
//	private int phoneWidth = 667;
//	private int phoneHeight = 375;
	//WiFi版本中设置使用
	private int andoridChannelPort = 6805;
	private int andoridPhonePort = 6802;
	private int iosChannelPort = 6804;
	private int iosPhonePort = 6806;
	
	private int androidCarDataPort=6831;//车身数据通道
	private int iosCarDataPort=6832;
	
	private int androidPcmDataPort=6833;//PCM数据通道
	private int iosPcmDataPort=6834;


	private int androidVoiceDataPort=6821;

	private int wifiUdpPort=6840;//wifi版本udp广播端口


	private boolean isAvailApp = true;

	private static VarHelper mVarHelper;

	/*
	private final static String mSignKey = "ce187ed67e05c2d8879bf66bbfdfc8b9,3f6ef23afd30cecdbcfdb439509c8c33,98310fac15c735901a6a45c987ef7eaf,9734e614b74ba2f237aed92315c0222a,66be62802de2c0755e59f67b94c95961,5f9c3f264115f7957d1d91da4e778486";

	private boolean isAvailApp(Context context)
	{
		String tm = getSignatureFingerprint(context.getPackageManager(), context.getPackageName());
//		Log.e("00000", "tm=" + tm);
		if(tm != null && mSignKey.indexOf(tm) != -1){
			return true;
		}
		return false;
	}

	private String getSignatureFingerprint(PackageManager pm, String packageName)
	{
		try
		{
			ApplicationInfo ai = pm.getApplicationInfo(packageName, 0);
			if(ai == null)
				return null;
			if((ai.flags & 0x1) != 0)
				return "SYSTEM";
			PackageInfo pi = pm.getPackageInfo(packageName, 64);
			if((pi == null) || (pi.signatures == null)
					|| (pi.signatures.length == 0)
					|| (pi.signatures[0] == null))
			{
				return null;
			}
			byte[] signature = pi.signatures[0].toByteArray();
			MessageDigest md = MessageDigest.getInstance("MD5");
			if(md == null)
				return null;
			byte[] digest = md.digest(signature);
			if(digest == null)
				return null;
			return toHex(digest);
		}
		catch(PackageManager.NameNotFoundException e)
		{
			return null;
		}
		catch(NoSuchAlgorithmException e)
		{
		}
		return null;
	}

	private String toHex(byte[] bytes)
	{
		StringBuffer sb = new StringBuffer(bytes.length * 2);
		for(byte b : bytes)
		{
			sb.append(String.format("%02x", new Object[]{Byte.valueOf(b)}));
		}
		return sb.toString();
	}
	*/

	private VarHelper(Context context)
	{
//		isAvailApp = isAvailApp(context);
//		Log.e("00000", "isAvailApp="+isAvailApp);
	}

	public final static VarHelper getInstance(Context context)
	{
		if(mVarHelper == null)
			mVarHelper = new VarHelper(context);
		return mVarHelper;
	}

	public final String getVariable(int flag)
	{
		if(!isAvailApp)
			return "about:blank";
		return "";
	}

	public final String getDevice() {
		if(!isAvailApp)
			return "";
		return device;
	}

	public final void setDevice(String device) {
		if(!isAvailApp)
			return;
		this.device = device;
	}

	public final int getChannelPort() {
		if(!isAvailApp)
			return 0;
		return channelPort;
	}

	public final void setChannelPort(int channelPort) {
		if(!isAvailApp)
			return;
		this.channelPort = channelPort;
	}

	public final int getPhonePort() {
		if(!isAvailApp)
			return 0;
		return phonePort;
	}

	public final void setPhonePort(int phonePort) {
		if(!isAvailApp)
			return;
		this.phonePort = phonePort;
	}
	
	public final int getDataChannelPort() {
		if(!isAvailApp)
			return 0;
		return dataChannelPort;
	}

	public final void setDataChannelPort(int dataChannelPort) {
		if(!isAvailApp)
			return;
		this.dataChannelPort = dataChannelPort;
	}

	public final int getTouchPort() {
		if(!isAvailApp)
			return 0;
		return touchPort;
	}

	public final int getPhoneWidth() {
		if(!isAvailApp)
			return 0;
		return phoneWidth;
	}

	public final void setPhoneWidth(int phoneWidth) {
		if(!isAvailApp)
			return;
		this.phoneWidth = phoneWidth;
	}

	public final int getPhoneHeight() {
		if(!isAvailApp)
			return 0;
		return phoneHeight;
	}

	public final void setPhoneHeight(int phoneHeight) {
		if(!isAvailApp)
			return;
		this.phoneHeight = phoneHeight;
	}

	public final String getAdb() {
		if(!isAvailApp)
			return "";
		return ADB;
	}

	public final String getAdbSu() {
		if(!isAvailApp)
			return "";
		return ADB_SU;
	}

	public final String getAdbChmod() {
		if(!isAvailApp)
			return "";
		return ADB_CHMOD;
	}

	public final String getAdbId() {
		if(!isAvailApp)
			return "";
		return ADB_ID;
	}

	public final String getAdbExit() {
		if(!isAvailApp)
			return "";
		return ADB_EXIT;
	}

	public final String getAdbDevices() {
		if(!isAvailApp)
			return "";
		return ADB_DEVICES;
	}

	public final String getAoaDevices() {
		if(!isAvailApp)
			return "";
		return AOA_DEVICES;
	}

	public final String getAdbKillServer() {
		if(!isAvailApp)
			return "";
		return ADB_KILL_SERVER;
	}

	public final String getAdbStartServer() {
		if(!isAvailApp)
			return "";
		return ADB_START_SERVER;
	}

	public final String getAdbShell() {
		if(!isAvailApp)
			return "";
		return ADB_SHELL;
	}

	public final String getAdbPush() {
		if(!isAvailApp)
			return "";
		return ADB_PUSH;
	}

	public final String getAdbGeteventTouch() {
		if(!isAvailApp)
			return "";
		return ADB_GETEVENT_TOUCH;
	}

	public final String getAdbInputKeyevent() {
		if(!isAvailApp)
			return "";
		return ADB_INPUT_KEYEVENT;
	}

	public final String getAdbInputTap() {
		if(!isAvailApp)
			return "";
		return ADB_INPUT_TAP;
	}

	public final String getAdbSendevent() {
		if(!isAvailApp)
			return "";
		return ADB_SENDEVENT;
	}

	public final String getAdbForwardTcp() {
		if(!isAvailApp)
			return "";
		return ADB_FORWARD_TCP;
	}

	public final String getAdbPsGrepWelink() {
		if(!isAvailApp)
			return "";
		return ADB_PS_GREP_WELINK;
	}

	public final String getAdbStartService() {
		if(!isAvailApp)
			return "";
		return ADB_START_SERVICE;
	}

	public final String getAdbAmStart() {
		if(!isAvailApp)
			return "";
		return ADB_AM_START;
	}

	public final String getAdbGetpropSdk() {
		if(!isAvailApp)
			return "";
		return ADB_GETPROP_SDK;
	}

	public final String getAdbDumpsysActivity() {
		if(!isAvailApp)
			return "";
		return ADB_DUMPSYS_ACTIVITY;
	}

	public final String getWlMarkAdb() {
		if(!isAvailApp)
			return "";
		return WL_MARK_ADB;
	}

	public final String getWlMarkTcp() {
		if(!isAvailApp)
			return "";
		return WL_MARK_TCP;
	}

	public final String getWlMarkService() {
		if(!isAvailApp)
			return "";
		return WL_MARK_SERVICE;
	}

	public final String getWlMarkDevice() {
		if(!isAvailApp)
			return "";
		return WL_MARK_DEVICE;
	}

	public final String getWlMarkUid() {
		if(!isAvailApp)
			return "";
		return WL_MARK_UID;
	}

	public final String getWlMarkServiceStarted() {
		if(!isAvailApp)
			return "";
		return WL_MARK_SERVICE_STARTED;
	}

	public final String getWlMarkServiceVersion() {
		if(!isAvailApp)
			return "";
		return WL_MARK_SERVICE_VERSION;
	}

	public final String getWlMarkServiceScreen() {
		if(!isAvailApp)
			return "";
		return WL_MARK_SERVICE_SCREEN;
	}

	public final String getWlMarkActivityError() {
		if(!isAvailApp)
			return "";
		return WL_MARK_ACTIVITY_ERROR;
	}

	public final String getWlMarkTouchEventid() {
		if(!isAvailApp)
			return "";
		return WL_MARK_TOUCH_EVENTID;
	}

	public final String getWlMarkTouchX() {
		if(!isAvailApp)
			return "";
		return WL_MARK_TOUCH_X;
	}

	public final String getWlMarkTouchY() {
		if(!isAvailApp)
			return "";
		return WL_MARK_TOUCH_Y;
	}

	public final String getWlMarkTouchDown() {
		if(!isAvailApp)
			return "";
		return WL_MARK_TOUCH_DOWN;
	}

	public final String getWlMarkTouchUp() {
		if(!isAvailApp)
			return "";
		return WL_MARK_TOUCH_UP;
	}

	public final String getWlMarkTouchFlush() {
		if(!isAvailApp)
			return "";
		return WL_MARK_TOUCH_FLUSH;
	}

	public final String getWlMarkEmulator() {
		if(!isAvailApp)
			return "";
		return WL_MARK_EMULATOR;
	}

	public final String getWlFilePath() {
		if(!isAvailApp)
			return "";
		return WL_FILE_PATH;
	}

	public final String getWdFilePath() {
		if(!isAvailApp)
			return "";
		return WD_FILE_PATH;
	}

	public final String getWlServerIp() {
		if(!isAvailApp)
			return "";
		return WL_SERVER_IP;
	}

	public final void setWlServerIp(String ip)
	{
		if(!isAvailApp)
			return;
		this.WL_SERVER_IP = ip;
	}

	public final String getMotionDown(int x, int y)
	{
		if(!isAvailApp)
			return "";
		return "motionevent "+x+" "+y+" down";
	}

	public final String getMotionMove(int x, int y)
	{
		if(!isAvailApp)
			return "";
		return "motionevent "+x+" "+y+" move";
	}

	public final String getMotionUp(int x, int y)
	{
		if(!isAvailApp)
			return "";
		return "motionevent "+x+" "+y+" up";
	}

	public final String getAdbCdPath() {
		if(!isAvailApp)
			return "";
		return ADB_CD_PATH;
	}

	public final String getIdbService() {
		if(!isAvailApp)
			return "";
		return IDB_SERVICE;
	}

	public final String getIdbForwardTcp() {
		if(!isAvailApp)
			return "";
		return IDB_FORWARD_TCP;
	}

	public final String getIdbForwardMsgTcp() {
		if(!isAvailApp)
			return "";
		return IDB_FORWARD_MSG_TCP;
	}

	public final String getIdbForwardDataTcp() {
		if(!isAvailApp)
			return "";
		return IDB_FORWARD_DATA_TCP;
	}

	public final String getWlMarkCheck() {
		if(!isAvailApp)
			return "";
		return WL_MARK_CHECK;
	}

	public final String getWlMarkDeviceinfo() {
		if(!isAvailApp)
			return "";
		return WL_MARK_DEVICEINFO;
	}

	public final String getAdbPsGrep() {
		if(!isAvailApp)
			return "";
		return ADB_PS_GREP;
	}

	public final String getAdbNameAdb() {
		if(!isAvailApp)
			return "";
		return ADB_NAME_ADB;
	}

	public final String getAdbNameEc() {
		if(!isAvailApp)
			return "";
		return ADB_NAME_EC;
	}

	public final String getAdbNameBdcl() {
		if(!isAvailApp)
			return "";
		return ADB_NAME_BDCL;
	}

	public final String getAdbNameWelink() {
		if(!isAvailApp)
			return "";
		return ADB_NAME_WELINK;
	}
	public final String getAdbNameAdbWl() {
		if(!isAvailApp)
			return "";
		return ADB_NAME_ADB_WL;
	}

	public final String getAdbSystemBin() {
		if(!isAvailApp)
			return "";
		return ADB_SYSTEM_BIN;
	}

	public final String getAdbSystemXbin() {
		if(!isAvailApp)
			return "";
		return ADB_SYSTEM_XBIN;
	}

	public final String getAdbSystemSbin() {
		if(!isAvailApp)
			return "";
		return ADB_SYSTEM_SBIN;
	}


	public static String getAdbUninstall() {
		return ADB_UNINSTALL;
	}

	public final String getAdbInstall() {
		if(!isAvailApp)
			return "";
		return ADB_INSTALL;
	}




	public final String getAdbChmodService() {
		if(!isAvailApp)
			return "";
		return ADB_CHMOD_SERVICE;
	}

	public final String getAdbGetSerialno() {
		if(!isAvailApp)
			return "";
		return ADB_GET_SERIALNO;
	}

	public final String getWlMarkAvailId() {
		if(!isAvailApp)
			return "";
		return WL_MARK_AVAIL_ID;
	}

	public final String getAdbSendmsgForMm() {
		if(!isAvailApp)
			return "";
		return ADB_SENDMSG_FOR_MM;
	}

	public final String getAdbInputText() {
		if(!isAvailApp)
			return "";
		return ADB_INPUT_TEXT;
	}

	public final String getWlMarkSuccess() {
		if(!isAvailApp)
			return "";
		return WL_MARK_SUCCESS;
	}

	public final String getAdbForwardRemove() {
		if(!isAvailApp)
			return "";
		return ADB_FORWARD_REMOVE;
	}

	public final String getWlMarkWifiAdbPort() {
		if(!isAvailApp)
			return "";
		return WL_MARK_WIFI_ADB_PORT;
	}

	public final String getAdbFind() {
		if(!isAvailApp)
			return "";
		return ADB_FIND;
	}

	public final String getWlMarkPath() {
		if(!isAvailApp)
			return "";
		return WL_MARK_PATH;
	}

	public final String getAdbPull() {
		if(!isAvailApp)
			return "";
		return ADB_PULL;
	}
	public final String getAdbRemove() {
		if(!isAvailApp)
			return "";
		return ADB_REMOVE;
	}
	public final String getAdbWelinkServer() {
		if(!isAvailApp)
			return "";
		return ADB_WELINK_SERVER;
	}
	public final String getAdbKillWelinkServer() {
		if(!isAvailApp)
			return "";
		return ADB_KILL_WELINK_SERVER;
	}
	public final String getAdbGetVersion() {
		if(!isAvailApp)
			return "";
		return ADB_GET_VERSION;
	}

	public final String getWlMarkSdk19() {
		if(!isAvailApp)
			return "";
		return WL_MARK_SDK19;
	}

	public final String getWlMarkSdk19Str() {
		if(!isAvailApp)
			return "";
		return WL_MARK_SDK19_STR;
	}


	public final String getADBBACK() {
		if (!isAvailApp)
			return "";
		return ADB_BACK;
	}
	public final String getDianchi(){
		if(!isAvailApp)
			return "";
		return ADB_DIANCHI;
	}
	public final String getDianchiVoltage(){
		if(!isAvailApp)
			return "";
		return ADB_DIANCHIVOLTAGE;
	}
//	public int getViewServerPort() {
//		if(!isAvailApp){
//			return 0;
//		}
//		return viewServerPort;
//	}

	public final String getWlFileIdbPath() {
		if(!isAvailApp)
			return "";
		return WL_FILE_IDB_PATH;
	}

	public final String getWlFileIoshelperPath() {
		if(!isAvailApp)
			return "";
		return WL_FILE_IOSHelper_PATH;
	}

	public final String getChmodIdb() {
		if(!isAvailApp)
			return "";
		return CHMOD_IDB;
	}

	public final String getChmodIoshelper() {
		if(!isAvailApp)
			return "";
		return CHMOD_IOSHELPER;
	}

	public final String getWlDirConfigPath() {
		if(!isAvailApp)
			return "";
		return WL_DIR_CONFIG_PATH;
	}

	public final String getWlPsIosHelper() {
		if(!isAvailApp)
			return "";
		return WL_PS_IOS_Helper;
	}

	public final String getWlPsIdb() {
		if(!isAvailApp)
			return "";
		return WL_PS_IDB;
	}

	public final String getWlFileIdb() {
		if(!isAvailApp)
			return "";
		return WL_FILE_IDB;
	}

//	public final String getAdbCheckViewserver() {
//		if(!isAvailApp)
//			return "";
//		return ADB_CHECK_VIEWSERVER;
//	}
//
//	public final String getAdbStartViewserver() {
//		if(!isAvailApp)
//			return "";
//		return ADB_START_VIEWSERVER;
//	}
//
//	public final String getAdbStopViewserver() {
//		if(!isAvailApp)
//			return "";
//		return ADB_STOP_VIEWSERVER;
//	}

	public final String getWdAppstoreFilePath() {
		if(!isAvailApp)
			return "";
		return WD_APPSTORE_FILE_PATH;
	}

	public final String getCommondRoot() {
		if(!isAvailApp)
			return "";
		return COMMOND_ROOT;
	}

	public final String getCommondExit() {
		if(!isAvailApp)
			return "";
		return COMMOND_EXIT;
	}

	public final String getMtkJpegCheckfile() {
		if(!isAvailApp)
			return "";
		return MTK_JPEG_CHECKFILE;
	}

	public final String getMtkModel() {
		if(!isAvailApp)
			return "";
		return MTK_MODEL;
	}


	public String getWelinkUnavail() {
		if(!isAvailApp)
			return "";
		return WELINK_UNAVAIL;
	}

	public final String getWlMarkError() {
		if(!isAvailApp)
			return "";
		return WL_MARK_ERROR;
	}

	public final String getWlMarkSystemStart() {
		if(!isAvailApp)
			return "";
		return WL_MARK_SYSTEM_START;
	}

	public final String getWlMarkUser() {
		if(!isAvailApp)
			return "";
		return WL_MARK_USER;
	}

	public final String getAdbPs() {
		if(!isAvailApp)
			return "";
		return ADB_PS;
	}
	public final String getActivationFilsePath(){
		if(!isAvailApp)
			return "";
		return ACTIVATION_CODES_PATH;
	}
	public final String getActivationFilseName(){
		if(!isAvailApp)
			return "";
		return ACTIVATION_CODES_NAME;
	}
	public final String getAppPackAgeName(){
		if(!isAvailApp)
			return "";
		return ADB_GET_APP_PKGNAME;
	}
	public final String getAdbPhoneModel() {
		if(!isAvailApp)
			return "";
		return ADB_PHONE_MODEL;
	}
	/**
	 * 获取ipv6 地址
	 * @return
	 */
	public final String getAbusyboxIfconfig() {
		if(!isAvailApp)
			return "";
		return ADB_BUSYBOX_IFCONFIG;
	}
	public final String getWlMarkIap2end() {
		if(!isAvailApp)
			return "";
		return WL_MARK_IAP2_END;
	}
	public final String getWlMarkIap2Address() {
		if(!isAvailApp)
			return "";
		return WL_MARK_IAP2_ADDRESS;
	}
	public final String getWlMarkIap2Waln() {
		if(!isAvailApp)
			return "";
		return WL_MARK_IAP2_WLAN;
	}
	public final String getWlMarkIap2Usb() {
		if(!isAvailApp)
			return "";
		return WL_MARK_IAP2_USB;
	}

	public  void resetAdbName(String adbName){
		if(!isAvailApp)
			return;
		ADB = adbName+" -s ";
		ADB_DEVICES = adbName+" devices";
		ADB_KILL_SERVER = adbName+" kill-server";
		ADB_START_SERVER = adbName+"  start-server";
		WL_MARK_ADB = adbName;
	}

	public final int getAoaChannelPort() {
		if(!isAvailApp)
			return 0;
		return aoaChannelPort;
	}

	public final void setAoaChannelPort(int aoaChannelPort) {
		if(!isAvailApp)
			return;
		this.aoaChannelPort = aoaChannelPort;
	}

	public void setAoaNameAoa(String name) {
		if(!isAvailApp)
			return ;
		AOA_NAME_AOA = name;
	}


	public final String getAoaNameAoa() {
		if(!isAvailApp)
			return "";
		return AOA_NAME_AOA;
	}

	public final String getAoaVersion() {
		if(!isAvailApp)
			return "";
		return AOA_VERSION;
	}

	public final String getAoaDevicesOnMark() {
		if(!isAvailApp)
			return "";
		return AOA_DEVICES_ON_MARK;
	}

	public final String getAoaDevicesOffMark() {
		if(!isAvailApp)
			return "";
		return AOA_DEVICES_OFF_MARK;
	}

	public final String getAoaDevicesMark() {
		if(!isAvailApp)
			return "";
		return AOA_DEVICES_MARK;
	}
	
	public final String getAoaPacketSize() {
		if(!isAvailApp)
			return "";
		return AOA_PACKET_SIZE;
	}

	public final String getAoaSwitch() {
		if(!isAvailApp)
			return "";
		return AOA_SWITCH;
	}

	public final int getAndroidChannelPort() {
		if(!isAvailApp)
			return 0;
		return andoridChannelPort;
	}

	public final int getAndroidPhonePort() {
		if(!isAvailApp)
			return 0;
		return andoridPhonePort;
	}


	public final int getIosChannelPort() {
		if(!isAvailApp)
			return 0;
		return iosChannelPort;
	}

	public final int getIosPhonePort() {
		if(!isAvailApp)
			return 0;
		return iosPhonePort;
	}
	public final void setCarChannelPort(int carDataPort) {
		if(!isAvailApp)
			return;
		this.carDataPort = carDataPort;
	}
	public final int getCarChannelPort() {
		if(!isAvailApp)
			return 0;
		return carDataPort;
	}

	public final void setPcmChannelPort(int pcmDataPort) {
		if(!isAvailApp)
			return;
		this.pcmDataPort = pcmDataPort;
	}
	public final int getPcmChannelPort() {
		if(!isAvailApp)
			return 0;
		return pcmDataPort;
	}

	public final int getAndroidCarDataChannelPort() {
		if(!isAvailApp)
			return 0;
		return androidCarDataPort;
	}
	public final int getAndroidPcmDataChannelPort() {
		if(!isAvailApp)
			return 0;
		return androidPcmDataPort;
	}
	public final int getIosCarDataChannelPort() {
		if(!isAvailApp)
			return 0;
		return iosCarDataPort;
	}
	public final int getIosPcmDataChannelPort() {
		if(!isAvailApp)
			return 0;
		return iosPcmDataPort;
	}
	public final int getAndroidVoiceDataChannelPort() {
		if(!isAvailApp)
			return 0;
		return androidVoiceDataPort;
	}
	public final int getIosVoiceDataChannelPort() {
		if(!isAvailApp)
			return 0;
		return dataChannelPort;
	}

	public final void setVoiceDataChannelPort(int voicePort) {
		if(!isAvailApp)
			return;
		this.voicePort = voicePort;
	}
	public final int getVoiceDataChannelPort() {
		if(!isAvailApp)
			return 0;
		return voicePort;
	}

	public final int getWifiUdpPort() {
		if(!isAvailApp)
			return 0;
		return wifiUdpPort;
	}

}
