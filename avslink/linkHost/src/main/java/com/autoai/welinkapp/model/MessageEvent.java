package com.autoai.welinkapp.model;

public class MessageEvent {
    public static final int SET_HIGHLIGHT_KEY = 1;
    public static final int RECEIVE_MESSAGE = 2;
    public static final int UPDATE_NAV_DATA = 3;
    public static final int STOP_NAVIGATE = 4;
    public static final int SHOW_RUNNING_PAGE = 5;
    public static final int GO_TO_MENU = 6;

    private final int mWhat;
    private final int mArg1;
    private final Object mObj;

    public MessageEvent(int what) {
        this(what, 0, null);
    }

    public MessageEvent(int what, int arg1) {
        this(what, arg1, null);
    }

    public MessageEvent(int what, Object obj) {
        this(what, 0, obj);
    }

    public MessageEvent(int what, int arg1, Object obj) {
        mWhat = what;
        mArg1 = arg1;
        mObj = obj;
    }

    public int getWhat() {
        return mWhat;
    }

    public int getArg1() {
        return mArg1;
    }

    public Object getObj() {
        return mObj;
    }

}
