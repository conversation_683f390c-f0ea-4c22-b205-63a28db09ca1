package com.autoai.welinkapp.checkconnect;

import android.content.Context;

import com.autoai.common.util.LogUtil;
import com.autoai.welinkapp.eap.EapLink;
import com.autoai.welinkapp.idb.IdbLink;
import com.autoai.welinkapp.model.PlatformAdaptor;
import com.autoai.welinkapp.model.SdkViewModel;
import com.autoai.welinkapp.model.UIResponder;

import java.util.logging.LogManager;

/**
 * Check connect Interface class.
 */
@SuppressWarnings("unused")
public class ConnectManager {
    private static final String TAG = "WL_DRIVER";

    /**
     * 所有 AOA 相关的操作日志
     */
    private static final String TAG_AOA = ".WL_AOA";

    /**
     * 所有 EAP 相关的操作日志
     */
    private static final String TAG_EAP = ".WL_EAP";

    private Context mContext = null;
    private DeviceStatusReceiver mStatusReceiver = null;
    private DeviceStatusListener mStatusListener = null;

    public static final int DEVICE_TYPE_NONE = 0;
    public static final int DEVICE_TYPE_AOA = 1;
    public static final int DEVICE_TYPE_EAP = 2;
    public static final int DEVICE_TYPE_WIFI_ANDROID = 3;
    public static final int DEVICE_TYPE_WIFI_IOS = 4;
    public static final int DEVICE_TYPE_IDB = 6;

    public static final int DEVICE_STATUS_NONE = 0;
    public static final int DEVICE_STATUS_CONNECTING = 1;
    public static final int DEVICE_STATUS_CONNECTED = 2;
    public static final int DEVICE_STATUS_FAIL = 3;

    /**
     * 互联业务逻辑初始化
     * 1）Android 有线AOA
     * 2）IOS 有线 idb|eap
     * 3）无线
     *
     * @param context
     * @param statusListener
     */
    public void init(Context context, DeviceStatusListener statusListener) {
        LogUtil.d(TAG, "ConnectManager::init() start");
        mContext = context;
        mStatusListener = statusListener;
//        ConnectAdapter.getInstance().init(mContext);
        LogUtil.d(TAG + TAG_AOA, "ConnectManager::init AOACheckDevice init start");
        AOACheckDevice.getInstance().init(mContext);
        if (IdbLink.isIdbModel())
            IDBCheckDevice.getInstance().init(mContext);
        if (EapLink.isEapModel()) {
            LogUtil.d(TAG + TAG_EAP, "ConnectManager::init EAPCheckDevice init start");
            EAPCheckDevice.getInstance().init(mContext);
        }
        //设备状态广播接收器
        LogUtil.d(TAG, "ConnectManager::init DeviceStatusReceiver register start");
        mStatusReceiver = new DeviceStatusReceiver(mContext, mStatusListener);
        mStatusReceiver.registerReceiver();
        LogUtil.d(TAG, "ConnectManager::init() complete");
    }



    public void deinit() {
        LogUtil.d(TAG + TAG_AOA, "ConnectManager::deinit AOACheckDevice deinit start");
        AOACheckDevice.getInstance().deinit();
        if (EapLink.isEapModel()) {
            LogUtil.d(TAG + TAG_EAP, "ConnectManager::deinit EAPCheckDevice deinit start");
            EAPCheckDevice.getInstance().deinit();
        }
        if (IdbLink.isIdbModel())
            IDBCheckDevice.getInstance().deinit();
//        ConnectAdapter.getInstance().deinit();

        if (null != mStatusReceiver) {
            LogUtil.d(TAG, "ConnectManager::deinit DeviceStatusReceiver unregister");
            mStatusReceiver.unregisterReceiver();
            mStatusReceiver = null;
        }

        mStatusListener = null;
        LogUtil.d(TAG, "ConnectManager::deinit complete");
    }

    /**
     *
     */
    public void startCheckConnect() {
        LogUtil.printStackTraceString(TAG, "ConnectManager::startCheckConnect");
        LogUtil.d(TAG, "ConnectManager::startCheckConnect needType=" + CommonData.iNeedConnectType +
                ", currentType=" + CommonData.iCurrentConnectType
        + " connectStatus: " + SdkViewModel.getInstance().connectStatus);
        //--> 开启有线设备搜索
        CommonData.isCheckingStart = true;

        if (CommonData.DEVICE_TYPE_AOA == CommonData.iNeedConnectType) {
            LogUtil.d(TAG + TAG_AOA, "ConnectManager::startCheckConnect AOA device check start");
            AOACheckDevice.getInstance().checkDevices();
        } else if (CommonData.DEVICE_TYPE_EAP == CommonData.iNeedConnectType) {
            LogUtil.d(TAG + TAG_EAP, "ConnectManager::startCheckConnect ---> EAP send CONNECT_STATUS_IN");
            CommonData.sendMsg(mContext, CommonData.CONNECT_STATUS_IN, CommonData.iNeedConnectType, null);
        } else if (CommonData.DEVICE_TYPE_IDB == CommonData.iNeedConnectType) {
            LogUtil.d(TAG, "ConnectManager::startCheckConnect IDB device check start");
            IDBCheckDevice.getInstance().checkDevices();
        } else if (CommonData.iNeedConnectType == CommonData.DEVICE_TYPE_NONE) {
            if (SdkViewModel.getInstance().connectStatus != PlatformAdaptor.CONNECT_STATE.CONNECTED
                    && SdkViewModel.getInstance().connectStatus != PlatformAdaptor.CONNECT_STATE.CONNECTING) {
                LogUtil.d(TAG, "ConnectManager::startCheckConnect resume devices, connectStatus=" + SdkViewModel.getInstance().connectStatus);
//             if(SdkViewModel.getInstance().connectStatus == 0){
                LogUtil.d(TAG + TAG_AOA, "ConnectManager::startCheckConnect AOACheckDevice resume");
                AOACheckDevice.getInstance().resume();
                LogUtil.d(TAG + TAG_EAP, "ConnectManager::startCheckConnect EAPCheckDevice resume");
                EAPCheckDevice.getInstance().resume();
            }
        }

        //--> 如果开启设备互联后，没有usb设备，那么开启无线连接扫描设备
        CommonData.iNeedConnectType = CommonData.DEVICE_TYPE_NONE;
        if (CommonData.iCurrentConnectType != CommonData.DEVICE_TYPE_WIFI_ANDROID &&
                CommonData.iCurrentConnectType != CommonData.DEVICE_TYPE_WIFI_IOS &&
                CommonData.iCurrentConnectType != CommonData.DEVICE_TYPE_WIFI_HARMONY) {
            startCheckWireless();
        }
    }

    public void stopCheckConnect() {
        LogUtil.d(TAG, "ConnectManager::stopCheckConnect iCurrentConnectType=" + CommonData.iCurrentConnectType);
        CommonData.isCheckingStart = false;
        if (CommonData.iCurrentConnectType != CommonData.DEVICE_TYPE_WIFI_ANDROID &&
                CommonData.iCurrentConnectType != CommonData.DEVICE_TYPE_WIFI_IOS &&
                CommonData.iCurrentConnectType != CommonData.DEVICE_TYPE_WIFI_HARMONY) {
            LogUtil.d(TAG, "ConnectManager::stopCheckConnect ---> stop wireless check");
            stopCheckWireless(STOP_BY_APP_REQUEST);
        }
    }

    /**
     * 设备连接状态变更
     *
     * @param type   设备类型, 取值范围 DEVICE_TYPE_*
     * @param status 连接状态, 取值范围 DEVICE_STATUS_*
     */
    public void connectStatusChange(int type, int status) {
        String deviceTypeStr = getDeviceTypeString(type);
        LogUtil.d(TAG, "ConnectManager::connectStatusChange <--- type=" + type + "(" + deviceTypeStr + "), status=" + status);

        if (type == DEVICE_TYPE_AOA) {
            LogUtil.d(TAG + TAG_AOA, "ConnectManager::connectStatusChange <--- AOA status=" + status);
        } else if (type == DEVICE_TYPE_EAP) {
            LogUtil.d(TAG + TAG_EAP, "ConnectManager::connectStatusChange <--- EAP status=" + status);
        }

        if (DEVICE_STATUS_FAIL == status || DEVICE_STATUS_NONE == status) {
//            ConnectAdapter.getInstance().setConnectStatus(type, DEVICE_STATUS_NONE);
            if (CommonData.DEVICE_TYPE_WIFI_HARMONY == type || CommonData.DEVICE_TYPE_WIFI_ANDROID == type || CommonData.DEVICE_TYPE_WIFI_IOS == type) {
                LogUtil.d(TAG, "ConnectManager::connectStatusChange wireless disconnect, stopCheckWireless");
                //wirelessDisconnect();
                stopCheckWireless(STOP_BY_CHANNEL_CONTROL_MSG);
            }
        } else if (DEVICE_STATUS_CONNECTED == status) {
//            ConnectAdapter.getInstance().setConnectStatus(type, DEVICE_STATUS_CONNECTED);
            LogUtil.d(TAG, "ConnectManager::connectStatusChange device connected successfully");
        } else {
            LogUtil.d(TAG, "ConnectManager::connectStatusChange other status=" + status);
        }
    }

    private String getDeviceTypeString(int type) {
        switch (type) {
            case DEVICE_TYPE_AOA: return "AOA";
            case DEVICE_TYPE_EAP: return "EAP";
            case DEVICE_TYPE_IDB: return "IDB";
            case DEVICE_TYPE_WIFI_ANDROID: return "WIFI_ANDROID";
            case DEVICE_TYPE_WIFI_IOS: return "WIFI_IOS";
            default: return "UNKNOWN";
        }
    }

//    private void wirelessDisconnect() {
//        if (CommonData.isCheckingStart) {
//            //WeLink is foreground, WLHardware.reset
//            stopConnectWireless();
//        } else {
//            //WeLink is background, WLHardware.close
//            stopCheckWireless();
//        }
//    }

    //TODO.WIRELESS
    public void startCheckWireless() {
        LogUtil.d(TAG, "ConnectManager::startCheckWireless ---> send WIRELESS_START_CHECK");
        CommonData.sendMsg(mContext, CommonData.WIRELESS_START_CHECK, 0, null);
    }

    /**
     * channl (native)层异常
     */
    private static final int STOP_BY_CHANNEL_CONTROL_MSG = 1;

    /**
     * 应用层主动请求超时
     */
    private static final int STOP_BY_APP_REQUEST = 2;

    /**
     * she tips:添加追溯入参
     */
    public void stopCheckWireless(int reason) {
        LogUtil.printStackTraceString(TAG, "ConnectManager::stopCheckWireless reason=" + reason);
        LogUtil.d(TAG, "ConnectManager::stopCheckWireless ---> send WIRELESS_STOP_CHECK");
        CommonData.sendMsg(mContext, CommonData.WIRELESS_STOP_CHECK, 0, null);
    }

    public void stopConnectWireless() {
        LogUtil.d(TAG, "ConnectManager::stopConnectWireless ---> send WIRELESS_STOP_CONNECT");
        CommonData.sendMsg(mContext, CommonData.WIRELESS_STOP_CONNECT, 0, null);
    }
}
