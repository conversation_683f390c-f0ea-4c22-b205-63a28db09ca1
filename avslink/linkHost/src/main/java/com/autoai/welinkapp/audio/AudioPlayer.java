package com.autoai.welinkapp.audio;

import android.content.Context;

public interface AudioPlayer {

    void init();

    void onCreate(Context mApplication);

    int getMusicAudioFoucs();

    void onAudioData(byte[] bytes, int i);

    void pcmStopPlay(int type);

    void muteStop();

    void audioSetVRStart();

    boolean muteStart();

    void audioStop();

    int getMusicState();

    void audioResume();
}
