package com.autoai.welinkapp.model;

import static com.autoai.welink.macro.WL_API_DEVICE_TYPE.AOA;
import static com.autoai.welink.macro.WL_API_DEVICE_TYPE.WLAN_ANDROID;
import static com.autoai.welink.param.HidConstants.*;
import static com.autoai.welinkapp.model.HidVerificationType.*;

import com.autoai.welink.sdk.CarBTConnectedInfo;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothAdapter;
import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;
import android.view.Surface;

import androidx.annotation.NonNull;

import com.autoai.avslinkhid.api.CalibrationCallBack;
import com.autoai.avslinkhid.api.HidIosCalibrationUtil;
import com.autoai.avslinkhid.api.HidUtil;
import com.autoai.common.util.LogUtil;
import com.autoai.common.util.ThreadUtils;
import com.autoai.welink.SdkApi;
import com.autoai.welink.SdkListener;
import com.autoai.welink.macro.WL_API_APP_MSG_TYPE;
import com.autoai.welink.macro.WL_API_DEVICE_TYPE;
import com.autoai.welink.macro.WL_API_PAGE_TYPE;
import com.autoai.welink.macro.WL_API_SDK_MSG_TYPE;
import com.autoai.welink.param.ConfigInfo;
import com.autoai.welink.param.HidConstants;
import com.autoai.welink.param.HidMessage;
import com.autoai.welink.param.HidTouch;
import com.autoai.welinkapp.checkconnect.CommonData;
import com.autoai.welinkapp.checkconnect.ConnectManager;
import com.autoai.welinkapp.checkconnect.DeviceStatusListener;
import com.autoai.welinkapp.datatransfer.DataTransfer;
import com.autoai.welinkapp.service.LanguageReceiver;
import com.autoai.welinkapp.util.FpsUtil;
import com.autoai.welinkapp.util.LocalUtil;
import com.autoai.welinkapp.util.MediaCodecUtil;
import com.autoai.welinkapp.util.WindowUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 *
 */
public class SdkViewModel implements SdkListener {
    private static final String TAG = "SdkViewModel";
    public static final String TAG_BT = "BTPair";
    private static final String SERIAL_NUM = "HPAPASLH83ADCSRATLAS7PCM095";
    /**
     * 音频流相关逻辑，暂未启用
     */
    public final AudioAdapter audioAdapter;

    /**
     * 当前hu主机蓝牙mac
     */
    private String mBluetoothMac = "FF:FF:FF:FF:FF:FF";
    private AutoAgreeCallback autoAgreeCallback;
    private HidCheckCallback hidCheckCallback;

    /**
     * iOS 校准重试次数
     */
    private int mIOSCalibrationRetryTimes = 0;

    /**
     * 标记校准是否已经开始，用于区分不同的ON_TOUCH_END_POINT场景
     */
    private boolean isCalibrationStarted = false;

    /**
     * native msg通道相关逻辑
     */
    private final SdkApi mSdkApi;

    /**
     * 涉及window相关逻辑
     */
    private final WindowUtil windowUtil;

    /**
     * 上下文引用
     */
    private final Context mApplication;

    /**
     * fixme：@shecw 重点关注
     */
    public PlatformAdaptor platformadaptor;
    private boolean mStartFlag = false;

    /**
     * 信道互联状态manager （有线，无线）
     */
    private final ConnectManager mConnectManager;
    /**
     * 数据从信道 -> 数据传输层（native so）
     */
    private final DataTransfer mDataTransfer;
    private int mFps = 0;
    private long mLastTime = 0;

    private int mPage = WL_API_PAGE_TYPE.INITIAL_PAGE;

    private int mDevType = 0;
    private int mRecordType = 0;
    private long mReqCheckSeconds = 0;

    public int connectStatus = 0;

    /**
     * iOS 蓝牙互联成功后是否需要自动校准
     */
    private boolean isFirstTimeLinkConnected = false;
    private Surface mSurface;
    private String mLocalVersionName;
    private static SdkViewModel mSdkInstance = null;

    private LanguageReceiver languageReceiver;

    private final Handler mainHandler;

    /**
     * UI 响应器, 部分场景, 业务层定制的页面切换请求
     */
    private UIResponder uiResponder;

    /**
     * 手机端传输的消息通知
     */
    private static ArrayList<OnMessageListener> onMessageListeners;

    /**
     * 互联状态修改的监听
     */
    private static ArrayList<OnLinkStatusChangeListener> onLinkStatusChangeListeners;

    private int phoneFps = 30;
    private int rtt = 0;
    private String serialNum = SERIAL_NUM;


    /**
     * hid 校准 超时任务
     */
    private Runnable hidVerifyTimeoutRunnable;

    /**
     * 手机方向变化时的监听
     * tips:横竖屏变换，造成触控策略调整，
     */
    private HidCallback hidCallback;

    /**
     * 是否互联成功后 首次收到视频数据
     */
    private AtomicBoolean isFirstVideo = new AtomicBoolean(true);

    /**
     * she tips：主要兼容应用内投屏使用
     * 【应用内投屏】一个基于adb 模式进行的功能，非正式投产
     *
     * @return
     */
    private String ipAddress;

    private SdkViewModel(@NonNull Context application) {
        mApplication = application.getApplicationContext();
        //--> 初始化window相关参数
        windowUtil = WindowUtil.getInstance(application);
        //--> 初始化SdkApi，具体见SdkApi
        mSdkApi = new SdkApi();

        //-->有线连接[传输相关]
        mDataTransfer = new DataTransfer();
        //-->有线连接[连接状态相关]
        mConnectManager = new ConnectManager();

        //---> 初始化包名，版本号
        getLocalVersionName(application);
        getLocalVersion(application);

        //--->声音传输相关，目前暂未实际作用
        audioAdapter = new AudioAdapter();
        platformadaptor = new PlatformAdaptor(this);
        //---> 初始化AOA/Idb/Eap等有线连接[连接状态相关]
        mConnectManager.init(mApplication, deviceStatusListener); //AOA:1 EAP:

        //---> 初始化AOA/Idb/Eap等有线连接[传输相关]
        mDataTransfer.init(mApplication, true);
        mainHandler = new Handler(Looper.getMainLooper());


        //--> 监听系统语言切换状态，主要是在收到监听回调后，通知手机端车机语言配置
        languageReceiver = new LanguageReceiver(mSdkApi);
        languageReceiver.registerReceiver();
        //--> 反控初始化
        HidUtil.init(mApplication);
    }

    public synchronized static SdkViewModel getInstance(@NonNull Context application) {
        if (mSdkInstance == null) {
            mSdkInstance = new SdkViewModel(application);
        }
        return mSdkInstance;
    }

    public static SdkViewModel getInstance() {
        if (mSdkInstance == null) {
            LogUtil.e(TAG, "not init!!!");
        }
        return mSdkInstance;
    }

    /**
     * 注册 UI 页面的响应器
     *
     * @param responder
     */
    public void registerUiResponder(UIResponder responder) {
        LogUtil.e(TAG, " SdkViewModel::registerUiResponder");
        this.uiResponder = responder;
    }

    private void getLocalVersion(Context ctx) {
        LogUtil.e(TAG, " SdkViewModel::getLocalVersion");
        int mLocalVersion;
        try {
            PackageInfo packageInfo = ctx.getApplicationContext()
                    .getPackageManager()
                    .getPackageInfo(ctx.getPackageName(), 0);
            mLocalVersion = packageInfo.versionCode;
//            LogUtil.d("TAG", "versionNum： " + com.autoai.welinkapp.BuildConfig.VERSION_NAME);
            LogUtil.d("TAG", "versionCode：" + mLocalVersion);
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
    }

    private void getLocalVersionName(Context ctx) {
        LogUtil.e(TAG, " SdkViewModel::getLocalVersionName");
        try {
            PackageInfo packageInfo = ctx.getApplicationContext()
                    .getPackageManager()
                    .getPackageInfo(ctx.getPackageName(), 0);
            mLocalVersionName = packageInfo.versionName;
            LogUtil.d("TAG", "versionName：" + mLocalVersionName);
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
    }

    public void setPhoneFps(int phoneFps) {
        LogUtil.e(TAG, " SdkViewModel::setPhoneFps --> phoneFps = " + phoneFps);
        this.phoneFps = phoneFps;
    }

    public void setRtt(int rtt) {
        LogUtil.e(TAG, " SdkViewModel::setRtt --> rtt = " + rtt);
        this.rtt = rtt;
    }

    public void setSerialNum(String serialNum) {
        LogUtil.e(TAG, " SdkViewModel::setSerialNum --> serialNum = " + serialNum);
        this.serialNum = serialNum;
    }

    public void onCreate(Context context) {
        LogUtil.e(TAG, " SdkViewModel::onCreate ");
        if (mStartFlag) {
            return;
        }
        mStartFlag = true;
        //--> 初始化 native 数据传输层
        initSdk();
        getBlueToothMac();

        //--> fixme:???
        mSdkApi.sendMsg(WL_API_APP_MSG_TYPE.BT_ADDR, mBluetoothMac.getBytes());
        //-->旧的音频管理，暂时保留
        audioAdapter.onCreate(mApplication);

        //-->  注册解码状态相关监听 :第一帧
        MediaCodecUtil.getInstance().setFirstFrameReadyListener(() -> dispatchMessage(new MessageEvent(MessageEvent.SHOW_RUNNING_PAGE)));
        //-->  补帧回调
        MediaCodecUtil.getInstance().setAppendingCacheFrameListener(() -> {
            //正在补帧回调  发送消息给手机端 发送关键帧
            LogUtil.d(TAG, "SdkViewModel::onAppendingCacheFrame --正在补帧回调  发送消息给手机端");
            Message message = new Message();
            //frame
            message.arg1 = 5;
            //time 单位毫秒
            message.arg2 = 1000;
            // 申请关键帧
            mSdkApi.sendMsg(WL_API_APP_MSG_TYPE.HU_REQ_KEY_FRAME, message);

            if (isFirstVideo.get() && connectStatus == CommonData.LINK_STATUS_CONNECTED) {
                isFirstVideo.set(false);
                dispatchLinkState(CommonData.LINK_STATUS_SCREEN_MIRRORING);
                onUpdatePage(UIResponder.SCREEN_PAGE_MIRRORING, null);
            }
        });

//        TelephonyManager tm = (TelephonyManager) context.getSystemService(Service.TELEPHONY_SERVICE);
//        tm.listen(phoneStateListener, PhoneStateListener.LISTEN_CALL_STATE);
    }

//    private final PhoneStateListener phoneStateListener = new PhoneStateListener() {
//        @Override
//        public void onCallStateChanged(int state, String phoneNumber) {
//            super.onCallStateChanged(state, phoneNumber);
//            switch (state) {
//                case TelephonyManager.CALL_STATE_IDLE:// 电话挂断
//                    LogUtil.i(TAG, "电话挂断...");
////                    onPhoneCallListener.onCallIdle();
//                    mSdkApi.sendMsg(WL_API_APP_MSG_TYPE.HU_BT_PHONE, 2);
//
//                    break;
//                case TelephonyManager.CALL_STATE_OFFHOOK: //电话通话的状态
//                    LogUtil.i(TAG, "正在通话...");
////                    onPhoneCallListener.onCallOffHook();
//                    mSdkApi.sendMsg(WL_API_APP_MSG_TYPE.HU_BT_PHONE, 1);
//
//                    break;
//                case TelephonyManager.CALL_STATE_RINGING: //电话响铃的状态
//                    LogUtil.i(TAG, "电话响铃");
////                    onPhoneCallListener.onCallRinging();
//                    mSdkApi.sendMsg(WL_API_APP_MSG_TYPE.HU_BT_PHONE, 1);
//                    break;
//                default:
//                    break;
//            }
//
//        }
//    };


    public void addMessageListener(OnMessageListener listener) {
        LogUtil.d(TAG, "SdkViewModel::addMessageListener");
        if (onMessageListeners == null) {
            onMessageListeners = new ArrayList<>();
        }
        onMessageListeners.add(listener);
    }

    public void removeMessageListener(OnMessageListener listener) {
        LogUtil.d(TAG, "SdkViewModel::removeMessageListener");
        if (onMessageListeners != null) {
            onMessageListeners.remove(listener);
        }
    }

    private void dispatchMessage(MessageEvent event) {
        LogUtil.d(TAG, "SdkViewModel::dispatchMessage");
        Runnable runnable = () -> {
            if (onMessageListeners != null) {
                for (OnMessageListener listener : onMessageListeners) {
                    listener.onMessage(event);
                }
            }
        };

        if (Looper.myLooper() == Looper.getMainLooper()) {
            runnable.run();
        } else {
            mainHandler.post(runnable);
        }
    }

    /**
     * 注册互联状态回调
     *
     * @param listener
     */
    public void addLinkStateChangeListener(OnLinkStatusChangeListener listener) {
        LogUtil.d(TAG, "SdkViewModel::addLinkStateChangeListener");
        if (onLinkStatusChangeListeners == null) {
            onLinkStatusChangeListeners = new ArrayList<>();
        }
        onLinkStatusChangeListeners.add(listener);
    }

    /**
     * 移除互联状态回调
     *
     * @param listener
     */
    public void removeLinkStateChangeListener(OnLinkStatusChangeListener listener) {
        LogUtil.d(TAG, "SdkViewModel::removeLinkStateChangeListener");
        if (onLinkStatusChangeListeners != null) {
            onLinkStatusChangeListeners.remove(listener);
        }
    }

    /**
     * 分发互联状态给应用层
     * she tips ： 会通过主线程callback回去
     *
     * @param status
     */
    private void dispatchLinkState(@CommonData.LinkStatus int status) {
        LogUtil.d(TAG, "SdkViewModel::dispatchLinkState --> status =" + status);
        Runnable runnable = () -> {
            if (onLinkStatusChangeListeners != null) {
                for (OnLinkStatusChangeListener listener : onLinkStatusChangeListeners) {
                    listener.onChange(status, mDevType);
                }
            }
        };

        if (Looper.myLooper() == Looper.getMainLooper()) {
            runnable.run();
        } else {
            mainHandler.post(runnable);
        }
    }


    /**
     * 互联状态callback ，触发自 {@link #mConnectManager}
     */
    private final DeviceStatusListener deviceStatusListener = new DeviceStatusListener() {


        /**
         * 游蛇别接入
         * @param type 参考{@link CommonData} DEVICE_TYPE_
         * @param ip WiFi互联的ip地址
         */
        @Override
        public void onDeviceIn(int type, String ip) {
            if (!TextUtils.isEmpty(ip)) {
                ipAddress = ip;
            }
//            if (CommonData.DEVICE_TYPE_NONE == mRecordType) {
            LogUtil.e(TAG, "SdkViewModel::onDeviceIn, type: " + type);
            mDevType = type;
            mRecordType = type;

            if (CommonData.DEVICE_TYPE_AOA == type) {
                mSdkApi.deviceChg(AOA, null);
                LogUtil.i(TAG, "SdkViewModel::onDeviceIn --> onReceive AOA device in");
            } else if (CommonData.DEVICE_TYPE_IDB == type) {
                mSdkApi.deviceChg(WL_API_DEVICE_TYPE.IDB, null);
                LogUtil.i(TAG, "SdkViewModel::onDeviceIn --> onReceive IDB device in");
            } else if (CommonData.DEVICE_TYPE_EAP == type) {
                LogUtil.i(TAG, "SdkViewModel::onDeviceIn --> onReceive EAP device in");
                mSdkApi.deviceChg(WL_API_DEVICE_TYPE.EAP, null);
            } else if (CommonData.DEVICE_TYPE_WIFI_IOS == type) {
                if (ip != null) {
                    LogUtil.i(TAG, "SdkViewModel::onDeviceIn --> onReceive iPhone device in ==>" + ip);
                    mSdkApi.deviceChg(WL_API_DEVICE_TYPE.WLAN_IPHONE, ip);
                }
            } else if (CommonData.DEVICE_TYPE_WIFI_ANDROID == type) {
                if (ip != null) {
                    LogUtil.i(TAG, "SdkViewModel::onDeviceIn --> onReceive Android device in ==>" + ip);
                    mSdkApi.deviceChg(WLAN_ANDROID, ip);
                }
            } else if (CommonData.DEVICE_TYPE_WIFI_HARMONY == type) {
                if (ip != null) {
                    LogUtil.i(TAG, "SdkViewModel::onDeviceIn --> onReceive HARMONY  device in ==>" + ip);
                    mSdkApi.deviceChg(WL_API_DEVICE_TYPE.WLAN_HARMONY, ip);
                }
            } else if (CommonData.DEVICE_TYPE_NONE == type) {
                LogUtil.i(TAG, "SdkViewModel::onDeviceIn --> onReceive NONE");
            }
//            } else {
//                LogUtil.i(TAG, "Device in duplication");
//            }
        }

        /**
         * 设备断开
         * @param type 参考{@link CommonData} DEVICE_TYPE_
         */
        @Override
        public void onDeviceOut(int type) {
            LogUtil.e(TAG, "SdkViewModel::onDeviceOut, type: " + type);
            mSdkApi.deviceChg(WL_API_DEVICE_TYPE.OUT, null);
        }

        /**
         * 连接中
         * @param type 参考{@link CommonData} DEVICE_TYPE_
         *             目前主要是无线互联感知到手机设备
         */
        @Override
        public void onDevicesConnecting(int type) {
            LogUtil.e(TAG, "SdkViewModel::onDevicesConnecting, type: " + type);
            dispatchLinkState(CommonData.LINK_STATUS_CONNECTING);
        }
    };


    private void initSdk() {
        LogUtil.e(TAG, "SdkViewModel::initSdk()");
        mSdkApi.setContext(mApplication);
        //--> 关键方法，所有native msg 事件都会通过注册回调进行callback通知
        mSdkApi.setCallbackFunc(this);

        ConfigInfo config = new ConfigInfo();
        config.configFilePath = "";
        config.huVersion = "V" + mLocalVersionName;
        config.serialNum = serialNum;
        config.huVerticalFull = 0;
        config.fps = phoneFps;
        config.rtt = rtt;
        config.videoWidth = windowUtil.getSurfaceWidth();
        config.videoHeight = windowUtil.getSurfaceHeight();
        config.videoX = windowUtil.getSurfacePoint().x;
        config.videoY = windowUtil.getSurfacePoint().y;

        mSdkApi.setConfig(config);
        mSdkApi.init();
    }

    public void onResume() {
        LogUtil.e(TAG, "SdkViewModel::onResume()");
        mSdkApi.sendMsg(WL_API_APP_MSG_TYPE.FOREGROUND, null);
    }

    /**
     *
     */
    public void startCheckConnect() {
        LogUtil.e(TAG, "SdkViewModel::startCheckConnect()");
        mConnectManager.startCheckConnect();
    }

    public void stopCheckConnect() {
        LogUtil.e(TAG, "SdkViewModel::stopCheckConnect()");
        mConnectManager.stopCheckConnect();
    }

    public void onPause() {
        LogUtil.e(TAG, "SdkViewModel::onPause()");
//        mConnectManager.stopCheckConnect();
        mSdkApi.sendMsg(WL_API_APP_MSG_TYPE.BACKGROUND, null);
    }

    /**
     * fixme @shecw ？？？
     */
    public void resetConnectStatus() {
        LogUtil.e(TAG, "SdkViewModel::resetConnectStatus()");
        if (deviceStatusListener != null) {
            LogUtil.i(TAG, "断开设备重置互联状态 error type: " + mDevType);
            deviceStatusListener.onDeviceOut(mDevType);
        }
    }

    public synchronized void onDestroy() {
        LogUtil.e(TAG, "SdkViewModel::onDestroy()");
        if (!mStartFlag) {
            return;
        }
        languageReceiver.unregisterReceiver();
        mSdkApi.deinit();
        mDataTransfer.deinit();
        mConnectManager.stopCheckConnect();
        mConnectManager.deinit();
        if (mDevType != CommonData.DEVICE_TYPE_NONE) {
            dispatchLinkState(CommonData.LINK_STATUS_NONE);
        }
        connectStatus = 0;
        CommonData.resetConnectStatus();
        mSdkInstance = null;
        mStartFlag = false;
    }

    public SdkApi getSdkApi() {
        LogUtil.e(TAG, "SdkViewModel::getSdkApi()");
        return mSdkApi;
    }

    public int getMusicAudioFoucs() {
        LogUtil.e(TAG, "SdkViewModel::getMusicAudioFoucs()");
        int focus = 0;
        try {
            // 0: no focus,  1:have focus
            focus = audioAdapter.getMusicState();
        } catch (NullPointerException e) {
            LogUtil.e(TAG, "error: ", e);
        }
        return focus;
    }

    public int getMusicFoucs() {
        return audioAdapter.getMusicAudioFoucs();
    }

    /**
     * fixme：？
     * 获取当前设备的蓝牙mac
     */
    @SuppressLint("HardwareIds")
    private void getBlueToothMac() {
        LogUtil.e(TAG, "SdkViewModel::getBlueToothMac()");
        BluetoothAdapter mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        if (mBluetoothAdapter == null) {
            return;
        }
        mBluetoothMac = mBluetoothAdapter.getAddress();
        if (null == mBluetoothMac) {
            mBluetoothMac = "FF:FF:FF:FF:FF:FF";
        }
    }

    /**
     * hmi层（ui）需要一系列状态，需要配合画面迁移，提示
     * {@link #uiResponder}
     *
     * @param page
     * @param para
     * @return
     */
    @Override
    public int onUpdatePage(int page, Object para) {
        LogUtil.printStackTraceString(TAG, "SdkViewModel::onUpdatePage --> page = " + page);
        if (uiResponder != null) {
            uiResponder.onUpdatePage(page, para);
        }
        // 切换互联设备的时候反注册
//        if (page == UIResponder.ERR_PAGE_HEART || page == UIResponder.INITIAL_PAGE) {
//            HidUtil.unregisterHID();
//        }

        mPage = page;
        return 0;
    }

    @Override
    public int onRecvMsg(int msg, Object object) {
        LogUtil.i(TAG, "SdkViewModel::onRecvMsg --> msg = " + msg + ", data: " + object);
        switch (msg) {
            case WL_API_SDK_MSG_TYPE.UPDATE_KEY:
                dispatchMessage(
                        new MessageEvent(MessageEvent.SET_HIGHLIGHT_KEY, object)
                );
                break;
            case WL_API_SDK_MSG_TYPE.NAVI_GUIDE_START:
                dispatchMessage(
                        new MessageEvent(MessageEvent.UPDATE_NAV_DATA, object)
                );
                break;
            case WL_API_SDK_MSG_TYPE.NAVI_GUIDE_STOP:
                dispatchMessage(
                        new MessageEvent(MessageEvent.STOP_NAVIGATE, object)
                );
                break;
            case WL_API_SDK_MSG_TYPE.RECV_MSG:
                dispatchMessage(
                        new MessageEvent(MessageEvent.RECEIVE_MESSAGE, object)
                );
            case WL_API_SDK_MSG_TYPE.START_RECORD:
                //MicRecord.getInstance().wl_audio_mic_start_record();
                break;
            case WL_API_SDK_MSG_TYPE.CONNECTING:
                mConnectManager.connectStatusChange(mDevType, CommonData.LINK_STATUS_CONNECTING);
//                MediaCodecUtil.getInstance().startDecoder(mSurface,
//                        windowUtil.getSurfaceWidth(),
//                        windowUtil.getSurfaceHeight());
                audioAdapter.onConnecting();

                // 分发连接状态
                dispatchLinkState(CommonData.LINK_STATUS_CONNECTING);
                break;
            case WL_API_SDK_MSG_TYPE.DISCONNECT:
                mConnectManager.connectStatusChange(mDevType, CommonData.LINK_STATUS_NONE);
                MediaCodecUtil.getInstance().stopDecoder();
                audioAdapter.onDisconnect();
                platformadaptor.sendMsg(
                        PlatformAdaptor.MSG_TYPE.AP_MSG_CONNECT_STATE,
                        PlatformAdaptor.CONNECT_STATE.DISCONNECT,
                        mSdkApi.getDeviceType());

                platformadaptor.SetPhoneVrStatus(PlatformAdaptor.PHONE_VR_STATUS.PHONE_VR_END);
                connectStatus = PlatformAdaptor.CONNECT_STATE.DISCONNECT;
                isFirstVideo.set(true);
                dispatchMessage(new MessageEvent(MessageEvent.GO_TO_MENU, null));

                // 分发连接状态
                dispatchLinkState(CommonData.LINK_STATUS_NONE);
                ThreadUtils.postOnMainThread(new Runnable() {
                    @Override
                    public void run() {
                        CommonData.resetConnectStatus();
                    }
                });
                // 重置蓝牙和
                updateBTPairedWithPhone(false);
                break;
            case WL_API_SDK_MSG_TYPE.CONNECT_ERR:
                mConnectManager.connectStatusChange(mDevType, CommonData.LINK_STATUS_FAIL);
                MediaCodecUtil.getInstance().stopDecoder();
                audioAdapter.onConnectError();
                platformadaptor.sendMsg(
                        PlatformAdaptor.MSG_TYPE.AP_MSG_CONNECT_STATE,
                        PlatformAdaptor.CONNECT_STATE.DISCONNECT,
                        mSdkApi.getDeviceType());

                platformadaptor.SetPhoneVrStatus(PlatformAdaptor.PHONE_VR_STATUS.PHONE_VR_END);
                connectStatus = PlatformAdaptor.CONNECT_STATE.CONNECTERR;
                isFirstVideo.set(true);
                // 分发连接状态
                //--> fixme:这个状态没分发上去吗？跟一下 0606
                dispatchLinkState(CommonData.LINK_STATUS_FAIL);
                ThreadUtils.postOnMainThread(new Runnable() {
                    @Override
                    public void run() {
                        CommonData.resetConnectStatus();
                    }
                });
                break;
            case WL_API_SDK_MSG_TYPE.CONNECTED:
                mConnectManager.connectStatusChange(mDevType, CommonData.LINK_STATUS_CONNECTED);
                platformadaptor.sendMsg(
                        PlatformAdaptor.MSG_TYPE.AP_MSG_CONNECT_STATE,
                        PlatformAdaptor.CONNECT_STATE.CONNECTED,
                        mSdkApi.getDeviceType());
                // 发送车机端语言设置
                mSdkApi.sendControlEnAndCh(LocalUtil.getLocal());
                // 发送车机端蓝牙地址
                mSdkApi.sendBTMacInfo(mBluetoothMac);
                if (platformadaptor.getRvcStartState() == PlatformAdaptor.BACKING_UP_STATE.START) {
                    mSdkApi.sendHuRvcState(1);   // 当车机端处于倒车中,向手机端发送倒车状态
                }
                connectStatus = PlatformAdaptor.CONNECT_STATE.CONNECTED;

                // 分发连接状态
                dispatchLinkState(CommonData.LINK_STATUS_CONNECTED);

                isFirstTimeLinkConnected = true;
                //注册hid
                HidUtil.registerHID(mDevType, windowUtil.getScreenWidth(), windowUtil.getScreenHeight());
                HidIosCalibrationUtil.registerCallback(callBack);
                if (autoAgreeCallback != null && (mDevType == WLAN_ANDROID || mDevType == AOA)) {
                    autoAgreeCallback.autoAgree(true);
                }

                LogUtil.i(TAG, "LinkHid  registerHID mDevType = " + mDevType + ", h264W = " + windowUtil.getScreenWidth() + ", h264H = " + windowUtil.getScreenHeight());

                break;
            case WL_API_SDK_MSG_TYPE.STOP_RECORD:
                //MicRecord.getInstance().wl_audio_mic_stop_record();
                break;
            case WL_API_SDK_MSG_TYPE.CALL_NUMBER:
                break;
            case WL_API_SDK_MSG_TYPE.TURN_BY_TURN:
                platformadaptor.sendMsg(PlatformAdaptor.MSG_TYPE.AP_MSG_TBT_DATA, PlatformAdaptor.CONNECT_STATE.CONNECTED, object);
                break;
            case WL_API_SDK_MSG_TYPE.MUSIC_PLAY_STATE:
                break;
            case WL_API_SDK_MSG_TYPE.MEDIA_STATE:
                platformadaptor.sendMsg(PlatformAdaptor.MSG_TYPE.AP_MSG_MUSIC_PLAY_STATE_RES, PlatformAdaptor.CONNECT_STATE.CONNECTED, object);
                break;
            case WL_API_SDK_MSG_TYPE.HOME_KEY:
                dispatchMessage(new MessageEvent(MessageEvent.GO_TO_MENU, null));
                break;
            case WL_API_SDK_MSG_TYPE.APPLY_MIC:
//                boolean state = false;
//                if(MicRecord.MIC_RECORD_RET_SUCCESS == MicRecord.getInstance().wl_audio_mic_start_record()) {
//                    state = true;
//                }
//                mSdkApi.sendHuMicState(state);
                break;
            case WL_API_SDK_MSG_TYPE.RELEASE_MIC:
                //MicRecord.getInstance().wl_audio_mic_stop_record();
                break;
            case WL_API_SDK_MSG_TYPE.MUTE_START:
                boolean state = audioAdapter.muteStart();
                mSdkApi.sendHuMicState(state);
                audioAdapter.audioSetVRStart();
                platformadaptor.SetPhoneVrStatus(PlatformAdaptor.PHONE_VR_STATUS.PHONE_VR_START);
                break;
            case WL_API_SDK_MSG_TYPE.MUTE_STOP:
                audioAdapter.muteStop();
                platformadaptor.SetPhoneVrStatus(PlatformAdaptor.PHONE_VR_STATUS.PHONE_VR_END);
                break;
            case WL_API_SDK_MSG_TYPE.PCM_STOP_PLAY:
                audioAdapter.pcmStopPlay((int) object);
                break;
            case WL_API_SDK_MSG_TYPE.REQ_BT_MUSIC_FOCUS:
                break;
            case WL_API_SDK_MSG_TYPE.RECORDING_SCREEN_STATE:
                LogUtil.i(TAG, "iRecordingScreenState = " + (int) object);
//                iRecordingScreenState = (int)object;
//                LogUtil.i(TAG, "iRecordingScreenState = " + iRecordingScreenState);
//                dispatchMessage(
//                        new MessageEvent(MessageEvent.RECORDING_SCREEN_STATE, iRecordingScreenState)
//                );
                break;
            case WL_API_SDK_MSG_TYPE.HID_TOUCH:
                HidTouch hidTouch = (HidTouch) object;
                //--->屏幕旋转发生改变
                HidUtil.setH264Coordinates(hidTouch);

                //---> 旋转事件callback给应用层
                hidCallback.onScreenRotationChange(hidTouch.h264W, hidTouch.h264H, hidTouch.angle, hidTouch.videoMode, hidTouch.platform);
                //TouchUtil.getInstance().setH264Coordinates(hidTouch);

                break;
            case WL_API_SDK_MSG_TYPE.CAPTURE_STATE:
                break;
            case WL_API_SDK_MSG_TYPE.NAVI_STATE:
                int naviState = (int) object;
                platformadaptor.sendMsg(
                        PlatformAdaptor.MSG_TYPE.AP_MSG_NAVI_STATE,
                        -1,
                        naviState);
                break;
            case WL_API_SDK_MSG_TYPE.ON_HID_TOUCH_POINT:
                //ios手机端发送 校准坐标
                HidMessage obj = (HidMessage) object;
                dispatchHidMessage(obj);
                break;
            case WL_API_SDK_MSG_TYPE.CAR_BT_CONNECTED:
                // 处理新的蓝牙连接协议，包含连接状态和手机MAC地址
                // 连接成功后 bt 连接状态时序有时间差，500ms延迟
                mainHandler.postDelayed(() -> {
                    handleCarBTConnected(object);
                }, 500);
                break;
            default:
                break;
        }
        return 0;
    }

    @Override
    public int onAudioData(byte[] bytes, int i) {
        LogUtil.i(TAG, "SdkViewModel::onAudioData");
        audioAdapter.onAudioData(bytes, i);
        return 0;
    }

    @Override
    public int onHuData(byte[] bytes, int i, int i1) {
        LogUtil.i(TAG, "SdkViewModel::onHuData");
        return 0;
    }

    // frame_type:2 I帧、3 P帧
//    private boolean noNeedFirstIFrame = false;
//    // 暂停后需要重新获取到关键帧
//    private boolean hasPaused = false;
    /**
     * 窗口最小化，解码器没有数据输入
     */
    private boolean paused = false;

    public void setPaused(boolean newPauseState) {
        Log.i(TAG, "SdkViewModel::setPaused paused = " + paused + ";newPauseState = " + newPauseState);
        //she tips：注意不要
        boolean lastPauseState = this.paused;
        if (lastPauseState != newPauseState && !newPauseState) {//--》如果恢复
            MediaCodecUtil.getInstance().appendCacheAfterResume();
        }
        this.paused = newPauseState;
    }

    public boolean isPaused() {
        Log.i(TAG, "SdkViewModel::isPaused paused = " + paused);
        return paused;
    }

    /**
     * she 测试flag 使用完删除
     */

    /**
     * 在解码器实际开启后，收到的实际I帧
     */
    @Override
    public int onVideoData(byte[] data, int size, int width, int height, int spspps_data_size, int frame_type) {
        Log.i(TAG, "SdkViewModel::onVideoData::size = " + size
                + " frame_type = " + frame_type
                + " spspps_data_size = " + spspps_data_size
                + " width = " + width
                + " height = " + height
                + " paused = " + paused
        );

        if (LogUtil.isFullLog()) {
            LogUtil.v(TAG, "size = " + size + " frame_type = " + frame_type + " spspps_data_size = " + spspps_data_size + " width = " + width + " height = " + height);
        }

        FpsUtil.printFps("onVideoData");
        if (data != null) {

//                //--> 补帧逻辑 fixme：@shecw，后续进一步优化整理该部分逻辑
            if (paused) {
//                    // 窗口最小化
                MediaCodecUtil.getInstance().cacheFrame(data, 0, data.length, spspps_data_size, frame_type);
                return 0;
            }

            int ret = MediaCodecUtil.getInstance().decodeFrame(data, 0, data.length, spspps_data_size, frame_type);
            if (ret < 0) {
                LogUtil.w(TAG, "onVideoData: decode fail! error code = " + ret);
            }

            if (isFirstVideo.get() && connectStatus == CommonData.LINK_STATUS_CONNECTED) {
                isFirstVideo.set(false);
                dispatchLinkState(CommonData.LINK_STATUS_SCREEN_MIRRORING);
                onUpdatePage(UIResponder.SCREEN_PAGE_MIRRORING, null);
            }

        }
        return 0;
    }

    @Override
    public int onUpdateData(byte[] bytes, int i) {
        return 0;
    }

    public void capture() {
        MediaCodecUtil.getInstance().capture();
    }

    /**
     * 通用的封装, Surface 启动的处理
     *
     * @param surface
     */
    public void startVideo(Surface surface, int width, int height) {
        LogUtil.d(TAG, "SdkViewModel::startVideo --> width: " + width + ", height: " + height);
        this.mSurface = surface;
        MediaCodecUtil.getInstance().startDecoder(surface,
                width,
                height);

    }

    /**
     * 通用的封装, Surface 销毁的处理
     *
     * @param surface
     */
    public void stopVideo(Surface surface) {
        LogUtil.d(TAG, "SdkViewModel::stopVideo");
        if (surface != mSurface) {
            return;
        }
        MediaCodecUtil.getInstance().stopDecoder();
    }

    public void sendScreenOrientationOp(int orientation) {
        mSdkApi.sendHuKeyState(orientation);
    }

    /**
     * ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓  反控校准 :fixme @sunwj 能否收到sdk中 ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓
     */
    public void sendReqRecordScreenOp() {
        LogUtil.d(TAG, "SdkViewModel::sendReqRecordScreenOp");
        mSdkApi.sendReqRecordScreenMsg(10);
    }


    /**
     * 开始校准，需求是如果互联成功后，如果蓝牙连接，则直接开始校准。
     */
    private void requestCalibration() {
        LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:requestCalibration starting auto calibration request");
        if (hidVerifyTimeoutRunnable == null) {
            hidVerifyTimeoutRunnable = () -> {
                if (HidIosCalibrationUtil.getHidVerificationState() == HID_VERIFY_TYPE_UN_CONNECT ||
                        HidIosCalibrationUtil.getHidVerificationState() == HID_VERIFY_TYPE_NONE) {
                    LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:requestCalibration timeout, sending REQ_FAIL, state=HID_VERIFY_TYPE_HID_CONNECT_VERIFICATION_FAIL");
                    HidIosCalibrationUtil.setHidVerificationState(HID_VERIFY_TYPE_HID_CONNECT_VERIFICATION_FAIL);
                    // hid 应答超时，可能是连接了不同手机、或者 hid 无反应
                    mSdkApi.sendIosHidTouchPoint(ON_TOUCH_POINT_REQ_FAIL);
                    onCheckHiTouchEvent(ON_HID_CONNECT_FAIL);
                }
            };
        }

        HidIosCalibrationUtil.setHidVerificationState(HID_VERIFY_TYPE_NONE);


        mainHandler.removeCallbacks(hidVerifyTimeoutRunnable);
        mainHandler.postDelayed(hidVerifyTimeoutRunnable, 6000);
        HidIosCalibrationUtil.requestCalibration();
    }


    /**
     * 收到 iOS 侧准备就绪消息ON_TOUCH_POINT_READY，并且回传了上一次的的坐标点
     *
     * @param obj 上一次保存的校准点
     */
    private void onTouchPointReady(List<Double> obj) {
        LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onTouchPointReady <<< received READY from iOS with historical points");
        isCalibrationStarted = true; // 标记校准已开始
        boolean checkPointRet = false;
        if (obj != null) {
            if (!obj.isEmpty()) {
                LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onTouchPointReady validating historical calibration points: " + obj);
                HidIosCalibrationUtil.cleanCache();
                checkPointRet = HidIosCalibrationUtil.checkPoint(obj);
            }
        }

        if (!checkPointRet || mIOSCalibrationRetryTimes > 0) {
            // 上一次的校准点无法利用，重新开始正式校准
            LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onTouchPointReady historical points invalid, starting formal calibration");
            HidIosCalibrationUtil.startVerification();
        } else {
            // 核对上一次校准点
            LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onTouchPointReady historical points valid, starting prepare calibration");
            HidIosCalibrationUtil.startPrepareVerification();
        }
    }

    /**
     * 预校准，收到 iOS 侧的校准点， 需要检查是否与发送的匹配。
     * 匹配： 则直接结束校准。
     * 不匹配： 重新开始校准流程。
     *
     * @param obj 预校准点
     */
    private void onTouchPointCheckPrepareCalibration(List<Double> obj) {
        LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onTouchPointCheckPrepareCalibration <<< received prepare calibration points from iOS");
        boolean checkPointRet = false;
        if (obj != null && !obj.isEmpty()) {
            checkPointRet = HidIosCalibrationUtil.checkPrepareCalibPoints(obj);
            LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onTouchPointCheckPrepareCalibration prepare points: " + obj + ", checkResult=" + checkPointRet);

            if (!checkPointRet) {
                // 上一次的校准点无法利用，重新开始正式校准
                LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onTouchPointCheckPrepareCalibration prepare points invalid, starting formal calibration");
                HidIosCalibrationUtil.startVerification();
            } else {
                // 直接结束校准
                LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onTouchPointCheckPrepareCalibration prepare points valid, calibration SUCCESS, sending END_SUCCESS");
                onTouchSendPointCheckSucc();
            }
        } else {
            // 蓝牙没有连上，或者连上其他手机了。
            LogUtil.e(TAG + TAG_IOS_CALIB, "SdkViewModel:onTouchPointCheckPrepareCalibration no prepare points received");
            HidIosCalibrationUtil.setHidVerificationState(HID_VERIFY_TYPE_VERIFICATION_FAIL);

            // 直接结束，不重试。等待用户调整手机 对话框弹出或者蓝牙连接问题
            mIOSCalibrationRetryTimes = 2;
            onCheckHiTouchEvent(ON_HID_FAIL);
        }
    }

    /**
     * 处理 hid 事件， 成功（ON_HID_SUCCESS），失败（ON_HID_CONNECT_FAIL），重试
     *
     * @param state
     */
    private void onCheckHiTouchEvent(int state) {
        LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onCheckHiTouchEvent processing calibration result state=" + state);
        // 互联成功后才不在继续校准
        if (state == ON_HID_SUCCESS) {
            isFirstTimeLinkConnected = false;
        }
        if (hidCheckCallback != null) {
            if (state == ON_HID_SUCCESS || state == ON_HID_CONNECT_FAIL || state == ON_HID_APP_BACKGROUND_FAIL || state == ON_HID_APP_NOT_FOREGROUND) {
                LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onCheckHiTouchEvent final result, notifying callback state=" + state);
                mIOSCalibrationRetryTimes = 0;
                hidCheckCallback.onCheck(state);
            } else {
                if (mIOSCalibrationRetryTimes < 2 && isBTPairedWithPhone()) {
                    LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onCheckHiTouchEvent retrying calibration, attempt=" + (mIOSCalibrationRetryTimes + 1) + "/" + 2);
                    mIOSCalibrationRetryTimes++;
                    HidIosCalibrationUtil.cleanCache();
                    //车机端发起重试
                    HidIosCalibrationUtil.requestCalibration();
                } else {
                    LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onCheckHiTouchEvent max retries reached, sending END_FAIL");
                    mIOSCalibrationRetryTimes = 0;
                    mSdkApi.sendIosHidTouchPoint(ON_TOUCH_POINT_END_FAIL);
                    hidCheckCallback.onCheck(state);

                }
            }
        } else {
            LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onCheckHiTouchEvent hidCheckCallback is null");
        }
    }

    /**
     * 注册回调通知:横竖屏变换，造成触控策略调整，
     *
     * @param hidCallback
     */
    public void setHidRegionCallback(HidCallback hidCallback) {
        LogUtil.d(TAG, "SdkViewModel::setHidRegionCallback");
        this.hidCallback = hidCallback;
    }

    /**
     * 接收 iOS 侧返回协议相关内容。
     *
     * @param message
     */
    private void dispatchHidMessage(HidMessage message) {
        LogUtil.d(TAG, "SdkViewModel:: dispatchHidMessage");
        if (message != null) {
            int type = message.getType();
            LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:dispatchHidMessage <<< received from iOS: " + HidConstants.getHidTouchPointStateFromIOSString(type) + " recMsg: " + message);
            if (type == ON_TOUCH_POINT_READY) {
                onTouchPointReady(message.getPointList());
            } else if (type == ON_TOUCH_SEND_PREPARE_POINT) {
                onTouchPointCheckPrepareCalibration(message.getPointList());
            } else if (type == ON_TOUCH_SEND_POINT) {
                onTouchSendPoint(message.getPointList());
            } else if (type == ON_TOUCH_END_POINT) {
                //ios手机端发送 校准失败 结束校准
                onTouchSendEnd();
            } else if (type == ON_TOUCH_APP_FRONT) {
                isCalibrationStarted = true; // 标记校准已开始
                startVerification();
            } else {
                LogUtil.e(TAG + TAG_IOS_CALIB, "SdkViewModel:dispatchHidMessage unknown message type: " + type);
            }
        }
    }


    /**
     * 正式校准流程， 收到 iOS侧 回传的点
     *
     * @param obj 收到的校准点
     */
    private void onTouchSendPoint(List<Double> obj) {
        LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onTouchSendPoint <<< received calibration points from iOS: " + obj);
        if (obj != null) {
            HidIosCalibrationUtil.cleanCache();
            if (HidIosCalibrationUtil.checkPoint(obj)) {
                LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onTouchSendPoint calibration points validation SUCCESS, sending END_SUCCESS");
                onTouchSendPointCheckSucc();
            } else {
                LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onTouchSendPoint calibration points validation FAILED, sending END_FAIL");
                HidIosCalibrationUtil.setHidVerificationState(HID_VERIFY_TYPE_VERIFICATION_FAIL);
                onCheckHiTouchEvent(ON_HID_FAIL);
            }
            return;
        }
        LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onTouchSendPoint received null calibration points, FAILED");
        HidIosCalibrationUtil.setHidVerificationState(HID_VERIFY_TYPE_VERIFICATION_FAIL);
        onCheckHiTouchEvent(ON_HID_FAIL);
    }


    /**
     * 反控校准校验成功
     */
    private void onTouchSendPointCheckSucc() {
        LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onTouchSendPointCheckSucc >>> sending END_SUCCESS to iOS");
        mSdkApi.sendIosHidTouchPoint(ON_TOUCH_POINT_END_SUCCESS);
        HidIosCalibrationUtil.setHidVerificationState(HID_VERIFY_TYPE_VERIFICATION_SUCCESS);
        isCalibrationStarted = false; // 重置状态
        onCheckHiTouchEvent(ON_HID_SUCCESS);
        if (autoAgreeCallback != null) {
            autoAgreeCallback.autoAgree(true);
        }
    }

    private void onTouchSendEnd() {
        LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onTouchSendEnd <<< iOS app not in foreground");
        HidIosCalibrationUtil.setBreakDown(true);
        HidIosCalibrationUtil.setHidVerificationState(HID_VERIFY_TYPE_VERIFICATION_FAIL);

        if (isCalibrationStarted) {
            // 场景1：校准过程中切换到后台
            LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onTouchSendEnd calibration interrupted - app switched to background during calibration");
            onCheckHiTouchEvent(ON_HID_APP_BACKGROUND_FAIL);
        } else {
            // 场景2：应用本来就不在前台
            LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onTouchSendEnd calibration failed - app not in foreground");
            onCheckHiTouchEvent(ON_HID_APP_NOT_FOREGROUND);
        }
        isCalibrationStarted = false; // 重置状态
    }

    /**
     * 车机端发起 ，开始校验（等待 iOS 侧确认是否在前台）
     */
    public void startVerification() {
        LogUtil.d(TAG, "SdkViewModel::startVerification()");
        if (HidIosCalibrationUtil.getHidVerificationState() == HID_VERIFY_TYPE_VERIFICATION_ING) {
            LogUtil.e(TAG + TAG_IOS_CALIB, "SdkViewModel:startVerification calibration already in progress, ignoring request, state=HID_VERIFY_TYPE_VERIFICATION_ING");
            return;
        }
        LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:startVerification >>> starting iOS calibration process");
        mIOSCalibrationRetryTimes = 0;
        isCalibrationStarted = false; // 重置状态
        HidIosCalibrationUtil.setBreakDown(false);
        HidIosCalibrationUtil.requestCalibration();
    }

    private CalibrationCallBack callBack = new CalibrationCallBack() {
        @Override
        public void onTouchPointRequest(int state) {
            LogUtil.d(TAG, "SdkViewModel::CalibrationCallBack::onTouchPoint --> onTouchPointRequest state = " + state);
            // 点击页面上校准按钮发送， 如果已经连接蓝牙，则发送 SUCC， 否则是 FAIL。
            if (state == ON_TOUCH_POINT_REQ_SUCCESS) {
                LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onTouchPointRequest >>> sending REQ_SUCCESS to iOS, BT connected, state=" + state);
                mainHandler.removeCallbacks(hidVerifyTimeoutRunnable);
                mSdkApi.sendIosHidTouchPoint(state);
                HidIosCalibrationUtil.setHidVerificationState(HID_VERIFY_TYPE_VERIFICATION_ING);
            } else if (state == ON_TOUCH_POINT_REQ_FAIL) {
                LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onTouchPointRequest >>> sending REQ_FAIL to iOS, BT not connected, state=" + state);
                // TODO(sunwj) 是否应该是ON_HID_CONNECT_FAIL， 否则会重试三次。
                onCheckHiTouchEvent(ON_HID_FAIL);
            } else {
                LogUtil.e(TAG + TAG_IOS_CALIB, "SdkViewModel:onTouchPointRequest error state=" + state);
            }
        }

        @Override
        public void onTouchBeginPoint() {
            LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onTouchBeginPoint >>> sending BEGIN to iOS");
            mSdkApi.sendIosHidTouchPoint(ON_TOUCH_POINT_BEGIN, 1);
        }

        @Override
        public void onTouchEndPoint() {
            LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onTouchEndPoint >>> sending END to iOS");
            mSdkApi.sendIosHidTouchPoint(ON_TOUCH_POINT_END, 1);
        }

        /**
         * 开始预校准
         */
        @Override
        public void onTouchPrepareBeginPoint() {
            LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onTouchPrepareBeginPoint >>> sending PREPARE_BEGIN to iOS");
            mSdkApi.sendIosHidTouchPoint(ON_TOUCH_POINT_PREPARE, 1);
        }

        /**
         * 结束预校准
         */
        @Override
        public void onTouchPrepareEndPoint() {
            LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:onTouchPrepareEndPoint >>> sending PREPARE_END to iOS");
            mSdkApi.sendIosHidTouchPoint(ON_TOUCH_POINT_PREPARE_END, 1);
        }

    };

    public void registerAutoAgreeCallback(AutoAgreeCallback autoAgreeCallback) {
        LogUtil.d(TAG, "SdkViewModel::registerAutoAgreeCallback()");
        this.autoAgreeCallback = autoAgreeCallback;
    }

    public void unregisterAutoAgreeCallback() {
        LogUtil.d(TAG, "SdkViewModel::unregisterAutoAgreeCallback()");
        this.autoAgreeCallback = null;
    }

    public void registerHidCheckCallback(HidCheckCallback callback) {
        LogUtil.d(TAG, "SdkViewModel::registerHidCheckCallback()");
        hidCheckCallback = callback;
    }

    public void unregisterHidCheckCallback(HidCheckCallback callback) {
        LogUtil.d(TAG, "SdkViewModel::unregisterHidCheckCallback()");
        hidCheckCallback = null;
    }


    // 蓝牙与Link设备是否匹配的状态
    private boolean btPairedWithPhone = false;



    /**
     * 更新蓝牙与Link设备的匹配状态
     * @param paired 是否匹配
     */
    public void updateBTPairedWithPhone(boolean paired) {
        LogUtil.d(TAG_BT + TAG_IOS_CALIB, "SdkViewModel:updateBTPairedWithPhone BT paired status=" + paired + ", deviceType=" + mDevType);

        // 将匹配状态传递给HidUtil
        HidUtil.updateBTPairedConnected(paired);
        btPairedWithPhone = paired;
        if (isFirstTimeLinkConnected) {
            LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:updateBTPairedWithPhone first time connected, checking device type");
            // 只有iOS设备才需要处理校准逻辑
            if (isIOSDevice()) {
                if (paired) {
                    // 蓝牙已配对，开始自动校准
                    LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:updateBTPairedWithPhone iOS device detected, BT paired, starting auto calibration");
                    requestCalibration();
                } else {
                    // 蓝牙未配对，发送校准失败消息给手机端，让手机端弹出屏幕录制按钮
                    LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:updateBTPairedWithPhone iOS device detected, BT not paired, sending calibration fail to trigger screen recording");
                    onCheckHiTouchEvent(ON_HID_FAIL);
                }
            } else {
                LogUtil.d(TAG + TAG_IOS_CALIB, "SdkViewModel:updateBTPairedWithPhone non-iOS device (type=" + mDevType + "), skipping calibration");
            }
        }
    }

    /**
     * 检查蓝牙是否与Link设备匹配
     * @return 是否匹配
     */
    public boolean isBTPairedWithPhone() {
        // 返回蓝牙与Link设备是否匹配的状态
        return btPairedWithPhone;
    }

    /**
     * 判断当前连接的设备是否为iOS设备
     * @return true if iOS device (EAP or WIFI_IOS)
     */
    private boolean isIOSDevice() {
        return mDevType == CommonData.DEVICE_TYPE_EAP ||
               mDevType == CommonData.DEVICE_TYPE_WIFI_IOS;
    }

    /**
     * 判断当前连接的设备是否为Android设备
     * @return true if Android device (AOA or WIFI_ANDROID)
     */
    private boolean isAndroidDevice() {
        return mDevType == CommonData.DEVICE_TYPE_AOA ||
               mDevType == CommonData.DEVICE_TYPE_WIFI_ANDROID;
    }

    /**
     * 处理新的车机蓝牙连接协议
     * 根据手机MAC地址判断是否需要进行设备匹配检查
     * @param object 包含连接状态和手机MAC地址的对象
     */
    private void handleCarBTConnected(Object object) {
        try {

            // 新协议：处理包含MAC地址的连接信息
            if (object instanceof CarBTConnectedInfo) {
                CarBTConnectedInfo btInfo =
                    (CarBTConnectedInfo) object;

                boolean connected = btInfo.isConnected();

                LogUtil.i(TAG + TAG_BT, "SdkViewModel::handleCarBTConnected <<< received: " + btInfo.toString());

                // 获取当前A2DP连接的手机MAC地址
                String connectedPhoneMac = getCurrentA2DPConnectedDeviceMac();
                if (connected) {

                    if (!connectedPhoneMac.isEmpty()) {
                        mSdkApi.sendPhoneBTMacAddress(connectedPhoneMac);
                        LogUtil.i(TAG + TAG_BT, "SdkViewModel::handleCarBTConnected >>> sent A2DP connected phone MAC to phone: " + connectedPhoneMac);
                    } else {
                        LogUtil.w(TAG + TAG_BT, "SdkViewModel::handleCarBTConnected no A2DP connected device found");
                    }
                }

                // 判断蓝牙配对状态：connected=true 或者 phoneMac等于connectedPhoneMac
                String phoneMac = btInfo.getPhoneMac();
                boolean isPhoneMacMatched = !phoneMac.isEmpty() && !connectedPhoneMac.isEmpty() &&
                                          phoneMac.equalsIgnoreCase(connectedPhoneMac);
                boolean finalPairedStatus = connected || isPhoneMacMatched;

                LogUtil.i(TAG + TAG_BT, "SdkViewModel::handleCarBTConnected pairing status: connected=" + connected +
                         ", phoneMac=" + phoneMac + ", connectedPhoneMac=" + connectedPhoneMac +
                         ", isPhoneMacMatched=" + isPhoneMacMatched + ", finalPairedStatus=" + finalPairedStatus);


                if (hidCallback != null) {
                    hidCallback.onCarBtPairedConnected(finalPairedStatus);
                }

            } else {
                LogUtil.e(TAG + TAG_BT, "SdkViewModel::handleCarBTConnected unknown object type: " + object.getClass());
            }
        } catch (Exception e) {
            LogUtil.e(TAG + TAG_BT, "SdkViewModel::handleCarBTConnected error handling CAR_BT_CONNECTED", e);
        }
    }




    /**
     * 获取当前A2DP连接的手机设备MAC地址
     * @return 当前连接的手机设备MAC地址，如果没有连接设备则返回空字符串
     */
    public String getCurrentA2DPConnectedDeviceMac() {
        try {
            // 获取当前连接的蓝牙设备（A2DP连接的设备）
            java.util.Set<android.bluetooth.BluetoothDevice> connectedDevices =
                HidUtil.getConnectedBluetoothDevices();

            if (connectedDevices.isEmpty()) {
                LogUtil.i(TAG + TAG_BT, "SdkViewModel::getCurrentA2DPConnectedDeviceMac no A2DP connected devices");
                return "";
            }

            // 通常只有一个A2DP连接的设备，取第一个
            for (android.bluetooth.BluetoothDevice device : connectedDevices) {
                String deviceMac = device.getAddress();
                if (deviceMac != null && !deviceMac.isEmpty()) {
                    LogUtil.i(TAG + TAG_BT, "SdkViewModel::getCurrentA2DPConnectedDeviceMac found A2DP connected device: " +
                             device.getName() + " (" + deviceMac + ")");
                    return deviceMac;
                }
            }

            LogUtil.w(TAG + TAG_BT, "SdkViewModel::getCurrentA2DPConnectedDeviceMac no valid MAC address found");
            return "";
        } catch (Exception e) {
            LogUtil.e(TAG + TAG_BT, "SdkViewModel::getCurrentA2DPConnectedDeviceMac error getting A2DP connected device MAC", e);
            return "";
        }
    }

    public String getIpAddress() {
        LogUtil.d(TAG, "SdkViewModel::getIpAddress");
        return ipAddress;
    }
}

