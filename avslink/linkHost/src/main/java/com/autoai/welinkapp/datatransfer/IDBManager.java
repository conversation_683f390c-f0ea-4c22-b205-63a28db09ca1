package com.autoai.welinkapp.datatransfer;

import android.annotation.SuppressLint;
import android.content.Context;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbManager;

import com.autoai.common.util.LogUtil;
import com.autoai.welink.macro.WL_API_SDK_MSG_TYPE;
import com.autoai.welinkapp.idb.ADBUtil;
import com.autoai.welinkapp.model.SdkViewModel;

import java.util.HashMap;

/**
 * IDB device data transfer, include send and receive thread.
 */
@SuppressWarnings("WeakerAccess")
public class IDBManager {
    private static final String TAG = "WL_DRIVER";
    private static final String FLAG = "IDB";

    @SuppressLint("StaticFieldLeak")
    private static IDBManager mInstance = null;
    private Context mContext = null;
    private UsbManager mUsbManager = null;

    private static final String IDB_SOCKET_HOSTNAME = "localhost";
    private static final int IDB_SOCKET_PHONE_PORT = 6806;
    private static final int IDB_SOCKET_MSG_PORT = 6804;
    private static final int IDB_SOCKET_DATA_PORT = 6819;

    private final static int VID_APPLE = 0x5ac;
    private final static int PID_RANGE_LOW = 0x1290;
    private final static int PID_RANGE_MAX = 0x12af;

    private final static String ADB_FORWARD_TCP = "forward tcp:";
    private final static String WL_MARK_TCP = "tcp:";

    private DeviceManager mDeviceManager = null;

    private TransferThread phoneTransferThread, msgTransferThread, dataTransferThread;

    private IDBManager() {
    }

    public static IDBManager getInstance() {
        if (null == mInstance) {
            synchronized (IDBManager.class) {
                if (null == mInstance) {
                    mInstance = new IDBManager();
                }
            }
        }
        return mInstance;
    }

    public void init(Context context) {
        LogUtil.d(TAG, "IDBManager init");
        mContext = context;
        mUsbManager = (UsbManager) mContext.getSystemService(Context.USB_SERVICE);
        mDeviceManager = new DeviceManager(mContext);
        try {
            phoneTransferThread = new TransferThread(mDeviceManager, IDB_SOCKET_HOSTNAME, IDB_SOCKET_PHONE_PORT, FLAG);
        } catch (Exception e) {
            LogUtil.e(TAG, "Create socket fail");
        }
        try {
            msgTransferThread = new TransferThread(mDeviceManager, IDB_SOCKET_HOSTNAME, IDB_SOCKET_MSG_PORT, FLAG);
        } catch (Exception e) {
            LogUtil.e(TAG, "Create socket fail");
            startForward();
        }
        try {
            dataTransferThread = new TransferThread(mDeviceManager, IDB_SOCKET_HOSTNAME, IDB_SOCKET_DATA_PORT, FLAG);
        } catch (Exception e) {
            LogUtil.e(TAG, "Create socket fail");
        }
    }

    public void deinit() {
        LogUtil.d(TAG, "IDBManager deinit");
        phoneTransferThread.deinit();
        msgTransferThread.deinit();
        dataTransferThread.deinit();
    }

    public void startIDB() {
        LogUtil.d(TAG, "IDBManager startIDB");
        if (!checkDevices()) {
            LogUtil.e(TAG, "check device fail");
        } else {
            phoneTransferThread.start();
            msgTransferThread.start();
            dataTransferThread.start();
        }
    }

    public void stopIDB() {
        LogUtil.d(TAG, "IDBManager stopIDB");
        phoneTransferThread.stop();
        dataTransferThread.stop();
        msgTransferThread.stop();
    }

    private boolean checkDevices() {
        if (mContext == null || mUsbManager == null) {
            LogUtil.e(TAG, "IDBManager checkDevices fail");
            return false;
        }

        HashMap<String, UsbDevice> deviceList = mUsbManager.getDeviceList();
        LogUtil.d(TAG, "IDBManager device count = " + deviceList.size());
        for (UsbDevice device : deviceList.values()) {
            if (device == null) {
                continue;
            }

            if (!isIosDevice(device)) {
                continue;
            }

            if (mDeviceManager.init(mUsbManager, device)) {
                LogUtil.d(TAG, "IDBManager init device success");
                return true;
            }
        }
        return false;
    }

    private boolean isIosDevice(UsbDevice device) {
        int vendorId = device.getVendorId();
        if (vendorId == VID_APPLE) {
            int productId = device.getProductId();
            return PID_RANGE_LOW <= productId && productId <= PID_RANGE_MAX;
        }
        return false;
    }

    private void startForward() {
        try {
            ADBUtil.execCommond(mContext, ADB_FORWARD_TCP +
                    IDB_SOCKET_PHONE_PORT + " " + WL_MARK_TCP + IDB_SOCKET_PHONE_PORT);
            ADBUtil.execCommond(mContext, ADB_FORWARD_TCP +
                    IDB_SOCKET_MSG_PORT + " " + WL_MARK_TCP + IDB_SOCKET_MSG_PORT);
        } catch (Exception e) {
        }
        LogUtil.e(TAG, "startForward");
        SdkViewModel.getInstance().onRecvMsg(WL_API_SDK_MSG_TYPE.CONNECT_ERR, null);
    }
}
