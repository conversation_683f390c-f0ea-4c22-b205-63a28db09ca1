package com.autoai.welinkapp.service;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.autoai.common.util.LogUtil;
import com.autoai.common.util.ProcessUtil;

/**
 * Receive BOOT_COMPLETED Broadcast, then start WifiCheckService.
 */
public class ServiceReceiver extends BroadcastReceiver {
    private static final String TAG = "WL_DRIVER";
    private static final String WELINK_SERVICE_NAME = "com.autoai.welinkapp.service.WifiCheckService";
    private static final String BOOT_COMPLETED_MSG = "android.intent.action.BOOT_COMPLETED";

    public ServiceReceiver() {
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        if (BOOT_COMPLETED_MSG.equals(action)) {
            LogUtil.i(TAG, "ServiceReceiver ACTION_BOOT_COMPLETED");
            Intent service = new Intent();
            service.setAction(WELINK_SERVICE_NAME);
            service.setPackage(context.getPackageName());
            context.startService(service);
            ProcessUtil.printProcess(getClass());
        }
    }

}

