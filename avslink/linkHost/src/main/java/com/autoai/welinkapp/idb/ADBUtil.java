package com.autoai.welinkapp.idb;

import android.content.Context;
import android.text.TextUtils;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.util.ArrayList;

public class ADBUtil {

    private static final byte[] COMMAND_EXIT;
    // 错误缓冲
    private static byte[] BUFFER;

    static {
        COMMAND_EXIT = "\nexit\n".getBytes();
        BUFFER = new byte[32];
    }

    /**
     * 执行命令
     *
     *
     *
     *
     * <pre>
     * eg: "/system/bin/ping", "-c", "4", "-s", "100","www.qiujuer.net"
     * </pre>
     *
     * @return 执行结果
     */
    public static ArrayList<String> execute(ArrayList<String> lists, boolean isUseSU) {
        Process process = null;
        ArrayList<String> lines = new ArrayList<String>();

        BufferedReader bReader = null;
        InputStreamReader isReader = null;

        InputStream in = null;
        InputStream err = null;
        OutputStream out = null;

        try {
            String param = "/system/bin/sh";
            int startIndex = 0;
            if (isUseSU) {
                param = lists.get(0);
                startIndex = 1;
                if (!param.equals("su")) {
                    param = "su";
                    startIndex = 0;
                }
            }

            ProcessBuilder pb = new ProcessBuilder();
            pb.redirectErrorStream(true);
            process = pb.command(param).start();
            out = process.getOutputStream();
            in = process.getInputStream();
            err = process.getErrorStream();

            int size = lists.size();
            // if(listener != null)
            // listener.debug("startIndex="+startIndex+",size="+size+"\n");
            for (int i = startIndex; i < size; i++) {
                String commond = lists.get(i);
                out.write((commond + "\n").getBytes());
                out.flush();
            }

            out.write(COMMAND_EXIT);
            out.flush();

            // if(listener != null)
            // listener.debug("Process start waitFor.\n");

            process.waitFor();

            // if(listener != null)
            // listener.debug("Process stop waitFor.\n");

            isReader = new InputStreamReader(in);
            bReader = new BufferedReader(isReader);

            // if(listener != null)
            // listener.debug("Process start read data.\n");
            String s;
            if ((s = bReader.readLine()) != null) {
                lines.add(s);
                while ((s = bReader.readLine()) != null) {
                    lines.add(s);
                }
            }
            // if(listener != null)
            // listener.debug("Process stop read data.\n");

            // if(listener != null)
            // listener.debug("Process start read err data.\n");

            while ((err.read(BUFFER)) > 0) {
            }

            // if(listener != null)
            // listener.debug("Process stop read err data.\n");
        } catch (IOException e) {
        } catch (Exception e) {
        } finally {

            closeAllStream(out, err, in, isReader, bReader);
            if (process != null) {

                processDestroy(process);
                process = null;
            }
        }
        return lines;
    }


    public static ArrayList<String> execute(ArrayList<String> lists,
                                            String mark, boolean isUseSU) {
        Process process = null;
        ArrayList<String> lines = new ArrayList<String>();

        BufferedReader bReader = null;
        InputStreamReader isReader = null;

        InputStream in = null;
        InputStream err = null;
        OutputStream out = null;

        try {
            String param = "/system/bin/sh";
            int startIndex = 0;
            if (isUseSU) {
                param = lists.get(0);
                startIndex = 1;
                if (!param.equals("su")) {
                    param = "su";
                    startIndex = 0;
                }
            }

            ProcessBuilder pb = new ProcessBuilder();
            pb.redirectErrorStream(true);
            process = pb.command(param).start();
            out = process.getOutputStream();
            in = process.getInputStream();
            err = process.getErrorStream();

            int size = lists.size();
            // if(listener != null)
            // listener.debug("startIndex="+startIndex+",size="+size+"\n");
            for (int i = startIndex; i < size; i++) {
                String commond = lists.get(i);
                out.write((commond + "\n").getBytes());
                out.flush();
            }

            out.write(COMMAND_EXIT);
            out.flush();

            // if(listener != null)
            // listener.debug("Process start waitFor.\n");

            process.waitFor();

            // if(listener != null)
            // listener.debug("Process stop waitFor.\n");

            isReader = new InputStreamReader(in);
            bReader = new BufferedReader(isReader);

            // if(listener != null)
            // listener.debug("Process start read data.\n");
            String s;
            if ((s = bReader.readLine()) != null) {
                lines.add(s);
                while ((s = bReader.readLine()) != null) {
                    lines.add(s);
                    if (s.contains(mark)) {
                        break;
                    }
                    ;
                }
            }
            // if(listener != null)
            // listener.debug("Process stop read data.\n");

            // if(listener != null)
            // listener.debug("Process start read err data.\n");

            while ((err.read(BUFFER)) > 0) {
            }

            // if(listener != null)
            // listener.debug("Process stop read err data.\n");
        } catch (IOException e) {
            // e.printStackTrace();
        } catch (Exception e) {
            // e.printStackTrace();
        } finally {
            closeAllStream(out, err, in, isReader, bReader);
            if (process != null) {
                processDestroy(process);
                process = null;
            }
        }
        return lines;
    }

    /**
     * 在车机上执行无返回值的adb命令
     *
     * @param commonds
     * @throws IOException
     */
    public static void execute2(ArrayList<String> commonds) throws IOException {
        if (commonds == null || commonds.isEmpty()) {
            return;
        }
        {
            Process process = null;
            DataOutputStream os = null;
            try {
                process = Runtime.getRuntime().exec("sh");
                os = new DataOutputStream(process.getOutputStream());

                int size = commonds.size();
                for (int i = 0; i < size; i++) {
                    String commond = commonds.get(i);
                    os.writeBytes(commond + "\n");
                    os.flush();

                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
                os.close();
            } catch (IOException e) {
                throw e;
            } finally {
                closeAllStream(os, null, null, null, null);
                if (process != null) {
                    processDestroy(process);
                }
            }
            return;
        }
    }

    /**
     * 销毁进程
     *
     * @param process 进程
     */
    public static void processDestroy(Process process) {
        if (process != null) {
            try {
                // 判断是否正常退出
                if (process.exitValue() != 0) {
                    killProcess(process);
                }
            } catch (IllegalThreadStateException e) {

                try {
                    process.destroy();
                    killProcess(process);
                } catch (Exception e2) {
                }
            }
        }
    }

    /**
     * 通过Android底层实现进程关闭
     *
     * @param process 进程
     */
    private static void killProcess(Process process) {
        int pid = getProcessId(process);

        if (pid != 0) {
            try {
                android.os.Process.killProcess(pid);
            } catch (Exception e) {
                try {
                    process.destroy();
                } catch (Exception ex) {
                }
            }
        }
    }

    /**
     * 获取进程的ID
     *
     * @param process 进程
     * @return
     */
    private static int getProcessId(Process process) {
        String str = process.toString();
        try {
            int i = str.indexOf("=") + 1;
            int j = str.indexOf("]");
            str = str.substring(i, j);
            return Integer.parseInt(str);
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 关闭所有流
     *
     * @param out      输出流
     * @param err      错误流
     * @param in       输入流
     * @param isReader 输入流封装
     * @param bReader  输入流封装
     */
    private static void closeAllStream(OutputStream out, InputStream err,
                                       InputStream in, InputStreamReader isReader, BufferedReader bReader) {
        if (out != null) {
            try {
                out.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        if (err != null) {
            try {
                err.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        if (in != null) {
            try {
                in.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        if (isReader != null) {
            try {
                isReader.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        if (bReader != null) {
            try {
                bReader.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public static void execCommond(Context context, String commond)
            throws IOException {
        String comStr = commond;
        VarHelper varHelper = VarHelper.getInstance(context);
        if (!commond.startsWith(varHelper.getWlMarkAdb())
                && !commond.startsWith(varHelper.getWlMarkPath())) {
            if (TextUtils.isEmpty(varHelper.getDevice()))
                return;
            comStr = varHelper.getAdb() + varHelper.getDevice() + " " + commond;
        }

        Process process = null;
        OutputStream out = null;
        try {
            String param = "/system/bin/sh";
            ProcessBuilder pb = new ProcessBuilder();
            pb.redirectErrorStream(true);
            process = pb.command(param).start();
            out = process.getOutputStream();

            out.write((comStr + "\n").getBytes());
            out.flush();
            out.write(COMMAND_EXIT);
            out.flush();

            process.waitFor();
        } catch (IOException e) {
        } catch (Exception e) {
        } finally {

            closeAllStream(out, null, null, null, null);
            if (process != null) {
                processDestroy(process);
                process = null;
            }
        }

    }


}
