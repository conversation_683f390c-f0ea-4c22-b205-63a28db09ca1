package com.autoai.welinkapp.model;

/**
 * <AUTHOR>
 */
public interface HidCallback {
    /**
     * 当手机切换横竖屏时调用
     *
     * @param width 手机宽
     * @param height 手机高
     * @param angle 手机方向
     * @param mode 视频流模式，0:方形，1：长方形
     * @param platform 手机类型，Android，ios
     */
    void onScreenRotationChange(int width, int height, int angle, int mode,String platform);
    
    /**
     * 当车机蓝牙连接状态变化时调用
     *
     * @param connected 是否已连接
     */
    void onCarBtPairedConnected(boolean connected);
}
