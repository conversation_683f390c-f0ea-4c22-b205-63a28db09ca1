package com.autoai.welinkapp.datatransfer;

import com.autoai.common.util.LogUtil;
import com.autoai.welinkapp.eap.EapLink;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * EAP device data transfer.
 */
@SuppressWarnings("WeakerAccess")
public class EAPManager {

    private static final boolean CHECK_EAP_MANAGER = LogUtil.isFullLog();

    private static final String TAG = "WL_DRIVER";

    /**
     * 所有 EAP 相关的操作日志
     */
    private static final String TAG_EAP = ".WL_EAP";
    private static EAPManager mInstance = null;
    private Socket mSocket = null;
    private EAPServerThread mEAPServerThread = null;
    private EAPSendThread mEAPSendThread = null;
    private EAPRecvThread mEAPRecvThread = null;
    private static final String EAP_SERVER_THREAD_NAME = "EAPServerThread";
    private static final String EAP_SEND_THREAD_NAME = "EAPSendThread";
    private static final String EAP_RECV_THREAD_NAME = "EAPRecvThread";

    private static final int EAP_BUF_SIZE = 16 * 1024;
    private static final int EAP_SOCKET_PORT = 6850;

    private static boolean isEAPDeviceStart = false;

    public EAPManager() {
    }

    public static EAPManager getInstance() {
        if (null == mInstance) {
            synchronized (EAPManager.class) {
                if (null == mInstance) {
                    mInstance = new EAPManager();
                }
            }
        }
        return mInstance;
    }

    public void init() {
        LogUtil.d(TAG + TAG_EAP, "EAPManager::init start");
        try {
            LogUtil.d(TAG + TAG_EAP, "EAPManager::init EAPServerThread start, port=" + EAP_SOCKET_PORT);
            mEAPServerThread = new EAPServerThread(EAP_SOCKET_PORT);
            mEAPServerThread.start();
        } catch (Exception e) {
            LogUtil.e(TAG + TAG_EAP, "EAPManager::init create socket fail: " + e.getMessage());
        }
        LogUtil.d(TAG + TAG_EAP, "EAPManager::init complete");
    }

    public void deinit() {
        LogUtil.d(TAG + TAG_EAP, "EAPManager::deinit start");
        try {
            if (null != mSocket) {
                LogUtil.d(TAG + TAG_EAP, "EAPManager::deinit close socket");
                mSocket.close();
                mSocket = null;
            }
        } catch (Exception e) {
            LogUtil.e(TAG + TAG_EAP, "EAPManager::deinit socket close exception: " + e.getMessage());
        }
        if (mEAPRecvThread != null) {
            LogUtil.d(TAG + TAG_EAP, "EAPManager::deinit cancel EAPRecvThread");
            mEAPRecvThread.cancel();
            mEAPRecvThread = null;
        }
        if (mEAPSendThread != null) {
            LogUtil.d(TAG + TAG_EAP, "EAPManager::deinit cancel EAPSendThread");
            mEAPSendThread.cancel();
            mEAPSendThread = null;
        }
        if (mEAPServerThread != null) {
            LogUtil.d(TAG + TAG_EAP, "EAPManager::deinit cancel EAPServerThread");
            mEAPServerThread.cancel();
            mEAPServerThread = null;
        }
        if (isEAPDeviceStart) {
            isEAPDeviceStart = false;
            LogUtil.d(TAG + TAG_EAP, "EAPManager::deinit close EAP node");
            closeNode();
        }
        LogUtil.d(TAG + TAG_EAP, "EAPManager::deinit complete");
    }

    public void startEAP() {
        LogUtil.d(TAG + TAG_EAP, "EAPManager::startEAP start");
        LogUtil.i(TAG + TAG_EAP, "EAPManager::startEAP eapBulkOpen start");
        boolean ret = EapLink.eapBulkOpen();
        LogUtil.i(TAG + TAG_EAP, "EAPManager::startEAP eapBulkOpen end");
        if (!ret) {
            LogUtil.e(TAG + TAG_EAP, "EAPManager::startEAP open eap node fail");
        } else {
            isEAPDeviceStart = true;
            LogUtil.i(TAG + TAG_EAP, "EAPManager::startEAP open eap node success");
        }
    }

    public void stopEAP() {
        LogUtil.d(TAG + TAG_EAP, "EAPManager::stopEAP start");
        if (mEAPRecvThread != null) {
            LogUtil.d(TAG + TAG_EAP, "EAPManager::stopEAP cancel EAPRecvThread");
            mEAPRecvThread.cancel();
            mEAPRecvThread = null;
        }
        if (mEAPSendThread != null) {
            LogUtil.d(TAG + TAG_EAP, "EAPManager::stopEAP cancel EAPSendThread");
            mEAPSendThread.cancel();
            mEAPSendThread = null;
        }
        if (isEAPDeviceStart) {
            isEAPDeviceStart = false;
            LogUtil.d(TAG + TAG_EAP, "EAPManager::stopEAP close EAP node");
            closeNode();
        }
        LogUtil.d(TAG + TAG_EAP, "EAPManager::stopEAP complete");
    }

    private void closeNode() {
        int len;
//        byte[] buf = new byte[EAP_BUF_SIZE];
        LogUtil.d(TAG + TAG_EAP, "EAPManager::closeNode read before close eap node");
        do {
//            Arrays.fill(buf, (byte) 0x00);
//            len = EapLink.eapBulkRead(buf, buf.length);
            byte[] buffer = EapLink.eapBulkRead(EAP_BUF_SIZE);
            if(buffer == null || buffer.length == 0){
                len = -1;
            }else{
                len = buffer.length;
            }
            LogUtil.d(TAG + TAG_EAP, "EAPManager::closeNode len=" + len);
        } while (len > 0);
        LogUtil.i(TAG + TAG_EAP, "EAPManager::closeNode eapBulkClose start");
        EapLink.eapBulkClose();
        LogUtil.i(TAG + TAG_EAP, "EAPManager::closeNode eapBulkClose end");
    }

    private class EAPServerThread extends Thread {
        private boolean isRunning = false;
        private ServerSocket mServerSocket = null;
        private Socket mThreadSocket = null;
        private BufferedInputStream mInputStream = null;
        private BufferedOutputStream mOutputStream = null;

        public EAPServerThread(int port) {
            LogUtil.d(TAG + TAG_EAP, "EAPServerThread::init port=" + port);
            setName(EAP_SERVER_THREAD_NAME);

            try {
                mServerSocket = new ServerSocket();
                mServerSocket.setReuseAddress(true);
                mServerSocket.bind(new InetSocketAddress(port));
                isRunning = true;
                LogUtil.d(TAG + TAG_EAP, "EAPServerThread::init socket bind success");
            } catch (Exception e) {
                LogUtil.e(TAG + TAG_EAP, "EAPServerThread::init create socket fail: " + e.getMessage());
            }
        }

        public void cancel() {
            if (null != mServerSocket) {
                try {
                    mServerSocket.close();
                } catch (IOException e) {
                    LogUtil.e(TAG, "close mServerSocket get IOException");
                }
                mServerSocket = null;
            }
            if (null != mThreadSocket) {
                try {
                    mThreadSocket.close();
                } catch (IOException e) {
                    LogUtil.e(TAG, "close mThreadSocket get IOException");
                }
                mThreadSocket = null;
            }
            if (null != mInputStream) {
                try {
                    mInputStream.close();
                } catch (IOException e) {
                    LogUtil.e(TAG, "close mInputStream get IOException");
                }
                mInputStream = null;
            }
            if (null != mOutputStream) {
                try {
                    mOutputStream.close();
                } catch (IOException e) {
                    LogUtil.e(TAG, "close mOutputStream get IOException");
                }
                mOutputStream = null;
            }

            isRunning = false;
        }

        public void closeSocket() {
            if (null != mInputStream) {
                try {
                    mInputStream.close();
                } catch (IOException e) {
                    LogUtil.e(TAG, "close mInputStream get IOException");
                }
                mInputStream = null;
            }
            if (null != mOutputStream) {
                try {
                    mOutputStream.close();
                } catch (IOException e) {
                    LogUtil.e(TAG, "close mOutputStream get IOException");
                }
                mOutputStream = null;
            }
            if (mSocket != null) {
                try {
                    mSocket.close();
                } catch (IOException e) {
                    LogUtil.e(TAG, "close mSocket get IOException");
                }
                mSocket = null;
            }
        }

        @Override
        public void run() {
            LogUtil.d(TAG + TAG_EAP, "EAPServerThread::run begin listen");
            try {
                while (isRunning) {
                    mThreadSocket = mServerSocket.accept();
                    LogUtil.d(TAG + TAG_EAP, "EAPServerThread::run <--- client connected");
                    if (null == mThreadSocket) {
                        LogUtil.e(TAG + TAG_EAP, "EAPServerThread::run client connect fail");
                        continue;
                    }
                    if (!isEAPDeviceStart) {
                        LogUtil.e(TAG + TAG_EAP, "EAPServerThread::run EAP not started, close connection");
                        mThreadSocket.close();
                        mThreadSocket = null;
                        continue;
                    }
                    if ((null == mInputStream) && (null == mOutputStream)) {
                        LogUtil.d(TAG + TAG_EAP, "EAPServerThread::run setup streams and start threads");
                        mSocket = mThreadSocket;
                        mSocket.setTcpNoDelay(true);
                        mInputStream = new BufferedInputStream(mSocket.getInputStream());
                        mOutputStream = new BufferedOutputStream(mSocket.getOutputStream());

                        LogUtil.d(TAG + TAG_EAP, "EAPServerThread::run start EAPSendThread");
                        mEAPSendThread = new EAPSendThread();
                        mEAPSendThread.start();
                        LogUtil.d(TAG + TAG_EAP, "EAPServerThread::run start EAPRecvThread");
                        mEAPRecvThread = new EAPRecvThread();
                        mEAPRecvThread.start();
                    } else {
                        LogUtil.e(TAG + TAG_EAP, "EAPServerThread::run threads already started, close connection");
                        mThreadSocket.close();
                        mThreadSocket = null;
                    }

                    sleep(100);
                }
                LogUtil.e(TAG + TAG_EAP, "EAPServerThread::run loop stopped");
            } catch (Exception e) {
                LogUtil.e(TAG + TAG_EAP, "EAPServerThread::run exception: " + e.getMessage());
            }

            LogUtil.d(TAG + TAG_EAP, "EAPServerThread::run thread exit");
        }
    }

    private class EAPSendThread extends Thread {
        private boolean isRunning;
        private final byte[] buffer = new byte[EAP_BUF_SIZE];

        public EAPSendThread() {
            LogUtil.d(TAG + TAG_EAP, "EAPSendThread::init created");
            setName(EAP_SEND_THREAD_NAME);
            isRunning = true;
        }

        public void cancel() {
            LogUtil.d(TAG + TAG_EAP, "EAPSendThread::cancel stop running");
            isRunning = false;
        }

        public int readData(byte[] data, int offset, int len) {
            if (mEAPServerThread.mInputStream == null) {
                LogUtil.e(TAG + TAG_EAP, "EAPSendThread::readData mInputStream is null");
                return -1;
            }
            int ret;
            try {
                ret = mEAPServerThread.mInputStream.read(data, offset, len);
            } catch (IOException e) {
                LogUtil.e(TAG + TAG_EAP, "EAPSendThread::readData IOException: " + e.getMessage());
                return -2;
            }
            return ret;
        }

        public int writeData(byte[] data, int offset, int len) {
            if (mEAPServerThread.mOutputStream == null) {
                LogUtil.e(TAG + TAG_EAP, "EAPSendThread::writeData mOutputStream is null");
                return -1;
            }
            try {
                mEAPServerThread.mOutputStream.write(data, offset, len);
                mEAPServerThread.mOutputStream.flush();
                if (CHECK_EAP_MANAGER)
                    LogUtil.v(TAG + TAG_EAP, "EAPSendThread::writeData ---> EAP to Socket, len=" + len);
            } catch (IOException e) {
                LogUtil.e(TAG + TAG_EAP, "EAPSendThread::writeData IOException: " + e.getMessage());
                return -2;
            }
            return len;
        }

        @Override
        public void run() {
            LogUtil.d(TAG + TAG_EAP, "EAPSendThread::run start");
            while (mSocket != null && isRunning) {
                if (!mSocket.isConnected()) {
                    LogUtil.e(TAG + TAG_EAP, "EAPSendThread::run socket disconnected");
                    break;
                }

                Arrays.fill(buffer, (byte) 0x00);
                int len = readData(buffer, 0, EAP_BUF_SIZE);
                if (len < 0) {
                    LogUtil.e(TAG + TAG_EAP, "EAPSendThread::run read socket fail, ret=" + len);
                    break;
                }
                if (CHECK_EAP_MANAGER)
                    LogUtil.v(TAG + TAG_EAP, "EAPSendThread::run ---> Socket to EAP, len=" + len);

                int ret = EapLink.eapBulkWrite(buffer, len);
                if (CHECK_EAP_MANAGER)
                    LogUtil.v(TAG + TAG_EAP, "EAPSendThread::run eapBulkWrite end, ret=" + ret);
                if (ret < 0) {
                    LogUtil.e(TAG + TAG_EAP, "EAPSendThread::run write EAP fail, ret=" + ret);
                    break;
                }
            }

            if (mEAPServerThread != null) {
                mEAPServerThread.closeSocket();
            }
            LogUtil.d(TAG + TAG_EAP, "EAPSendThread::run exit");
        }
    }

    private class EAPRecvThread extends Thread {
        private boolean isRunning;
//        private final byte[] bufferByte = new byte[EAP_BUF_SIZE];

        public EAPRecvThread() {
            LogUtil.d(TAG + TAG_EAP, "EAPRecvThread::init created");
            setName(EAP_RECV_THREAD_NAME);
            isRunning = true;
        }

        public void cancel() {
            LogUtil.d(TAG + TAG_EAP, "EAPRecvThread::cancel stop running");
            isRunning = false;
        }

        @Override
        public void run() {
            LogUtil.d(TAG + TAG_EAP, "EAPRecvThread::run start");
            while (mSocket != null && isRunning) {
                if (!mSocket.isConnected()) {
                    LogUtil.e(TAG + TAG_EAP, "EAPRecvThread::run socket disconnected");
                    break;
                }

//                Arrays.fill(bufferByte, (byte) 0x00);
                 byte[] buffer = EapLink.eapBulkRead(EAP_BUF_SIZE);

                if (buffer == null) {
                    LogUtil.e(TAG + TAG_EAP, "EAPRecvThread::run buffer is null, read EAP fail");
                    break;
                } else if (buffer.length == 0) {
//                    if (CHECK_EAP_MANAGER)
//                        LogUtil.v(TAG, "eapBulkRead end, len: " + len + ", continue");
                    try {
                        sleep(5);
                    } catch (InterruptedException e) {
                        LogUtil.e(TAG + TAG_EAP, "EAPRecvThread::run InterruptedException: " + e.getMessage());
                    }
                    continue;
                }

                if (CHECK_EAP_MANAGER) {
                    LogUtil.v(TAG + TAG_EAP, "EAPRecvThread::run <--- EAP to Socket, len=" + buffer.length);
                }
                try {
//                    int len = buffer.size();
//                    byte[] buf = new byte[len];
//                    for(int i = 0;i < len;i++){
//                        buf[i] = buffer.get(i);
//                     }
                    int ret = mEAPSendThread.writeData(buffer, 0, buffer.length);
                    if (ret < 0) {
                        LogUtil.e(TAG + TAG_EAP, "EAPRecvThread::run write socket fail, ret=" + ret);
                        break;
                    }
                } catch (NullPointerException e) {
                    LogUtil.e(TAG + TAG_EAP, "EAPRecvThread::run mEAPSendThread is null");
                }
            }

            if (mEAPServerThread != null) {
                mEAPServerThread.closeSocket();
            }
            LogUtil.d(TAG + TAG_EAP, "EAPRecvThread::run exit");
            LogUtil.d(TAG, "EAPRecvThread exit !!!");
        }
    }

}
