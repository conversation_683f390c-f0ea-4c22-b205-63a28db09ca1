package com.autoai.welinkapp.model;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.Log;

import com.autoai.common.util.LogUtil;
import com.autoai.welink.macro.WL_API_APP_MSG_TYPE;
import com.autoai.welink.macro.WL_API_HARD_KEY;
import com.autoai.welink.param.TurnByTurnInfo;
import com.autoai.welinkapp.platform.LinkPlatform;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.ref.WeakReference;

public class PlatformAdaptor {
    private final static String TAG = "PlatformAdaptor";
    private static int sNativeVrStatus = NATIVE_VR_STATUS.NATIVE_VR_END;
    private static int sPhoneVrStatus = PHONE_VR_STATUS.PHONE_VR_END;

    private int rvcStartState = BACKING_UP_STATE.END;
    private MyHandler handler;
    private LinkPlatform platform;
    private final SdkViewModel mSdkViewModel;


    private boolean showState = false;

    public PlatformAdaptor(SdkViewModel mSdkViewModel) {
        Log.i(TAG,"PlatformAdaptor::<init>");
        this.mSdkViewModel = mSdkViewModel;
    }

    public void setPlatform(LinkPlatform platform) {
        Log.i(TAG,"PlatformAdaptor::setPlatform");
        handler = new MyHandler(this, platform);
        this.platform = platform;
    }

    public void sendHuReqAppData(int i) {
        Log.i(TAG,"PlatformAdaptor::sendHuReqAppData");
        SdkViewModel.getInstance().getSdkApi().sendMsg(WL_API_APP_MSG_TYPE.HU_REQ_APP_DATA, i);
    }

    public void musicPlay() {
        Log.i(TAG,"PlatformAdaptor::musicPlay");
        SdkViewModel.getInstance().getSdkApi().sendMsg(WL_API_APP_MSG_TYPE.MUSIC_PLAY, 0);
    }

    public void musicStop() {
        Log.i(TAG,"PlatformAdaptor::musicStop");
        SdkViewModel.getInstance().getSdkApi().sendMsg(WL_API_APP_MSG_TYPE.MUSIC_STOP, 0);
    }

    public void startBackingUp() {
        Log.i(TAG,"PlatformAdaptor::startBackingUp");
        SdkViewModel.getInstance().getSdkApi().sendHuRvcState(1);
        if ((showState) && (null != mSdkViewModel)) {
            mSdkViewModel.stopCheckConnect();
        }
        rvcStartState = PlatformAdaptor.BACKING_UP_STATE.START;
    }

//    public void endBackingUp() {
//        SdkViewModel.getInstance().getSdkApi().sendHuRvcState(2);
//        if ((showState) && (null != mSdkViewModel)) {
//            mSdkViewModel.startCheckConnect();
//        }
//        rvcStartState = PlatformAdaptor.BACKING_UP_STATE.END;
//    }

    public String getDeviceSerialNum() {
        Log.i(TAG,"PlatformAdaptor::getDeviceSerialNum");
        if (platform != null) {
            return platform.getDeviceSerialNum();
        } else {
            return "";
        }
    }


    public interface CONNECT_STATE {
        int DISCONNECT = 1;   //未连接
        int CONNECTED = 2;   //连接成功
        int CONNECTING = 3;   //连接中
        int CONNECTERR = 4;  //断开连接
    }

    public interface BACKING_UP_STATE {
        int START = 0;   //开始倒车
        int END = 1;     //结束倒车
    }

    public interface MSG_TYPE {
        int AP_MSG_APP_STATE = 1;
        int AP_MSG_CONNECT_STATE = 2;
        int AP_MSG_DRIVING_RECORDER_RES = 3;
        int AP_MSG_ID3_DATA = 4;
        int AP_MSG_MUSIC_PLAY_STATE_RES = 5;
        int AP_MSG_TBT_DATA = 6;
        int AP_MSG_NAVI_STATE = 7;
        int MAX_NUM = 8;
    }

    public interface NATIVE_VR_STATUS {
        int NATIVE_VR_START = 1;
        int NATIVE_VR_ING = 2;
        int NATIVE_VR_END = 3;
    }

    public interface PHONE_VR_STATUS {
        int PHONE_VR_START = 1;
        int PHONE_VR_ING = 2;
        int PHONE_VR_END = 3;
    }


    //-----------------------------------send broadcast from wl to ds------------------------------------------------
    public int sendMsg(int msg, int arg1, Object para) {
        Log.i(TAG,"PlatformAdaptor::sendMsg");
        LogUtil.v(TAG, "sendMsg:msg =" + msg);
        if (0 >= msg || MSG_TYPE.MAX_NUM <= msg) {
            LogUtil.e(TAG, "THIS MSG IS NOT EXIST");
            return -1;
        }

        if (handler == null) {
            LogUtil.e(TAG, "LINK PLATFORM IS NOT EXIST");
            return -1;
        }

        Message message = new Message();
        message.what = msg;
        message.arg1 = arg1;
        message.obj = para;
        handler.sendMessage(message);
        return 0;
    }

    private static class MyHandler extends Handler {

        private final WeakReference<LinkPlatform> linkPlatformWeakReference;
        private final WeakReference<PlatformAdaptor> platformAdaptorWeakReference;

        public MyHandler(PlatformAdaptor adaptor, LinkPlatform platform) {
            super(Looper.myLooper());
            linkPlatformWeakReference = new WeakReference<>(platform);
            platformAdaptorWeakReference = new WeakReference<>(adaptor);
        }

        @Override
        public void handleMessage(Message msg) {
            LinkPlatform platform = linkPlatformWeakReference.get();
            if (platform == null) {
                return;
            }
            PlatformAdaptor adaptor = platformAdaptorWeakReference.get();
            if (adaptor == null) {
                return;
            }

            switch (msg.what) {
                case MSG_TYPE.AP_MSG_APP_STATE:
                    adaptor.showState = (boolean) msg.obj;
                    platform.noticeAppState((boolean) msg.obj);
                    break;
                case MSG_TYPE.AP_MSG_CONNECT_STATE:
                    platform.noticeConnectState(msg.arg1);
                    platform.noticeDeviceType((int) msg.obj, 0);
                    break;
                case MSG_TYPE.AP_MSG_DRIVING_RECORDER_RES:
                    break;
                case MSG_TYPE.AP_MSG_ID3_DATA:
                    platform.noticeTrackInfo(msg.obj);
                    break;
                case MSG_TYPE.AP_MSG_MUSIC_PLAY_STATE_RES:
                    platform.noticeMediaState((int) msg.obj);
                    break;
                case MSG_TYPE.AP_MSG_TBT_DATA:
                    StringBuffer mSegRemainDisInfo = new StringBuffer();
                    StringBuffer mTotalDistanceInfo = new StringBuffer();
                    setTurnByTurnInfo((TurnByTurnInfo) msg.obj, mSegRemainDisInfo, mTotalDistanceInfo);
                    LogUtil.d(TAG, "TBTroad ---" + mSegRemainDisInfo);
                    LogUtil.d(TAG, "TBTtotal ---" + mTotalDistanceInfo);
                    platform.noticeNaviInfo(mSegRemainDisInfo.toString(), mTotalDistanceInfo.toString());
                    break;
                case MSG_TYPE.AP_MSG_NAVI_STATE:
                    platform.noticeNaviState((int) msg.obj);
                    break;
                default:
                    break;
            }
        }
    }


    public void SetNativeVrStatus(int status) {
        sNativeVrStatus = status;
    }

    public int GetBgVrStatus() {
        return sNativeVrStatus;
    }

    public void SetPhoneVrStatus(int status) {
        sPhoneVrStatus = status;
    }

    private int GetPhoneVrStatus() {
        return sPhoneVrStatus;
    }

    public void NativeVrStart() {
        LogUtil.d(TAG, "PlatformAdaptor::NativeVrStart");
        SetNativeVrStatus(NATIVE_VR_STATUS.NATIVE_VR_START);
        mSdkViewModel.getSdkApi().sendMsg(WL_API_APP_MSG_TYPE.HU_LOCAL_VR_STATE, 1);
    }

    public void NativeVrEnd() {
        LogUtil.d(TAG, "PlatformAdaptor::NativeVrEnd");

        SetNativeVrStatus(NATIVE_VR_STATUS.NATIVE_VR_END);
        mSdkViewModel.getSdkApi().sendMsg(WL_API_APP_MSG_TYPE.HU_LOCAL_VR_STATE, 2);
    }

    private static void setTurnByTurnInfo(TurnByTurnInfo src, StringBuffer roadinfo, StringBuffer totalinfo) {
        LogUtil.d(TAG, "PlatformAdaptor::setTurnByTurnInfo");
        JSONObject jsonObjectroad = new JSONObject();
        JSONObject jsonObjecttotal = new JSONObject();

        try {
            jsonObjectroad.put("SegRemainDis", src.roadDistance);
            jsonObjectroad.put("RoadType", 0);
            jsonObjectroad.put("RoadIcon", src.naviCode);
            jsonObjectroad.put("RoadName", src.currentRoadName);
            LogUtil.d(TAG, "TBT ---" + src.currentRoadName);
            jsonObjectroad.put("NextNaviActionProgbar", src.nextProgress);
            jsonObjectroad.put("IntersectionZoomStatus", 0);

            jsonObjecttotal.put("TotalDistance", src.remainDistance);
            jsonObjecttotal.put("DistanceUint", 1);
            jsonObjecttotal.put("TimeLeft", src.remainTime);
            jsonObjecttotal.put("ArrivalTime", "NULL");

        } catch (JSONException e) {
            e.printStackTrace();
        }
        LogUtil.d(TAG, "TBTroad ---" + jsonObjectroad.toString());
        LogUtil.d(TAG, "TBTtotal ---" + jsonObjecttotal.toString());
        roadinfo.append(jsonObjectroad.toString());
        totalinfo.append(jsonObjecttotal.toString());

    }


    public int getRvcStartState() {
        return rvcStartState;
    }

    public void shortkeyUpPre() {
        LogUtil.d(TAG, "PlatformAdaptor::shortkeyUpPre short press=>KEYCODE_MEDIA_PREVIOUS!");
        mSdkViewModel.getSdkApi().sendHardKey(WL_API_HARD_KEY.WL_API_HARD_KEY_PRE, 0);
    }

    public void shortKeyUpNext() {
        LogUtil.d(TAG, "PlatformAdaptor::shortKeyUpNext short press=>KEYCODE_MEDIA_NEXT!");
        mSdkViewModel.getSdkApi().sendHardKey(WL_API_HARD_KEY.WL_API_HARD_KEY_NEXT, 0);
    }

    private int getVRState() {
        Log.i(TAG,"PlatformAdaptor::getVRState");
        if (sNativeVrStatus == NATIVE_VR_STATUS.NATIVE_VR_END) {
            if (sPhoneVrStatus == PHONE_VR_STATUS.PHONE_VR_END) {
                LogUtil.d(TAG, "getVRState ----------- 1");
                return 1;
            } else if (sPhoneVrStatus == PHONE_VR_STATUS.PHONE_VR_START) {
                LogUtil.d(TAG, "getVRState ----------- 2");
                return 2;
            }
        } else if (sNativeVrStatus == NATIVE_VR_STATUS.NATIVE_VR_START) {
            if (sPhoneVrStatus == PHONE_VR_STATUS.PHONE_VR_END) {
                LogUtil.d(TAG, "getVRState ----------- 3");
                return 3;
            } else if (sPhoneVrStatus == PHONE_VR_STATUS.PHONE_VR_START) {
                LogUtil.d(TAG, "getVRState ----------- 4");
                return 4;
            }
        }
        LogUtil.d(TAG, "getVRState ----------- 5");
        return 5;
    }

    public void LongKeyPressVrHandle() {
        Log.i(TAG,"PlatformAdaptor::LongKeyPressVrHandle");
        int vrState = getVRState();
        if (vrState == 1) {
            //run svr
            LogUtil.d(TAG, "LongKeyPress Run SVR -----------");
            if (mSdkViewModel.connectStatus == PlatformAdaptor.CONNECT_STATE.CONNECTED) {
                mSdkViewModel.getSdkApi().sendHardKey(WL_API_HARD_KEY.WL_API_HARD_KEY_VR_START, 0);
                if (!showState) {
                    platform.showWelink();
                }
            }
        }
    }

    public void keyPressVrHandle() {
        Log.i(TAG,"PlatformAdaptor::keyPressVrHandle");
        int vrState = getVRState();
        if (vrState == 2) {
            //stop svr
            LogUtil.d(TAG, "keyPress Stop SVR -----------");
            if (mSdkViewModel.connectStatus == PlatformAdaptor.CONNECT_STATE.CONNECTED) {
                mSdkViewModel.getSdkApi().sendHardKey(WL_API_HARD_KEY.WL_API_HARD_KEY_VR_END, 0);
            }
        }
    }

    public boolean isShowState() {
        Log.i(TAG,"PlatformAdaptor::isShowState");
        return showState;
    }
}
