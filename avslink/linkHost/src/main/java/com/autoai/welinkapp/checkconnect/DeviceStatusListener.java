package com.autoai.welinkapp.checkconnect;

/**
 * <AUTHOR>
 */
public interface DeviceStatusListener {
    /**
     * 获取到设备
     * @param type 参考{@link CommonData} DEVICE_TYPE_
     * @param ip WiFi互联的ip地址
     */
    void onDeviceIn(int type, String ip);

    /**
     * 断开设备
     * @param type 参考{@link CommonData} DEVICE_TYPE_
     */
    void onDeviceOut(int type);

    /**
     * 设备连接中
     * @param type 参考{@link CommonData} DEVICE_TYPE_
     */
    void onDevicesConnecting(int type);
}
