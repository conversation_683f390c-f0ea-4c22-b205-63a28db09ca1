package com.autoai.welinkapp.util;

import android.content.Context;
import android.graphics.Point;
import android.graphics.Rect;
import android.util.DisplayMetrics;
import android.view.Display;
import android.view.TouchDelegate;
import android.view.View;
import android.view.WindowManager;

import androidx.annotation.NonNull;

import com.autoai.common.util.LogUtil;

/**
 * shecw last checked 2025/4/15
 * 窗体Window 相关数值管理util
 */
public class WindowUtil {

    public static final String TAG = "WindowUtil";
    /**
     * 从手机端传递--> 车机端 实际录屏视频流宽
     */
    private int mVideoWidth;

    /**
     * 从手机端传递--> 车机端 实际录屏视频流高
     */
    private int mVideoHeight;
    /**
     * 车机端屏幕 实际 宽
     */
    private int mScreenHeight;

    /**
     * 车机端屏幕 实际 高
     */
    private int mScreenWidth;

    /**
     * 当前横竖屏
     */
    private boolean mIsVertical = false;

    /**
     * 显示surface 大小宽高  实际视频流缩放的大小
     * 初始化默认使用 mScreenWidth|mScreenHeight 初始化，互联成功后，
     * 会使用手机端视频流实际尺寸
     * she tips:会传到native so中，互联握手会发送给手机端
     */
    private int mSurfaceWidth = 1920;
    private int mSurfaceHeight = 1080;

    /**
     * 悬浮窗的位置（x,y）,锚定点为左上
     */
    private final Point mSurfacePoint = new Point(0, 0);

    /**
     * 单例引用
     */
    private static WindowUtil mWindowUtil = null;

    /**
     * 单例私有构造
     * @param context
     */
    private WindowUtil(Context context) {
        //--> fixme：getDisplayMetrics和getDisplayMetrics是否重复，后续check下进行优化
        Display display = getDisplayDefault(context);
        //--> 获取当前屏幕尺寸，并赋值
        getDisplayMetrics(display);
        //--> 初始化渲染Surface尺寸
        setSurfaceInfo();
        LogUtil.d(TAG, "density => " + context.getResources().getDisplayMetrics().density);

    }

    /**
     * Instance 单例
     * @param context
     * @return
     */
    public static synchronized WindowUtil getInstance(Context context) {
        if (mWindowUtil == null) {
            mWindowUtil = new WindowUtil(context);
        }
        return mWindowUtil;
    }

    /**
     * fixme：是否与{@link #getDisplayMetrics(Display)}重复，考虑删除
     * @param context
     * @return
     */
    private Display getDisplayDefault(Context context) {
        Point point = new Point();
        WindowManager wm = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        Display display = wm.getDefaultDisplay();
        display.getSize(point);
        LogUtil.d(TAG, "point => " + point.x + " " + point.y);
        mScreenWidth = point.x;
        mScreenHeight = point.y;
        return display;
    }

    /**
     * 获取当前屏幕尺寸，并赋值
     * @param display
     */
    private void getDisplayMetrics(Display display) {
        DisplayMetrics dm = new DisplayMetrics();
        display.getRealMetrics(dm);
        mScreenWidth = dm.widthPixels;
        mScreenHeight = dm.heightPixels;

        mSurfaceWidth = dm.widthPixels;
        mSurfaceHeight = dm.heightPixels;
    }

    //一个Parent只能设置一个View的TouchDelegate，设置多个时只有最后设置的生效
    public static void expandTouchArea(final View view, final int size) {
        final View parentView = (View) view.getParent();
        parentView.post(() -> {
            Rect rect = new Rect();
            view.getHitRect(rect);

            rect.top -= size;
            rect.bottom += size;
            rect.left -= size;
            rect.right += size;

            parentView.setTouchDelegate(new TouchDelegate(rect, view));
        });
    }


    /**
     * 赋值
     */
    private void setSurfaceInfo() {
        if (mScreenWidth < mScreenHeight) {
            mIsVertical = true;
        }
    }

    public int getScreenHeight() {
        return mScreenHeight;
    }
    public void setScreenHeight(int height){
        mScreenHeight = height;
    }

    public int getScreenWidth() {
        return mScreenWidth;
    }

    public void setScreenWH(int width,int height){
        mScreenHeight = height;
        mScreenWidth = width;
        setSurfaceInfo();
    }

    public void setVideoWH(int width,int height){
        mVideoWidth = width;
        mVideoHeight = height;
    }

    public int getVideoWidth() {
        return mVideoWidth;
    }

    public int getVideoHeight() {
        return mVideoHeight;
    }

    public Point getSurfacePoint() {
        return mSurfacePoint;
    }

    public int getSurfaceWidth() {
        return mSurfaceWidth;
    }

    public int getSurfaceHeight() {
        return mSurfaceHeight;
    }

    public boolean isVertical() {
        return mIsVertical;
    }


    public void setSurfaceHeight(int surfaceHeight) {
        this.mSurfaceHeight = surfaceHeight;
    }

    public void setSurfaceWidth(int surfaceWidth) {
        this.mSurfaceWidth = surfaceWidth;
    }

    @NonNull
    @Override
    public String toString() {
        return "WindowUtil{" +
                "mScreenHeight=" + mScreenHeight +
                ", mScreenWidth=" + mScreenWidth +
                ", mSurfaceWidth=" + mSurfaceWidth +
                ", mSurfaceHeight=" + mSurfaceHeight +
                ", mIsVertical=" + mIsVertical +
                ", mSurfacePoint=" + mSurfacePoint +
                '}';
    }
}
