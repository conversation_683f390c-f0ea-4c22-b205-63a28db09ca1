package com.autoai.welinkapp.datatransfer;

import com.autoai.common.util.LogUtil;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.Arrays;

/**
 * <AUTHOR>
 */
public class TransferThread {

    private static final String TAG = "WL_DRIVER";

    private static final boolean CHECK_TRANSFER = LogUtil.isFullLog();
    private Socket mSocket = null;

    private ServerThread mServerThread = null;
    private SendThread mSendThread = null;
    private RecvThread mRecvThread = null;
    private static final String SERVER_THREAD_NAME = "ServerThread";
    private static final String SEND_THREAD_NAME = "SendThread";
    private static final String RECV_THREAD_NAME = "RecvThread";

    private static final int BUF_SIZE = 16 * 1024;


    private DeviceManager mDeviceManager;
    private int port;
    private String flag;
    private boolean isDeviceStart = false;

    public TransferThread(DeviceManager mDeviceManager, String hostName, int port, String flag) {
        this.mDeviceManager = mDeviceManager;
        this.port = port;
        this.flag = flag;
        try {
            mServerThread = new ServerThread(hostName, port);
            mServerThread.start();
        } catch (Exception e) {
            logE("Create socket fail");
        }
    }

    public void deinit() {
        try {
            if (null != mSocket) {
                mSocket.close();
                mSocket = null;
            }
        } catch (Exception e) {
            logE("Exception in deinit");
        }
        if (mRecvThread != null) {
            mRecvThread.cancel();
            mRecvThread = null;
        }
        if (mSendThread != null) {
            mSendThread.cancel();
            mSendThread = null;
        }
        if (mServerThread != null) {
            mServerThread.cancel();
            mServerThread = null;
        }
        isDeviceStart = false;
    }

    public void start() {
        isDeviceStart = true;
    }

    public void stop() {
        if (mRecvThread != null) {
            mRecvThread.cancel();
            mRecvThread = null;
        }
        if (mSendThread != null) {
            mSendThread.cancel();
            mSendThread = null;
        }
        isDeviceStart = false;
    }

    public boolean isDeviceStart() {
        return isDeviceStart;
    }

    private class ServerThread extends Thread {
        private boolean isRunning = false;
        private ServerSocket mServerSocket = null;
        private Socket mThreadSocket = null;
        private BufferedInputStream mInputStream = null;
        private BufferedOutputStream mOutputStream = null;

        public ServerThread(String hostName, int port) {
            logD("ServerThread Created, port: " + port);
            setName(flag + SERVER_THREAD_NAME + port);

            try {
                mServerSocket = new ServerSocket();
                mServerSocket.setReuseAddress(true);
                mServerSocket.bind(new InetSocketAddress(hostName, port));
                isRunning = true;
            } catch (Exception e) {
                logE("Create Socket Fail");
            }
        }

        public void cancel() {
            if (null != mServerSocket) {
                try {
                    mServerSocket.close();
                } catch (IOException e) {
                    logE("close mServerSocket get IOException");
                }
                mServerSocket = null;
            }
            if (null != mThreadSocket) {
                try {
                    mThreadSocket.close();
                } catch (IOException e) {
                    logE("close mThreadSocket get IOException");
                }
                mThreadSocket = null;
            }
            if (null != mInputStream) {
                try {
                    mInputStream.close();
                } catch (IOException e) {
                    logE("close mInputStream get IOException");
                }
                mInputStream = null;
            }
            if (null != mOutputStream) {
                try {
                    mOutputStream.close();
                } catch (IOException e) {
                    logE("close mOutputStream get IOException");
                }
                mOutputStream = null;
            }

            isRunning = false;
        }

        public void closeSocket() {
            if (null != mInputStream) {
                try {
                    mInputStream.close();
                } catch (IOException e) {
                    logE("close mInputStream get IOException");
                }
                mInputStream = null;
            }
            if (null != mOutputStream) {
                try {
                    mOutputStream.close();
                } catch (IOException e) {
                    logE("close mOutputStream get IOException");
                }
                mOutputStream = null;
            }
            if (mSocket != null) {
                try {
                    mSocket.close();
                } catch (IOException e) {
                    logE("close mSocket get IOException");
                }
                mSocket = null;
            }
        }

        @Override
        public void run() {
            logD("Begin to listen in ServerThread");
            try {
                while (isRunning) {
                    mThreadSocket = mServerSocket.accept();
                    if (null == mThreadSocket) {
                        logE("client connected fail");
                        continue;
                    }
                    if (!isDeviceStart) {
                        logE(" is not start!");
                        mThreadSocket.close();
                        mThreadSocket = null;
                        continue;
                    }
                    if ((null == mInputStream) && (null == mOutputStream)) {
                        logD("client connected in ServerThread");
                        mSocket = mThreadSocket;
                        mSocket.setTcpNoDelay(true);
                        mInputStream = new BufferedInputStream(mSocket.getInputStream());
                        mOutputStream = new BufferedOutputStream(mSocket.getOutputStream());

                        mSendThread = new SendThread();
                        mSendThread.start();
                        mRecvThread = new RecvThread();
                        mRecvThread.start();
                    } else {
                        logE("thread is already start");
                        mThreadSocket.close();
                        mThreadSocket = null;
                    }
                    sleep(100);
                }
                logE("ServerThread loop stop !!!");
            } catch (Exception e) {
                logE("Get Exception in ServerThread");
            }

            logE("ServerThread exit !!!");
        }
    }

    private class SendThread extends Thread {
        private boolean isRunning;
        private final byte[] buffer = new byte[BUF_SIZE];

        public SendThread() {
            logD("SendThread Created");
            setName(flag + SEND_THREAD_NAME + port);
            isRunning = true;
        }

        public void cancel() {
            isRunning = false;
        }

        public int readData(byte[] data, int offset, int len) {
            if (mServerThread.mInputStream == null) {
                logE("mInputStream is null");
                return -1;
            }

            int ret;
            try {
                ret = mServerThread.mInputStream.read(data, offset, len);
            } catch (IOException e) {
                logE("readData IOException");
                return -2;
            }

            return ret;
        }

        public int writeData(byte[] data, int offset, int len) {
            if (mServerThread.mOutputStream == null) {
                logE("mOutputStream is null");
                return -1;
            }

            try {
                mServerThread.mOutputStream.write(data, offset, len);
                mServerThread.mOutputStream.flush();
            } catch (IOException e) {
                logE("writeData IOException");
                return -2;
            }

            return len;
        }

        @Override
        public void run() {
            while (mSocket != null && isRunning) {
                if (!mSocket.isConnected()) {
                    logE("socket is disconnected when read data");
                    break;
                }

                Arrays.fill(buffer, (byte) 0x00);
                int len = readData(buffer, 0, BUF_SIZE);
                if (len < 0) {
                    logE("read from socket fail, ret: " + len);
                    break;
                }
                if (CHECK_TRANSFER) {
                    logV("read from socket end, send to usb, len: " + len);
                }

                int ret = mDeviceManager.bulkTransferOut(buffer, len);
                if (ret < 0) {
                    logE("bulkTransferOut fail, ret: " + ret);
                    break;
                }
            }

            if (mServerThread != null) {
                mServerThread.closeSocket();
            }
            logD("SendThread exit !!!");
        }
    }

    private class RecvThread extends Thread {
        private boolean isRunning;
        private final byte[] buffer = new byte[BUF_SIZE];

        public RecvThread() {
            logD("RecvThread Created");
            setName(flag + RECV_THREAD_NAME + port);
            isRunning = true;
        }

        public void cancel() {
            isRunning = false;
        }

        @Override
        public void run() {
            while (mSocket != null && isRunning) {
                if (!mSocket.isConnected()) {
                    logE("RecvThread read data cancled");
                    break;
                }

                Arrays.fill(buffer, (byte) 0x00);
                int len = mDeviceManager.bulkTransferIn(buffer, BUF_SIZE);
                if (len < 0) {
                    logE("bulkTransferIn fail");
                    break;
                } else if (len == 0) {
                    continue;
                }
                if (CHECK_TRANSFER) {
                    logV("read from usb end, send to socket, len: " + len);
                }
                try {
                    int ret = mSendThread.writeData(buffer, 0, len);
                    if (ret < 0) {
                        logE("write to socket fail, ret: " + ret);
                        break;
                    }
                } catch (NullPointerException e) {
                    logE("RecvThread null");
                }
            }

            if (mServerThread != null) {
                mServerThread.closeSocket();
            }
            logD("RecvThread exit !!!");
        }
    }

    private String logMsg(String msg) {
        return flag + " " + msg;
    }

    private void logD(String msg) {
        LogUtil.d(TAG, logMsg(msg));
    }

    private void logE(String msg) {
        LogUtil.e(TAG, logMsg(msg));
    }

    private void logV(String msg) {
        LogUtil.v(TAG, logMsg(msg));
    }
}
