package com.autoai.welinkapp.idb;

import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 */
public class IdbLink {
    private static final AtomicReference<IdbDevice> IDB_DEVICE = new AtomicReference<>();

    private IdbLink() {
    }

    public static void init(IdbDevice device) {
        IDB_DEVICE.set(device);
    }

    public static boolean isIdbModel() {
        return IDB_DEVICE.get() != null;
    }

}
