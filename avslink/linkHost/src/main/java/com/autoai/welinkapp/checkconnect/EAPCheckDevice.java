package com.autoai.welinkapp.checkconnect;

import android.annotation.SuppressLint;
import android.content.Context;

import com.autoai.common.util.LogUtil;
import com.autoai.common.util.ThreadUtils;
import com.autoai.welinkapp.eap.EapStatus;
import com.autoai.welinkapp.eap.EapLink;
import com.autoai.welinkapp.eap.OnLinkEapListener;
import com.autoai.welinkapp.model.SdkViewModel;

/**
 * Check for EAP device.
 *
 * <AUTHOR>
 */
public class EAPCheckDevice
        implements OnLinkEapListener {
    private static final String TAG = "WL_DRIVER";

    /**
     * 所有 EAP 相关的操作日志
     */
    private static final String TAG_EAP = ".WL_EAP";

    @SuppressLint("StaticFieldLeak")
    private static EAPCheckDevice mInstance = null;
    private Context mContext = null;

    private boolean setUsbDeviceMode = false;

//    private static final String EAP_ACCESSORY_NAME = "com.navinfo.WeLinkVehicleProtocol";
//    private static final String EAP_BUNDLE_ID = "com.shangqi.SAICLinkLauncher";
//    private static final String EAP_NODE_NAME = "bulk_eap_welink";

    private EAPCheckDevice() {
    }

    public static EAPCheckDevice getInstance() {
        if (null == mInstance) {
            synchronized (EAPCheckDevice.class) {
                if (null == mInstance) {
                    mInstance = new EAPCheckDevice();
                }
            }
        }
        return mInstance;
    }

    public void init(Context context) {
        LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::init start");
        mContext = context;
        ThreadUtils.postOnBackgroundThread(() -> {
            LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::init initMfiI2cPath start");
            EapLink.initMfiI2cPath();
            LogUtil.i(TAG + TAG_EAP, "EAPCheckDevice::init initMfiI2cPath end");
            LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::init eapInit start");
            EapLink.eapInit();
            LogUtil.i(TAG + TAG_EAP, "EAPCheckDevice::init eapInit end");
            LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::init register callback");
            EapLink.linkDeviceCallbackRegister(this);
            LogUtil.i(TAG + TAG_EAP, "EAPCheckDevice::init callback register complete");
        });
        LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::init complete");
    }
    /**
     * 恢复EAP设备检测和连接
     *
     * 调用时机：
     * 1. ConnectManager.startCheckConnect() 中，当 iNeedConnectType == DEVICE_TYPE_NONE 时
     * 2. 当前连接状态不是 CONNECTED 或 CONNECTING 时
     * 3. 通常在应用启动或从后台恢复时，用于重新检测已连接的EAP设备

     * 使用场景：
     * - 应用启动时恢复之前的EAP连接
     * - 从其他连接类型切换回EAP连接时
     * - 后台切换到前台唤醒后重新检测EAP设备状态
     */
    public void resume(){
        LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::resume start");
        int eapStatus = EapLink.getEapStatus();
        LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::resume eapStatus=" + eapStatus);
        if(eapStatus == EapStatus.EAP_ATTACH){
            LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::resume device attached, set USB device mode");
            setUsbDeviceMode = true;
            EapLink.setUsbMode(true);
        }
        LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::resume complete");
    }
    public void deinit() {
        LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::deinit start");
        LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::deinit unregister callback");
        EapLink.unLinkDeviceCallbackRegister();
        if(setUsbDeviceMode){
            LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::deinit reset USB to host mode");
            setUsbDeviceMode = false;
            EapLink.setUsbMode(false);
            EapLink.resetUsb();
        }
        LogUtil.i(TAG + TAG_EAP, "EAPCheckDevice::deinit eapDeinit start");
        EapLink.eapDeinit();
        LogUtil.i(TAG + TAG_EAP, "EAPCheckDevice::deinit eapDeinit end");
        LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::deinit complete");
    }

    @Override
    public void onLinkDeviceCallbackType(int eapStatus) {
        LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType <--- eapStatus=" + eapStatus);
        switch (eapStatus) {
            case EapStatus.EAP_AUTHENTICATION_PASS:
                LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType <--- EAP_AUTHENTICATION_PASS");
                //3、该状态表示iap和鉴权都已经准备好了（即调用activeIap之后状态） 可以调用eap的接口进行读写eap了 这里APP根据自己的需求自行调用即可
                LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType eapLaunchApp start");
                EapLink.eapLaunchApp();
                LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType eapLaunchApp end");
                CommonData.strDeviceSerialNum = SdkViewModel.getInstance().platformadaptor.getDeviceSerialNum();
                LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType iOS SerialNum=" + CommonData.strDeviceSerialNum);
                if (CommonData.isCheckingStart) {
                    LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType ---> send CONNECT_STATUS_IN");
                    CommonData.sendMsg(mContext, CommonData.CONNECT_STATUS_IN, CommonData.DEVICE_TYPE_EAP, null);
                } else {
                    LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType checking not started, mark need connect");
                    CommonData.iNeedConnectType = CommonData.DEVICE_TYPE_EAP;
                }
                break;
            case EapStatus.EAP_ATTACH:
                LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType <--- EAP_ATTACH");
//                EapLink.activeEap();
                //1、苹果手机连接才会有该状态通知上来 第三方互联若需要连接则调用setUsbMode将usb翻转为DEVICE模式
                //注意：翻转为DEVICE模式后 该互联断开或者超时没有连接成功的时候一定要将usb翻转回到HOST模式 不然下一次插入设备是识别不到的
                LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType set USB device mode");
                setUsbDeviceMode = true;
                EapLink.setUsbMode(true);
                break;
            case EapStatus.EAP_DETACH:
                LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType <--- EAP_DETACH");
//              被触发方式  一种只插入usb线但没有调用setUsbMode切device模式，然后拔掉线，
//                另外一种就是调用了setUsbMode切成device模式就会来
//                EapLink.deactiveEap();
//                CommonData.sendMsg(mContext, CommonData.CONNECT_STATUS_OUT, CommonData.DEVICE_TYPE_EAP, null);
                break;
            case EapStatus.EAP_AUTHENTICATION_FAIL:
                LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType <--- EAP_AUTHENTICATION_FAIL");
                break;
            case EapStatus.EAP_ONLINE_ATTACH:
                //2、将usb翻转为DEVICE（即调用了setUsbMode之后状态）模式后会有该状态上来 需要调用activeIap初始化IAP
                //注意：这个状态在切换adb的时候也会触发 所以需要APP自行做过滤
                LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType <--- EAP_ONLINE_ATTACH");
                LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType activeEap start");
                EapLink.activeEap();
                break;
            case EapStatus.EAP_ONLINE_DETACH:
                //翻转DEVICE成功之后 然后拔掉usb线  注意：切换adb的时候也会有这个状态 app自行做过滤即可
                LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType <--- EAP_ONLINE_DETACH");

                if(setUsbDeviceMode){
                    LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType reset USB to host mode");
                    setUsbDeviceMode = false;
                    EapLink.setUsbMode(false);
                }
                LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType deactiveEap");
                EapLink.deactiveEap();
                //发送广播已调用 stop ——>eapBulkClose
                LogUtil.d(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType ---> send CONNECT_STATUS_OUT");
                CommonData.sendMsg(mContext, CommonData.CONNECT_STATUS_OUT, CommonData.DEVICE_TYPE_EAP, null);
//                EapLink.eapBulkClose();
                break;
            default:
                LogUtil.i(TAG + TAG_EAP, "EAPCheckDevice::onLinkDeviceCallbackType <--- unknown status=" + eapStatus);
                break;
        }
    }

}
