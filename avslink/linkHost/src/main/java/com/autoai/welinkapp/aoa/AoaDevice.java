package com.autoai.welinkapp.aoa;

import androidx.annotation.NonNull;

/**
 * <AUTHOR>
 */
public interface AoaDevice {

    /**
     * 生产商。
     * e.g. Mapbar, Inc
     * @return 生产商代号。
     */
    @NonNull
    String getManufacturer();

    /**
     * 车机型号
     * e.g. WeLink
     * @return 车机型号。
     */
    @NonNull
    String getModel();

    /**
     * 车机设备描述符
     * e.g. WeLink
     * @return 车机设备描述符。
     */
    @NonNull
    String getDescription();

    /**
     * 版本号
     * e.g. 1.0
     * @return 版本号
     */
    @NonNull
    default String getVersion() {
        return "1.0";
    }

    /**
     * 配件通用应用下载地址。
     * e.g. https://welink-rs.autoai.com/saic/ap32
     * @return 配件通用应用下载地址
     */
    @NonNull
    String getUri();

    /**
     * 序列号
     * e.g. mySerial
     * @return 序列号
     */
    @NonNull
    default String getSerial() {
        return "mySerial";
    }
}
