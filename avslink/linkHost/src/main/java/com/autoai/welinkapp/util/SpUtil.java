package com.autoai.welinkapp.util;

import static android.content.Context.MODE_PRIVATE;

import android.content.Context;
import android.content.SharedPreferences;

/**
 * <AUTHOR>
 */
public class SpUtil {
    public static final String TAG = "SpUtil";
    private Context mContext;
    private SharedPreferences sharedPreferences;
    private static SpUtil instance;
    public static final String SP_MAX_STEP = "maxStep";
    public static final String SP_MIN_STEP = "minStep";
    public static final String SP_MID_STEP = "midStep";

    public static SpUtil getInstance(Context context) {
        if(instance == null) {
            synchronized (SpUtil.class) {
                if(instance == null) {
                    instance = new SpUtil(context);
                }
            }
        }
        return instance;
    }

    SpUtil(Context context) {
        mContext = context;
        if(mContext != null) {
            sharedPreferences = mContext.getSharedPreferences("BlueToothHidConfig", MODE_PRIVATE);
        }
    }

    public void put(String key, Object object) {
        if(sharedPreferences == null || object == null){
            return;
        }
        SharedPreferences.Editor editor = sharedPreferences.edit();
        if (object instanceof String) {
            editor.putString(key, (String) object);
        } else if (object instanceof Integer) {
            editor.putInt(key, (Integer) object);
        } else if (object instanceof Boolean) {
            editor.putBoolean(key, (Boolean) object);
        } else if (object instanceof Float) {
            editor.putFloat(key, (Float) object);
        }
        editor.apply();
    }

    public float getFloat(String key, float defaultObject) {
        if(sharedPreferences == null || !sharedPreferences.contains(key)){
            return defaultObject;
        }
        return sharedPreferences.getFloat(key, defaultObject);
    }
}
