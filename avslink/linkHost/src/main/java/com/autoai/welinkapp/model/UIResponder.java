package com.autoai.welinkapp.model;

import com.autoai.welink.macro.WL_API_PAGE_TYPE;

/**
 * SDK 内部会根据状态请求对应的页面, 通过此类相应对应的 UI 请求
 */
public interface UIResponder {
    /**
     * < Page source off(background)
     */
    int SOURCE_OFF = WL_API_PAGE_TYPE.SOURCE_OFF;
    /**
     * < Page none
     */
    int INITIAL_PAGE = WL_API_PAGE_TYPE.INITIAL_PAGE;
    /**
     * < Page help
     */
    int HELP_PAGE = WL_API_PAGE_TYPE.HELP_PAGE;
    /**
     * < Page official account
     */
    int CONNECTING_PAGE_OFFICIAL_ACCOUNT = WL_API_PAGE_TYPE.CONNECTING_PAGE_OFFICIAL_ACCOUNT;
    /**
     * < Page connecting
     */
    int CONNECTING_PAGE = WL_API_PAGE_TYPE.CONNECTING_PAGE;
    /**
     * < Page sdk error
     */
    int ERR_PAGE_SDK = WL_API_PAGE_TYPE.ERR_PAGE_SDK;
    /**
     * < Page connect error
     */
    int ERR_PAGE_CONNECT = WL_API_PAGE_TYPE.ERR_PAGE_CONNECT;
    /**
     * < Page heart error
     */
    int ERR_PAGE_HEART = WL_API_PAGE_TYPE.ERR_PAGE_HEART;
    /**
     * < Page safe driving
     */
    int RUNNING_PAGE_SAFE_DRIVING = WL_API_PAGE_TYPE.RUNNING_PAGE_SAFE_DRIVING;
    /**
     * < Page wechat qrcode
     */
    int RUNNING_PAGE_WECHAT_QRCODE = WL_API_PAGE_TYPE.RUNNING_PAGE_WECHAT_QRCODE;
    /**
     * < Page wechat login confirm
     */
    int RUNNING_PAGE_WECHAT_CONFIRM_LOGIN = WL_API_PAGE_TYPE.RUNNING_PAGE_WECHAT_CONFIRM_LOGIN;
    /**
     * < Page wechat login success
     */
    int RUNNING_PAGE_WECHAT_LOGIN_SUCCESS = WL_API_PAGE_TYPE.RUNNING_PAGE_WECHAT_LOGIN_SUCCESS;
    /**
     * < Page limit
     */
    int RUNNING_PAGE_LIMIT = WL_API_PAGE_TYPE.RUNNING_PAGE_LIMIT;
    /**
     * < Page running
     */
    int RUNNING_PAGE = WL_API_PAGE_TYPE.RUNNING_PAGE;
    /**
     * usb 互联切换  无线到有线弹框 显示
     */
    int SHOW_USB_INTERFACE_DIALOG =  WL_API_PAGE_TYPE.NUM + 1;
    /**
     * usb 互联切换  无线到有线弹框  关闭
     */
    int DISMISS_USB_INTERFACE_DIALOG = WL_API_PAGE_TYPE.NUM + 2;
    /**
     * 开始投屏页面
     */
    int SCREEN_PAGE_MIRRORING = WL_API_PAGE_TYPE.NUM + 3;

    /**
     * 请求录屏手机侧取消后 重新发起录屏请求页面
     */
    int SCREEN_REQ_RECORD_SCREEN = WL_API_PAGE_TYPE.NUM + 4;
    /**
     * < Page max num, do not care
     */
    int NUM = WL_API_PAGE_TYPE.NUM;

    void onUpdatePage(int page, Object para);
}
