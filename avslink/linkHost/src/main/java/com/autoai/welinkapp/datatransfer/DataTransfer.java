package com.autoai.welinkapp.datatransfer;

import static com.autoai.welinkapp.checkconnect.CommonData.TAG_AOA;
import static com.autoai.welinkapp.checkconnect.CommonData.TAG_EAP;

import android.content.Context;

import com.autoai.common.util.LogUtil;
import com.autoai.welinkapp.checkconnect.ConnectManager;
import com.autoai.welinkapp.eap.EapLink;
import com.autoai.welinkapp.idb.IdbLink;

/**
 * Data transfer Interface class.
 */
public class  DataTransfer {
    private static final String TAG = "WL_DRIVER";


    private static final int DEVICE_TYPE_AOA = ConnectManager.DEVICE_TYPE_AOA;
    private static final int DEVICE_TYPE_EAP = ConnectManager.DEVICE_TYPE_EAP;
    private static final int DEVICE_TYPE_IDB = ConnectManager.DEVICE_TYPE_IDB;

    /**
     * 数据传输相关业务初始化
     * @param context
     * @param hidFlag
     */
    public void init(Context context, boolean hidFlag) {
        LogUtil.d(TAG, "DataTransfer::init hidFlag=" + hidFlag);
        LogUtil.d(TAG + TAG_AOA, "DataTransfer::init AOAManager init start");
        AOAManager.getInstance().init(context);
        if (EapLink.isEapModel()) {
            LogUtil.d(TAG + TAG_EAP, "DataTransfer::init EAPManager init start");
            EAPManager.getInstance().init();
        }
        if (IdbLink.isIdbModel()) {
            IDBManager.getInstance().init(context);
        }
        LogUtil.d(TAG, "DataTransfer::init() complete");
    }

    public void deinit() {
        LogUtil.d(TAG, "DataTransfer::deinit start");
        LogUtil.d(TAG + TAG_AOA, "DataTransfer::deinit AOAManager deinit start");
        AOAManager.getInstance().deinit();
        if (EapLink.isEapModel()) {
            LogUtil.d(TAG + TAG_EAP, "DataTransfer::deinit EAPManager deinit start");
            EAPManager.getInstance().deinit();
        }
        if (IdbLink.isIdbModel())
            IDBManager.getInstance().deinit();
        LogUtil.d(TAG, "DataTransfer::deinit complete");
    }

    /**
     * 开启连接
     * @param type
     */
    public void startConnect(int type) {
        LogUtil.d(TAG, "DataTransfer::startConnect type=" + type);
        if (DEVICE_TYPE_AOA == type) {
            LogUtil.d(TAG + TAG_AOA, "DataTransfer::startConnect AOA device start");
            AOAManager.getInstance().startAOA();
        } else if (DEVICE_TYPE_EAP == type) {
            LogUtil.d(TAG + TAG_EAP, "DataTransfer::startConnect EAP device start");
            EAPManager.getInstance().startEAP();
        } else if (DEVICE_TYPE_IDB == type) {
            LogUtil.d(TAG, "DataTransfer::startConnect IDB device start");
            IDBManager.getInstance().startIDB();
        } else {
            LogUtil.e(TAG, "DataTransfer::startConnect unknown type=" + type);
        }
    }

    public void stopConnect(int type) {
        LogUtil.d(TAG, "DataTransfer::stopConnect type=" + type);
        if (DEVICE_TYPE_AOA == type) {
            LogUtil.d(TAG + TAG_AOA, "DataTransfer::stopConnect AOA device stop");
            AOAManager.getInstance().stopAOA();
        } else if (DEVICE_TYPE_EAP == type) {
            LogUtil.d(TAG + TAG_EAP, "DataTransfer::stopConnect EAP device stop");
            EAPManager.getInstance().stopEAP();
        } else if (DEVICE_TYPE_IDB == type) {
            LogUtil.d(TAG, "DataTransfer::stopConnect IDB device stop");
            IDBManager.getInstance().stopIDB();
        } else {
            LogUtil.e(TAG, "DataTransfer::stopConnect unknown type=" + type);
        }
    }
}
