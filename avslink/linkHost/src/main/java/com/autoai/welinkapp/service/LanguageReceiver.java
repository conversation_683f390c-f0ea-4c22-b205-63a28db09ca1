package com.autoai.welinkapp.service;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;

import com.autoai.common.util.AppUtil;
import com.autoai.welink.SdkApi;
import com.autoai.welinkapp.util.LocalUtil;

/**
 * <AUTHOR>
 */
public class LanguageReceiver extends BroadcastReceiver {

    private SdkApi mSdkApi;

    public LanguageReceiver(SdkApi mSdkApi) {
        this.mSdkApi = mSdkApi;
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        if (mSdkApi != null) {
            mSdkApi.sendControlEnAndCh(LocalUtil.getLocal());   // 发送车机端语言设置
        }
    }

    public void registerReceiver() {
        IntentFilter filter = new IntentFilter();
        filter.addAction(Intent.ACTION_LOCALE_CHANGED);
        AppUtil.getContext().registerReceiver(this, filter,Context.RECEIVER_NOT_EXPORTED);
    }

    public void unregisterReceiver() {
        AppUtil.getContext().unregisterReceiver(this);
        mSdkApi = null;
    }
}
