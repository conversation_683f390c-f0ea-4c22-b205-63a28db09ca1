package com.autoai.welinkapp.util;

import com.autoai.common.util.LogUtil;

import java.util.HashMap;

public class FpsUtil {

    private static final String TAG = "FpsUtil";

    private static HashMap<String,FpsUtil> fpsUtilHashMap = new HashMap<>();

    private String flag;
    private int mFps;
    private long mLastTime;

    public FpsUtil(String flag) {
        this.flag = flag;
    }

    public static void printFps(String flag) {
        if (!fpsUtilHashMap.containsKey(flag)) {
            fpsUtilHashMap.put(flag,new FpsUtil(flag));
        }
        FpsUtil fpsUtil = fpsUtilHashMap.get(flag);
        if (fpsUtil != null) {
            fpsUtil.getFps();
        }
    }

    private void getFps() {
        mFps++;
        long timeStamp = System.currentTimeMillis();
        if (timeStamp - mLastTime >= 1000) {
            LogUtil.d(TAG, flag + " fps ==>" + mFps);
            mFps = 0;
            mLastTime = timeStamp;
        }
    }

}
