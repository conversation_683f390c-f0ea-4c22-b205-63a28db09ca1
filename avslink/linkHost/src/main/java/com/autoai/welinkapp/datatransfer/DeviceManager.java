package com.autoai.welinkapp.datatransfer;

import android.content.Context;
import android.hardware.usb.UsbConstants;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbDeviceConnection;
import android.hardware.usb.UsbEndpoint;
import android.hardware.usb.UsbInterface;
import android.hardware.usb.UsbManager;

import com.autoai.common.util.LogUtil;

/**
 * Manage usb device data transfer.
 */
@SuppressWarnings({"WeakerAccess", "unused"})
public class DeviceManager {
    private static final String TAG = "WL_DRIVER";

    private static final boolean CHECK_DEVICE_MANAGER = LogUtil.isFullLog();

    private UsbDevice mUsbDevice = null;
    private UsbInterface mUsbInterface = null;
    private UsbDeviceConnection mUsbDeviceConnection = null;

    private UsbEndpoint mUsbEndpointIn = null;
    private UsbEndpoint mUsbEndpointOut = null;
    private static final int BULK_ENDPOINT_IN_ADDRESS = 0;
    private static final int BULK_ENDPOINT_OUT_ADDRESS = 1;

    private static final int AOA_REGISTER_HID = 54;
    private static final int AOA_UNREGISTER_HID = 55;
    private static final int AOA_SET_HID_REPORT_DESC = 56;
    private static final int AOA_SEND_HID_EVENT = 57;

    private static final int POINT_BUFFER_LEN = 7;
    private static final int HID_POINT_VALUE = 0;
    private static final int HID_KEY_VALUE = 1;

    private static final int BUF_SIZE_MAX = 16 * 1024;
    private final byte[] mDataBuffer = new byte[BUF_SIZE_MAX];

    public DeviceManager() {
    }

    public DeviceManager(Context context) {
    }

    public boolean init(UsbManager manager, UsbDevice device) {
        LogUtil.d(TAG, "DeviceManager::init()" );
        if (manager == null || device == null) {
            LogUtil.e(TAG, "DeviceManager::init --> initUsbDevice fail");
            return false;
        }
        deinitUsbDevice();
        mUsbDevice = device;
        try {
            mUsbInterface = mUsbDevice.getInterface(0);
            mUsbDeviceConnection = manager.openDevice(mUsbDevice);
            mUsbDeviceConnection.claimInterface(mUsbInterface, true);
        } catch (Exception e) {
            LogUtil.e(TAG, "DeviceManager::init --> init fail");
            deinitUsbDevice();
            return false;
        }

        mUsbEndpointIn = mUsbInterface.getEndpoint(BULK_ENDPOINT_IN_ADDRESS);
        mUsbEndpointOut = mUsbInterface.getEndpoint(BULK_ENDPOINT_OUT_ADDRESS);
        return true;
    }

    public void deinitUsbDevice() {
        LogUtil.i(TAG, "DeviceManager::deinitUsbDevice");
        try {
            if (mUsbDeviceConnection != null) {
                mUsbDeviceConnection.releaseInterface(mUsbInterface);
                mUsbDeviceConnection.close();
            }
            mUsbDevice = null;
            mUsbDeviceConnection = null;
        } catch (Exception ex) {
            LogUtil.e(TAG, "DeviceManager::deinitUsbDevice --> deinitUsbDevice fail");
        }
    }

//    public int hidSendRegisterEvent(int w, int h) {
//        if (mUsbDevice == null || mUsbDeviceConnection == null) {
//            return -1;
//        }
//
//        byte x_l = (byte) (w & 0xFF);
//        byte x_h = (byte) (byte) ((w >> 8) & 0xFF);
//        byte y_l = (byte) (h & 0xFF);
//        byte y_h = (byte) (byte) ((h >> 8) & 0xFF);
//        byte[] reportBuf = {
//                0x05, 0x0D, 0x09, 0x04,         // USAGE_PAGE (Digitizers) // USAGE (Touch Screen)
//                (byte) 0xA1, 0x01,               // COLLECTION (Application)
//                (byte) 0x85, 0x01, 0x09, 0x22,   // REPORT_ID (Touch)   // USAGE (Finger)
//
//                (byte) 0xA1, 0x02,               // COLLECTION (Logical)
//                0x09, 0x55,                     // USAGE ( Contact Identifier)
//                0x15, 0x00, 0x25, 0x02,         // LOGICAL_MINIMUM (0)  // LOGICAL_MAXIMUM (5)
//                0x75, 0x08, (byte) 0x95, 0x01,   // REPORT_SIZE (8)  // REPORT_COUNT (1)
//                (byte) 0xB1, 0x05,               // FEATURE (Data,Var,Abs)
//                0x09, 0x54,                     // USAGE ( Contact Identifier)
//                (byte) 0x81, 0x02,               // INPUT (Data,Var,Abs)
//
//                0x05, 0x0D,                     // USAGE_PAGE (Digitizers)
//                0x09, 0x22,                     // USAGE (Finger)
//                (byte) 0xA1, 0x02,               // COLLECTION (Logical)
//                0x09, 0x42,                     // USAGE (Tip Switch)
//                0x15, 0x00,                     // LOGICAL_MINIMUM (0)
//                0x25, 0x01,                     // LOGICAL_MAXIMUM (1)
//                0x75, 0x01, (byte) 0x81, 0x02, 0x09, 0x32,   //REPORT_SIZE (1)// INPUT (Data,Var,Abs) // USAGE (In Range)
//                0x75, 0x01, (byte) 0x81, 0x02, 0x09, 0x51,   //REPORT_SIZE (1) // INPUT (Data,Var,Abs)// USAGE ( Contact Identifier)
//                0x25, 0x3F,                     //LOGICAL_MAXIMUM (3F)
//                0x75, 0x06, (byte) 0x81, 0x02,   //REPORT_SIZE (6)// INPUT (Data,Var,Abs)
//
//                0x05, 0x01, 0x09, 0x30,         //USAGE_PAGE (Generic Desk)  USAGE (X)
//                0x46, x_l, x_h, 0x26, x_l, x_h, // PHYSICAL_MAXIMUM (1024)   // LOGICAL_MAXIMUM
//                0x75, 0x10, (byte) 0x81, 0x02,   //REPORT_SIZE (10)// INPUT (Data,Var,Abs)
//
//                0x09, 0x31,                     // USAGE (Y)
//                0x46, y_l, y_h, 0x26, y_l, y_h,    // PHYSICAL_MAXIMUM (1028)   // LOGICAL_MAXIMUM
//                (byte) 0x81, 0x02,               // INPUT (Data,Var,Abs)
//                (byte) 0xC0,                     // END_COLLECTION
//
//                (byte) 0xC0,
//                (byte) 0xC0
//        };
//        if (mUsbDeviceConnection.controlTransfer(UsbConstants.USB_DIR_OUT | UsbConstants.USB_TYPE_VENDOR,
//                AOA_REGISTER_HID,
//                HID_POINT_VALUE,
//                reportBuf.length,
//                null,
//                0,
//                0) < 0) {
//            LogUtil.e(TAG, "register hid event failed");
//            return -2;
//        }
//
//        byte[] reportKeyBuf = {0x05, 0x0C, 0x09, 0x01, (byte) 0xA1, 0x01, (byte) 0x85, 0x01, 0x05, 0x0C,
//                0x15, 0x00, 0x25, 0x01, 0x75, 0x01, (byte) 0x95, 0x1C, 0x09, 0x40,
//                0x09, 0x42, 0x09, 0x43, 0x09, 0x44, 0x09, 0x45, 0x09, (byte) 0x8C,
//                0x09, (byte) 0xE2, 0x09, (byte) 0xB0, 0x09, (byte) 0xB5, 0x09, (byte) 0xB6,
//                0x09, (byte) 0xB7, 0x09, (byte) 0xCD, 0x09, (byte) 0xEA, 0x09, (byte) 0xE9,
//                0x0A, 0x23, 0x02, 0x0A, 0x24, 0x02, (byte) 0x81, 0x02, (byte) 0xC0};
//        if (mUsbDeviceConnection.controlTransfer(UsbConstants.USB_DIR_OUT | UsbConstants.USB_TYPE_VENDOR,
//                AOA_REGISTER_HID,
//                HID_KEY_VALUE,
//                reportKeyBuf.length,
//                null,
//                0,
//                0) < 0) {
//            LogUtil.e(TAG, "register hid key event failed");
//            return -3;
//        }
//
//        if (mUsbDeviceConnection.controlTransfer(UsbConstants.USB_DIR_OUT | UsbConstants.USB_TYPE_VENDOR,
//                AOA_SET_HID_REPORT_DESC,
//                HID_POINT_VALUE,
//                0,
//                reportBuf,
//                reportBuf.length,
//                0) < 0) {
//            LogUtil.e(TAG, "cannot set the report hid desc");
//            return -4;
//        }
//
//        if (mUsbDeviceConnection.controlTransfer(UsbConstants.USB_DIR_OUT | UsbConstants.USB_TYPE_VENDOR,
//                AOA_SET_HID_REPORT_DESC,
//                HID_KEY_VALUE,
//                0,
//                reportKeyBuf,
//                reportKeyBuf.length,
//                0) < 0) {
//            LogUtil.e(TAG, "cannot set the report hid key desc");
//            return -5;
//        }
//
//        LogUtil.i(TAG, "register hid success");
//        return 0;
//    }

//    public int hidSendUnregisterEvent(int id) {
//        if (mUsbDevice == null || mUsbDeviceConnection == null) {
//            return -1;
//        }
//
//        if (mUsbDeviceConnection.controlTransfer(UsbConstants.USB_DIR_OUT | UsbConstants.USB_TYPE_VENDOR,
//                AOA_UNREGISTER_HID,
//                HID_POINT_VALUE,
//                0,
//                null,
//                0,
//                0) < 0) {
//            LogUtil.e(TAG, "unregister hid event failed");
//            return -2;
//        }
//
//        if (mUsbDeviceConnection.controlTransfer(UsbConstants.USB_DIR_OUT | UsbConstants.USB_TYPE_VENDOR,
//                AOA_UNREGISTER_HID,
//                HID_KEY_VALUE,
//                0,
//                null,
//                0,
//                0) < 0) {
//            LogUtil.e(TAG, "unregister hid key event failed");
//            return -3;
//        }
//
//        LogUtil.i(TAG, "unregister hid end");
//        return 0;
//    }

//    public int hidSendHomeKeyEvent() {
//        if (mUsbDevice == null || mUsbDeviceConnection == null) {
//            return -1;
//        }
//        byte[] keyBuf = new byte[3];
//        keyBuf[0] = 0x01;
//        //keyBuf[1] = 0x00;
//        keyBuf[2] = 0x40; //home
//        if (mUsbDeviceConnection.controlTransfer(UsbConstants.USB_DIR_OUT | UsbConstants.USB_TYPE_VENDOR,
//                AOA_SEND_HID_EVENT,
//                HID_KEY_VALUE,
//                0,
//                keyBuf,
//                keyBuf.length,
//                0) < 0) {
//            LogUtil.e(TAG, "send home Key event  failed");
//            return -1;
//        }
//        return hidSendKeyEvent();
//    }

//    public int hidSendBackKeyEvent() {
//        if (mUsbDevice == null || mUsbDeviceConnection == null) {
//            return -1;
//        }
//        byte[] keyBuf = new byte[3];
//        keyBuf[0] = 0x01;
//        //keyBuf[1] = 0x00;
//        keyBuf[2] = (byte) 0x80; //back
//        if (mUsbDeviceConnection.controlTransfer(UsbConstants.USB_DIR_OUT | UsbConstants.USB_TYPE_VENDOR,
//                AOA_SEND_HID_EVENT,
//                HID_KEY_VALUE,
//                0,
//                keyBuf,
//                keyBuf.length,
//                0) < 0) {
//            LogUtil.e(TAG, "send home Key event  failed");
//            return -1;
//        }
//        return hidSendKeyEvent();
//    }

//    private int hidSendKeyEvent() {
//        byte[] keyBuf = new byte[3];
//        keyBuf[0] = 0x01;
//        //keyBuf[1] = 0x00;
//        //keyBuf[2] = 0x00;
//        if (mUsbDeviceConnection.controlTransfer(UsbConstants.USB_DIR_OUT | UsbConstants.USB_TYPE_VENDOR,
//                AOA_SEND_HID_EVENT,
//                HID_KEY_VALUE,
//                0,
//                keyBuf,
//                keyBuf.length,
//                0) < 0) {
//            LogUtil.e(TAG, "send Key event failed");
//            return -1;
//        }
//        return 0;
//    }

//    public int hidSendPointEvent(int id, int x, int y, int type) {
//        if (mUsbDevice == null || mUsbDeviceConnection == null) {
//            return -1;
//        }
//        if (CHECK_DEVICE_MANAGER)
//            LogUtil.e(TAG, "zhangyf hidSendPointEvent id:" + id + ", x:" + x + ", y:" + y + ", type:" + type);
//        byte[] pointBuf = new byte[POINT_BUFFER_LEN];
//        pointBuf[0] = 0x01;
//        byte temp = (byte) ((id << 2) & 0xFC);
//        if (0 == type) {
//            pointBuf[1] = 0x01;
//            pointBuf[2] = (byte) (0x03 | temp);
//        } else {
//            pointBuf[2] = temp; //(byte)(0x0|temp);
//        }
//        pointBuf[3] = (byte) (x & 0xFF);
//        pointBuf[4] = (byte) ((x >> 8) & 0xFF);
//        pointBuf[5] = (byte) (y & 0xFF);
//        pointBuf[6] = (byte) ((y >> 8) & 0xFF);
//
//        if (mUsbDeviceConnection.controlTransfer(UsbConstants.USB_DIR_OUT | UsbConstants.USB_TYPE_VENDOR,
//                AOA_SEND_HID_EVENT,
//                HID_POINT_VALUE,
//                0,
//                pointBuf,
//                pointBuf.length,
//                0) < 0) {
//            LogUtil.e(TAG, "sendPointEvent failed");
//            return -1;
//        }
//        return 0;
//    }

    public int bulkTransferIn(byte[] data, int len) {
        int ret;
        if (mUsbDeviceConnection == null || mUsbEndpointIn == null) {
            LogUtil.e(TAG, "input is null in bulkTransferIn");
            return -1;
        }

        ret = mUsbDeviceConnection.bulkTransfer(mUsbEndpointIn, data, len, 0);
        if (ret >= 0) {
            if (CHECK_DEVICE_MANAGER)
                LogUtil.v(TAG, "DeviceManager::bulkTransferIn --> bulkTransfer end, len: " + ret);
        } else {
            LogUtil.e(TAG, "DeviceManager::bulkTransferIn --> bulkTransferIn error, ret: " + ret);
        }
        return ret;
    }

    public int bulkTransferOut(byte[] data, int len) {
        int ret;
        int cnt = len;
        int readLen;
        int dataLen = 0;

        if (mUsbDeviceConnection == null || mUsbEndpointOut == null) {
            LogUtil.e(TAG, "DeviceManager::bulkTransferIn --> input is null in bulkTransferOut");
            return -1;
        }

        if (len <= BUF_SIZE_MAX) {
            ret = mUsbDeviceConnection.bulkTransfer(mUsbEndpointOut, data, len, 0);
            if (ret <= 0) {
                LogUtil.e(TAG, "DeviceManager::bulkTransferIn --> bulkTransferOut error 1: ret = " + ret);
                return ret;
            }
            if (CHECK_DEVICE_MANAGER)
                LogUtil.v(TAG, " send to usb end, len: " + len);
            dataLen = ret;
        } else {
            while (cnt > 0) {
                readLen = Math.min(cnt, BUF_SIZE_MAX);
                System.arraycopy(data, dataLen, mDataBuffer, 0, readLen);
                ret = mUsbDeviceConnection.bulkTransfer(mUsbEndpointOut, mDataBuffer, readLen, 0);
                if (ret <= 0) {
                    LogUtil.e(TAG, "DeviceManager::bulkTransferIn --> bulkTransferOut error 2: ret = " + ret);
                    return ret;
                }
                cnt -= ret;
                dataLen += ret;
            }
        }

        if (dataLen != len) {
            LogUtil.e(TAG, "DeviceManager::bulkTransferIn -->  bulkTransferOut error 3: dataLen = " + dataLen + ", len = " + len);
            return -2;
        }
        return dataLen;
    }
}
