package com.autoai.welinkapp.checkconnect;

import android.annotation.SuppressLint;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.hardware.usb.UsbConfiguration;
import android.hardware.usb.UsbConstants;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbDeviceConnection;
import android.hardware.usb.UsbInterface;
import android.hardware.usb.UsbManager;
import android.os.Build;
import android.util.Log;

import com.autoai.avslinkhid.api.HidUtil;
import com.autoai.common.util.LogUtil;
import com.autoai.welinkapp.aoa.AoaLink;


import java.lang.reflect.Method;
import java.util.HashMap;

/**
 * Check for AOA device.
 */
@SuppressWarnings("WeakerAccess")
public class AOACheckDevice {
    private static final String TAG = "WL_DRIVER";

    /**
     * 所有 AOA 相关的操作日志
     */
    private static final String TAG_AOA = ".WL_AOA";

    @SuppressLint("StaticFieldLeak")
    private static AOACheckDevice mInstance = null;
    private Context mContext = null;
    private UsbManager mUsbManager = null;
    private UsbDevice mUsbDevice = null;
    private UsbInterface mUsbInterface = null;
    private UsbDeviceConnection mUsbDeviceConnection = null;

    private static final int PID_ACCESSORY = 0x2D;
    private static final int VID_ACCESSORY = 0x18D1;

    private static final int AOA_GET_PROTOCOL = 51;
    private static final int AOA_SEND_IDENT = 52;
    private static final int AOA_START_ACCESSORY = 53;

    // USB控制传输超时时间常量 (毫秒)
    private static final int USB_CONTROL_TRANSFER_TIMEOUT = 5000;  // AOA协议操作超时时间

    private static final int STORAGE_INTERFACE_CLASS = 0x08;
    private static final int STORAGE_INTERFACE_SUBCLASS = 0x06;
    private static final int STORAGE_INTERFACE_PROTOCOL = 0x50;
    private static final int STORAGE_CONFIG_INTERFACE_COUNT = 1;

    private final static int VID_APPLE = 0x5ac;
    private final static int PID_RANGE_LOW = 0x1290;
    private final static int PID_RANGE_MAX = 0x12af;
    private static final int DEVICE_IPHONE_PRODUCT_ID = 0x1200;
    private static final int DEVICE_IPOD_PRODUCT_ID_MASK = 0xFF00;

    // 常见Android设备厂商VID
    private static final int VID_SAMSUNG = 0x04e8;
    private static final int VID_HTC = 0x0bb4;
    private static final int VID_MOTOROLA = 0x22b8;
    private static final int VID_LG = 0x1004;
    private static final int VID_HUAWEI = 0x12d1;
    private static final int VID_XIAOMI = 0x2717;
    private static final int VID_OPPO = 0x22d9;
    private static final int VID_VIVO = 0x2d95;
    private static final int VID_ONEPLUS = 0x2a70;

    private static final int VID_REALME = 0x18d1;

    // 常见USB基础设施设备VID (需要过滤掉的)
    private static final int VID_MICROCHIP = 0x424;  // Microchip Technology
    private static final int VID_LINUX_FOUNDATION = 0x1d6b;  // Linux Foundation
    private static final int VID_INTEL = 0x8087;  // Intel Corp

    // USB设备类别
    private static final int USB_CLASS_HUB = 9;  // USB Hub
    private static final int USB_CLASS_VENDOR_SPECIFIC = 255;  // 厂商特定

    private SystemUsbReceiver mSystemUsbReceiver;

    private static int iUsbFd = 0;
    private AOACheckDevice() {
    }

    public static AOACheckDevice getInstance() {
        if (null == mInstance) {
            synchronized (AOACheckDevice.class) {
                if (null == mInstance) {
                    mInstance = new AOACheckDevice();
                }
            }
        }
        return mInstance;
    }

    public void init(Context context) {
        LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::init start");
        mContext = context;
        mUsbManager = (UsbManager) mContext.getSystemService(Context.USB_SERVICE);

        LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::init SystemUsbReceiver register");
        mSystemUsbReceiver = new SystemUsbReceiver(mContext);
        mSystemUsbReceiver.registerReceiver();
        LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::init complete");
    }



    public void deinit() {
        LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::deinit start");
        LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::deinit USB device deinit");
        deinitUsbDevice();
        iUsbFd = 0;

        if (null != mSystemUsbReceiver) {
            LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::deinit SystemUsbReceiver unregister");
            mSystemUsbReceiver.unregisterReceiver();
            mSystemUsbReceiver = null;
        }
        LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::deinit AoaLink deinit");
        AoaLink.deinit();
        LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::deinit complete");
    }

    private boolean filterDevice(UsbDevice device) {
        LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::filterDevice  filtered device");
        if (null == device) {
            return true;
        }
        // serial number
        // she review tips:历史项目德赛对SerialNum进行过身份识别，目前无用，而且涉及到系统权限，android.permission.READ_PHONE_STATE
//        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
//            LogUtil.i(TAG, "AOACheckDevice::filterDevice() --> device = " + device);
//            String sn = device.getSerialNumber();
//            if (sn == null) {
//                LogUtil.e(TAG, "getSerialNumber null");
//                return true;
//            }
//            LogUtil.d(TAG, "AOACheckDevice::filterDevice() --> sn: " + sn);
//            LogUtil.d(TAG, "AOACheckDevice::filterDevice() --> CommonData sn: " + CommonData.strDeviceSerialNum);
//            if (!"".equals(CommonData.strDeviceSerialNum) && !sn.equals(CommonData.strDeviceSerialNum)) {
//                LogUtil.i(TAG, "Serial number is not same.");
//                return true;
//            }
//        }
        // usb storage device
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            UsbInterface usbInterface = device.getInterface(0);
            UsbConfiguration usbConfig = device.getConfiguration(0);
            if ((STORAGE_INTERFACE_CLASS == usbInterface.getInterfaceClass()) &&
                    (STORAGE_INTERFACE_SUBCLASS == usbInterface.getInterfaceSubclass()) &&
                    (STORAGE_INTERFACE_PROTOCOL == usbInterface.getInterfaceProtocol()) &&
                    (STORAGE_CONFIG_INTERFACE_COUNT == usbConfig.getInterfaceCount())) {
                LogUtil.i(TAG, "AOACheckDevice::filterDevice() --> This device is usb storage device");
                return true;
            }
        }
        if (isIosDevice(device)) {
            return true;
        }

        return false;
    }

    /**
     * 识别是否是ios设备
     * @param device
     * @return
     */
    private boolean isIosDevice(UsbDevice device) {
        int vendorId = device.getVendorId();
        if (vendorId == VID_APPLE) {
            int productId = device.getProductId();
            return (productId & DEVICE_IPOD_PRODUCT_ID_MASK) == DEVICE_IPHONE_PRODUCT_ID;
        }
        return false;
    }

    /**
     * 识别是否是Android手机设备
     * @param device USB设备
     * @return true if Android phone device
     */
    private boolean isAndroidPhoneDevice(UsbDevice device) {
        int vendorId = device.getVendorId();

        // 检查是否为已知的Android设备厂商
        switch (vendorId) {
            case VID_SAMSUNG:
            case VID_HTC:
            case VID_MOTOROLA:
            case VID_LG:
            case VID_HUAWEI:
            case VID_XIAOMI:
            case VID_OPPO:
            case VID_VIVO:
            case VID_ONEPLUS:
            case VID_REALME:
                LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::isAndroidPhoneDevice detected known Android vendor: 0x" + Integer.toHexString(vendorId));
                return true;
            default:
                // 对于未知厂商，通过设备类别和接口特征判断
                return isPotentialAndroidDevice(device);
        }
    }

    /**
     * 通过设备特征判断是否可能是Android设备
     * @param device USB设备
     * @return true if potentially Android device
     */
    private boolean isPotentialAndroidDevice(UsbDevice device) {
        int deviceClass = device.getDeviceClass();
        int vendorId = device.getVendorId();

        // 排除明确的非手机设备
        if (isInfrastructureDevice(device)) {
            return false;
        }

        // Android设备通常使用厂商特定类别或复合设备类别
        if (deviceClass == 0 || deviceClass == USB_CLASS_VENDOR_SPECIFIC) {
            // 检查所有接口特征
            int interfaceCount = device.getInterfaceCount();
            LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::isPotentialAndroidDevice checking " + interfaceCount + " interfaces");

            for (int i = 0; i < interfaceCount; i++) {
                try {
                    UsbInterface usbInterface = device.getInterface(i);
                    int interfaceClass = usbInterface.getInterfaceClass();

                    LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::isPotentialAndroidDevice interface[" + i + "] class=" + interfaceClass);

                    // Android设备常见的接口类别
                    if (interfaceClass == USB_CLASS_VENDOR_SPECIFIC ||
                        interfaceClass == 2 ||  // CDC (Communication Device Class)
                        interfaceClass == 3 ||  // HID
                        interfaceClass == 8 ||  // Mass Storage (可能是MTP模式)
                        interfaceClass == 6) {  // Still Image (PTP/MTP)

                        LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::isPotentialAndroidDevice potential Android device detected, interface[" + i + "] class=" + interfaceClass);
                        return true;
                    }
                } catch (Exception e) {
                    LogUtil.e(TAG + TAG_AOA, "AOACheckDevice::isPotentialAndroidDevice error checking interface[" + i + "]: " + e.getMessage());
                    // 继续检查下一个接口
                }
            }
        }

        return false;
    }

    /**
     * 识别是否是USB基础设施设备（Hub、控制器等）
     * @param device USB设备
     * @return true if infrastructure device
     */
    private boolean isInfrastructureDevice(UsbDevice device) {
        int vendorId = device.getVendorId();
        int deviceClass = device.getDeviceClass();
        String manufacturerName = device.getManufacturerName();
        String productName = device.getProductName();

        // 检查厂商ID
        if (vendorId == VID_MICROCHIP ||
            vendorId == VID_LINUX_FOUNDATION ||
            vendorId == VID_INTEL ||
            vendorId == 0x424) {  // Microchip的另一个VID
            LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::isInfrastructureDevice infrastructure vendor detected: 0x" + Integer.toHexString(vendorId));
            return true;
        }

        // 检查设备类别
        if (deviceClass == USB_CLASS_HUB) {
            LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::isInfrastructureDevice USB Hub detected");
            return true;
        }

        // 检查产品名称
        if (productName != null) {
            String productLower = productName.toLowerCase();
            if (productLower.contains("hub") ||
                productLower.contains("controller") ||
                productLower.contains("bridge") ||
                productLower.contains("xhci") ||
                productLower.contains("ehci") ||
                productLower.contains("ohci")) {
                LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::isInfrastructureDevice infrastructure device by name: " + productName);
                return true;
            }
        }

        // 检查厂商名称
        if (manufacturerName != null) {
            String manufacturerLower = manufacturerName.toLowerCase();
            if (manufacturerLower.contains("linux") ||
                manufacturerLower.contains("microchip") ||
                manufacturerLower.contains("intel")) {
                LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::isInfrastructureDevice infrastructure device by manufacturer: " + manufacturerName);
                return true;
            }
        }

        return false;
    }



    /**
     * 判断是否为潜在的手机设备（Android或iOS）
     * @param device USB设备
     * @return true if potentially a phone device
     */
    private boolean isPotentialPhoneDevice(UsbDevice device) {
        // 首先排除明确的基础设施设备
        if (isInfrastructureDevice(device)) {
            LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::isPotentialPhoneDevice device is infrastructure, not a phone");
            return false;
        }

        // 检查是否为iOS设备
        if (isIosDevice(device)) {
            LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::isPotentialPhoneDevice iOS device detected");
            return true;
        }

        // 检查是否为Android设备
        if (isAndroidPhoneDevice(device)) {
            LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::isPotentialPhoneDevice Android device detected");
            return true;
        }

        LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::isPotentialPhoneDevice device is not recognized as a phone");
        return false;
    }

    /**
     * 获取当前连接的usb device
     * @return
     */
    public boolean checkDevices() {
        LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::checkDevices start");
        if (mContext == null || mUsbManager == null) {
            LogUtil.e(TAG + TAG_AOA, "AOACheckDevice::checkDevices context or usbManager is null");
            return false;
        }

        if (!CommonData.isCheckingStart) {
            LogUtil.i(TAG + TAG_AOA, "AOACheckDevice::checkDevices checking not started");
            CommonData.iNeedConnectType = CommonData.DEVICE_TYPE_AOA;
            return false;
        }

        if (CommonData.iCurrentConnectStatus != CommonData.CONNECT_STATUS_OUT) {
              /* //todo 无线切有线判断逻辑
                && CommonData.iCurrentConnectType != CommonData.DEVICE_TYPE_WIFI_ANDROID
                    && CommonData.iCurrentConnectType != CommonData.DEVICE_TYPE_WIFI_IOS
                    */
            LogUtil.i(TAG + TAG_AOA, "AOACheckDevice::checkDevices already connected, status=" + CommonData.iCurrentConnectStatus);
            CommonData.iNeedConnectType = CommonData.DEVICE_TYPE_AOA;
            return false;
        }

        HashMap<String, UsbDevice> deviceList = mUsbManager.getDeviceList();
        LogUtil.i(TAG + TAG_AOA, "AOACheckDevice::checkDevices device count=" + deviceList.size());
        for (UsbDevice device : deviceList.values()) {
            if (device == null) {
                continue;
            }

            if (filterDevice(device)) {
                LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::checkDevices ignore filtered device");
                continue;
            }



            if (mUsbManager.hasPermission(device)) {

                // 检查是否为手机设备
                if (!isPotentialPhoneDevice(device)) {
                    LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::checkDevices device is not a phone, skip AOA check");
                    continue;
                }

                LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::checkDevices device has permission, init start");
                if (initUsbDevice(device)) {
                    LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::checkDevices init device success");
                    return true;
                }
            } else {
                LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::checkDevices device request permission");
                PendingIntent mPendingIntent = PendingIntent.getBroadcast(mContext, 0,
                        new Intent(SystemUsbReceiver.ACTION_USB_PERMISSION),
                        Build.VERSION.SDK_INT > Build.VERSION_CODES.R ? PendingIntent.FLAG_IMMUTABLE :
                                0);
                mUsbManager.requestPermission(device, mPendingIntent);
            }
        }
        return false;
    }

    public boolean initUsbDevice(UsbDevice device) {
        LogUtil.i(TAG + TAG_AOA,"AOACheckDevice::initUsbDevice start, device=" + device);
        LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::initUsbDevice deinit previous device");
        deinitUsbDevice();
        if (device == null || mUsbManager == null) {
            LogUtil.e(TAG + TAG_AOA, "AOACheckDevice::initUsbDevice device or usbManager is null");
            return false;
        }
        mUsbDevice = device;
        try {
            LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::initUsbDevice setup USB connection");
            mUsbInterface = mUsbDevice.getInterface(0);
            mUsbDeviceConnection = mUsbManager.openDevice(mUsbDevice);
            mUsbDeviceConnection.claimInterface(mUsbInterface, true);
            //注册hid
            LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::initUsbDevice register HID connection");
            HidUtil.setUsbDeviceConnection(mUsbDeviceConnection);
        } catch (Exception e) {
            LogUtil.e(TAG + TAG_AOA, "AOACheckDevice::initUsbDevice setup connection fail: " + e.getMessage());
            deinitUsbDevice();
            return false;
        }
       if (isAOADevice(mUsbDevice)) {
            LogUtil.i(TAG + TAG_AOA, "AOACheckDevice::initUsbDevice device already in AOA mode");
            LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::initUsbDevice ---> send CONNECT_STATUS_IN");
            CommonData.sendMsg(mContext, CommonData.CONNECT_STATUS_IN, CommonData.DEVICE_TYPE_AOA, null);
            return true;
        } else if (!switchToAOA()) {
            LogUtil.e(TAG + TAG_AOA, "AOACheckDevice::initUsbDevice switch to AOA fail");
            deinitUsbDevice();
            return false;
        }
        LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::initUsbDevice complete");
        return true;
    }

    /**
     * 关闭切换USB互联弹框
     */
    public void dismissUsbDialog(){
        // TODO: 无线切有线功能待产品完善后添加
    }
    public void deinitUsbDevice() {
        LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::deinitUsbDevice start");
        try {
            iUsbFd = 0;
            if (mUsbDeviceConnection != null) {
                LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::deinitUsbDevice release interface and close connection");
                mUsbDeviceConnection.releaseInterface(mUsbInterface);
                mUsbDeviceConnection.close();
            }

            mUsbDevice = null;
            mUsbDeviceConnection = null;
            LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::deinitUsbDevice complete");
        } catch (Exception ex) {
            LogUtil.e(TAG + TAG_AOA, "AOACheckDevice::deinitUsbDevice fail: " + ex.getMessage());
        }
    }

    private boolean isAOADevice(UsbDevice device) {
        if (device == null) {
            LogUtil.e(TAG + TAG_AOA, "AOACheckDevice::isAOADevice device is null");
            return false;
        }
        int vid = device.getVendorId();
        int pid = device.getProductId();
        boolean isAOA = (vid == VID_ACCESSORY && (pid >> 8 & 0x000000ff) == PID_ACCESSORY && (pid & 0x000f) <= 5);
        LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::isAOADevice vid=" + vid + ", pid=" + pid + ", isAOA=" + isAOA);
        return isAOA;
    }

    private void resetUsbDeviceIfNecessary() {
        LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::resetUsbDeviceIfNecessary start");
        try {
            UsbDeviceConnection usbConnection = mUsbManager.openDevice(mUsbDevice);
            usbConnection.releaseInterface(mUsbDevice.getInterface(0));
            Method resetDevice = UsbDeviceConnection.class.getMethod("resetDevice");
            Object result = resetDevice.invoke(usbConnection); // 调用反射方法，得到返回值
            LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::resetUsbDeviceIfNecessary result=" + result);
        } catch (Exception e) {
            LogUtil.e(TAG + TAG_AOA, "AOACheckDevice::resetUsbDeviceIfNecessary exception: " + e.getMessage());
        }
    }


    /**
     *  将手机切换成支持aoa的accessory mod
     * @return
     */
    private boolean switchToAOA() {
        LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::switchToAOA start");
        if (mUsbDevice == null || AoaLink.getAoaDevice() == null) {
            LogUtil.e(TAG + TAG_AOA, "AOACheckDevice::switchToAOA device or aoaDevice is null");
            return false;
        }

        int result; // 声明result变量供整个方法使用
        LogUtil.i(TAG,"AOACheckDevice::switchToAOA() --> 读取 配置信息");
        byte[] buffer = new byte[2];
        result = mUsbDeviceConnection.controlTransfer(UsbConstants.USB_DIR_IN | UsbConstants.USB_TYPE_VENDOR, AOA_GET_PROTOCOL, 0, 0, buffer, buffer.length, USB_CONTROL_TRANSFER_TIMEOUT);
        if (result < 0) {
            LogUtil.e(TAG + TAG_AOA, "get protocol version fail, result=" + result);
            return false;
        }
        int version = buffer[1] << 8 | buffer[0];
        LogUtil.i(TAG,"AOACheckDevice::switchToAOA() --> version = " + version);
        if (version < 1 || version > 2) {
            LogUtil.e(TAG+ TAG_AOA, "device not support AOA, version is " + version);
            return false;
        }

        LogUtil.i(TAG,"AOACheckDevice::switchToAOA() Host->Accessory (写) Manufacturer() = " + AoaLink.getAoaDevice().getManufacturer());
        result = mUsbDeviceConnection.controlTransfer(UsbConstants.USB_DIR_OUT | UsbConstants.USB_TYPE_VENDOR, AOA_SEND_IDENT, 0, 0, AoaLink.getAoaDevice().getManufacturer().getBytes(), AoaLink.getAoaDevice().getManufacturer().getBytes().length, USB_CONTROL_TRANSFER_TIMEOUT);
        if (result < 0) {
            LogUtil.e(TAG+ TAG_AOA, "send identity MANUFACTURER fail, result=" + result);
            return false;
        }

        LogUtil.i(TAG,"AOACheckDevice::switchToAOA() Host->Accessory (写) Model() = " + AoaLink.getAoaDevice().getModel());
        result = mUsbDeviceConnection.controlTransfer(UsbConstants.USB_DIR_OUT | UsbConstants.USB_TYPE_VENDOR, AOA_SEND_IDENT, 0, 1, AoaLink.getAoaDevice().getModel().getBytes(), AoaLink.getAoaDevice().getModel().getBytes().length, USB_CONTROL_TRANSFER_TIMEOUT);
        if (result < 0) {
            LogUtil.e(TAG+ TAG_AOA, "AOACheckDevice::switchToAOA() --> send identity MODEL fail, result=" + result);
            return false;
        }

        LogUtil.i(TAG,"AOACheckDevice::switchToAOA() Host->Accessory (写) Description = " + AoaLink.getAoaDevice().getDescription());
        result = mUsbDeviceConnection.controlTransfer(UsbConstants.USB_DIR_OUT | UsbConstants.USB_TYPE_VENDOR, AOA_SEND_IDENT, 0, 2, AoaLink.getAoaDevice().getDescription().getBytes(), AoaLink.getAoaDevice().getDescription().getBytes().length, USB_CONTROL_TRANSFER_TIMEOUT);
        if (result < 0) {
            LogUtil.e(TAG+ TAG_AOA, "AOACheckDevice::switchToAOA() --> send identity DESCRIPTION fail, result=" + result);
            return false;
        }

        LogUtil.i(TAG,"AOACheckDevice::switchToAOA() Host->Accessory (写) Version = " + AoaLink.getAoaDevice().getVersion());
        result = mUsbDeviceConnection.controlTransfer(UsbConstants.USB_DIR_OUT | UsbConstants.USB_TYPE_VENDOR, AOA_SEND_IDENT, 0, 3, AoaLink.getAoaDevice().getVersion().getBytes(), AoaLink.getAoaDevice().getVersion().getBytes().length, USB_CONTROL_TRANSFER_TIMEOUT);
        if (result < 0) {
            LogUtil.e(TAG + TAG_AOA, "AOACheckDevice::switchToAOA() --> send identity VERSION fail, result=" + result);
            return false;
        }

        LogUtil.i(TAG + TAG_AOA,"AOACheckDevice::switchToAOA ---> send Uri=" + AoaLink.getAoaDevice().getUri());
        result = mUsbDeviceConnection.controlTransfer(UsbConstants.USB_DIR_OUT | UsbConstants.USB_TYPE_VENDOR, AOA_SEND_IDENT, 0, 4, AoaLink.getAoaDevice().getUri().getBytes(), AoaLink.getAoaDevice().getUri().getBytes().length, USB_CONTROL_TRANSFER_TIMEOUT);
        if (result < 0) {
            LogUtil.e(TAG + TAG_AOA, "AOACheckDevice::switchToAOA send URI fail, result=" + result);
            return false;
        }

        LogUtil.i(TAG + TAG_AOA,"AOACheckDevice::switchToAOA ---> send Serial=" + AoaLink.getAoaDevice().getSerial());
        result = mUsbDeviceConnection.controlTransfer(UsbConstants.USB_DIR_OUT | UsbConstants.USB_TYPE_VENDOR, AOA_SEND_IDENT, 0, 5, AoaLink.getAoaDevice().getSerial().getBytes(), AoaLink.getAoaDevice().getSerial().getBytes().length, USB_CONTROL_TRANSFER_TIMEOUT);
        if (result < 0) {
            LogUtil.e(TAG + TAG_AOA, "AOACheckDevice::switchToAOA send SERIAL fail, result=" + result);
            return false;
        }

        LogUtil.i(TAG,"AOACheckDevice::switchToAOA() Host->Accessory (写) AOA_START_ACCESSORY ");
        result = mUsbDeviceConnection.controlTransfer(UsbConstants.USB_DIR_OUT | UsbConstants.USB_TYPE_VENDOR, AOA_START_ACCESSORY, 0, 0, null, 0, USB_CONTROL_TRANSFER_TIMEOUT);
        if (result < 0) {
            LogUtil.e(TAG+ TAG_AOA , "AOACheckDevice::switchToAOA() --> start accessory mode fail, result=" + result);
            return false;
        }

        LogUtil.i(TAG + TAG_AOA, "AOACheckDevice::switchToAOA switch to AOA mode success");
        return true;
    }


    /**
     * 恢复AOA设备检测和连接
     *
     * 调用时机：
     * 1. ConnectManager.startCheckConnect() 中，当 iNeedConnectType == DEVICE_TYPE_NONE 时
     * 2. 当前连接状态不是 CONNECTED 或 CONNECTING 时
     * 3. 通常在应用启动或从后台恢复时，用于重新检测已连接的USB设备
     * 使用场景：
     * - 应用启动时恢复之前的USB连接
     * - 从无线连接切换回有线连接时
     * - 后台切换到前台唤醒后重新检测USB设备状态
     */
    public void resume() {
        LogUtil.i(TAG + TAG_AOA, "AOACheckDevice::resume start, device count=" + (mUsbManager.getDeviceList() == null ? 0: mUsbManager.getDeviceList().size()));
        if (!mUsbManager.getDeviceList().isEmpty()) {
            if (mUsbDevice == null) {
                HashMap<String, UsbDevice> deviceList = mUsbManager.getDeviceList();
                LogUtil.i(TAG + TAG_AOA, "AOACheckDevice::resume find device, count=" + deviceList.size());
                for (UsbDevice device : deviceList.values()) {
                    if (device == null) {
                        continue;
                    }
                    if (filterDevice(device)) {
                        LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::resume ignore filtered device");
                        continue;
                    }
                    mUsbDevice = device;
                }
            }
            CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_OUT;
            if (isAOADevice(mUsbDevice)) {
                LogUtil.i(TAG + TAG_AOA, "AOACheckDevice::resume device already in AOA mode, reset");
                resetUsbDeviceIfNecessary();
            } else {
                LogUtil.d(TAG + TAG_AOA, "AOACheckDevice::resume device not in AOA mode, check devices");
                checkDevices();
            }
        }
    }
}
