package com.autoai.welinkapp;

import android.content.Context;
import android.content.IntentFilter;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.util.Log;
import android.view.MotionEvent;
import android.view.Surface;

import androidx.annotation.NonNull;


import com.autoai.common.util.LogUtil;
import com.autoai.welink.macro.WL_API_APP_MSG_TYPE;
import com.autoai.welink.macro.WL_API_HARD_KEY;
import com.autoai.welink.wireless.WLHardware;
import com.autoai.welinkapp.aoa.AoaDevice;
import com.autoai.welinkapp.aoa.AoaLink;
import com.autoai.welinkapp.audio.AudioPlayer;
import com.autoai.welinkapp.checkconnect.CommonData;
import com.autoai.welinkapp.eap.EapDevice;
import com.autoai.welinkapp.eap.EapLink;
import com.autoai.welinkapp.idb.IdbDevice;
import com.autoai.welinkapp.idb.IdbLink;
import com.autoai.welinkapp.model.AutoAgreeCallback;
import com.autoai.welinkapp.model.HidCallback;
import com.autoai.welinkapp.model.HidCheckCallback;
import com.autoai.welinkapp.model.OnLinkStatusChangeListener;
import com.autoai.welinkapp.model.OnMessageListener;
import com.autoai.welinkapp.model.PlatformAdaptor;
import com.autoai.welinkapp.model.SdkConfig;
import com.autoai.welinkapp.model.SdkViewModel;
import com.autoai.welinkapp.model.UIResponder;
import com.autoai.welinkapp.platform.LinkPlatform;
import com.autoai.welinkapp.service.WirelessReceiver;
import com.autoai.welinkapp.util.MediaCodecUtil;

/**
 * Link 互联 Host 端控制入口
 */
@SuppressWarnings("unused")
public class LinkHost {
    private static final String TAG = "LinkHost";

    private static SdkViewModel mSdkViewModel;

    private AutoAgreeCallback autoAgreeCallback;
    private static boolean isInit;

    private static WirelessReceiver mWirelessReceiver;

    private static Context mContext;

    /**
     * 用于事件调度
     */
    private static HandlerThread handlerThread;
    private static LinkHostDispatcher linkHostDispatcher;

    /**
     * 初始化入口
     *
     * @param context 上下文
     * @deprecated
     */
    public static void init(@NonNull Context context) {
        init(context, new AoaDevice() {
            @NonNull
            @Override
            public String getManufacturer() {
                return "Mapbar, Inc";
            }

            @NonNull
            @Override
            public String getModel() {
                return "WeLink";
            }

            @NonNull
            @Override
            public String getDescription() {
                return "WeLink";
            }

            @NonNull
            @Override
            public String getUri() {
                return "https://welink-rs.autoai.com/saic/ap32";
            }

            @NonNull
            @Override
            public String getSerial() {
                return "mySerial";
            }

            @NonNull
            @Override
            public String getVersion() {
                return "1.0";
            }
        }, null);


    }

    /**
     * 初始化入口,提供EapDevice驱动来支持EAP互联
     *
     * @param context 上下文
     * @param device  eap驱动
     * @deprecated
     */
    public static void init(@NonNull Context context, @NonNull EapDevice device) {
        init(context, new AoaDevice() {
            @NonNull
            @Override
            public String getManufacturer() {
                return "Mapbar, Inc";
            }

            @NonNull
            @Override
            public String getModel() {
                return "WeLink";
            }

            @NonNull
            @Override
            public String getDescription() {
                return "WeLink";
            }

            @NonNull
            @Override
            public String getUri() {
                return "https://welink-rs.autoai.com/saic/ap32";
            }

            @NonNull
            @Override
            public String getSerial() {
                return "mySerial";
            }

            @NonNull
            @Override
            public String getVersion() {
                return "1.0";
            }
        }, device);
    }

    /**
     * 初始化入口,提供AoaDevice驱动来支持AOA互联,提供EapDevice驱动来支持EAP互联.
     *
     * @param context   上下文
     * @param aoaDevice aoa驱动。如果为null，aoa不能正常工作
     * @param eapDevice eap驱动。如果为null，eap不能正常工作
     * @deprecated
     */
    public static void init(@NonNull Context context, AoaDevice aoaDevice, EapDevice eapDevice) {
        init(context, aoaDevice, eapDevice, null);
    }

    /**
     * 初始化入口,提供AoaDevice驱动来支持AOA互联,提供EapDevice驱动来支持EAP互联.
     *
     * @param context   上下文
     * @param aoaDevice aoa驱动。如果为null，aoa不能正常工作
     * @param eapDevice eap驱动。如果为null，eap不能正常工作
     * @deprecated
     */
    public static void init(@NonNull Context context, AoaDevice aoaDevice, EapDevice eapDevice, LinkPlatform platform) {
        init(context, aoaDevice, eapDevice, platform, null);
    }

    /**
     * 初始化入口,提供AoaDevice驱动来支持AOA互联,提供EapDevice驱动来支持EAP互联.
     *
     * @param context   上下文
     * @param aoaDevice aoa驱动。如果为null，aoa不能正常工作
     * @param eapDevice eap驱动。如果为null，eap不能正常工作
     * @deprecated
     */
    public static void init(@NonNull Context context, AoaDevice aoaDevice, EapDevice eapDevice, LinkPlatform platform, AudioPlayer audioPlayer) {
        init(context, "123445", 30, aoaDevice, eapDevice, platform, audioPlayer);
    }

    /**
     * 初始化入口,提供AoaDevice驱动来支持AOA互联,提供EapDevice驱动来支持EAP互联.
     *
     * @param context   上下文
     * @param aoaDevice aoa驱动。如果为null，aoa不能正常工作
     * @param eapDevice eap驱动。如果为null，eap不能正常工作
     */
    public static void init(@NonNull Context context, String serialNum, int fps, AoaDevice aoaDevice, EapDevice eapDevice, LinkPlatform platform, AudioPlayer audioPlayer) {
        init(context, serialNum, fps, aoaDevice, eapDevice, null, platform, audioPlayer,-1);
    }


    /**
     * 初始化入口,提供AoaDevice驱动来支持AOA互联,提供EapDevice驱动来支持EAP互联.
     *
     * @param context   上下文
     * @param aoaDevice aoa驱动。如果为null，aoa不能正常工作
     * @param eapDevice eap驱动。如果为null，eap不能正常工作
     */
    private static void  init(@NonNull Context context, String serialNum, int fps, AoaDevice aoaDevice, EapDevice eapDevice, IdbDevice idbDevice, LinkPlatform platform, AudioPlayer audioPlayer,int rtt) {
        if (isInit) {
            LogUtil.d(TAG, "Already inited, skip init invoke.");
            return;
        }
        if (aoaDevice != null) {
            LogUtil.v(TAG, "init AOA Device");
        }
        //--->将AOA所需的设备信息初始化，后边流程使用
        AoaLink.init(aoaDevice);
        if (eapDevice != null) {
            LogUtil.v(TAG, "init EAP Device");//IusbDevice devices
        }
        //--->将EAP所需的设备信息初始化，后边流程使用
        EapLink.init(eapDevice);
        if (idbDevice != null) {
            LogUtil.v(TAG, "init IDB Device");
        }
        //--->将IDB所需的设备信息初始化，后边流程使用
        IdbLink.init(idbDevice);
        //--->互联sdk初始化核心入口
        mSdkViewModel = SdkViewModel.getInstance(context);
        if (platform != null) {
            mSdkViewModel.platformadaptor.setPlatform(platform);
            platform.init(mSdkViewModel.platformadaptor);
        }

        //---> audioAdapter音频传输全投屏暂不使用
        if (audioPlayer != null) {
            mSdkViewModel.audioAdapter.init(audioPlayer);
        }

        //---> 设置播放帧率
        if (fps > 0) {
            mSdkViewModel.setPhoneFps(fps);
        }
           mSdkViewModel.setRtt(rtt);


        if (serialNum != null) {
            mSdkViewModel.setSerialNum(serialNum);
        }

        isInit = true;
        mContext = context;
        LogUtil.d(TAG, "Init done");

        //--->无线连接状态，注意不是原生wifi广播，而是业务中无感互联相关状态通过广播形式内部通知
        registerWirelessReceiver(context);

        handlerThread = new HandlerThread("platform-dispatch-thread") {
            @Override
            protected void onLooperPrepared() {
                super.onLooperPrepared();
                linkHostDispatcher = new LinkHostDispatcher(handlerThread.getLooper());
            }
        };
        handlerThread.start();
    }

    /**
     * 初始化入口,提供AoaDevice驱动来支持AOA互联,提供EapDevice驱动来支持EAP互联.
     *
     * @param context   上下文
     * @param sdkConfig SDK配置
     */
    public static void init(@NonNull Context context, SdkConfig sdkConfig) {
        init(context, sdkConfig.getSerialNum(), sdkConfig.getFps(), sdkConfig.getAoaDevice(), sdkConfig.getEapDevice(), sdkConfig.getIdbDevice(), sdkConfig.getPlatform(), sdkConfig.getAudioPlayer(),sdkConfig.getRtt());
    }


    /**
     * 配置debug模式
     */
    public static void applyDebug(boolean logDebug, boolean fileDebug) {
        WLHardware.enableLogCat(logDebug);
        WLHardware.enableLogFile(fileDebug);
        if (logDebug || fileDebug) {
            LogUtil.init(logDebug, LogUtil.VERBOSE, false, fileDebug ? LogUtil.getLogPath("linkSdkLog.log") : null);
        }
    }

    /**
     * 启动互联环境, 调用后允许 Device 端进行互联
     */
    public static void readyForLink(@NonNull Context context) {
        checkInit();

        LogUtil.d(TAG, "readyForLink to textureView");

//        registerWirelessReceiver(context);

        mSdkViewModel.onCreate(context); // 内部有判重逻辑

        mSdkViewModel.onResume();
        mSdkViewModel.startCheckConnect();
        mSdkViewModel.platformadaptor.sendMsg(
                PlatformAdaptor.MSG_TYPE.AP_MSG_APP_STATE,
                -1,
                true);
    }

    /**
     * 恢复投屏
     */
    public static void onResume() {
        mSdkViewModel.onResume();
    }

    /**
     * 暂停投屏
     */
    public static void onPause() {
        mSdkViewModel.onPause();
    }
    public static boolean isDisplayFrame() {
        return MediaCodecUtil.getInstance().isDisplayFrame();
    }

    private static void checkInit() {
        if (!isInit) {
            throw new IllegalStateException("LinkHost not init!!!");
        }
    }

    /**
     * 设置 Link 画面容器
     *
     * @param surface
     */
    public static void registerLinkSurface(@NonNull Surface surface, int width, int height) {
        checkInit();
        if (!surface.isValid()) {
            throw new RuntimeException("Surface 必须是有效的");
        }
        LogUtil.i("shecw1", "LinkHost::registerLinkSurface surface: " + surface + ", width: " + width + ", height: " + height);
        LogUtil.d(TAG, "registerLinkSurface surface: " + surface + ", width: " + width + ", height: " + height);
        mSdkViewModel.startVideo(surface, width, height);
    }

    public static void unRegisterLinkSurface(@NonNull Surface surface) {
        LogUtil.d(TAG, "unRegisterLinkSurface surface: " + surface);
        // 断开连接时统一停止
        mSdkViewModel.stopVideo(surface);
    }

    /**
     * 传递触控事件, 用于手机的反控
     *
     * @param motionEvent
     */
    public static void screenTouch(MotionEvent motionEvent) {
        checkInit();
        //此处功能暂无人调用，先屏蔽，由HidUtil统一处理
        //TouchUtil.getInstance().MulitTouch(motionEvent);
    }

    public static void sendButton(int type, int param) {
        checkInit();
        LogUtil.d(TAG, "sendButton type: " + type + ", param: " + param);
        mSdkViewModel.getSdkApi().sendButton(type, param);
    }

    /**
     * 设置手机方向变化时的监听
     *
     * @param hidCallback 手机切换横竖屏的回调
     */
    public static void setHidRegionCallback(HidCallback hidCallback) {
        checkInit();
        mSdkViewModel.setHidRegionCallback(hidCallback);
    }

    /**
     * 重置互联状态
     */
    public static void resetConnectStatus() {
        mSdkViewModel.resetConnectStatus();
    }

    /**
     * 注册页面的相应器, 相应 SDK 请求的 UI 切换
     *
     * @param responder
     */
    public static void registerUiResponder(UIResponder responder) {
        checkInit();
        mSdkViewModel.registerUiResponder(responder);
    }


    public static void addMessageListener(OnMessageListener listener) {
        checkInit();
        mSdkViewModel.addMessageListener(listener);
    }

    public static void removeMessageListener(OnMessageListener listener) {
        checkInit();
        mSdkViewModel.removeMessageListener(listener);
    }

    public static void addLinkStateChangeListener(OnLinkStatusChangeListener listener) {
        checkInit();
        mSdkViewModel.addLinkStateChangeListener(listener);
    }

    public static void removeLinkStateChangeListener(OnLinkStatusChangeListener listener) {
        checkInit();
        mSdkViewModel.removeLinkStateChangeListener(listener);
    }

    private static void registerWirelessReceiver(@NonNull Context context) {
        LogUtil.i(TAG, "WLHardware version: " + WLHardware.getVersion());
        LogUtil.i(TAG, "registerWirelessReceiver unRegisterWirelessReceiver");
        mWirelessReceiver = new WirelessReceiver();
        IntentFilter mWirelessFilter = new IntentFilter();
        mWirelessFilter.addAction(CommonData.START_CHECK_MSG);
        mWirelessFilter.addAction(CommonData.STOP_CHECK_MSG);
        mWirelessFilter.addAction(CommonData.STOP_CONNECT_MSG);
        context.registerReceiver(mWirelessReceiver, mWirelessFilter,Context.RECEIVER_NOT_EXPORTED);
    }

    private static void unRegisterWirelessReceiver(@NonNull Context context) {
        if (mWirelessReceiver != null) {
            LogUtil.i(TAG, "unRegisterWirelessReceiver unRegisterWirelessReceiver");
            context.unregisterReceiver(mWirelessReceiver);
        }
    }


    /**
     * 停止互联环境
     *
     * @param forceDisconnect 对于已互联状态, 是否强制断开连接
     */
    public static void stopForLink(Context context, boolean forceDisconnect) {
        if (!isInit) {
            throw new IllegalStateException("尚未开始投屏初始化, 请先启动投屏再进行此调用!!!");
        }

        LogUtil.d(TAG, "stopForLink!");
//        unRegisterWirelessReceiver(context);
        mSdkViewModel.platformadaptor.sendMsg(
                PlatformAdaptor.MSG_TYPE.AP_MSG_APP_STATE,
                -1,
                false);
        mSdkViewModel.onPause();
        mSdkViewModel.stopCheckConnect();

        if (forceDisconnect) {
            mSdkViewModel.onDestroy();
        }
    }

    /**
     * 完全销毁, 不再需要互联时调用. 可选调用
     */
    public static void destroy() {
        if (!isInit) {
            return;
        }
        if (mContext != null) {
            unRegisterWirelessReceiver(mContext);
            mContext = null;
        }
        // 程序终止的时候执行
        LogUtil.i(TAG, "destroy!");
        mSdkViewModel.onDestroy();
        isInit = false;
        LogUtil.exit();
    }

    public static int getMusicAudioFoucs() {
        checkInit();
        return mSdkViewModel.getMusicAudioFoucs();
    }

    @Deprecated
    public static void sendAudioReplyData(int i, byte[] byteProtocol, int length) {
        checkInit();
        if (mSdkViewModel != null && mSdkViewModel.getSdkApi() != null) {
            mSdkViewModel.getSdkApi().sendAudioReplyData(i, byteProtocol, length);
        }
    }

    @Deprecated
    public static void sendMicData(byte[] micData, int length) {
        checkInit();
        if (mSdkViewModel != null && mSdkViewModel.getSdkApi() != null) {
            mSdkViewModel.getSdkApi().sendMicData(micData, length);
        }
    }

    public static void sendID3Data(Object id3) {
        checkInit();
        mSdkViewModel.platformadaptor.sendMsg(PlatformAdaptor.MSG_TYPE.AP_MSG_ID3_DATA, -1, id3);
    }

    public static void sendMusicStop() {
        checkInit();
        if (mSdkViewModel != null && mSdkViewModel.getSdkApi() != null) {
            mSdkViewModel.getSdkApi().sendMsg(WL_API_APP_MSG_TYPE.MUSIC_STOP, 0);
        }
    }

    public static void sendMusicPlay() {
        checkInit();
        if (mSdkViewModel != null && mSdkViewModel.getSdkApi() != null) {
            mSdkViewModel.getSdkApi().sendMsg(WL_API_APP_MSG_TYPE.MUSIC_PLAY, 0);
        }
    }

    public static void sendVrEnd() {
        checkInit();
        if (mSdkViewModel != null && mSdkViewModel.getSdkApi() != null) {
            mSdkViewModel.getSdkApi().sendHardKey(WL_API_HARD_KEY.WL_API_HARD_KEY_VR_END, 0);
        }
    }


    private static class LinkHostDispatcher extends Handler {
        public LinkHostDispatcher(Looper looper) {
            super(looper);
        }

        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case 0:
                    checkInit();
                    if (mSdkViewModel != null && mSdkViewModel.getSdkApi() != null) {
                        Log.i(TAG, "LinkHost::checkIsPlayVideo");
                        mSdkViewModel.getSdkApi().sendMsg(WL_API_APP_MSG_TYPE.CAR_DRIVER_STATUS_CHANGE, msg.obj);
                    }
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 向手机端发送通知，查询当前手机上是否正在播放视频
     * 背景：该方法主要是处理汽车行车安全，走行规制等规制
     */
    public static void checkIsPlayVideo(int curDriveState) {
        linkHostDispatcher.removeMessages(0);
        Message msg = linkHostDispatcher.obtainMessage(0);
        msg.obj = curDriveState;
        linkHostDispatcher.sendMessageDelayed(msg,2000);
    }


    public static void registerAutoAgreeCallback(AutoAgreeCallback autoAgreeCallback){
        mSdkViewModel.registerAutoAgreeCallback(autoAgreeCallback);
    }

    public static void unregisterAutoAgreeCallback(){
        mSdkViewModel.unregisterAutoAgreeCallback();
    }

    public static void registerHidCheckCallback(HidCheckCallback callback) {
        mSdkViewModel.registerHidCheckCallback(callback);
    }

    public static void unregisterHidCheckCallback(HidCheckCallback callback) {
        mSdkViewModel.unregisterHidCheckCallback(callback);
    }
    

    public static void startVerification(){
        mSdkViewModel.startVerification();
    }
}
