//package com.autoai.welinkapp.checkconnect;
//
//import android.annotation.SuppressLint;
//import android.content.Context;
//
//import com.autoai.common.util.LogUtil;
//
///**
// * Adapter for device connect status callback(.jar).
// */
//public class ConnectAdapter {
//    private static final String TAG = "WL_DRIVER";
//    @SuppressLint("StaticFieldLeak")
//    private static ConnectAdapter mInstance = null;
//    private Context mContext = null;
////    private static SvPhoneLinkDeviceManager mSvPhoneLinkDeviceManager = null;
//
//    private static final int DEVICE_TYPE_AOA = 1;
//    private static final int DEVICE_TYPE_EAP = 2;
//    private static final int DEVICE_TYPE_WIFI_ANDROID = 3;
//    private static final int DEVICE_TYPE_WIFI_IOS = 4;
//
//    private static final int DEVICE_STATUS_NONE = 0;
//    private static final int DEVICE_STATUS_CONNECTED = 2;
//
//    private static final String ACTION_USB_ATTACH = "com.wldriver.checkconnect.USB_ATTACH";
//    private static final String ACTION_USB_DETACH = "com.wldriver.checkconnect.USB_DETACH";
//    private static final String PACKAGE_NAME = "com.autoai.welinkapp";
//
//    private static final String mSerialNum = null;
//    private static final String WIRELESS_SERIAL_NUM = "welink-wireless";
//
//    private ConnectAdapter() {
//    }
//
//    public static ConnectAdapter getInstance() {
//        if (null == mInstance) {
//            synchronized (ConnectAdapter.class) {
//                if (null == mInstance) {
//                    mInstance = new ConnectAdapter();
//                }
//            }
//        }
//        return mInstance;
//    }
//
//    public void init(Context context) {
//        LogUtil.d(TAG, "ConnectAdapter init");
//        mContext = context;
////        LogUtil.i(TAG, "[IF] registerPhoneLinkDeviceListener start");
////        mSvPhoneLinkDeviceManager = SvPhoneLinkDeviceManager.get(mContext);
////        mSvPhoneLinkDeviceManager.registerPhoneLinkDeviceListener(this);
////        LogUtil.i(TAG, "[IF] registerPhoneLinkDeviceListener end");
//    }
//
//    public void deinit() {
//        LogUtil.d(TAG, "ConnectAdapter deinit");
////        LogUtil.i(TAG, "[IF] unregisterPhoneLinkDeviceListener start");
////        mSvPhoneLinkDeviceManager.unregisterPhoneLinkDeviceListener(this);
////        LogUtil.i(TAG, "[IF] unregisterPhoneLinkDeviceListener end");
//    }
//
//    public void setConnectStatus(int deviceType, int deviceStatus) {
////        LogUtil.d(TAG, "ConnectAdapter setConnectStatus, type: " + deviceType + ", status: " + deviceStatus);
////        int type = getType(deviceType);
////        int status = getStatus(deviceStatus);
////        if (type < 0 || status < 0) {
////            LogUtil.e(TAG, "Unknown input, type: " + type + ", status: " + status);
////            return;
////        }
////        String strPara = WIRELESS_SERIAL_NUM;
////        if (deviceType < DEVICE_TYPE_WIFI_ANDROID) {
////            strPara = CommonData.strDeviceSerialNum;
////        }
////        LogUtil.i(TAG, "[IF] setPhoneLinkConnectState start");
////        LogUtil.i(TAG, "type: " + type + ", status: " + status + ", sn: " + strPara);
//////        SvRvcManager.getInstance().mRvcStatusListener.onRvcStatusChange(/*BACKING_UP_STATE.END*/1);
//////        boolean ret = mSvPhoneLinkDeviceManager.setPhoneLinkConnectState(type, status, strPara);
//////        LogUtil.i(TAG, "[IF] setPhoneLinkConnectState end, ret: " + ret);
////        if (DEVICE_STATUS_NONE == deviceStatus) {
////            CommonData.strDeviceSerialNum = "";
////        }
//    }
//
///*    public String getDeviceSerialNum() {
//        List<SvPhoneLinkDevice> deviceList = mSvPhoneLinkDeviceManager.getLinkDeviceContextAll();
//        int count = deviceList.size();
//        LogUtil.i(TAG, "SvPhoneLinkDevice, device count = " + count);
//        for (int i = 0; i < count; ++i) {
//            SvPhoneLinkDevice device = deviceList.get(i);
//            if (device == null) {
//                LogUtil.e(TAG, "getDeviceSerialNum device is null");
//                continue;
//            }
//            return device.info.serial_num;
//        }
//        return "";
//    }*/
//
//    public String getDeviceSerialNum() {
//        if (mSerialNum == null) {
//            return "";
//        } else {
//            return mSerialNum;
//        }
//    }
//
////    private int getType(int deviceType) {
////        int type = -1;
////        switch (deviceType) {
////            case DEVICE_TYPE_AOA:
////                type = SvPhoneLinkDeviceType.LinkDeviceType.DEVICE_TYPE_ANDROID_EXTERNAL_LINK;
////                break;
////            case DEVICE_TYPE_EAP:
////                type = SvPhoneLinkDeviceType.LinkDeviceType.DEVICE_TYPE_EXTERNAL_LINK;
////                break;
////            case DEVICE_TYPE_WIFI_ANDROID:
////            case DEVICE_TYPE_WIFI_IOS:
////                type = SvPhoneLinkDeviceType.LinkDeviceType.DEVICE_TYPE_WIRELESS_EXTERNAL_LINK;
////                break;
////            default:
////                LogUtil.e(TAG, "Unknown input type.");
////                break;
////        }
////        return type;
////    }
////
////    private int getStatus(int deviceStatus) {
////        int status = -1;
////        if (DEVICE_STATUS_CONNECTED == deviceStatus) {
////            status = SvPhoneLinkDeviceType.LinkDeviceStatus.DEVICE_STATUS_ATTACH;
////        } else if (DEVICE_STATUS_NONE == deviceStatus) {
////            status = SvPhoneLinkDeviceType.LinkDeviceStatus.DEVICE_STATUS_DETACH;
////        } else {
////            LogUtil.e(TAG, "Unknown input status.");
////        }
////        return status;
////    }
////
////    @Override
////    public void onLinkDeviceTypeListChange(List<SvPhoneLinkDevice> list) {
////    }
////
////    @Override
////    public void onLinkDeviceTipStatusChange(String s, int i, boolean b) {
////    }
////
////    @Override
////    public void onLinkDeviceContextChange(SvPhoneLinkDevice device) {
////        LogUtil.i(TAG, "onLinkDeviceContextChange type: " + device.type);
////        if (SvPhoneLinkDeviceType.LinkDeviceType.DEVICE_TYPE_EXTERNAL_LINK == device.type) {
////            //EAP
////            switch (device.status) {
////                case SvPhoneLinkDeviceType.LinkDeviceStatus.DEVICE_STATUS_ATTACH:
////                    mSerialNum = device.info.serial_num;
////                    //switch to Device mode
////                    LogUtil.i(TAG, "onLinkDeviceContextChange, DEVICE_STATUS_ATTACH, sn: " + mSerialNum);
////                    mSvPhoneLinkDeviceManager.usbRoleSwitch(device, SvPhoneLinkDeviceType.LinkDeviceUsbMode.DEVICE_MODE);
////                    break;
////                case SvPhoneLinkDeviceType.LinkDeviceStatus.DEVICE_STATUS_DETACH:
////                    LogUtil.i(TAG, "onLinkDeviceContextChange, DEVICE_STATUS_DETACH");
////                    mSerialNum = null;
////                    //switch to Host mode
////                    mSvPhoneLinkDeviceManager.usbRoleSwitch(device, SvPhoneLinkDeviceType.LinkDeviceUsbMode.HOST_MODE);
////                    Externallink.deactiveiAP();
////                    if (CommonData.DEVICE_TYPE_EAP == CommonData.iNeedConnectType) {
////                        CommonData.iNeedConnectType = CommonData.DEVICE_TYPE_NONE;
////                    }
////                    CommonData.sendMsg(mContext, CommonData.CONNECT_STATUS_OUT, CommonData.DEVICE_TYPE_EAP, null);
////                    break;
////                case SvPhoneLinkDeviceType.LinkDeviceStatus.DEVICE_STATUS_ONLINE:
////                    LogUtil.i(TAG, "onLinkDeviceContextChange, DEVICE_STATUS_ONLINE");
////                    Externallink.activeiAP(SvPhoneLinkDeviceType.LinkDeviceType.DEVICE_TYPE_EXTERNAL_LINK);
////                    break;
////                default:
////                    LogUtil.i(TAG, "onLinkDeviceContextChange---unknown");
////                    break;
////            }
////        } else if (SvPhoneLinkDeviceType.LinkDeviceType.DEVICE_TYPE_ANDROID_EXTERNAL_LINK == device.type) {
////            //AOA
////            LogUtil.i(TAG, "DEVICE_TYPE_ANDROID_EXTERNAL_LINK, status: " + device.status);
////            switch (device.status) {
////                case SvPhoneLinkDeviceType.LinkDeviceStatus.DEVICE_STATUS_ATTACH:
////                    mSerialNum = device.info.serial_num;
////                    LogUtil.d(TAG, "DEVICE_STATUS_ATTACH, sn: " + mSerialNum);
////                    Intent intentIn = new Intent(ACTION_USB_ATTACH);
////                    intentIn.setPackage(PACKAGE_NAME);
////                    mContext.sendBroadcast(intentIn);
////                    break;
////                case SvPhoneLinkDeviceType.LinkDeviceStatus.DEVICE_STATUS_DETACH:
////                    mSerialNum = null;
////                    Intent intentOut = new Intent(ACTION_USB_DETACH);
////                    intentOut.setPackage(PACKAGE_NAME);
////                    mContext.sendBroadcast(intentOut);
////                    break;
////                default:
////                    LogUtil.i(TAG, "onLinkDeviceContextChange---unknown");
////                    break;
////            }
////        } else {
////            LogUtil.d(TAG, "Other device type: " + device.type);
////        }
////    }
////
////    @Override
////    public void onWireLessLinkDeviceContextChange(int type, int status, Bundle bundle) {
////        LogUtil.d(TAG, "onWireLessLinkDeviceContextChange, type: " + type + ", status: ," + status + "bundle: " + bundle);
////        String strSn = bundle.getString("Serial_Num");
////        if (strSn == null) {
////            LogUtil.e(TAG, "bundle get sn failed.");
////            return;
////        }
////        if ((SvPhoneLinkDeviceType.LinkDeviceType.DEVICE_TYPE_WIRELESS_EXTERNAL_LINK == type) && (strSn.equals(WIRELESS_SERIAL_NUM))) {
////            //wireless
////            LogUtil.i(TAG, "DEVICE_TYPE_WIRELESS_EXTERNAL_LINK, status: " + status);
////            switch (status) {
////                case SvPhoneLinkDeviceType.LinkDeviceStatus.DEVICE_STATUS_ATTACH:
////                    LogUtil.d(TAG,"wireless DEVICE_STATUS_ATTACH");
////                    break;
////                case SvPhoneLinkDeviceType.LinkDeviceStatus.DEVICE_STATUS_DETACH:
////                    LogUtil.d(TAG,"wireless DEVICE_STATUS_DETACH");
////                    CommonData.sendMsg(mContext, CommonData.CONNECT_STATUS_OUT, CommonData.DEVICE_TYPE_WIFI_ANDROID, null);
////                    CommonData.sendMsg(mContext, CommonData.WIRELESS_STOP_CHECK, 0, null);
////                    break;
////                default:
////                    LogUtil.i(TAG, "onWireLessLinkDeviceContextChange---unknown");
////                    break;
////            }
////        } else {
////            LogUtil.d(TAG, "onWireLessLinkDeviceContextChange, Other device type: " + type);
////        }
////    }
////
////    @Override
////    public void onMultLinkDeviceSwitchChange(int i, String s, int i1, String s1, int i2) {
////    }
//
//}
