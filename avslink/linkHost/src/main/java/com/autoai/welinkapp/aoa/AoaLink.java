package com.autoai.welinkapp.aoa;

import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 */
public class AoaLink {
    private static final AtomicReference<AoaDevice> AOA_DEVICE_ATOMIC = new AtomicReference<>();

    public static void init(AoaDevice aoaDevice) {
        AOA_DEVICE_ATOMIC.set(aoaDevice);
    }

    public static AoaDevice getAoaDevice() {
        return AOA_DEVICE_ATOMIC.get();
    }

    public static void deinit() {
        AOA_DEVICE_ATOMIC.set(null);
    }
}
