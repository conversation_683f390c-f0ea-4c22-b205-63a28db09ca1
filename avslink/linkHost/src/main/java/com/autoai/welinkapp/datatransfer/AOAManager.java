package com.autoai.welinkapp.datatransfer;

import android.annotation.SuppressLint;
import android.content.Context;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbManager;

import com.autoai.common.util.LogUtil;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.Arrays;
import java.util.HashMap;

/**
 * AOA device data transfer, include send and receive thread.
 */
@SuppressWarnings("WeakerAccess")
public class AOAManager {
    public static int MAX_ERROR_COUNT = 200;
    private static final String TAG = "WL_DRIVER";

    /**
     * 所有 AOA 相关的操作日志
     */
    private static final String TAG_AOA = ".WL_AOA";

    private static final boolean CHECK_AOA_MANAGER = false;//LogUtil.isFullLog();

    @SuppressLint("StaticFieldLeak")
    private static AOAManager mInstance = null;
    private Context mContext = null;
    private UsbManager mUsbManager = null;
    private Socket mSocket = null;

    private AOAServerThread mAOAServerThread = null;
    private AOASendThread mAOASendThread = null;
    private AOARecvThread mAOARecvThread = null;
    private static final String AOA_SERVER_THREAD_NAME = "AOAServerThread";
    private static final String AOA_SEND_THREAD_NAME = "AOASendThread";
    private static final String AOA_RECV_THREAD_NAME = "AOARecvThread";

    private static final int AOA_BUF_SIZE = 16 * 1024;
    private static final int AOA_SOCKET_PORT = 6822;
    private static final int PID_ACCESSORY = 0x2D;
    private static final int VID_ACCESSORY = 0x18D1;

    private static int iWidth = 0;
    private static int iHeight = 0;

    private static final int HID_ORIENTATION_0 = 0;
    private static final int HID_ORIENTATION_90 = 1;
    private static final int HID_ORIENTATION_180 = 2;
    private static final int HID_ORIENTATION_270 = 3;

    private static final int HID_KEY_HOME = 0;
    private static final int HID_KEY_BACK = 1;

    private DeviceManager mDeviceManager = null;
    private static boolean isAOADeviceStart = false;

    public AOAManager() {
    }

    public static AOAManager getInstance() {
        if (null == mInstance) {
            synchronized (AOAManager.class) {
                if (null == mInstance) {
                    mInstance = new AOAManager();
                }
            }
        }
        return mInstance;
    }

    public void init(Context context) {
        LogUtil.d(TAG + TAG_AOA, "AOAManager::init start");
        mContext = context;
        mUsbManager = (UsbManager) mContext.getSystemService(Context.USB_SERVICE);
        LogUtil.d(TAG + TAG_AOA, "AOAManager::init DeviceManager create");
        mDeviceManager = new DeviceManager(mContext);
        try {
            LogUtil.d(TAG + TAG_AOA, "AOAManager::init AOAServerThread start, port=" + AOA_SOCKET_PORT);
            mAOAServerThread = new AOAServerThread(AOA_SOCKET_PORT);
            mAOAServerThread.start();
        } catch (Exception e) {
            LogUtil.e(TAG + TAG_AOA, "AOAManager::init create socket fail: " + e.getMessage());
        }
        LogUtil.d(TAG + TAG_AOA, "AOAManager::init complete");
    }

    public void deinit() {
        LogUtil.d(TAG + TAG_AOA, "AOAManager::deinit start");
        try {
            if (null != mSocket) {
                LogUtil.d(TAG + TAG_AOA, "AOAManager::deinit close socket");
                mSocket.close();
                mSocket = null;
            }
        } catch (Exception e) {
            LogUtil.e(TAG + TAG_AOA, "AOAManager::deinit socket close exception: " + e.getMessage());
        }
        if (mAOARecvThread != null) {
            LogUtil.d(TAG + TAG_AOA, "AOAManager::deinit cancel AOARecvThread");
            mAOARecvThread.cancel();
            mAOARecvThread = null;
        }
        if (mAOASendThread != null) {
            LogUtil.d(TAG + TAG_AOA, "AOAManager::deinit cancel AOASendThread");
            mAOASendThread.cancel();
            mAOASendThread = null;
        }
        if (mAOAServerThread != null) {
            LogUtil.d(TAG + TAG_AOA, "AOAManager::deinit cancel AOAServerThread");
            mAOAServerThread.cancel();
            mAOAServerThread = null;
        }
        if(mDeviceManager != null) {
            LogUtil.d(TAG + TAG_AOA, "AOAManager::deinit USB device deinit");
            mDeviceManager.deinitUsbDevice();
        }
        isAOADeviceStart = false;
        LogUtil.d(TAG + TAG_AOA, "AOAManager::deinit complete");
    }

    public void startAOA() {
        LogUtil.d(TAG + TAG_AOA, "AOAManager::startAOA start");
        if (!checkDevices()) {
            LogUtil.e(TAG + TAG_AOA, "AOAManager::startAOA check device fail");
        } else {
            isAOADeviceStart = true;
            LogUtil.d(TAG + TAG_AOA, "AOAManager::startAOA device start success");
        }
    }

    public void stopAOA() {
        LogUtil.d(TAG + TAG_AOA, "AOAManager::stopAOA start");
        if (mAOARecvThread != null) {
            LogUtil.d(TAG + TAG_AOA, "AOAManager::stopAOA cancel AOARecvThread");
            mAOARecvThread.cancel();
            mAOARecvThread = null;
        }
        if (mAOASendThread != null) {
            LogUtil.d(TAG + TAG_AOA, "AOAManager::stopAOA cancel AOASendThread");
            mAOASendThread.cancel();
            mAOASendThread = null;
        }
        isAOADeviceStart = false;
        iWidth = 0;
        iHeight = 0;
        LogUtil.d(TAG + TAG_AOA, "AOAManager::stopAOA complete");
    }

//    public int registerHID(int w, int h) {
//        iWidth = w;
//        iHeight = h;
//        int ret = mDeviceManager.hidSendRegisterEvent(iWidth, iHeight);
//        LogUtil.d(TAG, "register HID end, ret: " + ret);
//        return ret;
//    }

//    public int unregisterHID(int id) {
//        iWidth = 0;
//        iHeight = 0;
//        int ret = mDeviceManager.hidSendUnregisterEvent(id);
//        LogUtil.d(TAG, "unregister HID end, ret: " + ret);
//        return ret;
//    }

//    public int sendPointEvent(int id, int x, int y, int type, int orientation) {
//        if (CHECK_AOA_MANAGER)
//            LogUtil.e(TAG, "zhangyf sendPointEvent id:" + id + ", x:" + x + ", y:" + y + ", iWidth:" + iWidth + ", iHeight:" + iHeight + ", orientation:" + orientation);
//        int ret;
//        switch (orientation) {
//            case HID_ORIENTATION_0:
//                ret = mDeviceManager.hidSendPointEvent(id, x, y, type);
//                break;
//            case HID_ORIENTATION_90:
//                ret = mDeviceManager.hidSendPointEvent(id, (iHeight - y) * iWidth / iHeight, x * iHeight / iWidth, type);
//                break;
//            case HID_ORIENTATION_180:
//                ret = mDeviceManager.hidSendPointEvent(id, iWidth - x, iHeight - y, type);
//                break;
//            case HID_ORIENTATION_270:
//                ret = mDeviceManager.hidSendPointEvent(id, iWidth - (iHeight - y) * iWidth / iHeight, iHeight - x * iHeight / iWidth, type);
//                break;
//            default:
//                ret = -1;
//                LogUtil.e(TAG, "unknown orientation");
//                break;
//        }
//        return ret;
//    }

//    public int sendKeyEvent(int key) {
//        int ret;
//        switch (key) {
//            case HID_KEY_HOME:
//                ret = mDeviceManager.hidSendHomeKeyEvent();
//                break;
//            case HID_KEY_BACK:
//                ret = mDeviceManager.hidSendBackKeyEvent();
//                break;
//            default:
//                ret = -1;
//                LogUtil.e(TAG, "unknown key value");
//        }
//        return ret;
//    }

    public boolean isAOANotReady() {
        return !isAOADeviceStart;
    }

    private boolean checkDevices() {
        LogUtil.d(TAG + TAG_AOA, "AOAManager::checkDevices start");

        if (mContext == null || mUsbManager == null) {
            LogUtil.e(TAG + TAG_AOA, "AOAManager::checkDevices context or usbManager is null");
            return false;
        }

        HashMap<String, UsbDevice> deviceList = mUsbManager.getDeviceList();
        LogUtil.d(TAG + TAG_AOA, "AOAManager::checkDevices device count=" + deviceList.size());
        for (UsbDevice device : deviceList.values()) {
            if (device == null) {
                continue;
            }

            if (!isAOADevice(device)) {
                LogUtil.d(TAG + TAG_AOA, "AOAManager::checkDevices not AOA device, vid=" + device.getVendorId() + ", pid=" + device.getProductId());
                continue;
            }

            LogUtil.d(TAG + TAG_AOA, "AOAManager::checkDevices found AOA device, init start");
            if (mDeviceManager.init(mUsbManager, device)) {
                LogUtil.d(TAG + TAG_AOA, "AOAManager::checkDevices init device success");
                return true;
            }
        }
        LogUtil.d(TAG + TAG_AOA, "AOAManager::checkDevices no valid AOA device found");
        return false;
    }

    private boolean isAOADevice(UsbDevice device) {
        if (device == null) {
            LogUtil.e(TAG + TAG_AOA, "AOAManager::isAOADevice device is null");
            return false;
        }
        int vid = device.getVendorId();
        int pid = device.getProductId();
        boolean isAOA = (vid == VID_ACCESSORY && (pid >> 8 & 0x000000ff) == PID_ACCESSORY && (pid & 0x000f) <= 5);
        LogUtil.d(TAG + TAG_AOA, "AOAManager::isAOADevice vid=" + vid + ", pid=" + pid + ", isAOA=" + isAOA);
        return isAOA;
    }

    private class AOAServerThread extends Thread {
        private boolean isRunning = false;
        private ServerSocket mServerSocket = null;
        private Socket mThreadSocket = null;
        private BufferedInputStream mInputStream = null;
        private BufferedOutputStream mOutputStream = null;

        public AOAServerThread(int port) {
            LogUtil.d(TAG + TAG_AOA, "AOAServerThread::init port=" + port);
            setName(AOA_SERVER_THREAD_NAME);

            try {
                mServerSocket = new ServerSocket();
                mServerSocket.setReuseAddress(true);
                mServerSocket.bind(new InetSocketAddress(port));
                isRunning = true;
                LogUtil.d(TAG + TAG_AOA, "AOAServerThread::init socket bind success");
            } catch (Exception e) {
                LogUtil.e(TAG + TAG_AOA, "AOAServerThread::init create socket fail: " + e.getMessage());
            }
        }

        public void cancel() {
            if (null != mServerSocket) {
                try {
                    mServerSocket.close();
                } catch (IOException e) {
                    LogUtil.e(TAG, "close mServerSocket get IOException");
                }
                mServerSocket = null;
            }
            if (null != mThreadSocket) {
                try {
                    mThreadSocket.close();
                } catch (IOException e) {
                    LogUtil.e(TAG, "close mThreadSocket get IOException");
                }
                mThreadSocket = null;
            }
            if (null != mInputStream) {
                try {
                    mInputStream.close();
                } catch (IOException e) {
                    LogUtil.e(TAG, "close mInputStream get IOException");
                }
                mInputStream = null;
            }
            if (null != mOutputStream) {
                try {
                    mOutputStream.close();
                } catch (IOException e) {
                    LogUtil.e(TAG, "close mOutputStream get IOException");
                }
                mOutputStream = null;
            }

            isRunning = false;
        }

        public void closeSocket() {
            if (null != mInputStream) {
                try {
                    mInputStream.close();
                } catch (IOException e) {
                    LogUtil.e(TAG, "close mInputStream get IOException");
                }
                mInputStream = null;
            }
            if (null != mOutputStream) {
                try {
                    mOutputStream.close();
                } catch (IOException e) {
                    LogUtil.e(TAG, "close mOutputStream get IOException");
                }
                mOutputStream = null;
            }
            if (mSocket != null) {
                try {
                    mSocket.close();
                } catch (IOException e) {
                    LogUtil.e(TAG, "close mSocket get IOException");
                }
                mSocket = null;
            }
        }

        @Override
        public void run() {
            LogUtil.d(TAG + TAG_AOA, "AOAServerThread::run begin listen");
            try {
                // fixme: 有没有可能卡住这里。是否有复归设计？
                while (isRunning) {
                    mThreadSocket = mServerSocket.accept();
                    LogUtil.d(TAG + TAG_AOA, "AOAServerThread::run <--- client connected");
                    if (null == mThreadSocket) {
                        LogUtil.e(TAG + TAG_AOA, "AOAServerThread::run client connect fail");
                        continue;
                    }
                    if (!isAOADeviceStart) {
                        LogUtil.e(TAG + TAG_AOA, "AOAServerThread::run AOA not started, close connection");
                        mThreadSocket.close();
                        mThreadSocket = null;
                        continue;
                    }
                    if ((null == mInputStream) && (null == mOutputStream)) {
                        LogUtil.d(TAG + TAG_AOA, "AOAServerThread::run setup streams and start threads");
                        mSocket = mThreadSocket;
                        mSocket.setTcpNoDelay(true);
                        mInputStream = new BufferedInputStream(mSocket.getInputStream());
                        mOutputStream = new BufferedOutputStream(mSocket.getOutputStream());

                        LogUtil.d(TAG + TAG_AOA, "AOAServerThread::run start AOASendThread");
                        mAOASendThread = new AOASendThread();
                        mAOASendThread.start();
                        LogUtil.d(TAG + TAG_AOA, "AOAServerThread::run start AOARecvThread");
                        mAOARecvThread = new AOARecvThread();
                        mAOARecvThread.start();
                    } else {
                        LogUtil.e(TAG + TAG_AOA, "AOAServerThread::run threads already started, close connection");
                        mThreadSocket.close();
                        mThreadSocket = null;
                    }
                    sleep(100);
                }
                LogUtil.e(TAG + TAG_AOA, "AOAServerThread::run loop stopped");
            } catch (Exception e) {
                LogUtil.e(TAG + TAG_AOA, "AOAServerThread::run exception: " + e.getMessage());
            }

            LogUtil.e(TAG + TAG_AOA, "AOAServerThread::run thread exit");
        }
    }

    private class AOASendThread extends Thread {
        private int errorCount;
        private boolean isRunning;
        private final byte[] buffer = new byte[AOA_BUF_SIZE];

        public AOASendThread() {
            LogUtil.d(TAG + TAG_AOA, "AOASendThread::init created");
            setName(AOA_SEND_THREAD_NAME);
            isRunning = true;
        }

        public void cancel() {
            LogUtil.d(TAG + TAG_AOA, "AOASendThread::cancel stop running");
            isRunning = false;
        }

        public int readData(byte[] data, int offset, int len) {
            if (mAOAServerThread.mInputStream == null) {
                LogUtil.e(TAG + TAG_AOA, "AOASendThread::readData mInputStream is null");
                return -1;
            }

            int ret;
            try {
                ret = mAOAServerThread.mInputStream.read(data, offset, len);
            } catch (IOException e) {
                LogUtil.e(TAG + TAG_AOA, "AOASendThread::readData IOException: " + e.getMessage());
                return -2;
            }

            return ret;
        }

        public int writeData(byte[] data, int offset, int len) {
            if (mAOAServerThread.mOutputStream == null) {
                LogUtil.e(TAG + TAG_AOA, "AOASendThread::writeData mOutputStream is null");
                return -1;
            }

            try {
                mAOAServerThread.mOutputStream.write(data, offset, len);
                mAOAServerThread.mOutputStream.flush();
                if (CHECK_AOA_MANAGER)
                    LogUtil.v(TAG + TAG_AOA, "AOASendThread::writeData ---> USB to Socket, len=" + len);
            } catch (IOException e) {
                LogUtil.e(TAG + TAG_AOA, "AOASendThread::writeData IOException: " + e.getMessage());
                return -2;
            }

            return len;
        }

        @Override
        public void run() {
            LogUtil.d(TAG + TAG_AOA, "AOASendThread::run start");
            while (mSocket != null && isRunning) {
                if (!mSocket.isConnected()) {
                    LogUtil.e(TAG + TAG_AOA, "AOASendThread::run socket disconnected");
                    break;
                }

                Arrays.fill(buffer, (byte) 0x00);
                int len = readData(buffer, 0, AOA_BUF_SIZE);
                if (len < 0) {
                    LogUtil.e(TAG + TAG_AOA, "AOASendThread::run read socket fail, ret=" + len);
                    break;
                }
                if (CHECK_AOA_MANAGER)
                    LogUtil.v(TAG + TAG_AOA, "AOASendThread::run ---> Socket to USB, len=" + len);

                int ret = mDeviceManager.bulkTransferOut(buffer, len);
                if (ret < 0) {
                    LogUtil.e(TAG + TAG_AOA, "AOASendThread::run USB bulkTransferOut fail, ret=" + ret + ", errorCount=" + errorCount);
                    if (errorCount > MAX_ERROR_COUNT) {
                        errorCount = 0;
                        break;
                    }
                    try {
                        Thread.sleep(10L);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    errorCount++;
                }else {
                    errorCount = 0;
                }
            }

            if (mAOAServerThread != null) {
                mAOAServerThread.closeSocket();
            }
            LogUtil.d(TAG + TAG_AOA, "AOASendThread::run exit");
        }
    }

    private class AOARecvThread extends Thread {
        private int errorCount;
        private boolean isRunning;
        private final byte[] buffer = new byte[AOA_BUF_SIZE];

        public AOARecvThread() {
            LogUtil.d(TAG + TAG_AOA, "AOARecvThread::init created");
            setName(AOA_RECV_THREAD_NAME);
            isRunning = true;
        }

        public void cancel() {
            LogUtil.d(TAG + TAG_AOA, "AOARecvThread::cancel stop running");
            isRunning = false;
        }

        @Override
        public void run() {
            LogUtil.d(TAG + TAG_AOA, "AOARecvThread::run start");
            while (mSocket != null && isRunning) {
                if (!mSocket.isConnected()) {
                    LogUtil.e(TAG + TAG_AOA, "AOARecvThread::run socket disconnected");
                    break;
                }

                Arrays.fill(buffer, (byte) 0x00);
                int len = mDeviceManager.bulkTransferIn(buffer, AOA_BUF_SIZE);
                if (len < 0) {
                    LogUtil.e(TAG + TAG_AOA, "AOARecvThread::run USB bulkTransferIn fail, errorCount=" + errorCount);
                    if (errorCount > MAX_ERROR_COUNT) {
                        errorCount = 0;
                        break;
                    }
                    errorCount++;
                    try {
                        Thread.sleep(10L);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    continue;
                } else if (len == 0) {
                    errorCount = 0;
                    continue;
                }else {
                    errorCount = 0;
                }
                if (CHECK_AOA_MANAGER)
                    LogUtil.v(TAG + TAG_AOA, "AOARecvThread::run <--- USB to Socket, len=" + len);
                try {
                    int ret = mAOASendThread.writeData(buffer, 0, len);
                    if (ret < 0) {
                        LogUtil.e(TAG + TAG_AOA, "AOARecvThread::run write socket fail, ret=" + ret);
                        break;
                    }
                } catch (NullPointerException e) {
                    LogUtil.e(TAG + TAG_AOA, "AOARecvThread::run mAOASendThread is null");
                }
            }

            if (mAOAServerThread != null) {
                mAOAServerThread.closeSocket();
            }
            LogUtil.d(TAG + TAG_AOA, "AOARecvThread::run exit");
        }
    }
}
