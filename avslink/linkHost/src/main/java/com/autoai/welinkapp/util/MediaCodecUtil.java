package com.autoai.welinkapp.util;

import android.annotation.SuppressLint;
import android.graphics.ImageFormat;
import android.graphics.Rect;
import android.graphics.YuvImage;
import android.media.Image;
import android.media.ImageReader;
import android.media.MediaCodec;
import android.media.MediaCodecInfo;
import android.media.MediaFormat;
import android.os.Build;
import android.os.Handler;
import android.os.HandlerThread;
import android.util.Log;
import android.view.Surface;

import com.autoai.common.util.ExceptionUtil;
import com.autoai.common.util.LogUtil;
import com.autoai.welink.macro.WL_API_H264_FREAM_TYPE;

import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 用于硬件解码(MediaCodec)H264的工具类
 */
public class MediaCodecUtil {

    private static final boolean MEDIA_CODEC_DEBUG = LogUtil.isFullLog();

    private static final String TAG = "MediaCodecUtil";
    private Surface mSurface;
    private int width, height;
    //解码器
    private MediaCodec mDecoder = null;
    /**
     * 需要解码的类型
     * H.264 Advanced Video
     */
    private final static String MIME_TYPE = "video/avc";

    private static MediaCodecUtil mInstance = null;
    private final MediaCodec.BufferInfo mBufferInfo = new MediaCodec.BufferInfo();
    private DisplayThread mDisplayThread = null;
    private final Object mLock = new Object();
    /**
     * 该flag表现如下
     * 初始值为false，
     * 当【解码器】初始化成功，解码线程在解析第一率时，会调整该flag --> true
     * 当【解码器】被stop|release，会调整该flag --> false
     */
    private boolean mDisplayFrame = false;

    /**
     * 存储当次连接的头帧数据（sps + pps ）
     */
    private byte[] mSpsPpsData;

    /**
     * 记录留存最近一个I帧
     */
    private byte[] lastIFrameData;

    /**
     * 加码器是否开始flag标记
     */
    private boolean hasDecoderStarted = false;

    /**
     * 是否需要补帧的变量
     */
//    private boolean needUseCache = false;

    /**
     * 需要等待一个头数据
     */
    private boolean mNeedSpsPpsFlag = false;

    private ImageReader imageReaderTemp;

    private FirstFrameReadyListener firstFrameReadyListener;
    /**
     * 缓存帧的监听器
     */
    private AppendingCacheFrameListener appendingCacheFrameListener;

    private HandlerThread mHandlerThread;
    private Handler mDecodeHandler;

    /**
     * 纪录获取输入buffer失败的次数
     */
    private int dequeueInputBufferErrCount = 0;

    private void saveSpsPpsFrame(byte[] data, int size) {
        try {
            mSpsPpsData = new byte[size];
            System.arraycopy(data, 0, mSpsPpsData, 0, size);
            LogUtil.i(TAG, "MediaCodecUtil::saveSpsPpsFrame save sps pps frame: " + size);
        } catch (NullPointerException e) {
            LogUtil.e(TAG, "MediaCodecUtil::saveSpsPpsFrame save sps pps frame error!(null pointer)");
            LogUtil.e(TAG, ExceptionUtil.getExceptionToString(e));
        }
    }

    /**
     * 在解码器尚未开始的情况下，缓存一个最新的I帧
     *
     * @param data
     * @param size
     */
    private void saveIFrameWhenUnStarted(byte[] data, int offset, int size) {
        try {

            if (offset == 0) {
                //-->99%都是 0 开始，直接引用指向，性能销毁很低，避免内存拷贝
                lastIFrameData = data;
            } else {
                lastIFrameData = new byte[size];
                System.arraycopy(data, offset, lastIFrameData, 0, size);
            }
            LogUtil.i(TAG, "MediaCodecUtil::saveIFrameWhenUnStarted save saveIFrame frame: offset =  " + offset + ";size =" + size);
        } catch (NullPointerException e) {
            LogUtil.e(TAG, " MediaCodecUtil::saveIFrameWhenUnStarted save saveIFrame error!(null pointer)");
            LogUtil.e(TAG, ExceptionUtil.getExceptionToString(e));
        }
    }

    public boolean isDisplayFrame() {
        return mDisplayFrame;
    }

    public void setFirstFrameReadyListener(FirstFrameReadyListener firstFrameReadyListener) {
        LogUtil.e(TAG, " MediaCodecUtil::setFirstFrameReadyListener");
        this.firstFrameReadyListener = firstFrameReadyListener;
    }
    public void setAppendingCacheFrameListener(AppendingCacheFrameListener appendingCacheFrameListener) {
        LogUtil.e(TAG, " MediaCodecUtil::setAppendingCacheFrameListener");
        this.appendingCacheFrameListener = appendingCacheFrameListener;
    }
    public MediaCodecUtil() {
        initDecodeThread();
    }

    public static MediaCodecUtil getInstance() {
        if (mInstance == null) {
            mInstance = new MediaCodecUtil();
        }
        return mInstance;
    }

    public interface FirstFrameReadyListener {
        void onReady();

    }
    public interface AppendingCacheFrameListener {

        void onAppendingCacheFrame();
    }
    @SuppressLint("SdCardPath")
    public void capture() {
        try {

            if (imageReaderTemp == null) {
                int imageFormat = ImageFormat.YUV_420_888;
                imageReaderTemp = ImageReader.newInstance(
                        480,
                        480,
                        imageFormat,
                        3);
                imageReaderTemp.setOnImageAvailableListener(imageReader -> {
                    new Thread(() -> {
                        Image image = imageReader.acquireNextImage();
                        mDisplayThread.compressToJpeg("/sdcard/output_" + System.currentTimeMillis() + ".jpg", image);
                        image.close();
                        imageReader.close();
                    }).start();
                    mDecoder.setOutputSurface(mSurface);
                }, null);
            }
            mDecoder.setOutputSurface(imageReaderTemp.getSurface());
        } catch (Exception ex) {
            ex.printStackTrace();
        }

    }


    // 独立使用一个线程，循环解码降低延迟
    private class DisplayThread extends Thread {

        private final String TAG = "VideoToFrames";
        private final boolean VERBOSE = LogUtil.getDebugStates();

        private final int COLOR_FormatI420 = 1;
        private final int COLOR_FormatNV21 = 2;

        private boolean isRunning = true;

        public void stopThread() {
            isRunning = false;
        }


        @Override
        public void run() {
            try {
                while (isRunning) {
                    try {
                        // 获取输出buffer index
                        // 1000/60 = 16
                        int outputBufferIndex = mDecoder.dequeueOutputBuffer(mBufferInfo, 16000);
                        // LogUtil.d(TAG, "run: dequeue output1 end " + outputBufferIndex);

                        if (outputBufferIndex == MediaCodec.INFO_OUTPUT_FORMAT_CHANGED) {
                            //此处可以或得到视频的实际分辨率，用以修正宽高比
                            // fix();
                            LogUtil.v(TAG, "MediaCodecUtil::DisplayThread INFO_OUTPUT_FORMAT_CHANGED");
                        }

//                        LogUtil.i(TAG, "dequeueOutputBuffer  outputBufferIndex = " + outputBufferIndex);

                        //循环解码，直到数据全部解码完成
                        while (outputBufferIndex >= 0) {
                            try {

                                mDecoder.releaseOutputBuffer(outputBufferIndex, true);
                                //-----------------
                                if (!mDisplayFrame) {
                                    if (firstFrameReadyListener != null) {
                                        firstFrameReadyListener.onReady();
                                    }
                                    mDisplayFrame = true;
                                }
                            } catch (IllegalStateException e) {
                                LogUtil.e(TAG, ExceptionUtil.getExceptionToString(e));
                                break;
                            }
                            outputBufferIndex = mDecoder.dequeueOutputBuffer(mBufferInfo, 500);

                            // LogUtil.d(TAG, "run: dequeue output2 end " + outputBufferIndex);


                        }
                    } catch (IllegalStateException e) {
                        Thread.sleep(50);
                    } catch (NullPointerException e) {
                        LogUtil.w(TAG, "MediaCodecUtil::DisplayThread run1: decoder has been released!");
                        LogUtil.w(TAG, ExceptionUtil.getExceptionToString(e));
                        this.stopThread();
                    }
                }
            } catch (InterruptedException e) {
                LogUtil.e(TAG, ExceptionUtil.getExceptionToString(e));
            } catch (NullPointerException e) {
                LogUtil.w(TAG, "MediaCodecUtil::DisplayThread run2: decoder has been released!");
                LogUtil.w(TAG, ExceptionUtil.getExceptionToString(e));
                e.printStackTrace();
                this.stopThread();
            }
        }

        private boolean isImageFormatSupported(Image image) {
            int format = image.getFormat();
            switch (format) {
                case ImageFormat.YUV_420_888:
                case ImageFormat.NV21:
                case ImageFormat.YV12:
                    return true;
            }
            return false;
        }

        private byte[] getDataFromImage(Image image, int colorFormat) {
            if (colorFormat != COLOR_FormatI420 && colorFormat != COLOR_FormatNV21) {
                throw new IllegalArgumentException("only support COLOR_FormatI420 " + "and COLOR_FormatNV21");
            }
            if (!isImageFormatSupported(image)) {
                throw new RuntimeException("can't convert Image to byte array, format " + image.getFormat());
            }
            Rect crop = image.getCropRect();
            int format = image.getFormat();
            int width = crop.width();
            int height = crop.height();
            Image.Plane[] planes = image.getPlanes();
            byte[] data = new byte[width * height * ImageFormat.getBitsPerPixel(format) / 8];
            byte[] rowData = new byte[planes[0].getRowStride()];
            if (VERBOSE) {
                LogUtil.v(TAG, "MediaCodecUtil::getDataFromImage get data from " + planes.length + " planes");
            }
            int channelOffset = 0;
            int outputStride = 1;
            for (int i = 0; i < planes.length; i++) {
                switch (i) {
                    case 0:
                        channelOffset = 0;
                        outputStride = 1;
                        break;
                    case 1:
                        if (colorFormat == COLOR_FormatI420) {
                            channelOffset = width * height;
                            outputStride = 1;
                        } else if (colorFormat == COLOR_FormatNV21) {
                            channelOffset = width * height + 1;
                            outputStride = 2;
                        }
                        break;
                    case 2:
                        if (colorFormat == COLOR_FormatI420) {
                            channelOffset = (int) (width * height * 1.25);
                            outputStride = 1;
                        } else if (colorFormat == COLOR_FormatNV21) {
                            channelOffset = width * height;
                            outputStride = 2;
                        }
                        break;
                }
                ByteBuffer buffer = planes[i].getBuffer();
                int rowStride = planes[i].getRowStride();
                int pixelStride = planes[i].getPixelStride();
                if (VERBOSE) {
                    LogUtil.v(TAG, "MediaCodecUtil::getDataFromImage pixelStride " + pixelStride);
                    LogUtil.v(TAG, "MediaCodecUtil::getDataFromImage rowStride " + rowStride);
                    LogUtil.v(TAG, "MediaCodecUtil::getDataFromImage width " + width);
                    LogUtil.v(TAG, "MediaCodecUtil::getDataFromImage height " + height);
                    LogUtil.v(TAG, "MediaCodecUtil::getDataFromImage buffer size " + buffer.remaining());
                }
                int shift = (i == 0) ? 0 : 1;
                int w = width >> shift;
                int h = height >> shift;
                buffer.position(rowStride * (crop.top >> shift) + pixelStride * (crop.left >> shift));
                for (int row = 0; row < h; row++) {
                    int length;
                    if (pixelStride == 1 && outputStride == 1) {
                        length = w;
                        buffer.get(data, channelOffset, length);
                        channelOffset += length;
                    } else {
                        length = (w - 1) * pixelStride + 1;
                        buffer.get(rowData, 0, length);
                        for (int col = 0; col < w; col++) {
                            data[channelOffset] = rowData[col * pixelStride];
                            channelOffset += outputStride;
                        }
                    }
                    if (row < h - 1) {
                        buffer.position(buffer.position() + rowStride - length);
                    }
                }
                if (VERBOSE) {
                    LogUtil.v(TAG, "MediaCodecUtil::getDataFromImage Finished reading data from plane " + i);
                }
            }
            return data;
        }

        private boolean compressToJpeg(String fileName, Image image) {

            try {
                FileOutputStream outStream;
                try {
                    outStream = new FileOutputStream(fileName);
                } catch (IOException ioe) {
                    throw new RuntimeException("Unable to create output file " + fileName, ioe);
                }

                Rect rect = image.getCropRect();
                YuvImage yuvImage = new YuvImage(getDataFromImage(image, COLOR_FormatNV21), ImageFormat.NV21, rect.width(), rect.height(), null);
                yuvImage.compressToJpeg(rect, 100, outStream);
                return true;
            } catch (Exception ex) {
                ex.printStackTrace();
                return false;
            }
        }
    }


    public void startDecoder(Surface surface, int width, int height) {
        LogUtil.d(TAG, "MediaCodecUtil::startDecoder start");

//        LogUtil.printStackTraceString(TAG, "MediaCodecUtil::startDecoder start");

        if (mSurface != null && mSurface.isValid() && mSurface != surface) {
//            mSurface.release();
        }
        this.mSurface = surface;
        this.width = width;
        this.height = height;

        if (mDecoder != null) {
            try {
                mDecoder.setOutputSurface(surface);
            } catch (Exception e) {
                LogUtil.d(TAG, "MediaCodecUtil::startDecoder error:" + e.getMessage());
                e.printStackTrace();
                resetDecoder();
            }
        } else {
            // 重置完成, 内部调用 initDecoder()
            resetDecoder();
        }

        LogUtil.d(TAG, "MediaCodecUtil::startDecoder finish");
    }

    private void initDecoder() {
        synchronized (mLock) {
            initDecodeThread();
            if (mDecoder != null || mSurface == null) {
                LogUtil.e(TAG, "MediaCodecUtil::initDecoder mDecoder != null || mSurface == " + mSurface);
                return;
            }
            //stopDecoder();
            LogUtil.d(TAG, "MediaCodecUtil::initDecoder start! width:" + width + ", height:" + height);

            if (mSurface != null && !mSurface.isValid()) {
                LogUtil.w(TAG, "MediaCodecUtil::initDecoder surface has been released!");
                return;
            }

            //初始化MediaFormat
            MediaFormat mediaFormat = MediaFormat.createVideoFormat(MIME_TYPE, width, height);
            // 设置帧率，当前暂时未使用
//             mediaFormat.setInteger(MediaFormat.KEY_FRAME_RATE, 30);
//            mediaFormat.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, 3);
            // mediaFormat.setString("dis reorder", "1");
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                //启用低延迟解码时为 1），否则为 0。 默认值为 0。
                mediaFormat.setInteger(MediaFormat.KEY_LOW_LATENCY, 1);
            }
            mediaFormat.setInteger(MediaFormat.KEY_COLOR_FORMAT, MediaCodecInfo.CodecCapabilities.COLOR_FormatYUV422Flexible);
            try {
                //根据需要解码的类型创建解码器
                mDecoder = createDecoder();
                //配置MediaFormat以及需要显示的surface
                mDecoder.configure(mediaFormat, mSurface, null, 0);
//                mDecoder.configure(mediaFormat, null, null, 0);

            } catch (Exception e) {
                if (mDecoder != null) {
                    mDecoder.release();
                    mDecoder = null;
                }
                LogUtil.e(TAG, ExceptionUtil.getExceptionToString(e));
                return;
            }
            //开始解码
            mDecoder.start();
            LogUtil.i(TAG, "MediaCodecUtil::initDecoder Decoder 开启成功");

            mNeedSpsPpsFlag = true;
            hasDecoderStarted = true;
            if (mDisplayThread != null) {
                mDisplayThread.stopThread();
            }
            mDisplayThread = new DisplayThread();
            mDisplayThread.start();

            appendCacheFrameIfNeed();

            LogUtil.d(TAG, "MediaCodecUtil::initDecoder finish!");
        }
    }

    private MediaCodec createDecoder() throws IOException {
        MediaCodec mediaCodec;
     /*   try {
            mediaCodec = MediaCodec.createByCodecName("OMX.google.h264.decoder");
        } catch (Exception e) {
            mediaCodec = MediaCodec.createDecoderByType(MIME_TYPE);
        }*/
        //斯巴鲁车机用软解码器会花屏
        mediaCodec = MediaCodec.createDecoderByType(MIME_TYPE);
        return mediaCodec;
    }

    int ret = 0;

    public int decodeFrame(byte[] buf, int offset, int length, int spsppsDataSize, int frameType) {
        if (MEDIA_CODEC_DEBUG)
            LogUtil.v(TAG, "MediaCodecUtil::decodeFrame decodeFrame start");


        if (isAppendingCacheFrameFlag) {//-->正在补帧
            if (frameType == WL_API_H264_FREAM_TYPE.SPS_IDR || frameType == WL_API_H264_FREAM_TYPE.IDR) {
                //打断补帧
                isAppendingCacheFrameFlag = false;
                interruptAppendCacheFrame();
                //--> 缓存帧
                cacheFrame(buf, offset, length, spsppsDataSize, frameType);
                //--> 将帧传下去作缓存
                try {
                    if (mDecodeHandler != null) {
                        mDecodeHandler.post(() -> {
                            try {
                                ret = input2decoder(buf, offset, length, spsppsDataSize, frameType);
                            } catch (Exception e) {
                                LogUtil.e(TAG, "MediaCodecUtil::decodeFrame Error during decoding: " + e.getMessage());
                            }
                        });
                    }
                } catch (IllegalStateException e) {
                    LogUtil.w(TAG, ExceptionUtil.getExceptionToString(e));
                    resetDecoder();
                }
            }

        } else {//-->未补帧
            //--> 缓存帧
            cacheFrame(buf, offset, length, spsppsDataSize, frameType);
            try {
                if (mDecodeHandler != null){
                    mDecodeHandler.post(() -> {
                        try {
                            ret = input2decoder(buf, offset, length, spsppsDataSize, frameType);
                        } catch (Exception e) {
                            LogUtil.e(TAG, "MediaCodecUtil::decodeFrame Error during decoding: " + e.getMessage());
                        }
                    });
                }
            } catch (IllegalStateException e) {
                LogUtil.w(TAG, ExceptionUtil.getExceptionToString(e));
                resetDecoder();
            }

        }
        if (MEDIA_CODEC_DEBUG)
            LogUtil.v(TAG, "MediaCodecUtil::decodeFrame decodeFrame end");
        return ret;
    }


    /**
     * 解码核心方法：fixme:该方法逻辑后续调优
     * 对该方法进行了重构：更加清晰的将两个步骤进行拆分解耦
     * 1）处理帧逻辑
     * 2）将处理好的buffer input 到解码器解码
     *
     * @param buf
     * @param offset
     * @param length
     * @param spsppsDataSize
     * @param frameType
     * @return
     */
    private int input2decoder(byte[] buf, int offset, int length, int spsppsDataSize, int frameType) {
        if (MEDIA_CODEC_DEBUG) {
            LogUtil.v(TAG, "MediaCodecUtil::input2decoder start");
        }
        //  -->0  成功
        //  -->-1 抛帧，没有实际给到解码器
        //  -->-2 获取到解码器的inputBufferIndex = -1，没有获取到空闲的buffer
        //  -->-3 根据解码器的inputBufferIndex ，没有获取到空闲的buffer 为null
        //  -->-4 上层渲染surface还未注册
        int resultCode = 0;


        synchronized (mLock) {
            if (null == mDecoder && !hasDecoderStarted) {
                initDecoder();
                resultCode = (-4);
                return resultCode;
            }
            //-1表示一直等待；0表示不等待；其他大于0的参数表示等待毫秒数
            long mTimeout = 10 * 1000;
            //-->she tips 可以通过bufToDecode是否null来决定是否执行解码，但是为了逻辑清晰，添加flag
            boolean needDecodeMarkFlag = true;
            //实际要input到解码器解码的buf
            byte[] bufToDecode = null;


            //step1:处理帧
            LogUtil.i(TAG, "MediaCodecUtil::input2decoder:: mNeedSpsPpsFlag = " + mNeedSpsPpsFlag
                    + ";frameType = " + frameType
                    + ";needNextIFrameAfterAppended = " + needNextIFrameAfterAppended
                    + ";length = " + length
                    + ";buf.length = " + buf.length
                    + " ;mSpsPpsData == null? :" + (mSpsPpsData == null ? "null" : mSpsPpsData.length));

            //添加需要解码的数据
            if (mNeedSpsPpsFlag && frameType == WL_API_H264_FREAM_TYPE.SPS_IDR) {
                mNeedSpsPpsFlag = false;
            }

            //fixme：she tips 现有逻辑复杂 ,这里先做说明，后续优化处理
            // 1.正常时序： SPS_IDR P P ... IDR P P ...
            //   头帧：mNeedSpsPpsFlag = false; frameType == SPS_IDR; mSpsPpsData == null
            // 2.特殊时序： P P .. SPS_IDR P P ... IDR P P ...
            //   头帧：mNeedSpsPpsFlag = true; frameType == IDR; mSpsPpsData ！= null
            if (mNeedSpsPpsFlag) {//--> 需要给解码器一个sps+pps配置帧
                if (mSpsPpsData != null && frameType == WL_API_H264_FREAM_TYPE.IDR) {//-->mSpsPpsData已经有了，并且当前是一个关键帧
                    try {
                        //-->等一个idr帧，与头枕进行拼接
                        bufToDecode = new byte[mSpsPpsData.length + length];
                        System.arraycopy(buf, 0, bufToDecode, mSpsPpsData.length, length);
                        System.arraycopy(mSpsPpsData, 0, bufToDecode, 0, mSpsPpsData.length);
                        offset = 0;
                        length = mSpsPpsData.length + length;
                        mNeedSpsPpsFlag = false;
                        if (MEDIA_CODEC_DEBUG) {
                            LogUtil.v(TAG, "MediaCodecUtil::input2decoder set sps pps data to decoder");
                        }
                    } catch (NullPointerException e) {
                        if (MEDIA_CODEC_DEBUG) {
                            LogUtil.w(TAG, "MediaCodecUtil::input2decoder set sps pps data to decoder error!(null pointer)");
                        }
                        resultCode = -5;
                        return resultCode;
                    }
                } else {
                    //--> 如果没有头帧，后续的p帧调过
                    if (MEDIA_CODEC_DEBUG) {
                        LogUtil.w(TAG, "MediaCodecUtil::input2decoder drop p frame!");
                    }
                }
            } else {//-->此时已经有了配置帧
                //--> 配合补帧处理逻辑 {@link #appendCacheFrameIfNeed()}
                if (needNextIFrameAfterAppended) {
                    if (frameType == WL_API_H264_FREAM_TYPE.IDR) {
                        needNextIFrameAfterAppended = false;
                        bufToDecode = buf;
                    } else {
                        needDecodeMarkFlag = false;
                    }
                } else {
                    bufToDecode = buf;
                }
            }

            //step2:解码
            if (needDecodeMarkFlag && bufToDecode != null && bufToDecode.length > 0) {
                //--> 获取解码器可用的inputBufferIndex
                int inputBufferIndex = mDecoder.dequeueInputBuffer(mTimeout);
                LogUtil.i(TAG, " MediaCodecUtil::input2decoder inputBufferIndex = " + inputBufferIndex);
                LogUtil.i(TAG, " MediaCodecUtil::input2decoder bufToDecode.length = " + bufToDecode.length);
                if (inputBufferIndex >= 0) {
                    dequeueInputBufferErrCount = 0;
                    //--> 根据inputBufferIndex获取inputBuffer
                    ByteBuffer inputBuffer = mDecoder.getInputBuffer(inputBufferIndex);
                    if (inputBuffer != null) {
                        //清空buffer（容错处理）
                        inputBuffer.clear();
                        inputBuffer.put(bufToDecode, offset, length);
                        mDecoder.queueInputBuffer(inputBufferIndex, 0, length, 1, 0);
                    } else {
                        resultCode = -3;
                    }
                } else {
                    if (MEDIA_CODEC_DEBUG) {
                        LogUtil.w(TAG, "MediaCodecUtil::input2decoder: cannot get valid input buffer!");
                    }
                    dequeueInputBufferErrCount++;
                    if (dequeueInputBufferErrCount > 20) {
                        dequeueInputBufferErrCount = 0;
                        LogUtil.w(TAG, "MediaCodecUtil::input2decoder: cannot get valid input buffer!  mDecoder.flush()");
                        //多次获取缓冲区失败，方法清空解码器的状态 释放解码器
                        mDecoder.flush();
                    }
                    resultCode = -2;
                }

            } else {
                resultCode = -1;
            }
        }

        LogUtil.i(TAG, "MediaCodecUtil::input2decoder resultCode = " + resultCode);
        if (MEDIA_CODEC_DEBUG) {
            LogUtil.v(TAG, "MediaCodecUtil::input2decoder end");
        }
        return resultCode;
    }
    private boolean isThreadReady() {
        return mHandlerThread != null && mDecodeHandler != null && mHandlerThread.isAlive();
    }
    /**
     * 停止解码，释放解码器
     */
    public void stopDecoder() {
        LogUtil.i(TAG, "MediaCodecUtil::stopDecoder");
        LogUtil.d(TAG, "MediaCodecUtil::stopDecoder --> start");
        mSpsPpsData = null;
        lastIFrameData = null;
        synchronized (mLock) {

            if (mHandlerThread != null) {
                mHandlerThread.quitSafely();
                try {
                    mHandlerThread.join();
                } catch (InterruptedException e) {
                    LogUtil.e(TAG, ExceptionUtil.getExceptionToString(e));
                }
                mHandlerThread = null;
                mDecodeHandler = null;
            }

            LogUtil.d(TAG, "MediaCodecUtil::stopDecoder --> release");
            mNeedSpsPpsFlag = false;
            hasDecoderStarted = false;

            //补帧逻flag归为
            isAppendingCacheFrameFlag = false;
            interruptAppandFrameFlag = false;

            if (mDisplayThread != null) {
                mDisplayThread.stopThread();
                mDisplayThread = null;
            }

            if (mDecoder != null) {
                try {
                    mDecoder.stop();
                    mDecoder.release();
                } catch (Exception e) {
                    LogUtil.e(TAG, ExceptionUtil.getExceptionToString(e));
                }
                LogUtil.d(TAG, "MediaCodecUtil::stopDecoder -->  end");
            }
            mDecoder = null;
            mDisplayFrame = false;
        }
        LogUtil.d(TAG, "MediaCodecUtil::stopDecoder -->  finish");
    }

    public void resetDecoder() {
        LogUtil.d(TAG, "MediaCodecUtil::resetDecoder -->  start");
        synchronized (mLock) {
            LogUtil.d(TAG, "MediaCodecUtil::resetDecoder --> release");
            mNeedSpsPpsFlag = false;
            hasDecoderStarted = false;
            needNextIFrameAfterAppended = false;

            if (mDisplayThread != null) {
                mDisplayThread.stopThread();
                mDisplayThread = null;
            }

            if (mDecoder != null) {
                try {
                    mDecoder.stop();
                    mDecoder.release();
                } catch (Exception e) {
                    LogUtil.e(TAG, ExceptionUtil.getExceptionToString(e));
                }
                initDecodeThread();
                LogUtil.d(TAG, "MediaCodecUtil::resetDecoder --> end");
            }
            mDecoder = null;
            mDisplayFrame = false;
        }
        LogUtil.d(TAG, "MediaCodecUtil::resetDecoder --> finish");

        initDecoder();
    }

    /**
     * 在补帧过后{@link #appendCacheFrameIfNeed()}，因为所补的帧为 sps + pps + lastIFrame ，所以在补帧后，需要等到下个I帧，在此之前丢弃掉期间的p帧
     * 该标记位只会在补帧后 true
     */
    private boolean needNextIFrameAfterAppended = false;


    private CopyOnWriteArrayList<byte[]> cachePBuffer = new CopyOnWriteArrayList<>();

    //↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓互联成功后丢帧的处理↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓

    /**
     * 在解码器还
     * 1.未启动的时候，缓存配置帧 WL_API_H264_FREAM_TYPE.SPS_IDR + I + P帧list
     * 2.pause的时候
     *
     * @param buf
     * @param offset
     * @param length
     * @param spsppsDataSize
     * @param frameType
     */
    public void cacheFrame(byte[] buf, int offset, int length, int spsppsDataSize, int frameType) {
        LogUtil.i(TAG, "MediaCodecUtil::cacheFrame frameType = " + frameType );
//        needUseCache = true;
        if (spsppsDataSize > 0 && frameType == WL_API_H264_FREAM_TYPE.SPS_IDR) {
            saveSpsPpsFrame(buf, spsppsDataSize);
            lastIFrameData = new byte[buf.length - spsppsDataSize];
            System.arraycopy(buf, spsppsDataSize, lastIFrameData, 0, buf.length - spsppsDataSize);
            cachePBuffer.clear();
        } else if (frameType == WL_API_H264_FREAM_TYPE.IDR) {
            //-->如果解码器还未开启，并且是IDR关键帧，做一次缓存
            saveIFrameWhenUnStarted(buf, offset, length);
            cachePBuffer.clear();
        } else if (frameType == WL_API_H264_FREAM_TYPE.P) {
            cachePBuffer.add(buf);
            LogUtil.i(TAG, "MediaCodecUtil::cacheFrame cachePBuffer.size() = " + cachePBuffer.size());

//            if (cachePBuffer.size() < 1) {
//                cachePBuffer.add(buf);
//            }
        }
    }

    /**
     * 正在补帧ing
     */
    private boolean isAppendingCacheFrameFlag = false;


    /**
     * 暂停补帧
     */
    private boolean interruptAppandFrameFlag = false;

    private void interruptAppendCacheFrame() {
        interruptAppandFrameFlag = true;
    }

    /**
     * 补帧
     */
    public void appendCacheFrameIfNeed() {
        if (!isThreadReady()) {
            LogUtil.w(TAG, "MediaCodecUtil::appendCacheFrameIfNeed Decode thread not ready, skip appending cache frame");
            return;
        }

        LogUtil.i(TAG, "MediaCodecUtil::appendCacheFrameIfNeed ::  => ");
        mDecodeHandler.post(() -> {
            try {
                // 延迟执行补帧，确保解码器完全启动
                Thread.sleep(50); // 延迟 80ms
            } catch (InterruptedException e) {
                LogUtil.e(TAG, "MediaCodecUtil::appendCacheFrameIfNeed Interrupted during delay before appending frames");
            }
            synchronized (mLock) {
                if (mSurface == null || !mSurface.isValid()) {
                    LogUtil.w(TAG, "MediaCodecUtil::appendCacheFrameIfNeed Surface not valid, skip appendCacheFrameIfNeed");
                    return;
                }

                if (!hasDecoderStarted) {
                    LogUtil.w(TAG, "MediaCodecUtil::appendCacheFrameIfNeed Decoder not started yet, delay appendFrame until it starts.");
                    return;
                }

                if (isAppendingCacheFrameFlag) {
                    LogUtil.i(TAG, "MediaCodecUtil::appendCacheFrameIfNeed Already appending, skip");
                    return;
                }

                isAppendingCacheFrameFlag = true;
                if (appendingCacheFrameListener != null) {
                    appendingCacheFrameListener.onAppendingCacheFrame();
                }

                if (mSpsPpsData != null && mSpsPpsData.length > 0) {
                    LogUtil.i(TAG, "MediaCodecUtil::appendCacheFrameIfNeed :: 补sps_pps => ");
                    input2decoder(mSpsPpsData, 0, mSpsPpsData.length, mSpsPpsData.length, WL_API_H264_FREAM_TYPE.SPS_IDR);
                }

                if (lastIFrameData != null && lastIFrameData.length > 0) {
                    LogUtil.i(TAG, "MediaCodecUtil::appendCacheFrameIfNeed :: 补关键帧I帧 => ");

//                    int currentVersion = mLastIFrameVersion;

                    // 先补 I 帧
                    input2decoder(lastIFrameData, 0, lastIFrameData.length, lastIFrameData.length, WL_API_H264_FREAM_TYPE.IDR);
//                    LogUtil.i(TAG, "MediaCodecUtil::appendCacheFrameIfNeed :: mLastIFrameVersion => " + mLastIFrameVersion);
//                    LogUtil.i(TAG, "MediaCodecUtil::appendCacheFrameIfNeed :: currentVersion => " + currentVersion);

                    // 按需逐帧补 P 帧，并加入间隔
                    for (byte[] pFrame : cachePBuffer) {

                        if (interruptAppandFrameFlag ) {
                            LogUtil.i(TAG, "MediaCodecUtil::appendCacheFrameIfNeed :: 中断补帧 => ");
                            isAppendingCacheFrameFlag = false;
                            needNextIFrameAfterAppended = true;
                            break;
                        }

                        LogUtil.i(TAG, "MediaCodecUtil::appendCacheFrameIfNeed :: 补P帧 => ");
                        input2decoder(pFrame, 0, pFrame.length, pFrame.length, WL_API_H264_FREAM_TYPE.P);

                        try {
                            Thread.sleep(10); // 控制补帧频率，避免太快
                        } catch (InterruptedException e) {
                            LogUtil.e(TAG, "MediaCodecUtil::appendCacheFrameIfNeed 补帧休眠中断: " + e.getMessage());
                        }
                    }

                    cachePBuffer.clear(); // 清空缓存帧，防止残留干扰
                    needNextIFrameAfterAppended = true;
                }

                isAppendingCacheFrameFlag = false;
            }
        });
    }


    /**
     * 暂停后的补帧
     */
    public void appendCacheAfterResume() {
        if (!isThreadReady()) {
            LogUtil.w(TAG, "MediaCodecUtil::appendCacheAfterResume Decode thread not ready, skip appending cache frame");
            return;
        }

        LogUtil.i(TAG, "MediaCodecUtil::appendCacheAfterResume ::  => ");
        mDecodeHandler.post(() -> {
            try {
                // 延迟执行补帧，确保解码器完全启动
                Thread.sleep(50); // 延迟 80ms
            } catch (InterruptedException e) {
                LogUtil.e(TAG, "MediaCodecUtil::appendCacheAfterResume Interrupted during delay before appending frames");
            }
            synchronized (mLock) {
                if (mSurface == null || !mSurface.isValid()) {
                    LogUtil.w(TAG, "MediaCodecUtil::appendCacheAfterResume Surface not valid, skip appendCacheAfterResume");
                    return;
                }

                if (!hasDecoderStarted) {
                    LogUtil.w(TAG, "MediaCodecUtil::appendCacheAfterResume Decoder not started yet, delay appendFrame until it starts.");
                    return;
                }

                if (isAppendingCacheFrameFlag) {
                    LogUtil.i(TAG, "MediaCodecUtil::appendCacheAfterResume Already appending, skip");
                    return;
                }

                isAppendingCacheFrameFlag = true;
                if (appendingCacheFrameListener != null) {
                    appendingCacheFrameListener.onAppendingCacheFrame();
                }

                if (mSpsPpsData != null && mSpsPpsData.length > 0) {
                    LogUtil.i(TAG, "MediaCodecUtil::appendCacheAfterResume :: 补sps_pps => ");
                    input2decoder(mSpsPpsData, 0, mSpsPpsData.length, mSpsPpsData.length, WL_API_H264_FREAM_TYPE.SPS_IDR);
                }

                if (lastIFrameData != null && lastIFrameData.length > 0) {
                    LogUtil.i(TAG, "MediaCodecUtil::appendCacheAfterResume :: 补关键帧I帧 => ");

//                    int currentVersion = mLastIFrameVersion;

                    // 先补 I 帧
                    input2decoder(lastIFrameData, 0, lastIFrameData.length, lastIFrameData.length, WL_API_H264_FREAM_TYPE.IDR);
//                    LogUtil.i(TAG, "MediaCodecUtil::appendCacheFrameIfNeed :: mLastIFrameVersion => " + mLastIFrameVersion);
//                    LogUtil.i(TAG, "MediaCodecUtil::appendCacheFrameIfNeed :: currentVersion => " + currentVersion);
                    //todo 如果恢复画面时，缓存补帧最后一帧为I帧没有P帧的情况下，导致画面停滞。在缓存P帧为空的情况下，再补一次当前I帧可更新画面
                    if (cachePBuffer.isEmpty()) {
                        queueDecodeFrame(lastIFrameData, 0, lastIFrameData.length, WL_API_H264_FREAM_TYPE.IDR);
                    }
                    // 按需逐帧补 P 帧，并加入间隔
                    for (byte[] pFrame : cachePBuffer) {

                        if (interruptAppandFrameFlag ) {
                            LogUtil.i(TAG, "MediaCodecUtil::appendCacheAfterResume :: 中断补帧 => ");
                            isAppendingCacheFrameFlag = false;
                            needNextIFrameAfterAppended = true;
                            break;
                        }

                        LogUtil.i(TAG, "MediaCodecUtil::appendCacheAfterResume :: 补P帧 => ");
                        input2decoder(pFrame, 0, pFrame.length, pFrame.length, WL_API_H264_FREAM_TYPE.P);

                        try {
                            Thread.sleep(10); // 控制补帧频率，避免太快
                        } catch (InterruptedException e) {
                            LogUtil.e(TAG, "MediaCodecUtil::appendCacheAfterResume 补帧休眠中断: " + e.getMessage());
                        }
                    }

                    cachePBuffer.clear(); // 清空缓存帧，防止残留干扰
                    needNextIFrameAfterAppended = true;
                }

                isAppendingCacheFrameFlag = false;
            }
        });
    }
    //↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑互联成功后丢帧的处理↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑

    public void queueDecodeFrame(byte[] pFrame,int offset,int length,int frameType) {
        if (!isThreadReady()) {
            LogUtil.w(TAG, "MediaCodecUtil::queueDecodeFrame Decode thread not ready, skip decoding");
            return;
        }
        mDecodeHandler.post(() -> {
                if (mSurface == null || !mSurface.isValid()) {
                    LogUtil.w(TAG, "MediaCodecUtil::queueDecodeFrame Surface not ready, skip decoding");
                    return;
                }
                if (mDecoder == null || !hasDecoderStarted) {
                    LogUtil.w(TAG, "MediaCodecUtil::queueDecodeFrame Decoder not started yet");
                    return;
                }

                try {
                    input2decoder(pFrame, offset, length, length, frameType);
                } catch (Exception e) {
                    LogUtil.e(TAG, "MediaCodecUtil::queueDecodeFrame Error during decoding: " + e.getMessage());
                }
        });
    }
    private void initDecodeThread() {
        if (mHandlerThread == null || mHandlerThread.isInterrupted()) {
            mHandlerThread = new HandlerThread("H264_Decode_Thread");
            mHandlerThread.start();
            mDecodeHandler = new Handler(mHandlerThread.getLooper());
        }
    }
    public byte[] getSpsPpsData() {
        return mSpsPpsData;
    }

    public byte[] getLastIFrameData() {
        return lastIFrameData;

    }

    public boolean hasDecoderStarted() {
        return hasDecoderStarted;
    }
}