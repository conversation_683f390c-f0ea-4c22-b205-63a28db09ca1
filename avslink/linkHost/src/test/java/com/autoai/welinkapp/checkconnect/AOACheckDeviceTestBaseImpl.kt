package com.autoai.welinkapp.checkconnect

import android.hardware.usb.UsbDevice
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*

/**
 * 具体的测试类，继承 AOACheckDeviceTestBase
 * 用于测试 AOACheckDeviceTestBase 的功能
 */
class AOACheckDeviceTestBaseImpl : AOACheckDeviceTestBase() {

    @Test
    fun testCheckDevicesBasic() {
        // Given: 设置基本状态
        CommonData.isCheckingStart = false
        
        // When: 调用checkDevices
        val result = aoaCheckDevice.checkDevices()
        
        // Then: 应该返回false
        assertFalse(result, "检查未开始时应该返回false")
    }

    @Test
    fun testIsIosDeviceWithAppleDevice() {
        // Given: 创建Apple设备Mock
        val appleDevice = mockk<UsbDevice>()
        every { appleDevice.vendorId } returns 0x05AC // Apple VID
        every { appleDevice.productId } returns 0x1200

        // When: 调用isIosDevice辅助方法
        val result = callIsIosDevice(appleDevice)

        // Then: 应该返回true
        assertTrue(result, "Apple设备应该被识别为iOS设备")
    }

    @Test
    fun testIsIosDeviceWithNonAppleDevice() {
        // Given: 创建非Apple设备Mock
        val nonAppleDevice = mockk<UsbDevice>()
        every { nonAppleDevice.vendorId } returns 0x1234 // 非Apple VID
        every { nonAppleDevice.productId } returns 0x1200

        // When: 调用isIosDevice辅助方法
        val result = callIsIosDevice(nonAppleDevice)

        // Then: 应该返回false
        assertFalse(result, "非Apple设备不应该被识别为iOS设备")
    }

    @Test
    fun testCreateMockUsbDevice() {
        // Given & When: 创建Mock USB设备
        val mockDevice = createMockUsbDevice(0x05AC, 0x1200)

        // Then: 验证设备属性
        assertEquals(0x05AC, mockDevice.vendorId, "VendorId应该正确设置")
        assertEquals(0x1200, mockDevice.productId, "ProductId应该正确设置")
    }

    @Test
    fun testCreateMockUsbManager() {
        // Given & When: 创建Mock USB Manager
        val mockManager = createMockUsbManager()
        
        // Then: 验证Manager不为null
        assertNotNull(mockManager, "UsbManager应该不为null")
    }

    @Test
    fun testCreateMockContext() {
        // Given & When: 创建Mock Context
        val mockContext = createMockContext()
        
        // Then: 验证Context不为null
        assertNotNull(mockContext, "Context应该不为null")
    }

    @Test
    fun testResetCommonDataState() {
        // Given: 设置一些状态
        CommonData.isCheckingStart = true
        CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_IN

        // When: 手动重置状态（因为基类方法可能不可访问）
        CommonData.isCheckingStart = false
        CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_OUT
        CommonData.iNeedConnectType = CommonData.DEVICE_TYPE_NONE

        // Then: 验证状态被重置
        assertFalse(CommonData.isCheckingStart, "isCheckingStart应该被重置为false")
        assertEquals(CommonData.CONNECT_STATUS_OUT, CommonData.iCurrentConnectStatus, "连接状态应该被重置")
    }
}
