package com.autoai.welinkapp.checkconnect

import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import com.autoai.welinkapp.datatransfer.DataTransfer
import io.mockk.*
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.junit.jupiter.MockitoExtension
import org.junit.jupiter.api.Assertions.*
import java.lang.reflect.Method

/**
 * Unit tests for DeviceStatusReceiver class
 * 
 * 测试DeviceStatusReceiver类的所有功能，确保100%覆盖率
 * 主要测试设备状态广播接收和处理功能
 */
@ExtendWith(MockitoExtension::class)
class DeviceStatusReceiverTest {

    private lateinit var deviceStatusReceiver: DeviceStatusReceiver
    private lateinit var mockContext: Context
    private lateinit var mockListener: DeviceStatusListener
    private lateinit var mockDataTransfer: DataTransfer

    @BeforeEach
    fun setUp() {
        // Mock静态依赖
        mockkStatic("com.autoai.common.util.LogUtil")
        justRun { com.autoai.common.util.LogUtil.d(any(), any()) }
        justRun { com.autoai.common.util.LogUtil.i(any(), any()) }
        justRun { com.autoai.common.util.LogUtil.e(any(), any()) }

        mockkStatic("com.autoai.common.util.ProcessUtil")
        justRun { com.autoai.common.util.ProcessUtil.printProcess(any()) }

        // 创建Mock对象
        mockContext = mockk(relaxed = true)
        mockListener = mockk(relaxed = true)
        mockDataTransfer = mockk(relaxed = true)

        // Mock DataTransfer构造函数
        mockkConstructor(DataTransfer::class)
        every { anyConstructed<DataTransfer>().startConnect(any()) } returns Unit
        every { anyConstructed<DataTransfer>().stopConnect(any()) } returns Unit

        // 创建DeviceStatusReceiver实例
        deviceStatusReceiver = DeviceStatusReceiver(mockContext, mockListener)

        // 重置CommonData状态
        CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_OUT
        CommonData.iCurrentConnectType = CommonData.DEVICE_TYPE_NONE
        CommonData.iNeedConnectType = CommonData.DEVICE_TYPE_NONE
        CommonData.strNeedConnectIp = null
    }

    @AfterEach
    fun tearDown() {
        // 清理所有mock
        unmockkAll()
        
        // 重置CommonData状态
        CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_OUT
        CommonData.iCurrentConnectType = CommonData.DEVICE_TYPE_NONE
        CommonData.iNeedConnectType = CommonData.DEVICE_TYPE_NONE
        CommonData.strNeedConnectIp = null
    }

    // ==================== Helper Methods ====================

    /**
     * 使用反射调用私有方法
     */
    private fun callPrivateMethod(methodName: String, vararg args: Any?): Any? {
        return try {
            val paramTypes = when (methodName) {
                "filterDeviceInMsg" -> arrayOf(Int::class.java, String::class.java)
                "filterDeviceOutMsg" -> arrayOf(Int::class.java)
                "getDeviceTypeString" -> arrayOf(Int::class.java)
                else -> args.map { it?.javaClass ?: Any::class.java }.toTypedArray()
            }
            val method: Method = DeviceStatusReceiver::class.java.getDeclaredMethod(methodName, *paramTypes)
            method.isAccessible = true
            method.invoke(deviceStatusReceiver, *args)
        } catch (e: Exception) {
            println("Failed to call $methodName via reflection: ${e.message}")
            e.printStackTrace()
            null
        }
    }

    /**
     * 创建Intent
     */
    private fun createIntent(action: String, type: Int = -1, ip: String? = null): Intent {
        val intent = mockk<Intent>()
        every { intent.action } returns action
        every { intent.getIntExtra(CommonData.MSG_KEY_TYPE, -1) } returns type
        every { intent.getStringExtra(CommonData.MSG_KEY_IP) } returns ip
        return intent
    }

    // ==================== Constructor 测试 ====================

    @Test
    fun `constructor should initialize correctly`() {
        // When: 创建DeviceStatusReceiver
        val receiver = DeviceStatusReceiver(mockContext, mockListener)

        // Then: 应该正常创建
        assertNotNull(receiver, "DeviceStatusReceiver应该正常创建")
    }

    @Test
    fun `constructor should handle null listener`() {
        // When: 创建DeviceStatusReceiver with null listener
        val receiver = DeviceStatusReceiver(mockContext, null)

        // Then: 应该正常创建
        assertNotNull(receiver, "DeviceStatusReceiver应该能处理null listener")
    }

    // ==================== registerReceiver() 测试 ====================

    @Test
    fun `registerReceiver should register with correct actions`() {
        // Given: Mock IntentFilter构造函数和方法
        mockkConstructor(IntentFilter::class)
        every { anyConstructed<IntentFilter>().addAction(any()) } returns Unit

        // When: 调用registerReceiver
        deviceStatusReceiver.registerReceiver()

        // Then: 应该注册正确的actions
        verify { mockContext.registerReceiver(deviceStatusReceiver, any<IntentFilter>(), any()) }
        verify { anyConstructed<IntentFilter>().addAction(CommonData.DEVICE_IN_MSG) }
        verify { anyConstructed<IntentFilter>().addAction(CommonData.DEVICE_OUT_MSG) }
        verify { anyConstructed<IntentFilter>().addAction(CommonData.DEVICE_CONNECTING) }
    }

    // ==================== unregisterReceiver() 测试 ====================

    @Test
    fun `unregisterReceiver should unregister receiver`() {
        // When: 调用unregisterReceiver
        deviceStatusReceiver.unregisterReceiver()

        // Then: 应该取消注册
        verify { mockContext.unregisterReceiver(deviceStatusReceiver) }
    }

    // ==================== onReceive() 测试 ====================

    @Test
    fun `onReceive should handle DEVICE_IN_MSG action`() {
        // Given: DEVICE_IN_MSG Intent
        val intent = createIntent(CommonData.DEVICE_IN_MSG, CommonData.DEVICE_TYPE_AOA, "192.168.1.100")

        // When: 调用onReceive
        deviceStatusReceiver.onReceive(mockContext, intent)

        // Then: 应该处理设备连入消息
        verify { anyConstructed<DataTransfer>().startConnect(ConnectManager.DEVICE_TYPE_AOA) }
        verify { mockListener.onDeviceIn(CommonData.DEVICE_TYPE_AOA, "192.168.1.100") }
        assertEquals(CommonData.CONNECT_STATUS_IN, CommonData.iCurrentConnectStatus)
        assertEquals(CommonData.DEVICE_TYPE_AOA, CommonData.iCurrentConnectType)
    }

    @Test
    fun `onReceive should handle DEVICE_OUT_MSG action`() {
        // Given: 设置已连接状态
        CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_IN
        CommonData.iCurrentConnectType = CommonData.DEVICE_TYPE_AOA
        
        val intent = createIntent(CommonData.DEVICE_OUT_MSG, CommonData.DEVICE_TYPE_AOA)

        // When: 调用onReceive
        deviceStatusReceiver.onReceive(mockContext, intent)

        // Then: 应该处理设备断开消息
        verify { anyConstructed<DataTransfer>().stopConnect(ConnectManager.DEVICE_TYPE_AOA) }
        verify { mockListener.onDeviceOut(CommonData.DEVICE_TYPE_AOA) }
        assertEquals(CommonData.CONNECT_STATUS_OUT, CommonData.iCurrentConnectStatus)
    }

    @Test
    fun `onReceive should handle DEVICE_CONNECTING action`() {
        // Given: DEVICE_CONNECTING Intent
        val intent = createIntent(CommonData.DEVICE_CONNECTING, CommonData.DEVICE_TYPE_EAP)

        // When: 调用onReceive
        deviceStatusReceiver.onReceive(mockContext, intent)

        // Then: 应该通知listener连接中状态
        verify { mockListener.onDevicesConnecting(CommonData.DEVICE_TYPE_EAP) }
    }

    @Test
    fun `onReceive should handle unknown action`() {
        // Given: 未知action的Intent
        val intent = createIntent("unknown.action")

        // When: 调用onReceive
        deviceStatusReceiver.onReceive(mockContext, intent)

        // Then: 应该记录错误日志，不抛出异常
        assertTrue(true, "应该能处理未知action而不抛出异常")
    }

    @Test
    fun `onReceive should handle DEVICE_CONNECTING with null listener`() {
        // Given: null listener的receiver
        val receiverWithNullListener = DeviceStatusReceiver(mockContext, null)
        val intent = createIntent(CommonData.DEVICE_CONNECTING, CommonData.DEVICE_TYPE_EAP)

        // When: 调用onReceive
        receiverWithNullListener.onReceive(mockContext, intent)

        // Then: 应该正常处理，不抛出异常
        assertTrue(true, "应该能处理null listener的情况")
    }

    // ==================== filterDeviceInMsg() 私有方法测试 ====================

    @Test
    fun `filterDeviceInMsg should handle AOA device when not connected`() {
        // Given: 未连接状态
        CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_OUT

        // When: 调用filterDeviceInMsg
        callPrivateMethod("filterDeviceInMsg", CommonData.DEVICE_TYPE_AOA, "192.168.1.100")

        // Then: 应该启动AOA连接
        verify { anyConstructed<DataTransfer>().startConnect(ConnectManager.DEVICE_TYPE_AOA) }
        verify { mockListener.onDeviceIn(CommonData.DEVICE_TYPE_AOA, "192.168.1.100") }
        assertEquals(CommonData.CONNECT_STATUS_IN, CommonData.iCurrentConnectStatus)
        assertEquals(CommonData.DEVICE_TYPE_AOA, CommonData.iCurrentConnectType)
    }

    @Test
    fun `filterDeviceInMsg should handle EAP device when not connected`() {
        // Given: 未连接状态
        CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_OUT

        // When: 调用filterDeviceInMsg
        callPrivateMethod("filterDeviceInMsg", CommonData.DEVICE_TYPE_EAP, "192.168.1.101")

        // Then: 应该启动EAP连接
        verify { anyConstructed<DataTransfer>().startConnect(ConnectManager.DEVICE_TYPE_EAP) }
        verify { mockListener.onDeviceIn(CommonData.DEVICE_TYPE_EAP, "192.168.1.101") }
        assertEquals(CommonData.CONNECT_STATUS_IN, CommonData.iCurrentConnectStatus)
        assertEquals(CommonData.DEVICE_TYPE_EAP, CommonData.iCurrentConnectType)
    }

    @Test
    fun `filterDeviceInMsg should handle IDB device when not connected`() {
        // Given: 未连接状态
        CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_OUT

        // When: 调用filterDeviceInMsg
        callPrivateMethod("filterDeviceInMsg", CommonData.DEVICE_TYPE_IDB, "192.168.1.102")

        // Then: 应该启动IDB连接
        verify { anyConstructed<DataTransfer>().startConnect(ConnectManager.DEVICE_TYPE_IDB) }
        verify { mockListener.onDeviceIn(CommonData.DEVICE_TYPE_IDB, "192.168.1.102") }
        assertEquals(CommonData.CONNECT_STATUS_IN, CommonData.iCurrentConnectStatus)
        assertEquals(CommonData.DEVICE_TYPE_IDB, CommonData.iCurrentConnectType)
    }

    @Test
    fun `filterDeviceInMsg should handle wireless device when not connected`() {
        // Given: 未连接状态
        CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_OUT

        // When: 调用filterDeviceInMsg with wireless type
        callPrivateMethod("filterDeviceInMsg", CommonData.DEVICE_TYPE_WIFI_ANDROID, "192.168.1.103")

        // Then: 应该通知listener但不启动特定连接
        verify { mockListener.onDeviceIn(CommonData.DEVICE_TYPE_WIFI_ANDROID, "192.168.1.103") }
        assertEquals(CommonData.CONNECT_STATUS_IN, CommonData.iCurrentConnectStatus)
        assertEquals(CommonData.DEVICE_TYPE_WIFI_ANDROID, CommonData.iCurrentConnectType)
    }

    @Test
    fun `filterDeviceInMsg should handle device already connected with same type`() {
        // Given: 已连接相同类型设备
        CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_IN
        CommonData.iCurrentConnectType = CommonData.DEVICE_TYPE_AOA

        // When: 调用filterDeviceInMsg with same type
        callPrivateMethod("filterDeviceInMsg", CommonData.DEVICE_TYPE_AOA, "192.168.1.100")

        // Then: 应该记录日志但不重复连接
        // 验证没有调用startConnect
        verify(exactly = 0) { anyConstructed<DataTransfer>().startConnect(any()) }
    }

    @Test
    fun `filterDeviceInMsg should handle device already connected with different type`() {
        // Given: 已连接不同类型设备
        CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_IN
        CommonData.iCurrentConnectType = CommonData.DEVICE_TYPE_AOA

        // When: 调用filterDeviceInMsg with different type
        callPrivateMethod("filterDeviceInMsg", CommonData.DEVICE_TYPE_EAP, "192.168.1.101")

        // Then: 应该记录日志但不切换连接
        verify(exactly = 0) { anyConstructed<DataTransfer>().startConnect(any()) }
        assertEquals(CommonData.DEVICE_TYPE_AOA, CommonData.iCurrentConnectType) // 保持原类型
    }

    // ==================== filterDeviceOutMsg() 私有方法测试 ====================

    @Test
    fun `filterDeviceOutMsg should handle AOA device disconnect when connected`() {
        // Given: 已连接AOA设备
        CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_IN
        CommonData.iCurrentConnectType = CommonData.DEVICE_TYPE_AOA

        // When: 调用filterDeviceOutMsg
        callPrivateMethod("filterDeviceOutMsg", CommonData.DEVICE_TYPE_AOA)

        // Then: 应该停止AOA连接
        verify { anyConstructed<DataTransfer>().stopConnect(ConnectManager.DEVICE_TYPE_AOA) }
        verify { mockListener.onDeviceOut(CommonData.DEVICE_TYPE_AOA) }
        assertEquals(CommonData.CONNECT_STATUS_OUT, CommonData.iCurrentConnectStatus)
    }

    @Test
    fun `filterDeviceOutMsg should handle EAP device disconnect when connected`() {
        // Given: 已连接EAP设备
        CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_IN
        CommonData.iCurrentConnectType = CommonData.DEVICE_TYPE_EAP

        // When: 调用filterDeviceOutMsg
        callPrivateMethod("filterDeviceOutMsg", CommonData.DEVICE_TYPE_EAP)

        // Then: 应该停止EAP连接
        verify { anyConstructed<DataTransfer>().stopConnect(ConnectManager.DEVICE_TYPE_EAP) }
        verify { mockListener.onDeviceOut(CommonData.DEVICE_TYPE_EAP) }
        assertEquals(CommonData.CONNECT_STATUS_OUT, CommonData.iCurrentConnectStatus)
    }

    @Test
    fun `filterDeviceOutMsg should handle IDB device disconnect when connected`() {
        // Given: 已连接IDB设备
        CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_IN
        CommonData.iCurrentConnectType = CommonData.DEVICE_TYPE_IDB

        // When: 调用filterDeviceOutMsg
        callPrivateMethod("filterDeviceOutMsg", CommonData.DEVICE_TYPE_IDB)

        // Then: 应该停止IDB连接
        verify { anyConstructed<DataTransfer>().stopConnect(ConnectManager.DEVICE_TYPE_IDB) }
        verify { mockListener.onDeviceOut(CommonData.DEVICE_TYPE_IDB) }
        assertEquals(CommonData.CONNECT_STATUS_OUT, CommonData.iCurrentConnectStatus)
    }

    @Test
    fun `filterDeviceOutMsg should handle wireless device disconnect when connected`() {
        // Given: 已连接无线设备
        CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_IN
        CommonData.iCurrentConnectType = CommonData.DEVICE_TYPE_WIFI_ANDROID

        // When: 调用filterDeviceOutMsg
        callPrivateMethod("filterDeviceOutMsg", CommonData.DEVICE_TYPE_WIFI_ANDROID)

        // Then: 应该通知listener但不调用特定停止连接
        verify { mockListener.onDeviceOut(CommonData.DEVICE_TYPE_WIFI_ANDROID) }
        assertEquals(CommonData.CONNECT_STATUS_OUT, CommonData.iCurrentConnectStatus)
    }

    @Test
    fun `filterDeviceOutMsg should handle type mismatch when connected`() {
        // Given: 已连接AOA设备
        CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_IN
        CommonData.iCurrentConnectType = CommonData.DEVICE_TYPE_AOA

        // When: 调用filterDeviceOutMsg with different type
        callPrivateMethod("filterDeviceOutMsg", CommonData.DEVICE_TYPE_EAP)

        // Then: 应该记录错误并重置需要连接的类型
        assertEquals(CommonData.DEVICE_TYPE_NONE, CommonData.iNeedConnectType)
        assertNull(CommonData.strNeedConnectIp)
        // 验证没有调用stopConnect
        verify(exactly = 0) { anyConstructed<DataTransfer>().stopConnect(any()) }
    }

    @Test
    fun `filterDeviceOutMsg should handle already disconnected state`() {
        // Given: 已断开状态
        CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_OUT

        // When: 调用filterDeviceOutMsg
        callPrivateMethod("filterDeviceOutMsg", CommonData.DEVICE_TYPE_AOA)

        // Then: 应该记录错误日志但不执行断开操作
        verify(exactly = 0) { anyConstructed<DataTransfer>().stopConnect(any()) }
        verify(exactly = 0) { mockListener.onDeviceOut(any()) }
    }

    // ==================== getDeviceTypeString() 私有方法测试 ====================

    @Test
    fun `getDeviceTypeString should return correct strings for all device types`() {
        // Test all device types
        val testCases = mapOf(
            CommonData.DEVICE_TYPE_AOA to "AOA",
            CommonData.DEVICE_TYPE_EAP to "EAP",
            CommonData.DEVICE_TYPE_IDB to "IDB",
            CommonData.DEVICE_TYPE_WIFI_ANDROID to "WIFI_ANDROID",
            CommonData.DEVICE_TYPE_WIFI_IOS to "WIFI_IOS",
            CommonData.DEVICE_TYPE_WIFI_HARMONY to "WIFI_HARMONY",
            -999 to "UNKNOWN" // 未知类型
        )

        testCases.forEach { (type, expectedString) ->
            // When: 调用getDeviceTypeString
            val result = callPrivateMethod("getDeviceTypeString", type)

            // Then: 应该返回正确的字符串
            assertEquals(expectedString, result, "设备类型 $type 应该返回 $expectedString")
        }
    }

    @Test
    fun `getDeviceTypeString should handle edge case device types`() {
        // Test edge cases
        val edgeCases = listOf(0, Int.MAX_VALUE, Int.MIN_VALUE)

        edgeCases.forEach { type ->
            // When: 调用getDeviceTypeString
            val result = callPrivateMethod("getDeviceTypeString", type)

            // Then: 应该返回UNKNOWN
            assertEquals("UNKNOWN", result, "边界值设备类型 $type 应该返回 UNKNOWN")
        }
    }

    // ==================== 集成测试 ====================

    @Test
    fun `integration test - complete device connection flow`() {
        // Given: 初始状态
        CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_OUT

        // When: 设备连入
        val deviceInIntent = createIntent(CommonData.DEVICE_IN_MSG, CommonData.DEVICE_TYPE_AOA, "192.168.1.100")
        deviceStatusReceiver.onReceive(mockContext, deviceInIntent)

        // Then: 应该连接成功
        assertEquals(CommonData.CONNECT_STATUS_IN, CommonData.iCurrentConnectStatus)
        assertEquals(CommonData.DEVICE_TYPE_AOA, CommonData.iCurrentConnectType)

        // When: 设备断开
        val deviceOutIntent = createIntent(CommonData.DEVICE_OUT_MSG, CommonData.DEVICE_TYPE_AOA)
        deviceStatusReceiver.onReceive(mockContext, deviceOutIntent)

        // Then: 应该断开成功
        assertEquals(CommonData.CONNECT_STATUS_OUT, CommonData.iCurrentConnectStatus)
    }

    @Test
    fun `integration test - register and unregister receiver`() {
        // Given: Mock IntentFilter构造函数
        mockkConstructor(IntentFilter::class)
        every { anyConstructed<IntentFilter>().addAction(any()) } returns Unit

        // When: 注册和取消注册
        deviceStatusReceiver.registerReceiver()
        deviceStatusReceiver.unregisterReceiver()

        // Then: 应该正常完成
        verify { mockContext.registerReceiver(deviceStatusReceiver, any<IntentFilter>(), any()) }
        verify { mockContext.unregisterReceiver(deviceStatusReceiver) }
    }

    @Test
    fun `filterDeviceInMsg should handle null listener gracefully`() {
        // Given: null listener的receiver
        val receiverWithNullListener = DeviceStatusReceiver(mockContext, null)
        CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_OUT

        // When: 调用filterDeviceInMsg通过反射
        val method = DeviceStatusReceiver::class.java.getDeclaredMethod("filterDeviceInMsg", Int::class.java, String::class.java)
        method.isAccessible = true
        method.invoke(receiverWithNullListener, CommonData.DEVICE_TYPE_AOA, "192.168.1.100")

        // Then: 应该正常处理，不抛出异常
        assertEquals(CommonData.CONNECT_STATUS_IN, CommonData.iCurrentConnectStatus)
        assertEquals(CommonData.DEVICE_TYPE_AOA, CommonData.iCurrentConnectType)
    }

    @Test
    fun `filterDeviceOutMsg should handle null listener gracefully`() {
        // Given: null listener的receiver
        val receiverWithNullListener = DeviceStatusReceiver(mockContext, null)
        CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_IN
        CommonData.iCurrentConnectType = CommonData.DEVICE_TYPE_AOA

        // When: 调用filterDeviceOutMsg通过反射
        val method = DeviceStatusReceiver::class.java.getDeclaredMethod("filterDeviceOutMsg", Int::class.java)
        method.isAccessible = true
        method.invoke(receiverWithNullListener, CommonData.DEVICE_TYPE_AOA)

        // Then: 应该正常处理，不抛出异常
        assertEquals(CommonData.CONNECT_STATUS_OUT, CommonData.iCurrentConnectStatus)
    }
}
