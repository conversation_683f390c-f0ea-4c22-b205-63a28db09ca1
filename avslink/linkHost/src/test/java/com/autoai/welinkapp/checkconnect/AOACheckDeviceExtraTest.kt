package com.autoai.welinkapp.checkconnect

import android.hardware.usb.UsbDevice
import io.mockk.*
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.junit.jupiter.MockitoExtension
import org.junit.jupiter.api.Assertions.*
import java.lang.reflect.Method

/**
 * Additional Unit tests for AOACheckDevice class
 * 
 * Tests additional functionality including isIosDevice method and other core functionality.
 * 
 * 这个测试类专门测试 AOACheckDevice 中的额外方法
 * 补充现有测试覆盖率，特别是 isIosDevice 方法和其他核心功能
 */
@ExtendWith(MockitoExtension::class)
class AOACheckDeviceExtraTest : AOACheckDeviceTestBase() {

    companion object {
        // Constants from AOACheckDevice class
        private const val VID_APPLE = 0x5ac
        private const val VID_SAMSUNG = 0x04e8
        private const val VID_HTC = 0x0bb4
    }

    @BeforeEach
    override fun setUp() {
        super.setUp()
    }

    @AfterEach
    override fun tearDown() {
        super.tearDown()
    }

    /**
     * Helper method to call the real AOACheckDevice.isIosDevice method using reflection
     * 使用反射调用真实的AOACheckDevice.isIosDevice方法进行测试
     *
     * 由于isIosDevice是private方法，我们使用反射来访问它
     */
    private fun callIsIosDeviceReflection(device: UsbDevice): Boolean {
        return try {
            val method: Method = AOACheckDevice::class.java.getDeclaredMethod("isIosDevice", UsbDevice::class.java)
            method.isAccessible = true
            method.invoke(aoaCheckDevice, device) as Boolean
        } catch (e: Exception) {
            println("Failed to call isIosDevice via reflection: ${e.message}")
            false
        }
    }

    // ==================== isIosDevice() 测试 ====================

    /**
     * 测试场景：检测标准iPhone设备
     * 验证当USB设备具有Apple厂商ID和iPhone产品ID时，能正确识别为iOS设备
     */
    @Test
    fun `isIosDevice should return true for valid iOS device with iPhone product ID`() {
        // Given: Apple vendor ID and iPhone product ID
        val mockDevice = createMockUsbDevice(VID_APPLE, 0x1200)

        // When: Call real isIosDevice method
        val result = callIsIosDeviceReflection(mockDevice)

        // Then: Should return true
        assertTrue(result, "Device with Apple VID and iPhone PID should be recognized as iOS device")
    }

    /**
     * 测试场景：检测非Apple厂商的设备
     * 验证即使产品ID类似iPhone，但厂商ID不是Apple的设备不会被误识别为iOS设备
     */
    @Test
    fun `isIosDevice should return false for non-Apple vendor ID`() {
        // Given: Non-Apple vendor ID
        val mockDevice = createMockUsbDevice(VID_SAMSUNG, 0x1200)

        // When: Call real isIosDevice method
        val result = callIsIosDeviceReflection(mockDevice)

        // Then: Should return false
        assertFalse(result, "Non-Apple device should not be recognized as iOS device")
    }

    /**
     * 测试场景：检测Apple设备但产品ID无效的情况
     * 验证当设备是Apple厂商但产品ID不在iPhone/iPod范围内时，不会被误识别为iOS设备
     */
    @Test
    fun `isIosDevice should return false for Apple device with invalid product ID`() {
        // Given: Apple vendor ID but invalid product ID (outside iPhone/iPod range)
        val mockDevice = createMockUsbDevice(VID_APPLE, 0x1100)

        // When: Call real isIosDevice method
        val result = callIsIosDeviceReflection(mockDevice)

        // Then: Should return false
        assertFalse(result, "Apple device with invalid product ID should not be recognized as iOS device")
    }

    /**
     * 测试单例模式 - 验证getInstance返回同一个实例
     * 确保AOACheckDevice类正确实现了单例模式，多次调用getInstance()返回相同的实例
     */
    @Test
    fun `getInstance should return same instance`() {
        // When: 获取两次实例
        val instance1 = AOACheckDevice.getInstance()
        val instance2 = AOACheckDevice.getInstance()

        // Then: 验证是同一个实例
        assertSame(instance1, instance2, "getInstance应该返回同一个实例")
        assertNotNull(instance1, "实例不应该为null")
    }

    /**
     * 测试场景：检测多种iOS设备产品ID
     * 验证在有效产品ID范围内的多个不同产品ID都能被正确识别为iOS设备
     */
    @Test
    fun `isIosDevice should return true for various iOS device product IDs in valid range`() {
        // Test multiple product IDs that should be recognized as iOS devices
        val validProductIds = listOf(
            0x1200, // iPhone base
            0x1201, // iPod/iPhone variant
            0x1202, // iPod/iPhone variant
            0x12FF  // Last valid product ID in range
        )

        validProductIds.forEach { productId ->
            // Given: Apple vendor ID and valid product ID
            val mockDevice = createMockUsbDevice(VID_APPLE, productId)

            // When: Call real isIosDevice method
            val result = callIsIosDeviceReflection(mockDevice)

            // Then: Should return true
            assertTrue(result, "Device with Apple VID and product ID 0x${productId.toString(16)} should be recognized as iOS device")
        }
    }

    /**
     * 测试场景：批量检测多个非Apple厂商的设备
     * 验证各种不同厂商（三星、HTC等）的设备都不会被误识别为iOS设备
     */
    @Test
    fun `isIosDevice should return false for various non-Apple vendors`() {
        val nonAppleVendorIds = listOf(
            VID_SAMSUNG,
            VID_HTC,
            0x0000, // Invalid vendor
            0xFFFF  // Invalid vendor
        )

        nonAppleVendorIds.forEach { vendorId ->
            // Given: Non-Apple vendor ID
            val mockDevice = createMockUsbDevice(vendorId, 0x1200)

            // When: Call real isIosDevice method
            val result = callIsIosDeviceReflection(mockDevice)

            // Then: Should return false
            assertFalse(result, "Device with vendor ID 0x${vendorId.toString(16)} should not be recognized as iOS device")
        }
    }
}
