package com.autoai.welinkapp.checkconnect

import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.hardware.usb.UsbConfiguration
import android.hardware.usb.UsbConstants
import android.hardware.usb.UsbDevice
import android.hardware.usb.UsbDeviceConnection
import android.hardware.usb.UsbInterface
import android.hardware.usb.UsbManager
import android.os.Build
import com.autoai.welinkapp.aoa.AoaLink
import com.autoai.welinkapp.aoa.AoaDevice
import com.autoai.welinkapp.model.UIResponder
import io.mockk.*
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.junit.jupiter.MockitoExtension
import org.junit.jupiter.api.Assertions.*
import java.lang.reflect.Method

/**
 * Unit tests for AOACheckDevice class - Util Test Suite
 * 
 * 这个测试类专门测试从util目录搬移过来的AOACheckDevice测试用例
 * 主要测试isIosDevice方法和其他核心功能
 */
@ExtendWith(MockitoExtension::class)
class AOACheckDeviceUtilTest : AOACheckDeviceTestBase() {

    companion object {
        // Constants from AOACheckDevice class
        private const val VID_APPLE = 0x5ac
        private const val VID_ACCESSORY = 0x18D1
        private const val PID_ACCESSORY = 0x2D
        private const val DEVICE_IPHONE_PRODUCT_ID = 0x1200
        private const val DEVICE_IPOD_PRODUCT_ID_MASK = 0xFF00

        // Android vendor IDs for testing
        private const val VID_SAMSUNG = 0x04e8
        private const val VID_HTC = 0x0bb4
        private const val VID_MOTOROLA = 0x22b8
        private const val VID_LG = 0x1004
        private const val VID_HUAWEI = 0x12d1
        private const val VID_XIAOMI = 0x2717
        private const val VID_OPPO = 0x22d9
        private const val VID_VIVO = 0x2d95
        private const val VID_ONEPLUS = 0x2a70
        private const val VID_REALME = 0x18d1

        // Infrastructure device vendor IDs for testing
        private const val VID_MICROCHIP = 0x424
        private const val VID_LINUX_FOUNDATION = 0x1d6b
        private const val VID_INTEL = 0x8087
        private const val VID_GOOGLE = 0x18d1

        // USB device classes
        private const val USB_CLASS_HUB = 9
        private const val USB_CLASS_VENDOR_SPECIFIC = 255
        private const val USB_CLASS_CDC = 2
        private const val USB_CLASS_HID = 3
        private const val USB_CLASS_MASS_STORAGE = 8
        private const val USB_CLASS_STILL_IMAGE = 6
        private const val USB_CLASS_CDC_DATA = 10
        private const val USB_CLASS_AUDIO = 1

        // USB storage interface constants
        private const val STORAGE_INTERFACE_CLASS = 0x08
        private const val STORAGE_INTERFACE_SUBCLASS = 0x06
        private const val STORAGE_INTERFACE_PROTOCOL = 0x50
        private const val STORAGE_CONFIG_INTERFACE_COUNT = 1

        // AOA protocol constants
        private const val AOA_GET_PROTOCOL = 51
        private const val AOA_SEND_IDENT = 52
        private const val AOA_START_ACCESSORY = 53

        // USB控制传输超时时间常量 (毫秒)
        private const val USB_CONTROL_TRANSFER_TIMEOUT = 5000
    }

    @BeforeEach
    override fun setUp() {
        super.setUp()
    }

    @AfterEach
    override fun tearDown() {
        super.tearDown()
    }

    // ==================== Helper Methods ====================

    /**
     * 使用反射调用私有的isIosDevice方法
     * 由于isIosDevice是私有方法，需要通过反射来访问
     */
    private fun callIsIosDeviceViaReflection(device: UsbDevice): Boolean {
        return try {
            val method: Method = AOACheckDevice::class.java.getDeclaredMethod("isIosDevice", UsbDevice::class.java)
            method.isAccessible = true
            method.invoke(aoaCheckDevice, device) as Boolean
        } catch (e: Exception) {
            // 如果反射失败，返回false并记录错误
            println("Failed to call isIosDevice via reflection: ${e.message}")
            false
        }
    }

    // ==================== isIosDevice() 核心测试 ====================

    /**
     * 测试场景：检测标准iPhone设备
     * 验证当USB设备具有Apple厂商ID和iPhone产品ID时，能正确识别为iOS设备
     */
    @Test
    fun `isIosDevice should return true for valid iOS device with iPhone product ID`() {
        // Given: Apple vendor ID and iPhone product ID
        val mockDevice = createMockUsbDevice(VID_APPLE, 0x1200)

        // When: Call real isIosDevice method via reflection
        val result = callIsIosDeviceViaReflection(mockDevice)

        // Then: Should return true
        assertTrue(result, "Device with Apple VID and iPhone PID should be recognized as iOS device")
    }

    /**
     * 测试场景：检测iPod设备
     * 验证当USB设备具有Apple厂商ID和iPod产品ID范围内的ID时，能正确识别为iOS设备
     */
    @Test
    fun `isIosDevice should return true for valid iOS device with iPod product ID range`() {
        // Given: Apple vendor ID and iPod product ID within range
        val mockDevice = createMockUsbDevice(VID_APPLE, 0x1201)

        // When: Call real isIosDevice method via reflection
        val result = callIsIosDeviceViaReflection(mockDevice)

        // Then: Should return true
        assertTrue(result, "Device with Apple VID and iPod PID should be recognized as iOS device")
    }

    /**
     * 测试场景：批量检测多种iOS设备产品ID
     * 验证在有效产品ID范围内的多个不同产品ID都能被正确识别为iOS设备
     */
    @Test
    fun `isIosDevice should return true for various iOS device product IDs in valid range`() {
        // Test multiple product IDs that should be recognized as iOS devices
        val validProductIds = listOf(
            0x1200, // iPhone base
            0x1201, // iPod/iPhone variant
            0x1202, // iPod/iPhone variant
            0x12FF  // Last valid product ID in range
        )

        validProductIds.forEach { productId ->
            // Given: Apple vendor ID and valid product ID
            val mockDevice = createMockUsbDevice(VID_APPLE, productId)

            // When: Call real isIosDevice method via reflection
            val result = callIsIosDeviceViaReflection(mockDevice)

            // Then: Should return true
            assertTrue(result, "Device with Apple VID and product ID 0x${productId.toString(16)} should be recognized as iOS device")
        }
    }

    /**
     * 测试场景：检测Apple设备但产品ID无效的情况
     * 验证当设备是Apple厂商但产品ID不在iPhone/iPod范围内时，不会被误识别为iOS设备
     */
    @Test
    fun `isIosDevice should return false for Apple device with invalid product ID`() {
        // Given: Apple vendor ID but invalid product ID (outside iPhone/iPod range)
        val mockDevice = createMockUsbDevice(VID_APPLE, 0x1100)

        // When: Call real isIosDevice method via reflection
        val result = callIsIosDeviceViaReflection(mockDevice)

        // Then: Should return false
        assertFalse(result, "Apple device with invalid product ID should not be recognized as iOS device")
    }

    /**
     * 测试场景：批量检测Apple设备的无效产品ID
     * 验证多个超出iOS设备产品ID范围的Apple产品都不会被误识别为iOS设备
     */
    @Test
    fun `isIosDevice should return false for Apple device with product ID outside range`() {
        // Test various Apple product IDs that are NOT iOS devices
        val invalidProductIds = listOf(
            0x1000, // Too low
            0x1100, // Still too low
            0x1300, // Too high (different product line)
            0x1400, // Much too high
            0x2000  // Completely different product
        )

        invalidProductIds.forEach { productId ->
            // Given: Apple vendor ID but invalid product ID
            val mockDevice = createMockUsbDevice(VID_APPLE, productId)

            // When: Call real isIosDevice method via reflection
            val result = callIsIosDeviceViaReflection(mockDevice)

            // Then: Should return false
            assertFalse(result, "Apple device with product ID 0x${productId.toString(16)} should not be recognized as iOS device")
        }
    }

    /**
     * 测试场景：检测非Apple厂商的设备
     * 验证即使产品ID类似iPhone，但厂商ID不是Apple的设备不会被误识别为iOS设备
     */
    @Test
    fun `isIosDevice should return false for non-Apple vendor ID`() {
        // Given: Non-Apple vendor ID
        val mockDevice = createMockUsbDevice(VID_SAMSUNG, 0x1200)

        // When: Call real isIosDevice method via reflection
        val result = callIsIosDeviceViaReflection(mockDevice)

        // Then: Should return false
        assertFalse(result, "Non-Apple device should not be recognized as iOS device")
    }

    /**
     * 测试场景：批量检测多个非Apple厂商的设备
     * 验证各种不同厂商（三星、HTC、Google等）的设备都不会被误识别为iOS设备
     */
    @Test
    fun `isIosDevice should return false for various non-Apple vendors`() {
        val nonAppleVendorIds = listOf(
            VID_SAMSUNG,
            VID_HTC,
            VID_MOTOROLA,
            0x0000, // Invalid vendor
            0xFFFF  // Invalid vendor
        )

        nonAppleVendorIds.forEach { vendorId ->
            // Given: Non-Apple vendor ID
            val mockDevice = createMockUsbDevice(vendorId, 0x1200)

            // When: Call real isIosDevice method via reflection
            val result = callIsIosDeviceViaReflection(mockDevice)

            // Then: Should return false
            assertFalse(result, "Device with vendor ID 0x${vendorId.toString(16)} should not be recognized as iOS device")
        }
    }

    /**
     * 测试场景：检测产品ID边界值情况
     * 验证在有效产品ID范围边界附近的值能被正确处理，确保边界判断逻辑准确
     */
    @Test
    fun `isIosDevice should handle edge cases correctly`() {
        // Test edge cases around the product ID mask boundary
        val edgeCaseProductIds = mapOf(
            0x11FF to false, // Just below valid range
            0x1200 to true,  // Exact boundary - valid
            0x12FF to true,  // Top of valid range
            0x1300 to false  // Just above valid range
        )

        edgeCaseProductIds.forEach { (productId, expectedResult) ->
            // Given: Apple vendor ID and edge case product ID
            val mockDevice = createMockUsbDevice(VID_APPLE, productId)

            // When: Call real isIosDevice method via reflection
            val result = callIsIosDeviceViaReflection(mockDevice)

            // Then: Should match expected result
            if (expectedResult) {
                assertTrue(result, "Apple device with product ID 0x${productId.toString(16)} should be recognized as iOS device")
            } else {
                assertFalse(result, "Apple device with product ID 0x${productId.toString(16)} should not be recognized as iOS device")
            }
        }
    }

    /**
     * 测试场景：验证产品ID掩码逻辑
     * 测试产品ID掩码算法 (productId & 0xFF00) == 0x1200 的正确性，
     * 确保0x1200-0x12FF范围内的产品ID能通过掩码测试，范围外的不能通过
     */
    @Test
    fun `isIosDevice should validate product ID mask logic`() {
        // Test the mask logic: (productId & 0xFF00) == 0x1200
        // This means product IDs from 0x1200 to 0x12FF should be valid

        // Test product IDs that should pass the mask test
        val validMaskedIds = listOf(0x1200, 0x1250, 0x12AA, 0x12FF)
        validMaskedIds.forEach { productId ->
            val mockDevice = createMockUsbDevice(VID_APPLE, productId)

            val result = callIsIosDeviceViaReflection(mockDevice)
            assertTrue(result, "Product ID 0x${productId.toString(16)} should pass mask test (${productId and DEVICE_IPOD_PRODUCT_ID_MASK} == $DEVICE_IPHONE_PRODUCT_ID)")
        }

        // Test product IDs that should fail the mask test
        val invalidMaskedIds = listOf(0x1100, 0x1300, 0x1400, 0x2200)
        invalidMaskedIds.forEach { productId ->
            val mockDevice = createMockUsbDevice(VID_APPLE, productId)

            val result = callIsIosDeviceViaReflection(mockDevice)
            assertFalse(result, "Product ID 0x${productId.toString(16)} should fail mask test (${productId and DEVICE_IPOD_PRODUCT_ID_MASK} != $DEVICE_IPHONE_PRODUCT_ID)")
        }
    }

    // ==================== getInstance() 单例模式测试 ====================

    /**
     * 测试单例模式 - 验证getInstance返回同一个实例
     * 确保AOACheckDevice类正确实现了单例模式，多次调用getInstance()返回相同的实例
     */
    @Test
    fun `getInstance should return same instance`() {
        // When: 获取两次实例
        val instance1 = AOACheckDevice.getInstance()
        val instance2 = AOACheckDevice.getInstance()

        // Then: 验证是同一个实例
        assertSame(instance1, instance2, "getInstance应该返回同一个实例")
        assertNotNull(instance1, "实例不应该为null")
    }

    /**
     * 测试单例模式 - 多线程环境下的线程安全性
     * 验证在多线程环境下，getInstance()方法能正确处理并发访问，确保线程安全
     */
    @Test
    fun `getInstance should be thread safe`() {
        val instances = arrayOfNulls<AOACheckDevice>(2)
        val threads = arrayOfNulls<Thread>(2)

        // 创建两个线程同时获取实例
        for (i in 0..1) {
            threads[i] = Thread {
                instances[i] = AOACheckDevice.getInstance()
            }
        }

        // 启动线程并等待完成
        threads.forEach { it?.start() }
        threads.forEach { it?.join() }

        // 验证两个线程获取的是同一个实例
        assertSame(instances[0], instances[1], "多线程环境下应该返回同一个实例")
        assertNotNull(instances[0], "实例不应该为null")
    }

    // ==================== checkDevices() 设备检测测试 ====================

    /**
     * 测试checkDevices - 异常情况：检查未开始
     * 验证当CommonData.isCheckingStart为false时，checkDevices正确返回false并设置需要连接的类型
     */
    @Test
    fun `checkDevices should return false when checking not started`() {
        // Given: 设置CommonData状态为未开始检查
        CommonData.isCheckingStart = false

        // When: 调用checkDevices
        val result = aoaCheckDevice.checkDevices()

        // Then: 应该返回false
        assertFalse(result, "检查未开始时应该返回false")

        // 验证设置了需要连接的类型
        assertEquals(CommonData.DEVICE_TYPE_AOA, CommonData.iNeedConnectType, "应该设置需要连接的类型为AOA")
    }

    /**
     * 测试checkDevices - 异常情况：已经连接
     * 验证当设备已经连接时，checkDevices正确返回false，避免重复连接
     */
    @Test
    fun `checkDevices should return false when already connected`() {
        // Given: 设置CommonData状态为已连接
        CommonData.isCheckingStart = true
        CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_IN

        // When: 调用checkDevices
        val result = aoaCheckDevice.checkDevices()

        // Then: 应该返回false
        assertFalse(result, "已经连接时应该返回false")
    }

    /**
     * 测试checkDevices - 正常流程：有权限的手机设备但非AOA模式
     * 验证当检测到有权限的手机设备时，能正确初始化USB设备并尝试切换到AOA模式
     */
    @Test
    fun `checkDevices should process phone device with permission`() {
        // Given: 设置测试环境
        CommonData.isCheckingStart = true
        CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_OUT

        // 创建Mock设备和Context
        val mockDevice = createMockUsbDevice(VID_SAMSUNG, 0x1234)
        val mockContext = createMockContext()
        val mockUsbManager = mockk<UsbManager>()

        // 设置USB相关Mock
        every { mockContext.getSystemService(Context.USB_SERVICE) } returns mockUsbManager
        every { mockUsbManager.hasPermission(mockDevice) } returns true
        every { mockUsbManager.getDeviceList() } returns hashMapOf("device1" to mockDevice)

        // 重新初始化AOACheckDevice
        aoaCheckDevice.init(mockContext)

        // When: 调用checkDevices
        val result = aoaCheckDevice.checkDevices()

        // Then: 验证调用了权限检查
        verify { mockUsbManager.hasPermission(mockDevice) }
    }

    // ==================== initUsbDevice() USB设备初始化测试 ====================

    /**
     * 测试initUsbDevice - 异常情况：设备为null
     * 验证当传入null设备时，initUsbDevice能正确处理并返回false
     */
    @Test
    fun `initUsbDevice should return false when device is null`() {
        // When: 传入null设备
        val result = aoaCheckDevice.initUsbDevice(null)

        // Then: 应该返回false
        assertFalse(result, "设备为null时应该返回false")
    }

    /**
     * 测试initUsbDevice - 正常流程：已经是AOA设备
     * 验证当设备已经处于AOA模式时，initUsbDevice能正确识别并发送连接状态消息
     */
    @Test
    fun `initUsbDevice should handle AOA device correctly`() {
        // Given: 设置AOA设备
        val mockContext = createMockContext()
        val mockUsbManager = mockk<UsbManager>()
        val mockUsbDeviceConnection = mockk<UsbDeviceConnection>(relaxed = true)
        val mockUsbDevice = createMockUsbDevice(VID_ACCESSORY, 0x2D00) // AOA设备

        every { mockContext.getSystemService(Context.USB_SERVICE) } returns mockUsbManager
        every { mockUsbManager.openDevice(mockUsbDevice) } returns mockUsbDeviceConnection
        every { mockUsbDeviceConnection.claimInterface(any(), any()) } returns true

        // Mock HidUtil静态方法
        mockkStatic("com.autoai.avslinkhid.api.HidUtil")
        every { com.autoai.avslinkhid.api.HidUtil.setUsbDeviceConnection(any()) } returns true

        // 设置AOA设备特有的Mock
        mockkStatic("com.autoai.welinkapp.aoa.AoaLink")
        val mockAoaDevice = mockk<AoaDevice>()
        every { AoaLink.getAoaDevice() } returns mockAoaDevice

        aoaCheckDevice.init(mockContext)

        // When: 调用initUsbDevice
        val result = aoaCheckDevice.initUsbDevice(mockUsbDevice)

        // Then: 应该返回true（识别为AOA设备）
        assertTrue(result, "AOA设备应该返回true")
    }

    // ==================== resume() 设备检测恢复测试 ====================

    /**
     * 测试resume - 正常流程：检测到AOA设备并重置
     * 验证当恢复时检测到AOA设备时，能正确调用重置逻辑
     */
    @Test
    fun `resume should reset AOA device when detected`() {
        // Given: 设置有AOA设备的环境
        val mockDevice = createMockUsbDevice(VID_ACCESSORY, 0x2D00) // AOA设备
        val mockContext = createMockContext()
        val mockUsbManager = mockContext.getSystemService(Context.USB_SERVICE) as UsbManager

        every { mockUsbManager.deviceList } returns hashMapOf("device1" to mockDevice)

        // 直接设置CommonData状态
        CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_IN

        aoaCheckDevice.init(mockContext)

        // When: 调用resume
        aoaCheckDevice.resume()

        // Then: 验证设置了连接状态为断开
        assertEquals(CommonData.CONNECT_STATUS_OUT, CommonData.iCurrentConnectStatus, "AOA设备检测到时应该重置连接状态")
    }

    /**
     * 测试resume - 边界情况：设备列表为空
     * 验证当没有USB设备连接时，resume方法能正确处理空设备列表
     */
    @Test
    fun `resume should handle empty device list`() {
        // Given: Mock空设备列表
        val mockContext = createMockContext()
        val mockUsbManager = mockk<UsbManager>()

        every { mockContext.getSystemService(Context.USB_SERVICE) } returns mockUsbManager
        every { mockUsbManager.deviceList } returns hashMapOf()

        aoaCheckDevice.init(mockContext)

        // When: 调用resume
        aoaCheckDevice.resume()

        // Then: 方法应该正常完成，不抛出异常
        assertTrue(true, "空设备列表时resume应该正常完成")
    }

    // ==================== init() 方法测试 ====================

    /**
     * 测试init方法 - 正常流程：正确初始化Context和UsbManager
     * 验证init方法能正确设置Context、获取UsbManager服务并注册SystemUsbReceiver
     */
    @Test
    fun `init should setup context and usbManager correctly`() {
        // Given: 创建新的AOACheckDevice实例
        val freshInstance = AOACheckDevice.getInstance()
        val mockContext = createMockContext()
        val mockUsbManager = mockContext.getSystemService(Context.USB_SERVICE) as UsbManager

        // When: 调用init方法
        freshInstance.init(mockContext)

        // Then: 验证Context和UsbManager被正确设置
        // 通过调用需要这些依赖的方法来验证初始化是否成功
        val result = freshInstance.checkDevices()

        // 验证UsbManager被正确获取和使用
        verify { mockContext.getSystemService(Context.USB_SERVICE) }
        verify { mockUsbManager.deviceList }
    }

    /**
     * 测试init方法 - 异常情况：Context返回null UsbManager
     * 验证当Context.getSystemService返回null时，init方法能正确处理
     */
    @Test
    fun `init should handle null usbManager from context`() {
        // Given: Mock Context返回null UsbManager
        val mockContext = mockk<Context>(relaxed = true)
        every { mockContext.getSystemService(Context.USB_SERVICE) } returns null
        every { mockContext.registerReceiver(any(), any()) } returns null
        every { mockContext.registerReceiver(any(), any(), any()) } returns null
        every { mockContext.registerReceiver(any(), any(), any(), any()) } returns null
        every { mockContext.unregisterReceiver(any()) } just Runs

        val freshInstance = AOACheckDevice.getInstance()

        // When: 调用init方法
        freshInstance.init(mockContext)

        // Then: 后续调用应该能正确处理null UsbManager
        val result = freshInstance.checkDevices()
        assertFalse(result, "null UsbManager时checkDevices应该返回false")
    }

    // ==================== deinit() 方法测试 ====================

    /**
     * 测试deinit方法 - 正常流程：正确清理资源
     * 验证deinit方法能正确清理USB设备连接、注销接收器并清理AoaLink
     */
    @Test
    fun `deinit should cleanup resources correctly`() {
        // Given: 初始化AOACheckDevice
        val mockContext = createMockContext()
        aoaCheckDevice.init(mockContext)

        // Mock AoaLink静态方法
        mockkStatic("com.autoai.welinkapp.aoa.AoaLink")
        every { AoaLink.deinit() } just Runs

        // When: 调用deinit方法
        aoaCheckDevice.deinit()

        // Then: 验证AoaLink.deinit被调用
        verify { AoaLink.deinit() }

        // 验证后续调用checkDevices返回false（因为已经deinit）
        val result = aoaCheckDevice.checkDevices()
        assertFalse(result, "deinit后checkDevices应该返回false")
    }

    /**
     * 测试deinit方法 - 异常情况：重复调用deinit
     * 验证多次调用deinit方法不会导致异常或资源泄露
     */
    @Test
    fun `deinit should handle multiple calls gracefully`() {
        // Given: 初始化AOACheckDevice
        val mockContext = createMockContext()
        aoaCheckDevice.init(mockContext)

        // Mock AoaLink静态方法
        mockkStatic("com.autoai.welinkapp.aoa.AoaLink")
        every { AoaLink.deinit() } just Runs

        // When: 多次调用deinit方法
        aoaCheckDevice.deinit()
        aoaCheckDevice.deinit() // 第二次调用

        // Then: 应该不会抛出异常
        assertTrue(true, "多次调用deinit应该正常完成")

        // 验证AoaLink.deinit被调用了至少一次
        verify(atLeast = 1) { AoaLink.deinit() }
    }

    // ==================== dismissUsbDialog() 方法测试 ====================

    /**
     * 测试dismissUsbDialog - 正常调用
     * 验证dismissUsbDialog方法能正常调用而不抛出异常
     */
    @Test
    fun `dismissUsbDialog should execute without exception`() {
        // When: 调用dismissUsbDialog
        aoaCheckDevice.dismissUsbDialog()

        // Then: 应该正常完成，不抛出异常
        assertTrue(true, "dismissUsbDialog应该正常执行")
    }

    // ==================== 边界条件和异常处理测试 ====================

    /**
     * 测试异常处理 - USB设备权限请求
     * 验证当设备没有权限时，checkDevices能正确请求权限
     */
    @Test
    fun `checkDevices should request permission for device without permission`() {
        // Given: 设置无权限的手机设备
        CommonData.isCheckingStart = true
        CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_OUT

        val mockDevice = createMockUsbDevice(VID_SAMSUNG, 0x1234)
        val mockContext = createMockContext()
        val mockUsbManager = mockk<UsbManager>()

        every { mockContext.getSystemService(Context.USB_SERVICE) } returns mockUsbManager
        every { mockUsbManager.hasPermission(mockDevice) } returns false
        every { mockUsbManager.getDeviceList() } returns hashMapOf("device1" to mockDevice)
        every { mockUsbManager.requestPermission(any<UsbDevice>(), any()) } returns Unit

        // Mock PendingIntent创建
        mockkStatic(PendingIntent::class)
        val mockPendingIntent = mockk<PendingIntent>()
        every { PendingIntent.getBroadcast(any(), any(), any(), any()) } returns mockPendingIntent

        aoaCheckDevice.init(mockContext)

        // When: 调用checkDevices
        val result = aoaCheckDevice.checkDevices()

        // Then: 应该返回false并请求权限
        assertFalse(result, "无权限设备应该返回false")
        verify { mockUsbManager.requestPermission(mockDevice, mockPendingIntent) }
    }
}
