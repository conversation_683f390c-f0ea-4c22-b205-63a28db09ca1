package com.autoai.welinkapp.checkconnect

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.junit.jupiter.MockitoExtension
import org.junit.jupiter.api.Assertions.*

/**
 * Unit tests for CommonData inner classes
 * 
 * 测试CommonData内部类的所有功能，确保100%覆盖率
 */
@ExtendWith(MockitoExtension::class)
class CommonDataInnerClassTest {

    // ==================== DeviceType.Print 测试 ====================

    @Test
    fun `DeviceType Print toString should return correct string for DEVICE_TYPE_NONE`() {
        // When: 调用toString with DEVICE_TYPE_NONE
        val result = CommonData.DeviceType.Print.toString(CommonData.DEVICE_TYPE_NONE)

        // Then: 应该返回正确的字符串
        assertEquals("1 : DEVICE_TYPE_NONE", result)
    }

    @Test
    fun `DeviceType Print toString should return correct string for DEVICE_TYPE_AOA`() {
        // When: 调用toString with DEVICE_TYPE_AOA
        val result = CommonData.DeviceType.Print.toString(CommonData.DEVICE_TYPE_AOA)

        // Then: 应该返回正确的字符串
        assertEquals("2 : DEVICE_TYPE_AOA", result)
    }

    @Test
    fun `DeviceType Print toString should return correct string for DEVICE_TYPE_IDB`() {
        // When: 调用toString with DEVICE_TYPE_IDB
        val result = CommonData.DeviceType.Print.toString(CommonData.DEVICE_TYPE_IDB)

        // Then: 应该返回正确的字符串
        assertEquals("3 : DEVICE_TYPE_IDB", result)
    }

    @Test
    fun `DeviceType Print toString should return correct string for DEVICE_TYPE_EAP`() {
        // When: 调用toString with DEVICE_TYPE_EAP
        val result = CommonData.DeviceType.Print.toString(CommonData.DEVICE_TYPE_EAP)

        // Then: 应该返回正确的字符串
        assertEquals("6 : DEVICE_TYPE_EAP", result)
    }

    @Test
    fun `DeviceType Print toString should return correct string for DEVICE_TYPE_WIFI_ANDROID`() {
        // When: 调用toString with DEVICE_TYPE_WIFI_ANDROID
        val result = CommonData.DeviceType.Print.toString(CommonData.DEVICE_TYPE_WIFI_ANDROID)

        // Then: 应该返回正确的字符串
        assertEquals("5 : DEVICE_TYPE_WIFI_ANDROID", result)
    }

    @Test
    fun `DeviceType Print toString should return correct string for DEVICE_TYPE_WIFI_IOS`() {
        // When: 调用toString with DEVICE_TYPE_WIFI_IOS
        val result = CommonData.DeviceType.Print.toString(CommonData.DEVICE_TYPE_WIFI_IOS)

        // Then: 应该返回正确的字符串
        assertEquals("4 : DEVICE_TYPE_WIFI_IOS", result)
    }

    @Test
    fun `DeviceType Print toString should return correct string for DEVICE_TYPE_WIFI_HARMONY`() {
        // When: 调用toString with DEVICE_TYPE_WIFI_HARMONY
        val result = CommonData.DeviceType.Print.toString(CommonData.DEVICE_TYPE_WIFI_HARMONY)

        // Then: 应该返回正确的字符串
        assertEquals("7 : DEVICE_TYPE_WIFI_HARMONY", result)
    }

    @Test
    fun `DeviceType Print toString should return NA for unknown device type`() {
        // When: 调用toString with unknown device type
        val result = CommonData.DeviceType.Print.toString(999)

        // Then: 应该返回NA
        assertEquals("999 : NA", result)
    }

    // ==================== LinkStatus.Print 测试 ====================

    @Test
    fun `LinkStatus Print toString should return correct string for LINK_STATUS_NONE`() {
        // When: 调用toString with LINK_STATUS_NONE
        val result = CommonData.LinkStatus.Print.toString(CommonData.LINK_STATUS_NONE)

        // Then: 应该返回正确的字符串
        assertEquals("0 : LINK_STATUS_NONE: 未连接", result)
    }

    @Test
    fun `LinkStatus Print toString should return correct string for LINK_STATUS_FAIL`() {
        // When: 调用toString with LINK_STATUS_FAIL
        val result = CommonData.LinkStatus.Print.toString(CommonData.LINK_STATUS_FAIL)

        // Then: 应该返回正确的字符串
        assertEquals("3 : LINK_STATUS_FAIL: 连接失败", result)
    }

    @Test
    fun `LinkStatus Print toString should return correct string for LINK_STATUS_CONNECTING`() {
        // When: 调用toString with LINK_STATUS_CONNECTING
        val result = CommonData.LinkStatus.Print.toString(CommonData.LINK_STATUS_CONNECTING)

        // Then: 应该返回正确的字符串
        assertEquals("1 : LINK_STATUS_CONNECTING: 连接中", result)
    }

    @Test
    fun `LinkStatus Print toString should return correct string for LINK_STATUS_CONNECTED`() {
        // When: 调用toString with LINK_STATUS_CONNECTED
        val result = CommonData.LinkStatus.Print.toString(CommonData.LINK_STATUS_CONNECTED)

        // Then: 应该返回正确的字符串
        assertEquals("2 : LINK_STATUS_CONNECTED: 已连接", result)
    }

    @Test
    fun `LinkStatus Print toString should return correct string for LINK_STATUS_SCREEN_MIRRORING`() {
        // When: 调用toString with LINK_STATUS_SCREEN_MIRRORING
        val result = CommonData.LinkStatus.Print.toString(CommonData.LINK_STATUS_SCREEN_MIRRORING)

        // Then: 应该返回正确的字符串
        assertEquals("4 : LINK_STATUS_SCREEN_MIRRORING: 同意录屏权限，开始投屏", result)
    }

    @Test
    fun `LinkStatus Print toString should return NA for unknown link status`() {
        // When: 调用toString with unknown link status
        val result = CommonData.LinkStatus.Print.toString(999)

        // Then: 应该返回NA
        assertEquals("999 : NA", result)
    }

    // ==================== 集成测试 ====================

    @Test
    fun `DeviceType Print should handle all defined device types`() {
        // Given: 所有定义的设备类型
        val deviceTypes = listOf(
            CommonData.DEVICE_TYPE_NONE,
            CommonData.DEVICE_TYPE_AOA,
            CommonData.DEVICE_TYPE_EAP,
            CommonData.DEVICE_TYPE_WIFI_ANDROID,
            CommonData.DEVICE_TYPE_WIFI_IOS,
            CommonData.DEVICE_TYPE_WIFI_HARMONY,
            CommonData.DEVICE_TYPE_IDB
        )

        // When & Then: 测试所有设备类型
        for (deviceType in deviceTypes) {
            val result = CommonData.DeviceType.Print.toString(deviceType)
            assertNotNull(result, "结果不应该为null")
            assertTrue(result.contains(":"), "结果应该包含冒号分隔符")
            assertTrue(result.startsWith(deviceType.toString()), "结果应该以设备类型值开始")
        }
    }

    @Test
    fun `LinkStatus Print should handle all defined link statuses`() {
        // Given: 所有定义的链接状态
        val linkStatuses = listOf(
            CommonData.LINK_STATUS_NONE,
            CommonData.LINK_STATUS_FAIL,
            CommonData.LINK_STATUS_CONNECTING,
            CommonData.LINK_STATUS_CONNECTED,
            CommonData.LINK_STATUS_SCREEN_MIRRORING
        )

        // When & Then: 测试所有链接状态
        for (linkStatus in linkStatuses) {
            val result = CommonData.LinkStatus.Print.toString(linkStatus)
            assertNotNull(result, "结果不应该为null")
            assertTrue(result.contains(":"), "结果应该包含冒号分隔符")
            assertTrue(result.startsWith(linkStatus.toString()), "结果应该以链接状态值开始")
        }
    }

    @Test
    fun `Print classes should handle edge cases`() {
        // When & Then: 测试边界情况
        
        // 测试负数
        val negativeDeviceResult = CommonData.DeviceType.Print.toString(-1)
        assertEquals("-1 : NA", negativeDeviceResult)
        
        val negativeLinkResult = CommonData.LinkStatus.Print.toString(-1)
        assertEquals("-1 : NA", negativeLinkResult)
        
        // 测试大数值
        val largeDeviceResult = CommonData.DeviceType.Print.toString(Integer.MAX_VALUE)
        assertEquals("${Integer.MAX_VALUE} : NA", largeDeviceResult)
        
        val largeLinkResult = CommonData.LinkStatus.Print.toString(Integer.MAX_VALUE)
        assertEquals("${Integer.MAX_VALUE} : NA", largeLinkResult)
    }

    @Test
    fun `Print classes should be consistent in format`() {
        // When: 调用不同的Print方法
        val deviceResult = CommonData.DeviceType.Print.toString(CommonData.DEVICE_TYPE_AOA)
        val linkResult = CommonData.LinkStatus.Print.toString(CommonData.LINK_STATUS_CONNECTED)

        // Then: 格式应该一致
        assertTrue(deviceResult.matches(Regex("\\d+ : .+")), "设备类型格式应该是 '数字 : 描述'")
        assertTrue(linkResult.matches(Regex("\\d+ : .+")), "链接状态格式应该是 '数字 : 描述'")
    }
}
