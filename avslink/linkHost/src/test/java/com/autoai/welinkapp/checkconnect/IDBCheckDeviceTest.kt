package com.autoai.welinkapp.checkconnect

import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.hardware.usb.UsbConfiguration
import android.hardware.usb.UsbDevice
import android.hardware.usb.UsbInterface
import android.hardware.usb.UsbManager
import android.os.Build
import com.autoai.welinkapp.idb.ADBUtil
import com.autoai.welinkapp.idb.IdbLink
import io.mockk.*
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.junit.jupiter.MockitoExtension
import org.junit.jupiter.api.Assertions.*
import java.lang.reflect.Method

/**
 * Unit tests for IDBCheckDevice class
 * 
 * 测试IDBCheckDevice类的所有功能，确保100%覆盖率
 * 主要测试iOS设备的IDB连接检测功能
 */
@ExtendWith(MockitoExtension::class)
class IDBCheckDeviceTest {

    private lateinit var idbCheckDevice: IDBCheckDevice
    private lateinit var mockContext: Context
    private lateinit var mockUsbManager: UsbManager
    private lateinit var mockUsbDevice: UsbDevice

    companion object {
        // Constants from IDBCheckDevice class
        private const val VID_APPLE = 0x5ac
        private const val PID_RANGE_LOW = 0x1290
        private const val PID_RANGE_MAX = 0x12af
        private const val STORAGE_INTERFACE_CLASS = 0x08
        private const val STORAGE_INTERFACE_SUBCLASS = 0x06
        private const val STORAGE_INTERFACE_PROTOCOL = 0x50
        private const val STORAGE_CONFIG_INTERFACE_COUNT = 1
    }

    @BeforeEach
    fun setUp() {
        // Mock静态依赖
        mockkStatic("com.autoai.common.util.LogUtil")
        justRun { com.autoai.common.util.LogUtil.d(any(), any()) }
        justRun { com.autoai.common.util.LogUtil.i(any(), any()) }
        justRun { com.autoai.common.util.LogUtil.e(any(), any()) }

        mockkStatic("com.autoai.welinkapp.idb.IdbLink")
        every { IdbLink.isIdbModel() } returns true

        mockkStatic("com.autoai.welinkapp.idb.ADBUtil")
        every { ADBUtil.execute(any(), any()) } returns arrayListOf()
        every { ADBUtil.execute2(any()) } returns Unit

        mockkStatic("com.autoai.common.util.ThreadUtils")
        justRun { com.autoai.common.util.ThreadUtils.postOnBackgroundThread(any()) }

        // 获取IDBCheckDevice实例
        idbCheckDevice = IDBCheckDevice.getInstance()

        // 创建Mock对象
        mockContext = mockk(relaxed = true)
        mockUsbManager = mockk(relaxed = true)
        mockUsbDevice = mockk(relaxed = true)

        // 设置Context返回UsbManager
        every { mockContext.getSystemService(Context.USB_SERVICE) } returns mockUsbManager
    }

    @AfterEach
    fun tearDown() {
        // 清理所有mock
        unmockkAll()
        
        // 重置CommonData状态
        CommonData.isCheckingStart = false
        CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_OUT
        CommonData.strDeviceSerialNum = ""
    }

    // ==================== Helper Methods ====================

    /**
     * 创建Mock UsbDevice
     */
    private fun createMockUsbDevice(
        vendorId: Int, 
        productId: Int, 
        serialNumber: String? = "test_serial",
        interfaceClass: Int = 0,
        interfaceSubclass: Int = 0,
        interfaceProtocol: Int = 0,
        interfaceCount: Int = 1
    ): UsbDevice {
        val device = mockk<UsbDevice>()
        val usbInterface = mockk<UsbInterface>()
        val usbConfiguration = mockk<UsbConfiguration>()

        every { device.vendorId } returns vendorId
        every { device.productId } returns productId
        every { device.serialNumber } returns serialNumber
        every { device.getInterface(0) } returns usbInterface
        every { device.getConfiguration(0) } returns usbConfiguration

        every { usbInterface.interfaceClass } returns interfaceClass
        every { usbInterface.interfaceSubclass } returns interfaceSubclass
        every { usbInterface.interfaceProtocol } returns interfaceProtocol
        every { usbConfiguration.interfaceCount } returns interfaceCount

        return device
    }

    /**
     * 使用反射调用私有方法
     */
    private fun callPrivateMethod(methodName: String, vararg args: Any?): Any? {
        return try {
            val paramTypes = when (methodName) {
                "filterDevice" -> arrayOf(UsbDevice::class.java)
                "isIosDevice" -> arrayOf(UsbDevice::class.java)
                "startIDBService" -> arrayOf()
                "killIDBService" -> arrayOf()
                "checkIosDevice" -> arrayOf()
                else -> args.map { it?.javaClass ?: Any::class.java }.toTypedArray()
            }
            val method: Method = IDBCheckDevice::class.java.getDeclaredMethod(methodName, *paramTypes)
            method.isAccessible = true
            method.invoke(idbCheckDevice, *args)
        } catch (e: Exception) {
            println("Failed to call $methodName via reflection: ${e.message}")
            e.printStackTrace()
            null
        }
    }

    // ==================== getInstance() 单例模式测试 ====================

    @Test
    fun `getInstance should return same instance`() {
        // When: 获取两次实例
        val instance1 = IDBCheckDevice.getInstance()
        val instance2 = IDBCheckDevice.getInstance()

        // Then: 验证是同一个实例
        assertSame(instance1, instance2, "getInstance应该返回同一个实例")
        assertNotNull(instance1, "实例不应该为null")
    }

    @Test
    fun `getInstance should be thread safe`() {
        val instances = arrayOfNulls<IDBCheckDevice>(2)
        val threads = arrayOfNulls<Thread>(2)

        // 创建两个线程同时获取实例
        for (i in 0..1) {
            threads[i] = Thread {
                instances[i] = IDBCheckDevice.getInstance()
            }
        }

        // 启动线程并等待完成
        threads.forEach { it?.start() }
        threads.forEach { it?.join() }

        // 验证两个线程获取的是同一个实例
        assertSame(instances[0], instances[1], "多线程环境下应该返回同一个实例")
        assertNotNull(instances[0], "实例不应该为null")
    }

    // ==================== init() 和 deinit() 测试 ====================

    @Test
    fun `init should setup context and usbManager correctly`() {
        // When: 调用init方法
        idbCheckDevice.init(mockContext)

        // Then: 验证Context和UsbManager被正确设置
        verify { mockContext.getSystemService(Context.USB_SERVICE) }
    }

    @Test
    fun `deinit should cleanup resources correctly`() {
        // Given: 初始化IDBCheckDevice
        idbCheckDevice.init(mockContext)

        // When: 调用deinit方法
        idbCheckDevice.deinit()

        // Then: 方法应该正常完成，不抛出异常
        assertTrue(true, "deinit应该正常完成")
    }

    // ==================== checkDevices() 测试 ====================

    @Test
    fun `checkDevices should return false when context is null`() {
        // Given: 未初始化的IDBCheckDevice
        val freshInstance = IDBCheckDevice.getInstance()

        // When: 调用checkDevices
        val result = freshInstance.checkDevices()

        // Then: 应该返回false
        assertFalse(result, "context为null时应该返回false")
    }

    @Test
    fun `checkDevices should return false when IdbLink is not IdbModel`() {
        // Given: IdbLink.isIdbModel()返回false
        every { IdbLink.isIdbModel() } returns false
        idbCheckDevice.init(mockContext)

        // When: 调用checkDevices
        val result = idbCheckDevice.checkDevices()

        // Then: 应该返回false
        assertFalse(result, "IdbLink不是IdbModel时应该返回false")
    }

    @Test
    fun `checkDevices should return false when checking not started`() {
        // Given: 设置检查未开始
        CommonData.isCheckingStart = false
        idbCheckDevice.init(mockContext)

        // When: 调用checkDevices
        val result = idbCheckDevice.checkDevices()

        // Then: 应该返回false并设置需要连接的类型
        assertFalse(result, "检查未开始时应该返回false")
        assertEquals(CommonData.DEVICE_TYPE_IDB, CommonData.iNeedConnectType, "应该设置需要连接的类型为IDB")
    }

    @Test
    fun `checkDevices should return false when already connected`() {
        // Given: 设置已连接状态
        CommonData.isCheckingStart = true
        CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_IN
        idbCheckDevice.init(mockContext)

        // When: 调用checkDevices
        val result = idbCheckDevice.checkDevices()

        // Then: 应该返回false
        assertFalse(result, "已连接时应该返回false")
        assertEquals(CommonData.DEVICE_TYPE_IDB, CommonData.iNeedConnectType, "应该设置需要连接的类型为IDB")
    }

    @Test
    fun `checkDevices should process device with permission`() {
        // Given: 设置正常检查环境
        CommonData.isCheckingStart = true
        CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_OUT
        
        val device = createMockUsbDevice(VID_APPLE, 0x1295) // 在有效范围内
        every { mockUsbManager.deviceList } returns hashMapOf("device1" to device)
        every { mockUsbManager.hasPermission(device) } returns true
        
        idbCheckDevice.init(mockContext)

        // When: 调用checkDevices
        val result = idbCheckDevice.checkDevices()

        // Then: 应该返回true
        assertTrue(result, "有权限的iOS设备应该返回true")
    }

    @Test
    fun `checkDevices should request permission for device without permission`() {
        // Given: 设置无权限的设备
        CommonData.isCheckingStart = true
        CommonData.iCurrentConnectStatus = CommonData.CONNECT_STATUS_OUT
        
        val device = createMockUsbDevice(VID_APPLE, 0x1295)
        every { mockUsbManager.deviceList } returns hashMapOf("device1" to device)
        every { mockUsbManager.hasPermission(device) } returns false
        every { mockUsbManager.requestPermission(any<UsbDevice>(), any()) } returns Unit

        // Mock PendingIntent创建
        mockkStatic(PendingIntent::class)
        val mockPendingIntent = mockk<PendingIntent>()
        every { PendingIntent.getBroadcast(any(), any(), any(), any()) } returns mockPendingIntent

        idbCheckDevice.init(mockContext)

        // When: 调用checkDevices
        val result = idbCheckDevice.checkDevices()

        // Then: 应该返回false并请求权限
        assertFalse(result, "无权限设备应该返回false")
        verify { mockUsbManager.requestPermission(device, mockPendingIntent) }
    }

    // ==================== filterDevice() 私有方法测试 ====================

    @Test
    fun `filterDevice should return true for null device`() {
        // When: 传入null设备
        val result = callPrivateMethod("filterDevice", null)

        // Then: 应该返回true（被过滤）
        assertTrue(result as Boolean, "null设备应该被过滤")
    }

    @Test
    fun `filterDevice should return true for device with null serial number`() {
        // Given: 设备序列号为null
        val device = createMockUsbDevice(VID_APPLE, 0x1295, null)

        // When: 调用filterDevice
        val result = callPrivateMethod("filterDevice", device)

        // Then: 应该返回true（被过滤）
        assertTrue(result as Boolean, "序列号为null的设备应该被过滤")
    }

    @Test
    fun `filterDevice should return true for device with different serial number`() {
        // Given: 设置不同的序列号
        CommonData.strDeviceSerialNum = "expected_serial"
        val device = createMockUsbDevice(VID_APPLE, 0x1295, "different_serial")

        // When: 调用filterDevice
        val result = callPrivateMethod("filterDevice", device)

        // Then: 应该返回true（被过滤）
        assertTrue(result as Boolean, "序列号不匹配的设备应该被过滤")
    }

    @Test
    fun `filterDevice should return true for USB storage device`() {
        // Given: USB存储设备
        val device = createMockUsbDevice(
            VID_APPLE, 0x1295, "test_serial",
            STORAGE_INTERFACE_CLASS, STORAGE_INTERFACE_SUBCLASS,
            STORAGE_INTERFACE_PROTOCOL, STORAGE_CONFIG_INTERFACE_COUNT
        )

        // When: 调用filterDevice
        val result = callPrivateMethod("filterDevice", device)

        // Then: 应该返回true（被过滤）
        assertTrue(result as Boolean, "USB存储设备应该被过滤")
    }

    @Test
    fun `filterDevice should return false for valid device`() {
        // Given: 有效的设备
        CommonData.strDeviceSerialNum = ""  // 空字符串表示不检查序列号
        val device = createMockUsbDevice(VID_APPLE, 0x1295, "test_serial")

        // When: 调用filterDevice
        val result = callPrivateMethod("filterDevice", device)

        // Then: 应该返回false（不被过滤）
        assertFalse(result as Boolean, "有效设备不应该被过滤")
    }

    // ==================== initUsbDevice() 测试 ====================

    @Test
    fun `initUsbDevice should return false for null device`() {
        // Given: 初始化IDBCheckDevice
        idbCheckDevice.init(mockContext)

        // When: 传入null设备
        val result = idbCheckDevice.initUsbDevice(null)

        // Then: 应该返回false
        assertFalse(result, "null设备应该返回false")
    }

    @Test
    fun `initUsbDevice should return false for non-iOS device`() {
        // Given: 非iOS设备
        val device = createMockUsbDevice(0x1234, 0x5678) // 非Apple厂商ID
        idbCheckDevice.init(mockContext)

        // When: 调用initUsbDevice
        val result = idbCheckDevice.initUsbDevice(device)

        // Then: 应该返回false
        assertFalse(result, "非iOS设备应该返回false")
    }

    @Test
    fun `initUsbDevice should return true for valid iOS device`() {
        // Given: 有效的iOS设备
        val device = createMockUsbDevice(VID_APPLE, 0x1295)
        idbCheckDevice.init(mockContext)

        // When: 调用initUsbDevice
        val result = idbCheckDevice.initUsbDevice(device)

        // Then: 应该返回true
        assertTrue(result, "有效的iOS设备应该返回true")
    }

    // ==================== isIosDevice() 私有方法测试 ====================

    @Test
    fun `isIosDevice should return true for Apple device in valid PID range`() {
        // Given: Apple设备，产品ID在有效范围内
        val device = createMockUsbDevice(VID_APPLE, 0x1295) // 在范围内

        // When: 调用isIosDevice
        val result = callPrivateMethod("isIosDevice", device)

        // Then: 应该返回true
        assertTrue(result as Boolean, "Apple设备在有效PID范围内应该返回true")
    }

    @Test
    fun `isIosDevice should return false for Apple device outside PID range`() {
        // Given: Apple设备，产品ID超出范围
        val device = createMockUsbDevice(VID_APPLE, 0x1200) // 超出范围

        // When: 调用isIosDevice
        val result = callPrivateMethod("isIosDevice", device)

        // Then: 应该返回false
        assertFalse(result as Boolean, "Apple设备超出PID范围应该返回false")
    }

    @Test
    fun `isIosDevice should return false for non-Apple device`() {
        // Given: 非Apple设备
        val device = createMockUsbDevice(0x1234, 0x1295)

        // When: 调用isIosDevice
        val result = callPrivateMethod("isIosDevice", device)

        // Then: 应该返回false
        assertFalse(result as Boolean, "非Apple设备应该返回false")
    }

    @Test
    fun `isIosDevice should test PID range boundaries`() {
        // Test lower boundary
        val deviceLow = createMockUsbDevice(VID_APPLE, PID_RANGE_LOW)
        val resultLow = callPrivateMethod("isIosDevice", deviceLow)
        assertTrue(resultLow as Boolean, "PID下边界应该返回true")

        // Test upper boundary
        val deviceHigh = createMockUsbDevice(VID_APPLE, PID_RANGE_MAX)
        val resultHigh = callPrivateMethod("isIosDevice", deviceHigh)
        assertTrue(resultHigh as Boolean, "PID上边界应该返回true")

        // Test below lower boundary
        val deviceBelowLow = createMockUsbDevice(VID_APPLE, PID_RANGE_LOW - 1)
        val resultBelowLow = callPrivateMethod("isIosDevice", deviceBelowLow)
        assertFalse(resultBelowLow as Boolean, "PID低于下边界应该返回false")

        // Test above upper boundary
        val deviceAboveHigh = createMockUsbDevice(VID_APPLE, PID_RANGE_MAX + 1)
        val resultAboveHigh = callPrivateMethod("isIosDevice", deviceAboveHigh)
        assertFalse(resultAboveHigh as Boolean, "PID高于上边界应该返回false")
    }

    // ==================== isCheckDeviceRunning() 测试 ====================

    @Test
    fun `isCheckDeviceRunning should return false initially`() {
        // When: 调用isCheckDeviceRunning
        val result = idbCheckDevice.isCheckDeviceRunning

        // Then: 应该返回false
        assertFalse(result, "初始状态应该返回false")
    }

    @Test
    fun `isCheckDeviceRunning should return true after successful initUsbDevice`() {
        // Given: 成功初始化USB设备
        val device = createMockUsbDevice(VID_APPLE, 0x1295)
        idbCheckDevice.init(mockContext)
        idbCheckDevice.initUsbDevice(device)

        // When: 调用isCheckDeviceRunning
        val result = idbCheckDevice.isCheckDeviceRunning

        // Then: 应该返回true
        assertTrue(result, "成功初始化USB设备后应该返回true")
    }

    // ==================== deinitUsbDevice() 测试 ====================

    @Test
    fun `deinitUsbDevice should cleanup USB device`() {
        // Given: 已初始化USB设备
        val device = createMockUsbDevice(VID_APPLE, 0x1295)
        idbCheckDevice.init(mockContext)
        idbCheckDevice.initUsbDevice(device)

        // When: 调用deinitUsbDevice
        idbCheckDevice.deinitUsbDevice()

        // Then: isCheckDeviceRunning应该返回false
        assertFalse(idbCheckDevice.isCheckDeviceRunning, "清理后应该返回false")
    }

    // ==================== 私有方法测试 ====================

    @Test
    fun `startIDBService should handle ADBUtil execution`() {
        // Given: Mock ADBUtil返回足够的行数
        every { ADBUtil.execute(any(), any()) } returns arrayListOf("line1", "line2", "line3")

        // When: 调用startIDBService
        val result = callPrivateMethod("startIDBService")

        // Then: 应该返回true
        assertTrue(result as Boolean, "ADBUtil返回足够行数时应该返回true")
    }

    @Test
    fun `startIDBService should return false when insufficient lines`() {
        // Given: Mock ADBUtil返回不足的行数
        every { ADBUtil.execute(any(), any()) } returns arrayListOf("line1")

        // When: 调用startIDBService
        val result = callPrivateMethod("startIDBService")

        // Then: 应该返回false
        assertFalse(result as Boolean, "ADBUtil返回不足行数时应该返回false")
    }

    @Test
    fun `killIDBService should process IDB processes correctly`() {
        // Given: Mock ADBUtil返回包含idb进程的行
        every { ADBUtil.execute(any(), any()) } returns arrayListOf(
            "  1234 idb",
            "  5678 idb"
        )

        // When: 调用killIDBService
        callPrivateMethod("killIDBService")

        // Then: 方法应该正常完成
        assertTrue(true, "killIDBService应该正常完成")
    }
}
