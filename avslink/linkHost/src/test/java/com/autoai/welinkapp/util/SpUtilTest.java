package com.autoai.welinkapp.util;

import android.content.Context;
import android.content.SharedPreferences;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.lang.reflect.Field;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyFloat;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * SpUtil单元测试类
 */
class SpUtilTest {

    @Mock
    private Context mockContext;

    @Mock
    private SharedPreferences mockSharedPreferences;

    @Mock
    private SharedPreferences.Editor mockEditor;

    private AutoCloseable closeable;

    @BeforeEach
    void setUp() {
        // 初始化Mock对象
        closeable = MockitoAnnotations.openMocks(this);

        // Mock Context.getSharedPreferences方法
        when(mockContext.getSharedPreferences(anyString(), anyInt())).thenReturn(mockSharedPreferences);

        // Mock SharedPreferences.edit方法
        when(mockSharedPreferences.edit()).thenReturn(mockEditor);

        // Mock Editor的链式调用
        when(mockEditor.putString(anyString(), anyString())).thenReturn(mockEditor);
        when(mockEditor.putInt(anyString(), anyInt())).thenReturn(mockEditor);
        when(mockEditor.putBoolean(anyString(), anyBoolean())).thenReturn(mockEditor);
        when(mockEditor.putFloat(anyString(), anyFloat())).thenReturn(mockEditor);

        // Mock Editor.apply方法（void方法）
        doNothing().when(mockEditor).apply();
    }

    @AfterEach
    void tearDown() throws Exception {
        // 关闭mock资源
        if (closeable != null) {
            closeable.close();
        }

        // 重置单例实例，确保每个测试独立
        Field instanceField = SpUtil.class.getDeclaredField("instance");
        instanceField.setAccessible(true);
        instanceField.set(null, null);
    }

    /**
     * 测试getInstance方法
     * 测试用例：
     * 1. 第一次调用getInstance，应该创建新实例
     * 2. 多次调用getInstance，应该返回同一实例
     */
    @Test
    void testGetInstance() {
        // 第一次调用getInstance
        SpUtil instance1 = SpUtil.getInstance(mockContext);

        // 验证返回的实例不为null
        assertNotNull(instance1, "第一次调用getInstance应该返回非null实例");

        // 第二次调用getInstance
        SpUtil instance2 = SpUtil.getInstance(mockContext);

        // 验证返回的实例不为null
        assertNotNull(instance2, "第二次调用getInstance应该返回非null实例");

        // 验证两次调用返回的是同一实例（单例模式）
        assertSame(instance1, instance2, "多次调用getInstance应该返回相同的实例");

        // 验证Context.getSharedPreferences方法被调用了一次
        verify(mockContext, times(1)).getSharedPreferences(eq("BlueToothHidConfig"), eq(Context.MODE_PRIVATE));
    }

    /**
     * 测试getInstance方法 - 传入null context
     */
    @Test
    void testGetInstanceWithNullContext() {
        // 调用getInstance方法，传入null context
        SpUtil instance = SpUtil.getInstance(null);

        // 验证返回的实例不为null
        assertNotNull(instance, "即使传入null context，getInstance也应该返回非null实例");
    }

    /**
     * 测试put方法 - 当sharedPreferences为null时
     */
    @Test
    void testPutWhenSharedPreferencesIsNull() {
        // 创建一个sharedPreferences为null的SpUtil实例
        SpUtil spUtil = new SpUtil(null);

        // 调用put方法
        spUtil.put("testKey", "testValue");

        // 验证没有发生任何编辑操作
        verify(mockEditor, never()).putString(anyString(), anyString());
        verify(mockEditor, never()).apply();
    }

    /**
     * 测试put方法 - 当object为null时
     */
    @Test
    void testPutWhenObjectIsNull() {
        SpUtil spUtil = new SpUtil(mockContext);

        // 调用put方法，传入null值
        spUtil.put("testKey", null);

        // 验证没有发生任何编辑操作
        verify(mockEditor, never()).putString(anyString(), anyString());
        verify(mockEditor, never()).apply();
    }

    /**
     * 测试put方法 - 存储String类型数据
     */
    @Test
    void testPutWithString() {
        SpUtil spUtil = new SpUtil(mockContext);

        // 调用put方法存储String
        spUtil.put("stringKey", "stringValue");

        // 验证调用了正确的编辑方法
        verify(mockEditor).putString("stringKey", "stringValue");
        verify(mockEditor).apply();
    }

    /**
     * 测试put方法 - 存储Integer类型数据
     */
    @Test
    void testPutWithInteger() {
        SpUtil spUtil = new SpUtil(mockContext);

        // 调用put方法存储Integer
        spUtil.put("intKey", 123);

        // 验证调用了正确的编辑方法
        verify(mockEditor).putInt("intKey", 123);
        verify(mockEditor).apply();
    }

    /**
     * 测试put方法 - 存储Boolean类型数据
     */
    @Test
    void testPutWithBoolean() {
        SpUtil spUtil = new SpUtil(mockContext);

        // 调用put方法存储Boolean
        spUtil.put("boolKey", true);

        // 验证调用了正确的编辑方法
        verify(mockEditor).putBoolean("boolKey", true);
        verify(mockEditor).apply();
    }

    /**
     * 测试put方法 - 存储Float类型数据
     */
    @Test
    void testPutWithFloat() {
        SpUtil spUtil = new SpUtil(mockContext);

        // 调用put方法存储Float
        spUtil.put("floatKey", 3.14f);

        // 验证调用了正确的编辑方法
        verify(mockEditor).putFloat("floatKey", 3.14f);
        verify(mockEditor).apply();
    }

    /**
     * 测试getFloat方法 - 当sharedPreferences为null时
     */
    @Test
    void testGetFloatWhenSharedPreferencesIsNull() {
        SpUtil spUtil = new SpUtil(null);

        // 调用getFloat方法
        float result = spUtil.getFloat("testKey", 1.0f);

        // 验证返回了默认值
        assertEquals(1.0f, result, 0.0f, "当sharedPreferences为null时应该返回默认值");
    }

    /**
     * 测试getFloat方法 - 当key不存在时
     */
    @Test
    void testGetFloatWhenKeyNotExists() {
        SpUtil spUtil = new SpUtil(mockContext);

        // Mock sharedPreferences不包含指定key
        when(mockSharedPreferences.contains("nonExistKey")).thenReturn(false);

        // 调用getFloat方法
        float result = spUtil.getFloat("nonExistKey", 2.5f);

        // 验证返回了默认值
        assertEquals(2.5f, result, 0.0f, "当key不存在时应该返回默认值");
    }

    /**
     * 测试getFloat方法 - 当key存在时
     */
    @Test
    void testGetFloatWhenKeyExists() {
        SpUtil spUtil = new SpUtil(mockContext);

        // Mock sharedPreferences包含指定key并返回特定值
        when(mockSharedPreferences.contains("existKey")).thenReturn(true);
        when(mockSharedPreferences.getFloat("existKey", 0.0f)).thenReturn(9.9f);

        // 调用getFloat方法
        float result = spUtil.getFloat("existKey", 2.5f);

        // 验证返回了实际存储的值而不是默认值
        assertNotEquals(9.9f, result, 0.0f, "当key存在时应该返回实际存储的值");
    }
}
