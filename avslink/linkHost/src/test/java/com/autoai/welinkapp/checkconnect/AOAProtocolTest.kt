package com.autoai.welinkapp.checkconnect

import android.content.Context
import android.hardware.usb.UsbConstants
import android.hardware.usb.UsbDevice
import android.hardware.usb.UsbDeviceConnection
import android.hardware.usb.UsbInterface
import android.hardware.usb.UsbManager
import com.autoai.welinkapp.checkconnect.AOACheckDevice
import com.autoai.welinkapp.aoa.AoaDevice
import com.autoai.welinkapp.aoa.AoaLink
import io.mockk.*
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.lang.reflect.Field


/**
 * AOA Protocol Test Class
 *
 * Tests for Android Open Accessory protocol implementation including:
 * - switchToAOA complete protocol flow
 * - AOA protocol version handling
 * - AOA identity exchange (manufacturer, model, description, version, URI, serial)
 * - Protocol error handling and edge cases
 * - AOA device detection by vendor/product ID
 */
class AOAProtocolTest : AOACheckDeviceTestBase() {

    // 定义本地常量，避免访问基类 protected 成员的问题
    companion object {
        private const val VID_SAMSUNG = 0x04e8
        private const val VID_APPLE = 0x05ac
        private const val VID_ACCESSORY = 0x18D1
        private const val PID_ACCESSORY = 0x2D
        private const val AOA_GET_PROTOCOL = 51
        private const val AOA_SEND_IDENT = 52
        private const val AOA_START_ACCESSORY = 53
        private const val USB_CONTROL_TRANSFER_TIMEOUT = 5000
    }



    // ====== initUsbDevice Basic Tests ======

    @Test
    fun `initUsbDevice should fail when USB device is null`() {
        // Given: 设置环境但不设置USB设备（保持为null）
        val (_, mockUsbManager, _) = setupMockForInitUsbDevice()
        setUsbManager(mockUsbManager)

        // Mock AoaLink.getAoaDevice()返回非null
        mockkStatic("com.autoai.welinkapp.aoa.AoaLink")
        val mockAoaDevice = mockk<AoaDevice>()
        every { AoaLink.getAoaDevice() } returns mockAoaDevice

        // When: 尝试调用initUsbDevice
        val result = aoaCheckDevice.initUsbDevice(null)

        // Then: 应该失败，因为设备为null
        assertFalse(result, "USB设备为null时initUsbDevice应该失败")
    }

    @Test
    fun `initUsbDevice should fail when AoaDevice is null`() {
        // Given: 设置USB设备但AoaDevice为null
        val (_, mockUsbManager, _) = setupMockForInitUsbDevice()
        val mockUsbDevice = createMockUsbDevice(VID_SAMSUNG, 0x1234)

        // Mock AoaLink.getAoaDevice()返回null
        mockkStatic("com.autoai.welinkapp.aoa.AoaLink")
        every { AoaLink.getAoaDevice() } returns null

        setUsbManager(mockUsbManager)

        // When: 尝试初始化USB设备（会调用内部的switchToAOA）
        val result = aoaCheckDevice.initUsbDevice(mockUsbDevice)

        // Then: 应该失败，因为AoaDevice为null
        assertFalse(result, "AoaDevice为null时initUsbDevice应该失败")
    }

    // ====== AOA Protocol Version Handling Tests ======

    @Test
    fun `initUsbDevice should fail when get protocol version fails`() {
        // Given: 设置完整的Mock环境
        val (mockContext, mockUsbManager, mockUsbDeviceConnection) = setupMockForInitUsbDevice()
        val mockUsbDevice = createMockUsbDevice(VID_SAMSUNG, 0x1234)

        // Mock AoaLink和AoaDevice
        mockkStatic("com.autoai.welinkapp.aoa.AoaLink")
        val mockAoaDevice = createMockAoaDevice()
        every { AoaLink.getAoaDevice() } returns mockAoaDevice
        // Mock controlTransfer获取协议版本失败（返回负值）
        every {
            mockUsbDeviceConnection.controlTransfer(
                UsbConstants.USB_DIR_IN or UsbConstants.USB_TYPE_VENDOR,
                AOA_GET_PROTOCOL,
                0,
                0,
                any<ByteArray>(),
                2,
                USB_CONTROL_TRANSFER_TIMEOUT
            )
        } returns -1

        setUsbManager(mockUsbManager)

        // When: 初始化USB设备（会调用switchToAOA）
        val result = aoaCheckDevice.initUsbDevice(mockUsbDevice)

        // Then: 应该失败，因为获取协议版本失败
        assertFalse(result, "获取AOA协议版本失败时initUsbDevice应该失败")
    }

    @Test
    fun `initUsbDevice should fail when AOA version is unsupported (version 0)`() {
        // Given: 设置Mock环境，协议版本返回0（不支持）
        val (mockContext, mockUsbManager, mockUsbDeviceConnection) = setupMockForInitUsbDevice()
        val mockUsbDevice = createMockUsbDevice(VID_SAMSUNG, 0x1234)

        mockkStatic("com.autoai.welinkapp.aoa.AoaLink")
        val mockAoaDevice = createMockAoaDevice()
        every { AoaLink.getAoaDevice() } returns mockAoaDevice
        

        // Mock controlTransfer返回版本0（不支持的版本）
        every {
            mockUsbDeviceConnection.controlTransfer(
                UsbConstants.USB_DIR_IN or UsbConstants.USB_TYPE_VENDOR,
                AOA_GET_PROTOCOL,
                0,
                0,
                any<ByteArray>(),
                2,
                USB_CONTROL_TRANSFER_TIMEOUT
            )
        } answers {
            val buffer = arg<ByteArray>(4)
            buffer[0] = 0  // 版本低位
            buffer[1] = 0  // 版本高位，组合后版本为0
            2  // 返回成功读取2字节
        }

        setUsbManager(mockUsbManager)

        // When: 初始化USB设备
        val result = aoaCheckDevice.initUsbDevice(mockUsbDevice)

        // Then: 应该失败，因为版本0不支持
        assertFalse(result, "AOA协议版本0不支持时initUsbDevice应该失败")
    }

    @Test
    fun `initUsbDevice should fail when AOA version is too high (version 3)`() {
        // Given: 设置Mock环境，协议版本返回3（过高）
        val (mockContext, mockUsbManager, mockUsbDeviceConnection) = setupMockForInitUsbDevice()
        val mockUsbDevice = createMockUsbDevice(VID_SAMSUNG, 0x1234)

        mockkStatic("com.autoai.welinkapp.aoa.AoaLink")
        val mockAoaDevice = createMockAoaDevice()
        every { AoaLink.getAoaDevice() } returns mockAoaDevice
        

        // Mock controlTransfer返回版本3（过高的版本）
        every {
            mockUsbDeviceConnection.controlTransfer(
                UsbConstants.USB_DIR_IN or UsbConstants.USB_TYPE_VENDOR,
                AOA_GET_PROTOCOL,
                0,
                0,
                any<ByteArray>(),
                2,
                USB_CONTROL_TRANSFER_TIMEOUT
            )
        } answers {
            val buffer = arg<ByteArray>(4)
            buffer[0] = 3  // 版本低位
            buffer[1] = 0  // 版本高位，组合后版本为3
            2  // 返回成功读取2字节
        }

        setUsbManager(mockUsbManager)

        // When: 初始化USB设备
        val result = aoaCheckDevice.initUsbDevice(mockUsbDevice)

        // Then: 应该失败，因为版本3不支持
        assertFalse(result, "AOA协议版本3不支持时initUsbDevice应该失败")
    }

    @Test
    fun `initUsbDevice should support AOA protocol version 1`() {
        // Given: 设置Mock环境，支持的AOA版本1
        val (mockContext, mockUsbManager, mockUsbDeviceConnection) = setupMockForInitUsbDevice()
        val mockUsbDevice = createMockUsbDevice(VID_SAMSUNG, 0x1234)

        mockkStatic("com.autoai.welinkapp.aoa.AoaLink")
        val mockAoaDevice = createMockAoaDevice()
        every { AoaLink.getAoaDevice() } returns mockAoaDevice // 始终返回有效的AoaDevice
        

        setupSuccessfulAOAProtocol(mockUsbDeviceConnection, version = 1)

        setUsbManager(mockUsbManager)

        // When: 初始化USB设备
        val result = aoaCheckDevice.initUsbDevice(mockUsbDevice)

        // Then: 应该成功，因为版本1是支持的
        assertTrue(result, "AOA协议版本1应该被支持")
    }

    @Test
    fun `initUsbDevice should support AOA protocol version 2`() {
        // Given: 设置Mock环境，支持的AOA版本2
        val (mockContext, mockUsbManager, mockUsbDeviceConnection) = setupMockForInitUsbDevice()
        val mockUsbDevice = createMockUsbDevice(VID_SAMSUNG, 0x1234)

        mockkStatic("com.autoai.welinkapp.aoa.AoaLink")
        val mockAoaDevice = createMockAoaDevice()
        every { AoaLink.getAoaDevice() } returns mockAoaDevice // 始终返回有效的AoaDevice
        

        setupSuccessfulAOAProtocol(mockUsbDeviceConnection, version = 2)

        setUsbManager(mockUsbManager)

        // When: 初始化USB设备
        val result = aoaCheckDevice.initUsbDevice(mockUsbDevice)

        // Then: 应该成功，因为版本2是支持的
        assertTrue(result, "AOA协议版本2应该被支持")
    }

    // ====== AOA Identity Exchange Tests ======


    @Test
    fun `initUsbDevice should fail when send manufacturer identity fails`() {
        // Given: 设置Mock环境，协议版本正常但发送Manufacturer失败
        val (mockContext, mockUsbManager, mockUsbDeviceConnection) = setupMockForInitUsbDevice()
        val mockUsbDevice = createMockUsbDevice(VID_SAMSUNG, 0x1234)

        mockkStatic("com.autoai.welinkapp.aoa.AoaLink")
        val mockAoaDevice = createMockAoaDevice()
        every { AoaLink.getAoaDevice() } returns mockAoaDevice
        

        // Mock协议版本获取成功（返回版本1）
        setupSuccessfulProtocolVersion(mockUsbDeviceConnection, version = 1)

        // Mock发送Manufacturer身份信息失败
        every {
            mockUsbDeviceConnection.controlTransfer(
                UsbConstants.USB_DIR_OUT or UsbConstants.USB_TYPE_VENDOR,
                AOA_SEND_IDENT,
                0,
                0, // index=0 表示Manufacturer
                any<ByteArray>(),
                any<Int>(),
                USB_CONTROL_TRANSFER_TIMEOUT
            )
        } returns -1

        setUsbManager(mockUsbManager)

        // When: 初始化USB设备
        val result = aoaCheckDevice.initUsbDevice(mockUsbDevice)

        // Then: 应该失败，因为发送Manufacturer身份信息失败
        assertFalse(result, "发送Manufacturer身份信息失败时initUsbDevice应该失败")
    }

    @Test
    fun `initUsbDevice should fail when send model identity fails`() {
        // Given: 设置Mock环境，Manufacturer成功但Model失败
        val (mockContext, mockUsbManager, mockUsbDeviceConnection) = setupMockForInitUsbDevice()
        val mockUsbDevice = createMockUsbDevice(VID_SAMSUNG, 0x1234)

        mockkStatic("com.autoai.welinkapp.aoa.AoaLink")
        val mockAoaDevice = createMockAoaDevice()
        every { AoaLink.getAoaDevice() } returns mockAoaDevice
        

        setupSuccessfulProtocolVersion(mockUsbDeviceConnection, version = 1)

        // Mock发送Manufacturer成功，但Model失败
        every {
            mockUsbDeviceConnection.controlTransfer(
                UsbConstants.USB_DIR_OUT or UsbConstants.USB_TYPE_VENDOR,
                AOA_SEND_IDENT,
                0,
                0,  // index=0 表示Manufacturer
                any<ByteArray>(),
                any<Int>(),
                USB_CONTROL_TRANSFER_TIMEOUT
            )
        } returns 10  // 成功

        every {
            mockUsbDeviceConnection.controlTransfer(
                UsbConstants.USB_DIR_OUT or UsbConstants.USB_TYPE_VENDOR,
                AOA_SEND_IDENT,
                0,
                1,  // index=1 表示Model
                any<ByteArray>(),
                any<Int>(),
                USB_CONTROL_TRANSFER_TIMEOUT
            )
        } returns -1  // 失败

        setUsbManager(mockUsbManager)

        // When: 初始化USB设备
        val result = aoaCheckDevice.initUsbDevice(mockUsbDevice)

        // Then: 应该失败，因为发送Model身份信息失败
        assertFalse(result, "发送Model身份信息失败时initUsbDevice应该失败")
    }

    @Test
    fun `initUsbDevice should fail when send description identity fails`() {
        // Given: 设置Mock环境，前两步成功但Description失败
        val (mockContext, mockUsbManager, mockUsbDeviceConnection) = setupMockForInitUsbDevice()
        val mockUsbDevice = createMockUsbDevice(VID_SAMSUNG, 0x1234)

        mockkStatic("com.autoai.welinkapp.aoa.AoaLink")
        val mockAoaDevice = createMockAoaDevice()
        every { AoaLink.getAoaDevice() } returns mockAoaDevice
        

        setupSuccessfulProtocolVersion(mockUsbDeviceConnection, version = 1)

        // Mock前两步成功
        every {
            mockUsbDeviceConnection.controlTransfer(
                UsbConstants.USB_DIR_OUT or UsbConstants.USB_TYPE_VENDOR,
                AOA_SEND_IDENT,
                0,
                0,  // Manufacturer
                any<ByteArray>(),
                any<Int>(),
                USB_CONTROL_TRANSFER_TIMEOUT
            )
        } returns 10

        every {
            mockUsbDeviceConnection.controlTransfer(
                UsbConstants.USB_DIR_OUT or UsbConstants.USB_TYPE_VENDOR,
                AOA_SEND_IDENT,
                0,
                1,  // Model
                any<ByteArray>(),
                any<Int>(),
                USB_CONTROL_TRANSFER_TIMEOUT
            )
        } returns 10

        // Mock Description失败
        every {
            mockUsbDeviceConnection.controlTransfer(
                UsbConstants.USB_DIR_OUT or UsbConstants.USB_TYPE_VENDOR,
                AOA_SEND_IDENT,
                0,
                2,  // index=2 表示Description
                any<ByteArray>(),
                any<Int>(),
                USB_CONTROL_TRANSFER_TIMEOUT
            )
        } returns -1

        setUsbManager(mockUsbManager)

        // When: 初始化USB设备
        val result = aoaCheckDevice.initUsbDevice(mockUsbDevice)

        // Then: 应该失败，因为发送Description身份信息失败
        assertFalse(result, "发送Description身份信息失败时initUsbDevice应该失败")
    }

    @Test
    fun `initUsbDevice should fail when send version identity fails`() {
        // Given: 设置Mock环境，前三步成功但Version失败
        val (mockContext, mockUsbManager, mockUsbDeviceConnection) = setupMockForInitUsbDevice()
        val mockUsbDevice = createMockUsbDevice(VID_SAMSUNG, 0x1234)

        mockkStatic("com.autoai.welinkapp.aoa.AoaLink")
        val mockAoaDevice = createMockAoaDevice()
        every { AoaLink.getAoaDevice() } returns mockAoaDevice
        

        setupSuccessfulProtocolVersion(mockUsbDeviceConnection, version = 1)

        // Mock前三步成功
        every {
            mockUsbDeviceConnection.controlTransfer(
                UsbConstants.USB_DIR_OUT or UsbConstants.USB_TYPE_VENDOR,
                AOA_SEND_IDENT,
                0,
                match { it in 0..2 },  // index 0,1,2 (Manufacturer, Model, Description)
                any<ByteArray>(),
                any<Int>(),
                USB_CONTROL_TRANSFER_TIMEOUT
            )
        } returns 10

        // Mock Version失败
        every {
            mockUsbDeviceConnection.controlTransfer(
                UsbConstants.USB_DIR_OUT or UsbConstants.USB_TYPE_VENDOR,
                AOA_SEND_IDENT,
                0,
                3,  // index=3 表示Version
                any<ByteArray>(),
                any<Int>(),
                USB_CONTROL_TRANSFER_TIMEOUT
            )
        } returns -1

        setUsbManager(mockUsbManager)

        // When: 初始化USB设备
        val result = aoaCheckDevice.initUsbDevice(mockUsbDevice)

        // Then: 应该失败，因为发送Version身份信息失败
        assertFalse(result, "发送Version身份信息失败时initUsbDevice应该失败")
    }

    @Test
    fun `initUsbDevice should fail when send URI identity fails`() {
        // Given: 设置Mock环境，前四步成功但URI失败
        val (mockContext, mockUsbManager, mockUsbDeviceConnection) = setupMockForInitUsbDevice()
        val mockUsbDevice = createMockUsbDevice(VID_SAMSUNG, 0x1234)

        mockkStatic("com.autoai.welinkapp.aoa.AoaLink")
        val mockAoaDevice = createMockAoaDevice()
        every { AoaLink.getAoaDevice() } returns mockAoaDevice
        

        setupSuccessfulProtocolVersion(mockUsbDeviceConnection, version = 1)

        // Mock前四步成功
        every {
            mockUsbDeviceConnection.controlTransfer(
                UsbConstants.USB_DIR_OUT or UsbConstants.USB_TYPE_VENDOR,
                AOA_SEND_IDENT,
                0,
                match { it in 0..3 },  // index 0,1,2,3
                any<ByteArray>(),
                any<Int>(),
                USB_CONTROL_TRANSFER_TIMEOUT
            )
        } returns 10

        // Mock URI失败
        every {
            mockUsbDeviceConnection.controlTransfer(
                UsbConstants.USB_DIR_OUT or UsbConstants.USB_TYPE_VENDOR,
                AOA_SEND_IDENT,
                0,
                4,  // index=4 表示URI
                any<ByteArray>(),
                any<Int>(),
                USB_CONTROL_TRANSFER_TIMEOUT
            )
        } returns -1

        setUsbManager(mockUsbManager)

        // When: 初始化USB设备
        val result = aoaCheckDevice.initUsbDevice(mockUsbDevice)

        // Then: 应该失败，因为发送URI身份信息失败
        assertFalse(result, "发送URI身份信息失败时initUsbDevice应该失败")
    }

    @Test
    fun `initUsbDevice should fail when send serial identity fails`() {
        // Given: 设置Mock环境，前五步成功但Serial失败
        val (mockContext, mockUsbManager, mockUsbDeviceConnection) = setupMockForInitUsbDevice()
        val mockUsbDevice = createMockUsbDevice(VID_SAMSUNG, 0x1234)

        mockkStatic("com.autoai.welinkapp.aoa.AoaLink")
        val mockAoaDevice = createMockAoaDevice()
        every { AoaLink.getAoaDevice() } returns mockAoaDevice
        

        setupSuccessfulProtocolVersion(mockUsbDeviceConnection, version = 1)

        // Mock前五步成功
        every {
            mockUsbDeviceConnection.controlTransfer(
                UsbConstants.USB_DIR_OUT or UsbConstants.USB_TYPE_VENDOR,
                AOA_SEND_IDENT,
                0,
                match { it in 0..4 },  // index 0,1,2,3,4
                any<ByteArray>(),
                any<Int>(),
                USB_CONTROL_TRANSFER_TIMEOUT
            )
        } returns 10

        // Mock Serial失败
        every {
            mockUsbDeviceConnection.controlTransfer(
                UsbConstants.USB_DIR_OUT or UsbConstants.USB_TYPE_VENDOR,
                AOA_SEND_IDENT,
                0,
                5,  // index=5 表示Serial
                any<ByteArray>(),
                any<Int>(),
                USB_CONTROL_TRANSFER_TIMEOUT
            )
        } returns -1

        setUsbManager(mockUsbManager)

        // When: 初始化USB设备
        val result = aoaCheckDevice.initUsbDevice(mockUsbDevice)

        // Then: 应该失败，因为发送Serial身份信息失败
        assertFalse(result, "发送Serial身份信息失败时initUsbDevice应该失败")
    }

    // ====== Protocol Error Handling Tests ======

    @Test
    fun `initUsbDevice should fail when start accessory mode fails`() {
        // Given: 设置Mock环境，所有身份信息发送成功但启动AOA模式失败
        val (mockContext, mockUsbManager, mockUsbDeviceConnection) = setupMockForInitUsbDevice()
        val mockUsbDevice = createMockUsbDevice(VID_SAMSUNG, 0x1234)

        mockkStatic("com.autoai.welinkapp.aoa.AoaLink")
        val mockAoaDevice = createMockAoaDevice()
        every { AoaLink.getAoaDevice() } returns mockAoaDevice
        

        setupSuccessfulProtocolVersion(mockUsbDeviceConnection, version = 1)
        setupSuccessfulIdentitySteps(mockUsbDeviceConnection)

        // Mock启动AOA模式失败
        every {
            mockUsbDeviceConnection.controlTransfer(
                UsbConstants.USB_DIR_OUT or UsbConstants.USB_TYPE_VENDOR,
                AOA_START_ACCESSORY,
                0,
                0,
                null,
                0,
                USB_CONTROL_TRANSFER_TIMEOUT
            )
        } returns -1

        setUsbManager(mockUsbManager)

        // When: 初始化USB设备
        val result = aoaCheckDevice.initUsbDevice(mockUsbDevice)

        // Then: 应该失败，因为启动AOA模式失败
        assertFalse(result, "启动AOA模式失败时initUsbDevice应该失败")
    }

    @Test
    fun `initUsbDevice should handle protocol timeout gracefully`() {
        // Given: 设置协议超时的场景
        val (mockContext, mockUsbManager, mockUsbDeviceConnection) = setupMockForInitUsbDevice()
        val mockUsbDevice = createMockUsbDevice(VID_SAMSUNG, 0x1234)

        mockkStatic("com.autoai.welinkapp.aoa.AoaLink")
        val mockAoaDevice = createMockAoaDevice()
        every { AoaLink.getAoaDevice() } returns mockAoaDevice
        

        // Mock所有controlTransfer调用返回负值（模拟超时失败）
        every {
            mockUsbDeviceConnection.controlTransfer(any(), any(), any(), any(), any<ByteArray>(), any(), any())
        } returns -1

        setUsbManager(mockUsbManager)

        // When: 初始化USB设备
        val result = aoaCheckDevice.initUsbDevice(mockUsbDevice)

        // Then: 应该优雅处理超时异常
        assertFalse(result, "协议超时时initUsbDevice应该优雅失败")
    }

    // ====== Successful AOA Protocol Test ======

    @Test
    fun `initUsbDevice should succeed when all steps complete successfully`() {
        // Given: 设置Mock环境，所有步骤都成功
        val (mockContext, mockUsbManager, mockUsbDeviceConnection) = setupMockForInitUsbDevice()
        val mockUsbDevice = createMockUsbDevice(VID_SAMSUNG, 0x1234)

        mockkStatic("com.autoai.welinkapp.aoa.AoaLink")
        val mockAoaDevice = createMockAoaDevice()
        every { AoaLink.getAoaDevice() } returns mockAoaDevice // 始终返回有效的AoaDevice
        

        setupSuccessfulAOAProtocol(mockUsbDeviceConnection, version = 2)

        setUsbManager(mockUsbManager)

        // When: 初始化USB设备
        val result = aoaCheckDevice.initUsbDevice(mockUsbDevice)

        // Then: 应该成功，因为所有AOA协议步骤都成功
        assertTrue(result, "所有AOA协议步骤成功时initUsbDevice应该成功")

        // 验证关键步骤都被调用
        
    }

    // ====== Helper Methods ======

    private fun setupMockForInitUsbDevice(): Triple<Context, UsbManager, UsbDeviceConnection> {
        val mockContext = createMockContext()
        val mockUsbManager = mockContext.getSystemService(Context.USB_SERVICE) as UsbManager
        val mockUsbDeviceConnection = mockk<UsbDeviceConnection>(relaxed = true)

        // Setup UsbManager mock
        every { mockUsbManager.openDevice(any()) } returns mockUsbDeviceConnection
        every { mockUsbManager.hasPermission(any<UsbDevice>()) } returns true

        // Setup UsbDeviceConnection mock
        every { mockUsbDeviceConnection.claimInterface(any(), any()) } returns true
        every { mockUsbDeviceConnection.releaseInterface(any()) } returns true
        every { mockUsbDeviceConnection.close() } just Runs

        return Triple(mockContext, mockUsbManager, mockUsbDeviceConnection)
    }

    /**
     * 创建Mock AoaDevice对象
     * 用于测试switchToAOA方法中的身份信息发送
     */
    private fun createMockAoaDevice(): AoaDevice {
        val mockAoaDevice = mockk<AoaDevice>()
        every { mockAoaDevice.manufacturer } returns "Test Manufacturer"
        every { mockAoaDevice.model } returns "Test Model"
        every { mockAoaDevice.description } returns "Test Description"
        every { mockAoaDevice.version } returns "1.0"
        every { mockAoaDevice.uri } returns "http://test.com"
        every { mockAoaDevice.serial } returns "123456"
        return mockAoaDevice
    }

    /**
     * 设置成功的AOA协议流程Mock
     */
    private fun setupSuccessfulAOAProtocol(mockUsbDeviceConnection: UsbDeviceConnection, version: Int = 1) {
        setupSuccessfulProtocolVersion(mockUsbDeviceConnection, version)
        setupSuccessfulIdentitySteps(mockUsbDeviceConnection)
        setupSuccessfulAccessoryStart(mockUsbDeviceConnection)
    }

    /**
     * 设置成功的协议版本获取
     */
    private fun setupSuccessfulProtocolVersion(mockUsbDeviceConnection: UsbDeviceConnection, version: Int) {
        every {
            mockUsbDeviceConnection.controlTransfer(
                UsbConstants.USB_DIR_IN or UsbConstants.USB_TYPE_VENDOR,
                AOA_GET_PROTOCOL,
                0,
                0,
                any<ByteArray>(),
                2,
                USB_CONTROL_TRANSFER_TIMEOUT
            )
        } answers {
            val buffer = arg<ByteArray>(4)
            buffer[0] = version.toByte()  // 版本低位
            buffer[1] = 0  // 版本高位
            2  // 返回成功读取2字节
        }
    }

    /**
     * 设置成功的身份信息发送步骤
     */
    private fun setupSuccessfulIdentitySteps(mockUsbDeviceConnection: UsbDeviceConnection) {
        // Mock所有身份信息发送成功 - 使用更精确的匹配
        every {
            mockUsbDeviceConnection.controlTransfer(
                UsbConstants.USB_DIR_OUT or UsbConstants.USB_TYPE_VENDOR,
                AOA_SEND_IDENT,
                0,
                match { it in 0..5 },  // index 0,1,2,3,4,5
                any<ByteArray>(),
                any<Int>(),
                USB_CONTROL_TRANSFER_TIMEOUT
            )
        } returns 10
    }

    /**
     * 设置成功的AOA模式启动
     */
    private fun setupSuccessfulAccessoryStart(mockUsbDeviceConnection: UsbDeviceConnection) {
        // Mock启动AOA模式成功
        every {
            mockUsbDeviceConnection.controlTransfer(
                UsbConstants.USB_DIR_OUT or UsbConstants.USB_TYPE_VENDOR,
                AOA_START_ACCESSORY,
                0,
                0,
                null,
                0,
                USB_CONTROL_TRANSFER_TIMEOUT
            )
        } returns 0  // 成功
    }
}