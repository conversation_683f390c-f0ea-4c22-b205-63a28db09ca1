package com.autoai.welinkapp.checkconnect

import android.hardware.usb.UsbDevice
import io.mockk.*
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.junit.jupiter.MockitoExtension
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import java.lang.reflect.Method

/**
 * Unit tests for AOACheckDevice class - Migrated from util directory
 *
 * Tests the isIosDevice function which identifies whether a USB device is an iOS device
 * based on vendor ID and product ID characteristics.
 *
 * 这个测试类专门测试 AOACheckDevice 中的 isIosDevice 方法
 * 该方法用于检测USB设备是否为iOS设备
 *
 * 从util目录搬移过来的测试用例，使用反射访问私有方法
 */
@ExtendWith(MockitoExtension::class)
class AOACheckDeviceFromUtilTest {

    private lateinit var aoaCheckDevice: AOACheckDevice

    companion object {
        // Constants from AOACheckDevice class
        private const val VID_APPLE = 0x5ac
        private const val VID_SAMSUNG = 0x04e8
    }

    @BeforeEach
    fun setUp() {
        // 获取AOACheckDevice实例
        aoaCheckDevice = AOACheckDevice.getInstance()
    }

    @AfterEach
    fun tearDown() {
        // 清理所有mock
        unmockkAll()
    }

    // ==================== Helper Methods ====================

    /**
     * 使用反射调用私有的isIosDevice方法
     * 由于isIosDevice是私有方法，需要通过反射来访问
     */
    private fun callIsIosDeviceViaReflection(device: UsbDevice): Boolean {
        return try {
            val method: Method = AOACheckDevice::class.java.getDeclaredMethod("isIosDevice", UsbDevice::class.java)
            method.isAccessible = true
            method.invoke(aoaCheckDevice, device) as Boolean
        } catch (e: Exception) {
            // 如果反射失败，返回false并记录错误
            println("Failed to call isIosDevice via reflection: ${e.message}")
            false
        }
    }

    /**
     * 创建简单的Mock UsbDevice
     */
    private fun createMockUsbDevice(vendorId: Int, productId: Int): UsbDevice {
        val mockDevice = mockk<UsbDevice>()
        every { mockDevice.vendorId } returns vendorId
        every { mockDevice.productId } returns productId
        return mockDevice
    }

    // ==================== isIosDevice() 核心测试 ====================

    /**
     * 测试场景：检测标准iPhone设备
     * 验证当USB设备具有Apple厂商ID和iPhone产品ID时，能正确识别为iOS设备
     */
    @Test
    fun `isIosDevice should return true for valid iOS device with iPhone product ID`() {
        // Given: Apple vendor ID and iPhone product ID
        val mockDevice = createMockUsbDevice(VID_APPLE, 0x1200)

        // When: Call real isIosDevice method via reflection
        val result = callIsIosDeviceViaReflection(mockDevice)

        // Then: Should return true
        assertTrue(result, "Device with Apple VID and iPhone PID should be recognized as iOS device")
    }

    /**
     * 测试场景：检测iPod设备
     * 验证当USB设备具有Apple厂商ID和iPod产品ID范围内的ID时，能正确识别为iOS设备
     */
    @Test
    fun `isIosDevice should return true for valid iOS device with iPod product ID range`() {
        // Given: Apple vendor ID and iPod product ID within range
        val mockDevice = createMockUsbDevice(VID_APPLE, 0x1201)

        // When: Call real isIosDevice method via reflection
        val result = callIsIosDeviceViaReflection(mockDevice)

        // Then: Should return true
        assertTrue(result, "Device with Apple VID and iPod PID should be recognized as iOS device")
    }

    /**
     * 测试场景：检测Apple设备但产品ID无效的情况
     * 验证当设备是Apple厂商但产品ID不在iPhone/iPod范围内时，不会被误识别为iOS设备
     */
    @Test
    fun `isIosDevice should return false for Apple device with invalid product ID`() {
        // Given: Apple vendor ID but invalid product ID (outside iPhone/iPod range)
        val mockDevice = createMockUsbDevice(VID_APPLE, 0x1100)

        // When: Call real isIosDevice method via reflection
        val result = callIsIosDeviceViaReflection(mockDevice)

        // Then: Should return false
        assertFalse(result, "Apple device with invalid product ID should not be recognized as iOS device")
    }

    /**
     * 测试场景：检测非Apple厂商的设备
     * 验证即使产品ID类似iPhone，但厂商ID不是Apple的设备不会被误识别为iOS设备
     */
    @Test
    fun `isIosDevice should return false for non-Apple vendor ID`() {
        // Given: Non-Apple vendor ID
        val mockDevice = createMockUsbDevice(VID_SAMSUNG, 0x1200)

        // When: Call real isIosDevice method via reflection
        val result = callIsIosDeviceViaReflection(mockDevice)

        // Then: Should return false
        assertFalse(result, "Non-Apple device should not be recognized as iOS device")
    }
}
