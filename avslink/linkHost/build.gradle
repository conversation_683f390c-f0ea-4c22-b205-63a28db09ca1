apply plugin: 'com.android.library'
apply plugin: 'jacoco'

ext {
    VERSION = LINK_HOST_VERSION
    GROUP_ID = "com.autoai.avs.link"
    ARTIFACT_ID = "linkhost"
}

apply from: file('../maven_push.gradle')
apply from: "../config.gradle"
apply plugin: 'org.jetbrains.kotlin.android'

android {
    compileSdkVersion rootProject.linkCompileSdkVersion

    defaultConfig {
        minSdkVersion rootProject.linkMinSdkVersion
        targetSdkVersion rootProject.linkTargetSdkVersion

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    signingConfigs {
        platform {
            storeFile file("../platform.keystore")
            storePassword "000000"
            keyAlias "desay"
            keyPassword "000000"
        }
    }
    buildTypes.each { bt ->
        bt.signingConfig = signingConfigs.platform
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }

//    sourceSets.configureEach {
//        if (name == "main") {
//            java.srcDir("src/main/java")
//            java.srcDirs += "C:\\work_space\\projects\\avslinkhid\\link-hid\\avslinkhid\\src\\main\\java"
//        }
//    }

    testOptions {
        unitTests.all {
            useJUnitPlatform()
            unitTests.returnDefaultValues = true
        }
    }
}

dependencies {
    api project(path: ':linkSdk')
    api project(':linkCommon')
    implementation 'androidx.appcompat:appcompat:1.2.0'
    api "com.autoai.avs.link:linkhid:${LINK_HID_VERSION}"

    api project(path: ':app-scrcpy')

    testImplementation "org.junit.jupiter:junit-jupiter-api:${JUNIT_VERSION}"
    testRuntimeOnly "org.junit.jupiter:junit-jupiter-engine:${JUNIT_VERSION}"
    testImplementation "org.junit.jupiter:junit-jupiter-params:${JUNIT_VERSION}" // 可选，用于参数化测试
    testImplementation "org.mockito:mockito-core:${MOCKITO_VERSION}"
    testImplementation "org.mockito:mockito-inline:${MOCKITO_VERSION}" // 用于模拟 final/static 方法
    testImplementation "org.mockito:mockito-junit-jupiter:${MOCKITO_VERSION}" // 提供 MockitoExtension 支持
    testImplementation 'org.robolectric:robolectric:4.10.3' // 添加 Robolectric 支持

    // 添加 MockK 支持（用于 Kotlin 测试基类）
    testImplementation 'io.mockk:mockk:1.12.8' // MockK for Kotlin (compatible with JVM 1.8)
}

jacoco {
    toolVersion = "0.8.12" //代码覆盖库jacoco版本号
}
tasks.withType(Test) {
    jacoco.includeNoLocationClasses = true
    jacoco.excludes = ['jdk.internal.*']
}
// 适配多 Flavor 场景， 直接运行 gralde jacocoTestReportT2Debug  即可
android.libraryVariants.configureEach { variant ->
    task "jacocoTestReport${variant.flavorName.capitalize()}${variant.buildType.name.capitalize()}"(type: JacocoReport,
            dependsOn: ["test${variant.flavorName.capitalize()}${variant.buildType.name.capitalize()}UnitTest"]) {
        group = 'Reporting'
        description = 'Generate Jacoco coverage reports after running tests.'
        reports {
            xml.required = true
            csv.required =  false
        }
        def javaClasses = fileTree(dir: "build/intermediates/javac", excludes: [
            '**/R.class',
            '**/R$*.class',
            '**/*$ViewInjector*.*',
            '**/BuildConfig.*'
        ])
        def kotlinClasses = fileTree(dir: "build/tmp/kotlin-classes/", excludes: [
            '**/R.class',
            '**/R$*.class',
            '**/*$ViewInjector*.*',
            '**/BuildConfig.*'
        ])

        // 过滤掉测试类目录
        def filteredKotlinClasses = kotlinClasses.filter { file ->
            !file.path.contains("debugUnitTest") &&
            !file.path.contains("releaseUnitTest") &&
            !file.name.endsWith("Test.class") &&
            !file.name.endsWith("TestBase.class") &&
            !file.name.endsWith("TestImpl.class") &&
            !file.name.contains("Test\$") &&
            !file.name.contains("TestApplication") &&
            // 过滤掉所有测试相关的类
            !file.path.contains("/test/") &&
            !file.path.contains("\\test\\")
        }
        classDirectories.setFrom(files([javaClasses], [filteredKotlinClasses]))
        sourceDirectories.setFrom(files('src/main/java'))
        executionData.setFrom(fileTree(dir: "build/", includes: ['**/*.exec']))
    }
}