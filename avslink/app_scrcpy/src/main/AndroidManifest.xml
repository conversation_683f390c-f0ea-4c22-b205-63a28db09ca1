<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" /> <!-- 网络 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" /> <!-- 悬浮窗 -->
    <uses-permission android:name="android.permission.SYSTEM_OVERLAY_WINDOW" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.USB_PERMISSION" /> <!-- USB -->
    <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> <!-- 桌面快捷方式 -->
    <uses-permission
        android:name="android.permission.PACKAGE_USAGE_STATS"
        tools:ignore="ProtectedPermissions" /> <!-- 使用统计 -->

    <uses-feature android:name="android.hardware.usb.host" />

    <!--  <application-->
    <!--    android:name=".helper.MyApplication"-->
    <!--    android:icon="@mipmap/ic_launcher"-->
    <!--    android:installLocation="internalOnly"-->
    <!--    android:label="@string/app_name"-->
    <!--    android:roundIcon="@mipmap/ic_launcher_round"-->
    <!--    android:supportsRtl="true"-->
    <!--    android:theme="@android:style/Theme.DeviceDefault.Light.NoActionBar"-->
    <!--    android:usesCleartextTraffic="true"-->
    <!--    tools:targetApi="31">-->
    <application
        android:usesCleartextTraffic="true"
        tools:targetApi="31"
        android:hardwareAccelerated="true">
        <activity
            android:name=".MainActivity"
            android:configChanges="keyboardHidden|screenSize|orientation"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

<!--                <category android:name="android.intent.category.LAUNCHER" />-->
            </intent-filter>
        </activity>
        <activity
            android:name=".MonitorActivity"
            android:configChanges="keyboardHidden|screenSize|orientation|uiMode"
            android:exported="false" />
        <activity
            android:name=".IpActivity"
            android:exported="false" />
        <activity
            android:name=".LogActivity"
            android:exported="false" />
        <activity
            android:name=".WebViewActivity"
            android:exported="false" />
        <activity
            android:name=".PairActivity"
            android:exported="false" />
        <activity
            android:name=".SetActivity"
            android:configChanges="keyboardHidden|screenSize|orientation|uiMode"
            android:exported="false" />
        <activity
            android:name=".client.view.FullActivity"
            android:exported="false"
            android:hardwareAccelerated="true" />
        <activity
            android:name=".AdbKeyActivity"
            android:exported="false" />
        <activity
            android:name=".StartDeviceActivity"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:theme="@style/Transparent" />

    </application>

</manifest>