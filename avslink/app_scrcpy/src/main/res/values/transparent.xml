<?xml version="1.0" encoding="utf-8"?>
<resources>

  <style name="Transparent" parent="android:Theme.Holo.Light.Dialog">
    <item name="android:windowFullscreen">true</item>
    <item name="android:fitsSystemWindows">true</item>
    <item name="android:gravity">center</item>
    <item name="android:layout_gravity">center</item>
    <item name="android:windowActionBar">false</item>
    <item name="android:windowMinWidthMinor">100%</item>
    <item name="android:windowMinWidthMajor">100%</item>
    <item name="android:backgroundDimEnabled">false</item>
    <item name="android:windowFrame">@null</item>
    <item name="android:windowIsFloating">true</item>
    <item name="android:windowIsTranslucent">true</item>
    <item name="android:windowNoTitle">true</item>
    <item name="android:background">@null</item>
    <item name="android:windowBackground">@android:color/transparent</item>
  </style>
</resources>