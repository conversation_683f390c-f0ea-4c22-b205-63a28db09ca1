<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools"
  android:layout_width="match_parent"
  android:layout_height="match_parent"
  android:background="@color/background"
  android:orientation="vertical"
  tools:context=".AdbKeyActivity">

  <ImageView
    android:id="@+id/back_button"
    android:layout_width="54dp"
    android:layout_height="54dp"
    android:padding="15dp"
    android:src="@drawable/chevron_left"
    android:tint="@color/onBackground" />

  <ScrollView
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:orientation="vertical"
      android:paddingStart="15dp"
      android:paddingEnd="15dp">

      <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginBottom="5dp"
        android:text="@string/adb_key_pub"
        android:textColor="@color/onBackground"
        android:textSize="@dimen/middleFont" />

      <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/background_cron"
        android:orientation="vertical"
        android:padding="15dp">

        <EditText
          android:id="@+id/adb_key_pub"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:textColor="@color/onCardBackground"
          android:textColorHint="@color/onCardBackgroundSecond"
          android:textSize="@dimen/smallSmallFont" />
      </LinearLayout>

      <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="5dp"
        android:text="@string/adb_key_pri"
        android:textColor="@color/onBackground"
        android:textSize="@dimen/middleFont" />

      <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/background_cron"
        android:orientation="vertical"
        android:padding="15dp">

        <EditText
          android:id="@+id/adb_key_pri"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:textColor="@color/onCardBackground"
          android:textColorHint="@color/onCardBackgroundSecond"
          android:textSize="@dimen/smallSmallFont" />

      </LinearLayout>

      <Button
        android:id="@+id/ok"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="30dp"
        android:layout_marginBottom="60dp"
        android:background="@drawable/background_cron"
        android:backgroundTint="@color/button"
        android:gravity="center"
        android:text="@string/adb_key_button"
        android:textColor="@color/onButton"
        android:textSize="@dimen/smallFont" />
    </LinearLayout>

  </ScrollView>
</LinearLayout>