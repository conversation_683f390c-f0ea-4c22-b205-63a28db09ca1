<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  android:layout_width="match_parent"
  android:layout_height="wrap_content"
  android:layout_margin="15dp"
  android:background="@drawable/background_cron"
  android:backgroundTint="@color/cardBackground"
  android:elevation="8dp"
  android:gravity="center"
  android:orientation="vertical"
  android:paddingStart="15dp"
  android:paddingTop="15dp"
  android:paddingEnd="15dp"
  android:paddingBottom="10dp">

  <TextView
    android:id="@+id/text"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:gravity="center"
    android:text="@string/tip_reconnect"
    android:textColor="@color/clientBar"
    android:textSize="@dimen/middleFont" />

  <LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    android:gravity="center"
    android:orientation="horizontal"
    android:paddingStart="12dp"
    android:paddingTop="12dp"
    android:paddingEnd="12dp">

    <Button
      android:id="@+id/button_confirm"
      android:layout_width="0dp"
      android:layout_height="wrap_content"
      android:layout_weight="1"
      android:background="@drawable/background_cron"
      android:backgroundTint="@color/clientBar"
      android:gravity="center"
      android:text="@string/confirm"
      android:textColor="@color/cardBackground"
      android:textSize="@dimen/smallFont" />

    <Space
      android:layout_width="10dp"
      android:layout_height="wrap_content" />

    <Button
      android:id="@+id/button_cancel"
      android:layout_width="0dp"
      android:layout_height="wrap_content"
      android:layout_weight="1"
      android:background="@drawable/background_cron"
      android:backgroundTint="@color/onCardBackgroundSecond"
      android:gravity="center"
      android:text="@string/cancel"
      android:textColor="@color/cardBackground"
      android:textSize="@dimen/smallFont" />

  </LinearLayout>

</LinearLayout>