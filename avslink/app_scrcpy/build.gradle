plugins {
  id 'com.android.library' version '7.1.3'
  id 'org.jetbrains.kotlin.android' version '1.8.0'
}
//version '8.1.3'
android {
  namespace 'top.eiyooooo.easycontrol.app'
  compileSdk rootProject.ext.android.compileSdk

  defaultConfig {
    minSdk 21
    targetSdk rootProject.ext.android.targetSdk
    versionCode 10610
    versionName "1.6.10"
    ndk {
      abiFilters "arm64-v8a", "armeabi-v7a", "x86", "x86_64"
    }
  }

//  viewBinding {
//    enabled = true
//  }

  buildFeatures {
    viewBinding true
  }

  buildTypes {
    debug {
      buildConfigField "boolean", "ENABLE_DEBUG_FEATURE", "true"
      debuggable true
    }
    release {
      buildConfigField "boolean", "ENABLE_DEBUG_FEATURE", "false"
      debuggable false
      minifyEnabled false
      shrinkResources false
      proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
    }
  }

  compileOptions {
    sourceCompatibility JavaVersion.VERSION_1_8
    targetCompatibility JavaVersion.VERSION_1_8
  }

  packagingOptions {
    resources.excludes.add("META-INF/*")
  }

}

dependencies {
  implementation 'com.github.MuntashirAkon:libadb-android:3.0.0'
  implementation 'com.github.MuntashirAkon:sun-security-android:1.1'
  implementation 'org.lsposed.hiddenapibypass:hiddenapibypass:4.3'
}
