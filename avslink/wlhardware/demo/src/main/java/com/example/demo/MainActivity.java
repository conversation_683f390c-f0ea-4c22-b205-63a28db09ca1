package com.example.demo;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;

import android.Manifest;
import android.app.Activity;
import android.bluetooth.BluetoothAdapter;
import android.content.Context;
import android.content.pm.PackageManager;
import android.net.wifi.WifiManager;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.CompoundButton;
import android.widget.Switch;
import android.widget.TextView;

import com.autoai.welink.wireless.WLHardware;
import com.autoai.welink.wireless.WLHardwareListener;
import com.autoai.welink.wireless.ap.WiFiAP;
import com.autoai.welink.wireless.ap.WiFiAPListener;
import com.autoai.welink.wireless.direct.DockChannel;
import com.autoai.welink.wireless.direct.DockChannelListener;

public class MainActivity extends AppCompatActivity implements CompoundButton.OnCheckedChangeListener,View.OnClickListener, WLHardwareListener {
    TextView textViewLog;
    void _info(String text){
        Log.e("wlhw_demo", text);
        MainActivity.this.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (textViewLog !=null){
                    textViewLog.append(text+"\r\n");
                    int offset= textViewLog.getLineCount()* textViewLog.getLineHeight();
                    if (offset > (textViewLog.getHeight())) {
                        textViewLog.scrollTo(0, offset - textViewLog.getHeight());
                    }
                }
            }
        });
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        textViewLog=findViewById(R.id.textView);
        Switch switch_ble = findViewById(R.id.switch_ble);
        Switch switch_wifi = findViewById(R.id.switch_wifi);
        Switch switch_hotspot = findViewById(R.id.switch_hotspot);
        Switch switch_bt = findViewById(R.id.switch_bt);
        Switch switch_gc = findViewById(R.id.switch_gc);

        switch_ble.setOnCheckedChangeListener(this);
        switch_wifi.setOnCheckedChangeListener(this);
        switch_hotspot.setOnCheckedChangeListener(this);
        switch_bt.setOnCheckedChangeListener(this);
        switch_gc.setOnCheckedChangeListener(this);

        ActivityCompat.requestPermissions(MainActivity.this, new String[]{Manifest.permission.INTERNET,
                Manifest.permission.ACCESS_WIFI_STATE,
                Manifest.permission.CHANGE_WIFI_STATE,
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.WRITE_SETTINGS,

        }, 0x101);
        WLHardware.getInstance(this.getApplicationContext()).enableLogCat(true);
        WLHardware.getInstance(this.getApplicationContext()).enableLogFile(true);
        _info("onCreate--------");
        updateStatus();
    }

    @Override
    public void onClick(View v) {
        int id=v.getId();
        if (id==R.id.btn_clear_log){
            textViewLog.setText("");
            WLHardware.getInstance(this.getApplicationContext()).close();
            System.exit(0);
        }else if (id==R.id.btn_update_status){
            updateStatus();
        }
    }

    @Override
    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
        if (!buttonView.isPressed())return;
        int id=buttonView.getId();
        if (id==R.id.switch_ble){
            if (isChecked) {
                _info("switch_ble--------open");
                WLHardware.getInstance(this.getApplicationContext()).open(null, 0, 5745, "WeLink", this);
            } else {
                _info("switch_ble--------close");
                WLHardware.getInstance(this.getApplicationContext()).close();
            }
        } else if (id == R.id.switch_wifi) {
            if (isChecked) {
                _info("switch_wifi--------open");
                enableWifi(this, true);
            } else {
                _info("switch_wifi--------close");
                enableWifi(this, false);
            }
        } else if (id == R.id.switch_hotspot) {
            if (isChecked) {
                _info("switch_hotspot--------open");
                wiFiAP.open();
            } else {
                _info("switch_hotspot--------close");
                wiFiAP.close();
            }
        } else if (id == R.id.switch_bt) {
            if (isChecked) {
                _info("switch_bt--------open "+ BluetoothAdapter.getDefaultAdapter().enable());
            } else {
                _info("switch_bt--------close "+BluetoothAdapter.getDefaultAdapter().disable());
            }
        } else if (id == R.id.switch_gc) {
            if (isChecked) {
                _info("switch_gc--------open ");
//                dockChannel.initGC();

            } else {
                _info("switch_gc--------close ");
                dockChannel.disconnect();
                dockChannel.release();
            }
        }
    }

    /**
     * 正在对接设备
     * @param type 对接设备的类型：
     *             Android：0
     *             iOS：1
     */
    @Override
    public void onDocking(int type) {
        _info("onDocking "+type);
    }

    /**
     * 对接成功
     * @param ip 对接设备的IP
     */
    @Override
    public void onDocked(String ip) {
        _info("onDocked  ==========="+ip);
    }

    /**
     * 对接断开
     */
    @Override
    public void onDisdock() {
        _info("onDisdock  ===========");
    }

    /**
     * Wi-Fi、BLE已经关闭
     * @param reason 0: Wi-Fi没有打开；1：BLE没有打开
     */
    @Override
    public void onError(int reason) {
        _info("onError  ===========reason="+reason);
        //如果此处close了, 那就需要在合适的位置去再打开
        WLHardware.getInstance(this.getApplicationContext()).close();
        Switch switch_ble = findViewById(R.id.switch_ble);
        switch_ble.setChecked(false);
    }

    void updateStatus(){
        if (wiFiAP==null){
            wiFiAP=new WiFiAP(this,wiFiAPListener);
        }
        if (dockChannel==null){
            dockChannel=new DockChannel(this,null,dockChannelListener);
        }
        Switch switch_wifi = findViewById(R.id.switch_wifi);
        switch_wifi.setChecked(getWifiStatus(this)== WifiManager.WIFI_STATE_ENABLED);

        Switch switch_hotspot = findViewById(R.id.switch_hotspot);
        switch_hotspot.setChecked(WiFiAP.isEnable(this));

        Switch switch_bt = findViewById(R.id.switch_bt);
        switch_bt.setChecked(BluetoothAdapter.getDefaultAdapter().isEnabled());

        Switch switch_gc = findViewById(R.id.switch_gc);
        switch_gc.setChecked(false);
    }
    //=========================================================
    //辅助操作代码,wifi/hotspot
    public int getWifiStatus(Activity activity) {
        if (ActivityCompat.checkSelfPermission(activity, Manifest.permission.ACCESS_WIFI_STATE) != PackageManager.PERMISSION_GRANTED)
            return WifiManager.WIFI_STATE_UNKNOWN;
        WifiManager wifiManager = (WifiManager) activity.getSystemService(Context.WIFI_SERVICE);
        int wifiState=WifiManager.WIFI_STATE_UNKNOWN;
        if (wifiManager != null) {
            wifiState = wifiManager.getWifiState();
        }
        _info("getWifiStatus return "+wifiState);
        return wifiState;
    }

    //车机上实测, 无需系统签名就能开关wifi
     void enableWifi(Activity activity, boolean enable) {
        WifiManager wm = (WifiManager) activity.getSystemService(Context.WIFI_SERVICE);
        boolean ret=wm.setWifiEnabled(enable);
         _info("enableWifi return "+ret);
     }

     void getHotspotStatus(){

     }
     void enableHotspot(boolean enable){

     }
    WiFiAP wiFiAP ;
    WiFiAPListener wiFiAPListener= new WiFiAPListener() {
        @Override
        public void onOpened() {
            _info("WiFiAPListener  onOpened");
        }
    };

    DockChannel dockChannel;
    DockChannelListener dockChannelListener=new DockChannelListener(){

        @Override
        public void onConnecting(String address, int port) {
            _info("DockChannelListener onConnecting "+address+",port="+port);
        }

        @Override
        public void onConnected(String ip, int port) {
            _info("DockChannelListener onConnected "+ip+",port="+port);
        }

        @Override
        public void onDisconnected() {
            _info("DockChannelListener onDisconnected");
        }

        @Override
        public void onError() {
            _info("DockChannelListener onError");
        }
    };
}