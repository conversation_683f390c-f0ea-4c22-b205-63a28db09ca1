// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    repositories {
        maven{ url 'https://maven.aliyun.com/nexus/content/groups/public/'}
        maven{ url 'https://maven.aliyun.com/nexus/content/repositories/jcenter'}
        maven{ url 'https://maven.aliyun.com/nexus/content/repositories/google'}
//        google()
//        jcenter()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:4.1.3'

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        maven{ url 'https://maven.aliyun.com/nexus/content/groups/public/'}
        maven{ url 'https://maven.aliyun.com/nexus/content/repositories/jcenter'}
        maven{ url 'https://maven.aliyun.com/nexus/content/repositories/google'}
//        google()
//        jcenter()
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}