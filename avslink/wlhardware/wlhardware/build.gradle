plugins {
    id 'com.android.library'
    id 'jacoco'
}

ext {
    VERSION = WLHARDWARE_VERSION
    GROUP_ID = "com.autoai.avs.link"
    ARTIFACT_ID = "wlhardware"
}

if (file("../../maven_push.gradle").exists()) {
    apply from: file('../../maven_push.gradle')
}

android {
    compileSdkVersion 30
    buildToolsVersion "30.0.3"

    def gitCount = Integer.parseInt('git rev-list HEAD --count'.execute([], project.rootDir).text.trim())
    def gitVersion = 'git describe --tags --always --long --dirty=-dev'.execute().text.trim()

    defaultConfig {
        minSdkVersion 26
        targetSdkVersion 30
        versionCode gitCount
        versionName gitVersion
        
        consumerProguardFiles 'consumer-rules.pro'

        buildConfigField "long", "VERSION_CODE", String.valueOf(versionCode)
        buildConfigField "String", "VERSION_NAME", "\"" + versionName + "\""
    }

    signingConfigs {
        release {
            storeFile file("key/wedrive2014.keystore")
            storePassword "wedrive2014"
            keyAlias "wedrive"
            keyPassword "wedrive2014"
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            zipAlignEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    gradle.projectsEvaluated {
        tasks.withType(JavaCompile) {
            File configFile2 = file('libs/framework.jar')
            String absolutePath2 = configFile2.absolutePath.replaceAll("\\\\", "\\\\\\\\")
            String Path2 = '-Xbootclasspath/p:' + absolutePath2
            options.compilerArgs.add(Path2)
        }
    }
    testOptions {
        unitTests.all {
            useJUnitPlatform()
            unitTests.returnDefaultValues = true
        }
    }
}

dependencies {
    implementation project(':linkCommon')

    testImplementation "org.junit.jupiter:junit-jupiter-api:${JUNIT_VERSION}"
    testRuntimeOnly "org.junit.jupiter:junit-jupiter-engine:${JUNIT_VERSION}"
    testImplementation "org.junit.jupiter:junit-jupiter-params:${JUNIT_VERSION}" // 可选，用于参数化测试
    testImplementation "org.mockito:mockito-core:${MOCKITO_VERSION}"
    testImplementation "org.mockito:mockito-inline:${MOCKITO_VERSION}" // 用于模拟 final/static 方法
    testImplementation "org.mockito:mockito-junit-jupiter:${MOCKITO_VERSION}" // 提供 MockitoExtension 支持
}

jacoco {
    toolVersion = "0.8.12" //代码覆盖库jacoco版本号
}
tasks.withType(Test) {
    jacoco.includeNoLocationClasses = true
    jacoco.excludes = ['jdk.internal.*']
}
// 适配多 Flavor 场景， 直接运行 gralde jacocoTestReportT2Debug  即可
android.libraryVariants.configureEach { variant ->
    task "jacocoTestReport${variant.flavorName.capitalize()}${variant.buildType.name.capitalize()}"(type: JacocoReport,
            dependsOn: ["test${variant.flavorName.capitalize()}${variant.buildType.name.capitalize()}UnitTest"]) {
        group = 'Reporting'
        description = 'Generate Jacoco coverage reports after running tests.'
        reports {
            xml.required = true
            csv.required =  false
        }
        def javaClasses = fileTree(dir: "build/intermediates/javac", excludes: ['**/R.class', '**/R$*.class', '**/*$ViewInjector*.*', '**/BuildConfig.*'])
        def kotlinClasses = fileTree(dir: "build/tmp/kotlin-classes/", excludes: ['**/R.class', '**/R$*.class', '**/*$ViewInjector*.*', '**/BuildConfig.*'])
        classDirectories.setFrom(files([javaClasses], [kotlinClasses]))
        sourceDirectories.setFrom(files('src/main/java'))
        executionData.setFrom(fileTree(dir: "build/", includes: ['**/*.exec']))
    }
}