package com.autoai.welink.wireless.utiliy;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.Log;

import com.autoai.welink.wireless.BuildConfig;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.RandomAccessFile;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

public class AnFileLog {
    static private String TAG = "AnFileLog";
    static private String externalDir = "";
    static private final String FLAG_DIR = "/Test/welinklog/wlhardware-log.txt";
    static private final String LOG_FILE = "/wlhardware-log.txt";
    /**
     * 单行日志打印，初始堆栈信息的选取Index，提取出来，默认值为4
     */
    private static final int STACK_INDEX = 4;
    private static final AtomicBoolean isLoggable = new AtomicBoolean(true);
    /**
     * logcat输出的时候，是否同时进行IO输出
     */
    private static final AtomicBoolean filePrintAble = new AtomicBoolean(false);
    static public void init(Context context) {
        File file = context.getExternalFilesDir(null);
        if (file != null) {
            externalDir = file.getAbsolutePath()+FLAG_DIR;
            Log.d(TAG, "externalDir==="+externalDir);
            com.tencent.mars.xlog.Log.d(TAG, "externalDir==="+externalDir);
        }
    }
    /**
     * 日志级别：任何信息
     */
    private static final int VERBOSE = 1;
    /**
     * 日志级别：调试
     */
    private static final int DEBUG = 2;
    /**
     * 日志级别：信息
     */
    private static final int INFO = 3;
    /**
     * 日志级别：警告
     */
    private static final int WARN = 4;
    /**
     * 日志级别:错误
     */
    private static final int ERROR = 5;

    private static final class FilePrintRunnable implements Runnable {
        private PrintWriter printWriter = null;
        private final int level;
        private final String tag;
        private final String msg;
        private final Throwable t;
        private String logFileName;

        private FilePrintRunnable(int level, String tag, String msg, Throwable t) {
            this.level = level;
            this.tag = tag;
            this.msg = msg;
            this.t = t;
        }

        /**
         * 格式化时间
         */
        private static String dateTimeFormat( String pattern) {
            @SuppressLint("SimpleDateFormat") SimpleDateFormat timeFormat = new SimpleDateFormat(pattern);
            return timeFormat.format(new Date());
        }

        @Override
        public void run() {
            boolean exception = false;
            try {
                if (null == printWriter) {
                    String filePath = logFileName;
                    if (filePath==null){
                        return;
                    }
                    checkLogFile(filePath);
                    File logFile = new File(filePath);
                    printWriter = new PrintWriter(new FileOutputStream(logFile, true));
                }

                // 级别
                printWriter.print("|");
                printWriter.print(levelName(level));

                // 时间
                printWriter.print("|");
                printWriter.print(dateTimeFormat("MM-dd HH:mm:ss.SSS"));

                // TAG
                printWriter.print("|");
                printWriter.print(tag);

                // 信息
                printWriter.print("|");
                printWriter.print(msg);

                // 异常
                if (null != t) {
                    t.printStackTrace(printWriter);
                }

                printWriter.println();
                printWriter.flush();

            } catch (IOException e) {
                Log.e(TAG, "", e);
                exception = true;
            }
            boolean closeFlag = workQueue.isEmpty() || exception;
            if (printWriter != null && closeFlag) {
                printWriter.close();
                printWriter = null;
            }
        }

        private String levelName(int level) {
            switch (level) {
                case VERBOSE:
                    return "VERBOSE";
                case DEBUG:
                    return "DEBUG";
                case INFO:
                    return "INFO";
                case WARN:
                    return "WARN";
                case ERROR:
                    return "ERROR";
                default:
                    return "DEFAULT";
            }
        }

        private void setLogFileName(String aLogFileName) {
            logFileName = aLogFileName;
        }
    }

    static public void enableLogCat(boolean enable) {
        isLoggable.set(enable);
        Log.d(TAG, "enable==="+enable);
        com.tencent.mars.xlog.Log.d(TAG, "enable==="+enable);
    }

    static public void enableLogFile(boolean enable) {
        Log.d(TAG, "enableLogFile==="+enable);
        com.tencent.mars.xlog.Log.d(TAG, "enableLogFile==="+enable);
        filePrintAble.set(enable);
    }

    static public void e(String tag, String msg) {
        Log.i(tag, msg);
        com.tencent.mars.xlog.Log.i(tag, msg);
//        /*if (outputLogcat) {//既按指定tag输出,也会按统一的tag输出一次,便于同时查看所有log
//            Log.w(tag, str);
//        }*/
//        if(isLoggable.get()) {
//            smartPrint(DEBUG, tag, msg, null, false);
//        }
    }

    public static void printStackTraceString(String tag, String msg) {
        String stackTraceString =
                Log.getStackTraceString(new Throwable(msg));
        Log.e(
                tag,
                " \n" +
                        "  调用栈： ==》 " + stackTraceString
        );
        com.tencent.mars.xlog.Log.e(
                tag,
                " \n" +
                        "  调用栈： ==》 " + stackTraceString
        );
    }

    private static final BlockingQueue<Runnable> workQueue = new LinkedBlockingQueue<>();
    private static final ExecutorService executorService = new ThreadPoolExecutor(1,
            1,
            0L,
            TimeUnit.MILLISECONDS,
            workQueue,
            r -> new Thread(r, "welinklauncher_LogManager_executorService"));

    private static void filePrint(int level, String tag, String msg, Throwable t) {
        FilePrintRunnable fp = new FilePrintRunnable(level, tag, msg, t);
        fp.setLogFileName(externalDir);
        executorService.submit(fp);
    }

    private static void checkLogFile(String filePath) {
        File file = new File(filePath);
        if (!file.exists()) {
            File parentFile = file.getParentFile();
            if (parentFile != null) {
                boolean mkdirs = parentFile.mkdirs();
//                i(TAG, !mkdirs ? file.getAbsolutePath() + "文件夹创建失败" : "文件夹创建成功");
            }
            boolean newFile = false;
            try {
                newFile = file.createNewFile();
            } catch (IOException e) {
                i(TAG, e.getMessage());
            }
            if (!newFile) {
//                i(TAG, file.getAbsolutePath() + file.getName() + "文件创建失败");
            }
        } else {
//            i(TAG, file.getAbsolutePath() + "文件已存在");
        }
    }
    public static void i(String tag, String msg) {
        smartPrint(INFO, tag, msg, null, false);
    }
    private static void smartPrint(int level, String tag, String msg, Throwable t, boolean printStackTrace) {

        //当前获取的为主线程
        Thread currentThread = Thread.currentThread();
        StackTraceElement[] stackTrace = currentThread.getStackTrace();
        int i = STACK_INDEX;
        String msgResult = currentThread.getId() +
                "|" + getCodeLocation(CodeLocationStyle.FIRST, stackTrace[i]) +
                "|" + msg;
        print(level, tag, msgResult, t);
        if (filePrintAble.get()) {
            filePrint(level, tag, msgResult, t);
        }
        i++;
        for (; printStackTrace && i < stackTrace.length; i++) {
            String s = getCodeLocation(CodeLocationStyle.SUBSEQUENT, stackTrace[i]).toString();
            print(level, tag, s, null);
            if (filePrintAble.get()) {
                filePrint(level, tag, s, null);
            }
        }
    }

    private static StringBuilder getCodeLocation(CodeLocationStyle style, StackTraceElement stackTraceElement) {
        String className = stackTraceElement.getClassName();
        int lineNumber = stackTraceElement.getLineNumber();
        String methodName = stackTraceElement.getMethodName();
        String fileName = stackTraceElement.getFileName();
        StringBuilder sb = new StringBuilder();
        if (style.isAt()) {
            sb.append("	at ");
        }
        if (style.isSimpleClassName()) {
            sb.append(getSimpleName(className));
        } else {
            sb.append(className);
        }
        sb.append(".").append(methodName).append("(").append(fileName).append(":").append(lineNumber).append(")");
        return sb;
    }
    private static String getSimpleName(String className) {
        String[] split = className.split("\\.");
        return split[split.length - 1];
    }
    public enum CodeLocationStyle {
        /**
         * 第一行
         */
        FIRST(true, true),
        /**
         * 随后的行
         */
        SUBSEQUENT(true, true);

        /**
         * 是否添加at字眼在行首
         */
        private final boolean isAt;

        /**
         * 是否使用简单类名
         */
        private final boolean isSimpleClassName;

        CodeLocationStyle(boolean isAt, boolean isSimpleClassName) {
            this.isAt = isAt;
            this.isSimpleClassName = isSimpleClassName;
        }

        /**
         * @return the {@link #isAt}
         */
        public boolean isAt() {
            return isAt;
        }

        /**
         * @return the {@link #isSimpleClassName}
         */
        public boolean isSimpleClassName() {
            return isSimpleClassName;
        }

    }
    private static void print(int level, String tag, String msg, Throwable t) {
        switch (level) {
            case VERBOSE:
                dealVerbose(tag, msg, t);
                break;
            case DEBUG:
                dealDebug(tag, msg, t);
                break;
            case INFO:
                if (t != null) {
                    Log.i(tag, msg, t);
                    com.tencent.mars.xlog.Log.i(tag, msg, t);
                } else {
                    Log.i(tag, msg);
                    com.tencent.mars.xlog.Log.i(tag, msg);
                }
                break;
            case WARN:
                if (t != null) {
                    Log.w(tag, msg, t);
                    com.tencent.mars.xlog.Log.w(tag, msg, t);
                } else {
                    Log.w(tag, msg);
                    com.tencent.mars.xlog.Log.w(tag, msg);
                }
                break;
            case ERROR:
                if (t != null) {
                    Log.e(tag, msg, t);
                    com.tencent.mars.xlog.Log.e(tag, msg, t);
                } else {
                    Log.e(tag, msg);
                    com.tencent.mars.xlog.Log.e(tag, msg);
                }
                break;
            default:
                break;
        }
    }
    private static void dealVerbose(String tag, String msg, Throwable t) {
        if (t != null) {
            Log.v(tag, msg, t);
            com.tencent.mars.xlog.Log.v(tag, msg, t);
        } else {
            Log.v(tag, msg);
            com.tencent.mars.xlog.Log.v(tag, msg);
        }
    }

    private static void dealDebug(String tag, String msg, Throwable t) {
        if (t != null) {
            Log.d(tag, msg, t);
            com.tencent.mars.xlog.Log.d(tag, msg, t);
        } else {
            Log.d(tag, msg);
            com.tencent.mars.xlog.Log.d(tag, msg);
        }
    }
}

