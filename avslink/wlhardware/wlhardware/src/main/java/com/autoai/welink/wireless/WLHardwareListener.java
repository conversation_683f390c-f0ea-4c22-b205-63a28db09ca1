package com.autoai.welink.wireless;

public interface WLHardwareListener {
    /**
     * 正在对接设备
     * @param type 对接设备的类型：
     *             Android：0
     *             iOS：1
     *             Harmony:2
     */
    void onDocking(int type);

    /**
     * 对接成功
     * @param ip 对接设备的IP
     */
    void onDocked(String ip);

    /**
     * 对接断开
     */
    void onDisdock();

    /**
     * Wi-Fi、BLE已经关闭
     * @param reason 0: Wi-Fi没有打开；1：BLE没有打开
     */
    void onError(int reason);
}
