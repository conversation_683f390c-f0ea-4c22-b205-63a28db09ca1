package com.autoai.welink.wireless.ap;

import static android.os.Build.VERSION.SDK_INT;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.wifi.WifiConfiguration;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.os.Handler;
import android.util.Log;

import com.autoai.welink.wireless.utiliy.AnFileLog;
import com.autoai.welink.wireless.utiliy.MainHandler;
import com.autoai.welink.wireless.utiliy.WiFiUtil;

import java.lang.reflect.Method;
import java.net.NetworkInterface;
import java.util.Collections;
import java.util.List;

/**
 * 负责控制车机Wi-Fi AP的打开与关闭
 */
public class WiFiAP {
    WiFiAPListener listener;
    Context mContext;
    private String ssid;
    public static String passwd="123456789";
    public static String apName="Llink_"+Build.MODEL;
    /**
     * 查看是否支持5g  热点, 只获取一次
     */
    private static Boolean is5GHzBandSupported = null;

    /**
     *  open 重试次数
     */
    public static int count = 0;
    public WiFiAP(Context context, WiFiAPListener listener) {
        mContext = context;
        this.listener = listener;
        this.ssid = WiFiUtil.getApName(context);
        setPassword(WiFiUtil.getApPassword(context));
        Log.i("shecw4","WiFiAP:: this.ssid = " + this.ssid
                + " ;this.passwd = " + this.passwd
                + " ;this.apName = " + this.apName);
    }

    /**
     * 获取Wi-Fi AP的SSID
     */
    public String getSSID() {
        AnFileLog.e("wlhw-ap", "getSSID " + this.ssid);
        return ssid;
    }

    /**
     * 获取Wi-Fi AP的密码
     */
    public String getPassword() {
        return passwd;
    }

    /**
     * 获取Wi-Fi AP的密码
     */
    public void setPassword(String passwd) {
        if (passwd!=null && passwd.length()>1) {
            this.passwd = passwd;
            AnFileLog.e("wlhw-ap", "setPassword " + this.passwd);
        }else {
            AnFileLog.e("wlhw-ap", "setPassword fail!" + passwd);
        }
    }

    //判断wifiap是否已经开启
    public boolean isEnable(){
        try {
            if (wifiManager == null) {
                wifiManager = (WifiManager) mContext.getSystemService(Context.WIFI_SERVICE);
            }
            if (wifiManager != null) {
                boolean enable=wifiManager.isWifiApEnabled();

                AnFileLog.e("wlhw-ap", "isWIFIAPEnable " + enable+",is5GHzBandSupported:"+wifiManager.is5GHzBandSupported());
                return enable;
            }
        } catch (Exception e) {
            AnFileLog.e("wlhw-ap", "getCurrentConfigPassword  fail! " + e);
        }
        return false;
    }

    /**
     * 是否支持5g热点
     */
    public boolean is5GHzBandSupported(){
        if (wifiManager == null) {
            wifiManager = (WifiManager) mContext.getSystemService(Context.WIFI_SERVICE);
        }
        if (wifiManager != null) {
            is5GHzBandSupported = wifiManager.is5GHzBandSupported();
            AnFileLog.e("wlhw-ap", "is5GHzBandSupported:"+is5GHzBandSupported);
            return is5GHzBandSupported;
        }
        return false;
    }


    /**
     * 打开Wi-Fi AP
     */
    public void open() {
        try {
            if (WiFiUtil.getWifiStatus(mContext) == WifiManager.WIFI_STATE_ENABLED){
                if (android.os.Build.VERSION.SDK_INT < 30) {
                    WiFiUtil.enableWifi(mContext, false);
                }
            }
            if (connectivityManager == null) {
                connectivityManager = (ConnectivityManager) mContext.getSystemService(Context.CONNECTIVITY_SERVICE);
            }
            AnFileLog.e("wlhw-ap", " WiFiAP::open() open wifi ap");
            setWifiInfo();
            //无返回值
            connectivityManager.startTethering(ConnectivityManager.TETHERING_WIFI, true, mTetheringCallback);
        } catch (Exception e) {
            AnFileLog.e("wlhw-ap", "WiFiAP::open  fail! " + e);
        }
    }

    /**
     * 关闭Wi-Fi AP
     */
    public void close() {
        try {
            if (connectivityManager == null) {
                connectivityManager = (ConnectivityManager) mContext.getSystemService(Context.CONNECTIVITY_SERVICE);
            }
            AnFileLog.e("wlhw-ap", "WiFiAP::close wifi ap");
            //无返回值
            connectivityManager.stopTethering(ConnectivityManager.TETHERING_WIFI);
        } catch (Exception e) {
            AnFileLog.e("wlhw-ap", "WiFiAP::close  fail! " + e);
        }
    }

    ConnectivityManager.OnStartTetheringCallback mTetheringCallback = new ConnectivityManager.OnStartTetheringCallback() {
        public void onTetheringStarted() {
            AnFileLog.e("wlhw-ap", "WiFiAP::OnStartTetheringCallback::onTetheringStarted");
//            setWifiInfo();
            if (listener != null) {
                listener.onOpened();
            }
        }

        public void onTetheringFailed() {
            AnFileLog.e("wlhw-ap", "WiFiAP::OnStartTetheringCallback::onTetheringFailed");
            count++;
            if(count < 10) {
                new Handler(MainHandler.getInstance().getLooper()).postDelayed(() -> {
                    //close 后open 会打开失败，所以再打开一次
                    open();
                }, 500);
            }else{
                count = 0;
            }
        }
    };

    ConnectivityManager connectivityManager;
    WifiManager wifiManager;
    WifiConfiguration currentConfig;

    /**
     * 设置热点信息
     */
    public void setWifiInfo() {
        AnFileLog.e("wlhw-ap", "setWifiInfo in");
        try {
            if (wifiManager == null) {
                wifiManager = ((WifiManager) mContext.getSystemService(Context.WIFI_SERVICE));
            }
            if (android.os.Build.VERSION.SDK_INT >= 30) {
                setAPConfig();
            } else {
                //WifiConfiguration config = new WifiConfiguration();
                WifiConfiguration config = wifiManager.getWifiApConfiguration();
                AnFileLog.e("wlhw-ap", "WiFiAP::setWifiInfo() getWifiApConfiguration =" + config);
                config.preSharedKey = getPassword();
                config.SSID = getSSID();
                //--> 0:2.4GHz频段  1:5GHz频段  2:自动选择(默认值)
                config.apBand = 1;
                AnFileLog.e("wlhw-ap", "WiFiAP::setWifiInfo() ssid=" + config.SSID + ",passwd=" + config.preSharedKey);
                //android.net.IpConfiguration dhcpConfiguration = new android.net.IpConfiguration(android.net.IpConfiguration.IpAssignment.DHCP, android.net.IpConfiguration.ProxySettings.NONE, null, null);
                android.net.IpConfiguration dhcpConfiguration = config.getIpConfiguration();
                AnFileLog.e("wlhw-ap", "getIpConfiguration=" + dhcpConfiguration);
                /*8257车机 打开热点出现代码和手动打开热点都打不开异常，经沟通 屏蔽此ip功能 ，添加热点权限
                dhcpConfiguration.setIpAssignment(android.net.IpConfiguration.IpAssignment.DHCP);
                config.setIpConfiguration(dhcpConfiguration);*/
                wifiManager.setWifiApConfiguration(config);
                int ret = wifiManager.updateNetwork(config);
                AnFileLog.e("wlhw-ap", "wifiManager.updateNetwork" + ret);
            }
        } catch (Exception e) {
            AnFileLog.e("wlhw-ap", "error setWifiInfo fail!!!" + e);
        }
    }

    /**
     * 从新配置开启AP热点信息
     */
    public void setAPConfig() {
        AnFileLog.e("wlhw-ap", "setAPConfig for version >= 30");
        try {
            // 反射获取SoftApConfiguration类
            Class<?> softApConfigClass = Class.forName("android.net.wifi.SoftApConfiguration");
            // 反射获取SoftApConfiguration.Builder类
            Class<?> builderClass = Class.forName("android.net.wifi.SoftApConfiguration$Builder");

            // 创建Builder实例
            Object builderInstance = builderClass.getDeclaredConstructor().newInstance();

            // 设置热点名称 (SSID)
            Method setSsidMethod = builderClass.getDeclaredMethod("setSsid", String.class);
            setSsidMethod.invoke(builderInstance, getSSID());

            // 设置安全类型为WPA2
            Method setPassphraseMethod = builderClass.getDeclaredMethod("setPassphrase", String.class, int.class);
            setPassphraseMethod.invoke(builderInstance, getPassword(), 1); // 1表示WPA2_PSK
           //获取5Ghz 值
          if(is5GHzBandSupported()){
            int apType = softApConfigClass.getDeclaredField("BAND_5GHZ").getInt(null);
            AnFileLog.e("wlhw-ap", "apType= "+apType);
            //设置热点类型5Ghz  36,5GHz 频段的一个信道

              Method setBandMethod = builderClass.getMethod("setBand", int.class);
              setBandMethod.invoke(builderInstance, apType);
              //可选设置5Ghz 频段
//            Method setApTypeMethod = builderClass.getDeclaredMethod("setChannel", int.class, int.class);
//            setApTypeMethod.invoke(builderInstance, 36, apType);
            }

            // 构建SoftApConfiguration实例
            Method buildMethod = builderClass.getDeclaredMethod("build");
            Object softApConfigInstance = buildMethod.invoke(builderInstance);

            Method setSoftApMethod = wifiManager.getClass().getDeclaredMethod("setSoftApConfiguration", softApConfigClass);
            setSoftApMethod.invoke(wifiManager, softApConfigInstance);
        } catch (Exception e) {
            AnFileLog.e("wlhw-ap", "error setWifiInfo fail!!! " + e);
        }
    }

    //获取实际配置中的ssid
    public String getCurrentConfigSSID(){
        try {
            if (wifiManager == null) {
                wifiManager = (WifiManager) mContext.getSystemService(Context.WIFI_SERVICE);
            }
            if (wifiManager != null) {
                currentConfig = wifiManager.getWifiApConfiguration();
            }
            if (currentConfig != null) {
         	   AnFileLog.e("wlhw-ap", "getCurrentConfigSSID " + currentConfig.SSID);
                return currentConfig.SSID;
            }
        } catch (Exception e) {
            AnFileLog.e("wlhw-ap", "getCurrentConfigSSID  fail! " + e);
        }
        return null;
    }
    //获取实际配置中的密码
    public String getCurrentConfigPassword(){
        try {
            if (wifiManager == null) {
                wifiManager = (WifiManager) mContext.getSystemService(Context.WIFI_SERVICE);
            }
            if (wifiManager != null) {
                currentConfig = wifiManager.getWifiApConfiguration();
            }
            if (currentConfig != null) {
            AnFileLog.e("wlhw-ap", "getCurrentConfigPassword " + currentConfig.preSharedKey);
                return currentConfig.preSharedKey;
            }
        } catch (Exception e) {
            AnFileLog.e("wlhw-ap", "getCurrentConfigPassword  fail! " + e);
        }
        return null;
    }

    public static String getMacFromHardware() {
        try {
            List<NetworkInterface> all = Collections.list(NetworkInterface.getNetworkInterfaces());
            for (NetworkInterface nif : all) {
                if (!nif.getName().equalsIgnoreCase("wlan0")) continue;

                byte[] macBytes = nif.getHardwareAddress();
                if (macBytes == null) {
                    return "";
                }

                StringBuilder res1 = new StringBuilder();
                for (byte b : macBytes) {
                    res1.append(String.format("%02X:", b));
                }

                if (res1.length() > 0) {
                    res1.deleteCharAt(res1.length() - 1);
                }
                return res1.toString();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "FF:FF:FF:FF:FF:FF";
    }
}
