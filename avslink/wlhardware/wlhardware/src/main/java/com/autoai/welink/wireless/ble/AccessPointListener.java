package com.autoai.welink.wireless.ble;

/**
 * 负责完成BLE时序
 */
public interface AccessPointListener {
    /**
     * 正在对接设备
     * @param type 对接设备的类型：
     *             0: 需要做为gc去连接go, GO签名为参数signature, 连接超时为go_timeout
     *             1: 本机创建热点供其他设备加入热点, 热点信息为参数ssid和passwd
     *             2: 加入指定热点,, 热点信息为参数ssid和passwd
     * @param signature GO签名，type为0时值有意义
     * @param go_timeout 连接GO的超时时间，type为0时值有意义
     * @param ssid 热点ssid，type为1或2时值有意义
     * @param passwd 热点密码，type为1或2时值有意义
     */
    void onAccess(int type, String signature, int go_timeout, String ssid, String passwd);
    /**
     * BLE模块出现错误
     */
    void onError();
    /**
     * BLE连接后连续读了2次，没有执行写操作
     */
    void onReadTwiceWithoutWrite();
}
