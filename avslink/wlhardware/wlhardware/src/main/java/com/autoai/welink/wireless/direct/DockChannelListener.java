package com.autoai.welink.wireless.direct;

public interface DockChannelListener {

    /**
     * 外部调用，主动断开连接
     */
     static final int DOCK_CHANNEL_DISCONNECTED_REASON_POSITIVE_DISCONNECT = 1;

    static final int DOCK_CHANNEL_DISCONNECTED_REASON_INNER_DISCONNECT = 2;

    void onConnecting(String address, int port);
    void onConnected(String ip, int port);

    /**
     *
     */
    void onDisconnected(int reason);
    void onError();
}
