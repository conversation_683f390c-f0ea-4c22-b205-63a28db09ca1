package com.autoai.welink.wireless.net;

import static com.autoai.welink.wireless.net.NetInspectorListener.NET_INSPECTOR_DISCONNECTED_REASON_POSITIVE_DISCONNECT;

import android.os.Handler;

import com.autoai.welink.wireless.utiliy.AnFileLog;
import com.autoai.welink.wireless.utiliy.MainHandler;

import java.net.HttpURLConnection;
import java.net.SocketTimeoutException;
import java.net.URL;

/**
 *  1. 通过iOS UDP广播判断连接是否成功、连接是否断开
 *  2. 通过Android 硬件心跳服务判断连接是否断开
 */
public class NetInspector {
    private final NetInspectorListener listener;


    /**
     * 包名，用于心跳包携带字段
     */
    private final String packageName;
    private final int port;
    private final String deviceBrand;

    /**
     * ios 接收udp端口号
     */
    private static final int IOS_UDP_PORT = 6840;

    /**
     * 心跳包频率
     */
    private static final int HTTP_BEAT_HEART_TIMEOUT = 1000;
    private static final int HTTP_BEAT_HEART_TIMEOUT_COUNT = 2;
    private final long INSPECT_TIMEOUT;

    /**
     * 心跳包线程
     */
    private Thread androidHeatBeatThread = null;

    private UDPReceive iOSUDPReceive = null;
    private boolean iOSConnected = false;
    private Handler timeout = null;
    private Runnable timeoutRunnable = null;

    public NetInspector(String packageName, int port, String deviceBrand, long INSPECT_TIMEOUT, NetInspectorListener listener) {
        this.packageName = packageName != null? packageName: "";
        this.port = port;
        this.deviceBrand = deviceBrand != null? deviceBrand: "";
        this.INSPECT_TIMEOUT = INSPECT_TIMEOUT;
        this.listener = listener;
    }

    public void startWithiOS() {
        iOSDisconnect();
        AnFileLog.e("wlhw-net","startWithiOS UDPReceive INSPECT_TIMEOUT="+INSPECT_TIMEOUT);
        iOSConnected = false;
        timeout = new Handler(MainHandler.getInstance().getLooper());
        timeoutRunnable = this::iOSDisconnect;
//        timeout.postDelayed(timeoutRunnable, INSPECT_TIMEOUT);

        iOSUDPReceive = new UDPReceive(IOS_UDP_PORT, new UDPReceiveListener() {
            @Override
            public void onReceive(String ip, String content) {
                AnFileLog.e("wlhw-net","UDPReceive onReceive ip="+ip+",content="+content+",iOSConnected:"+iOSConnected);
                if (!content.equals("welink:") &&
                        (!content.startsWith("welink:") || !content.contains(":" + deviceBrand)) &&
                        !content.startsWith("WLServer")) {
                    return;
                }
                if (!iOSConnected) {
                    iOSConnected = true;
                    listener.onIOSDeviceConnected(ip,content);
                }

                if (timeout != null) {
                    AnFileLog.e("wlhw-net","UDPReceive onReceive postDelayed iOSConnected:"+iOSConnected);
                    timeout.removeCallbacks(timeoutRunnable);
                    timeout.postDelayed(timeoutRunnable, INSPECT_TIMEOUT * 2);
                }
            }

            @Override
            public void onError() {
                AnFileLog.e("wlhw-net","UDPReceive onError");
               iOSDisconnect();
            }
        });
    }



    /**
     * 无感互联成功后，发送心跳包，作为信道层连接状态
     * @param ip
     * @param http_port
     */
    public void startWithAndroid(String ip, int http_port) {
        AnFileLog.e("wlhw-net","NetInspector::startWithAndroid INSPECT_TIMEOUT = " + INSPECT_TIMEOUT);
        androidDisconnect();
//        iOSDisconnect();
        timeout = new Handler(MainHandler.getInstance().getLooper());
        timeoutRunnable = this::androidDisconnect;
        timeout.postDelayed(timeoutRunnable, INSPECT_TIMEOUT * 2);
        androidHeatBeatThread = new Thread() {
            /**
             * 添加重试次数
             */
            int countTimeout = 0;
            @Override
            public void run() {
                while (!isInterrupted()) {
                    HttpURLConnection connection = null;
                    try {
                        AnFileLog.e("wlhw-net","NetInspector::androidThread  connection.connect sleep="+HTTP_BEAT_HEART_TIMEOUT+",ConnectTimeout="+INSPECT_TIMEOUT);

                        String urlstring="http://" + ip + ":" + http_port + "/signup?ver=1&package=" + packageName + "&port=" + port + "&device=" + deviceBrand;
                        URL url = new URL(urlstring);
                        AnFileLog.e("wlhw-net","NetInspector::androidThread connection.connect "+urlstring);
                        connection = (HttpURLConnection) url.openConnection();

                        connection.setRequestMethod("GET");
                        connection.setReadTimeout((int)INSPECT_TIMEOUT);
                        connection.setConnectTimeout((int)INSPECT_TIMEOUT);

                        long t1=System.currentTimeMillis();
                        connection.connect();
                        AnFileLog.e("wlhw-net","NetInspector::androidThread 4 无感互联 车机端发送心跳包 start  -->" + System.currentTimeMillis());

                        if (connection.getResponseCode() == 200) {
                            AnFileLog.e("wlhw-net","NetInspector::androidThread 5 无感互联 成功收到心跳包 start  -->" + System.currentTimeMillis());
                            countTimeout = 0;
                            if (timeout != null) {
                                timeout.removeCallbacks(timeoutRunnable);
                                timeout.postDelayed(timeoutRunnable, (INSPECT_TIMEOUT + HTTP_BEAT_HEART_TIMEOUT) * 2);
                            }
                            long t2=System.currentTimeMillis();
                            AnFileLog.e("wlhw-net","NetInspector::androidThread  connection.connect onHeartbeat cost="+(t2-t1));
                            listener.onAndroidDeviceHeartbeat();
                        } else {
                            AnFileLog.e("wlhw-net", "androidDisconnect 2: " + connection.getResponseCode());
                            new Handler(MainHandler.getInstance().getLooper()).post(() -> androidDisconnect());
                        }
                    } catch (SocketTimeoutException e) {
                        AnFileLog.e("wlhw-net","SocketTimeoutException: " + e.getMessage());
                        if (++countTimeout >= HTTP_BEAT_HEART_TIMEOUT_COUNT) {
                            AnFileLog.e("wlhw-net","androidDisconnect 3: " + e.getMessage());
                            new Handler(MainHandler.getInstance().getLooper()).post(() -> androidDisconnect());
                            return;
                        }
                    } catch (Exception e) {
                        AnFileLog.e("wlhw-net","androidDisconnect 4: " + e.getMessage());
                        if (++countTimeout >=5) {
                            //其他异常重试5次后断开
                            new Handler(MainHandler.getInstance().getLooper()).post(() -> androidDisconnect());
                            return;
                        }
                    } finally {
                        if (connection != null) {
                            connection.disconnect();
                        }
                    }
                    try {
                        sleep(HTTP_BEAT_HEART_TIMEOUT);
                    } catch (InterruptedException e) {
                        return;
                    }
                }
            }
        };

        androidHeatBeatThread.start();
    }




    public void stop() {
        //--> 断开ios
        AnFileLog.e("wlhw-net","NetInspector::stop  iOSDisconnect");
        iOSDisconnect();

        //--> 断开Android
        AnFileLog.e("wlhw-net","NetInspector::stop  androidDisconnect");
        androidDisconnect();
    }

    /**
     * android 断开连接回调
     */
    private void androidDisconnect() {
        AnFileLog.e("wlhw-net","NetInspector::androidDisconnect "+ androidHeatBeatThread);
        if (timeout != null) {
            timeout.removeCallbacks(timeoutRunnable);
            timeout = null;
        }
        timeoutRunnable = null;
        if (androidHeatBeatThread != null) {
            androidHeatBeatThread.interrupt();
            androidHeatBeatThread = null;
            listener.onDisconnected(NET_INSPECTOR_DISCONNECTED_REASON_POSITIVE_DISCONNECT);
        }
    }


    /**
     * ios断开连接回调
     */
    public void iOSDisconnect() {
        AnFileLog.e("wlhw-net","NetInspector::iOSDisconnect "+iOSUDPReceive+",iOSConnected="+iOSConnected);
        if (iOSUDPReceive != null) {
            iOSUDPReceive.release();
            iOSUDPReceive = null;
            if (timeout != null) {
                timeout.removeCallbacks(timeoutRunnable);
                timeout = null;
            }
            timeoutRunnable = null;
            if(iOSConnected) {
                iOSConnected = false;
                // 临时修改 解决ios第一次无感连接 必现断开的问题，注释这里的原因问光诚
                //listener.onDisconnected();
            }
        }
    }


    public void stopIOSUdp() {
        AnFileLog.e("wlhw-net","NetInspector::stopIOSUdp = "+iOSUDPReceive+",iOSConnected="+iOSConnected);
        if (iOSUDPReceive != null) {
            iOSUDPReceive.release();
            iOSUDPReceive = null;
            if (timeout != null) {
                timeout.removeCallbacks(timeoutRunnable);
                timeout = null;
            }
            timeoutRunnable = null;
            if(iOSConnected) {
                iOSConnected = false;
            }
        }
    }
}
