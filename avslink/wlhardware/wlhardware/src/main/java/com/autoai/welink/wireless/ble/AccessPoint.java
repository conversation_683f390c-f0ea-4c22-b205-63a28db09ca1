package com.autoai.welink.wireless.ble;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattCharacteristic;
import android.bluetooth.BluetoothGattDescriptor;
import android.bluetooth.BluetoothGattServer;
import android.bluetooth.BluetoothGattServerCallback;
import android.bluetooth.BluetoothGattService;
import android.bluetooth.BluetoothManager;
import android.bluetooth.BluetoothProfile;
import android.bluetooth.le.AdvertiseCallback;
import android.bluetooth.le.AdvertiseData;
import android.bluetooth.le.AdvertiseSettings;
import android.bluetooth.le.BluetoothLeAdvertiser;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.os.Handler;
import android.os.ParcelUuid;
import android.text.TextUtils;

import com.autoai.welink.wireless.WLHardware;
import com.autoai.welink.wireless.utiliy.AnFileLog;
import com.autoai.welink.wireless.utiliy.MainHandler;
import com.autoai.welink.wireless.utiliy.WiFiUtil;

import java.util.Arrays;
import java.util.HashSet;
import java.util.UUID;

/**
 * 负责完成BLE接入时序
 */
public class AccessPoint {

    /**
     * 在ble通信作为数据提供方进行数据交互 每一步通信超时时间
     */
    private static final long ACCESS_POINT_TIMEOUT = 5000;

    /**
     * 上下文
     */
    Context mContext;

    /**
     * 负责蓝牙ble对外部事件callback
     */
    private final AccessPointListener listener;

    /**
     * 蓝牙系统相关action的监听
     */
    BluetoothMonitorReceiver bleListenerReceiver;
    private boolean bleConnected =false;
    /**
     * she tips ：MainHandler非主ui线程，单独设计的线程
     */
    private Handler timeout =  new Handler(MainHandler.getInstance().getLooper());;
    private Runnable timeoutRunnable = new Runnable() {
        @Override
        public void run() {
            AnFileLog.e("wlhw-ble","AccessPoint::timeoutRunnable call cancelBLEConnection");
            cancelBLEConnection();
        }
    };
    private int readCnt=0;
//    private boolean writeBLE=false;

    int Frequency=5745;//带一个默认值
    private String DeviceBrand;

    /**
     * 系统蓝牙管manager
     */
    private BluetoothManager mBluetoothManager;

//    BluetoothDevice mBluetoothDevice = null;
    /**
     * 保存连接蓝牙设备
     */
    private HashSet<BluetoothDevice> mBluetoothDevice = new HashSet<>();

    /**
     * 系统adapter，是实际蓝牙api通用接口适配
     */
    private BluetoothAdapter mBTAdapter;

    /**
     * 用于设备被发现的ble mBTAdvertiser
     */
    private BluetoothLeAdvertiser mBTAdvertiser;

    /**
     * ble通信【中心设备】，gatt协议结构
     *  GattServer
     *    -- GattService A          -> uuid   xx
     *      -- GattCharacteristic   -> uuid   xx
     *      -- GattCharacteristic   -> uuid   xx
     *      -- GattCharacteristic   -> uuid   xx
     *      -- GattDescriptor       -> uuid   xx
     *   -- GattService B
     *       -- GattCharacteristic  -> uuid   xx
     *       -- GattCharacteristic  -> uuid   xx
     *       -- GattCharacteristic  -> uuid   xx
     *       -- GattDescriptor      -> uuid   xx
     */
    BluetoothGattServer mBluetoothGattServer;

    /**
     * 用于承载 ble数据交互的GattService
     */
    BluetoothGattService mBluetoothGattService;
    /**
     * 系统注册 {@link #mBluetoothGattService} 的身份uuid
     */
    public static final UUID bleUUID_Service = UUID.fromString("f56d4814-aac0-c56a-fc87-cd4d6980d247");

    /**
     * 用于蓝牙ble广播识别的uuid，用于手机端scan时候进行匹配
     */
    private final UUID  bleAdvertisetUuid = new UUID(0xf56d4814aac0c56aL, 0xfc87cd4d6980d247L);


    //====↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓====
    /**
     * 版本号 welink_version GattCharacteristic
     */
    BluetoothGattCharacteristic mBluetoothGattCharacteristic_version;

    /**
     * 系统注册 {@link #mBluetoothGattCharacteristic_version} 的身份 uuid
     */
    public static final UUID bleUUID_Version = UUID.fromString("cc1dffbf-73ff-17ad-af07-e625edd8279f");

    /**
     * 系统注册 {@link #mBluetoothGattCharacteristic_version} 的实际value
     */
    String welinkVersion = "v,1";
    //====↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑=====



    //====↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓====
    /**
     * 设备类型Android|ios GattCharacteristic
     */
    BluetoothGattCharacteristic mBluetoothGattCharacteristic_type;

    /**
     * 系统注册 {@link #mBluetoothGattCharacteristic_type} 的身份 uuid
     */
    public static final UUID bleUUID_Type = UUID.fromString("7bdfd2e5-d7b5-c787-96df-d286a1f14b41");
    /**
     * 系统注册 {@link #mBluetoothGattCharacteristic_type} 的实际value
     */
    String welinkType = "ok";
    //====↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑=====



    //====↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓====
    /**
     * 热点信息
     */
    BluetoothGattCharacteristic mBluetoothGattCharacteristic_ap;

    /**
     * 系统注册 {@link #mBluetoothGattCharacteristic_ap} 的唯一id
     */
    public static final UUID bleUUID_AP = UUID.fromString("7a2cd581-5fd7-6c3d-ef20-b2885c75c510");
    //====↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑=====


    /**
     * 暂未使用 旧版本兼容
     */
    BluetoothGattDescriptor mBluetoothGattDescriptor;

    public AccessPoint(Context context, String deviceBrand, int frequency, AccessPointListener listener) {
        mContext=context;
        DeviceBrand=deviceBrand;
        Frequency=frequency;
        this.listener=listener;
        AnFileLog.e("wlhw-ble","deviceBrand="+ deviceBrand+",frequency="+Frequency);
        initBLE();
    }



    /**
     * 开始BLE广播
     */
    synchronized public void start() {
        AnFileLog.e("wlhw-ble","start() bleConnected="+bleConnected);
        startAdvertise();
    }





    /**
     * 初始化蓝牙ble
     */
    public void initBLE() {
        AnFileLog.e("wlhw-ble","initBLE");
        //--> 校验是否支持ble
        if (isBLESupported()) {
            //--> step 1.添加蓝牙ble监听receiver
            addBTReceiver();
            //--> 系统蓝牙manager
            mBluetoothManager = (BluetoothManager) mContext.getSystemService(Context.BLUETOOTH_SERVICE);
            if (mBluetoothManager != null) {
                //--> 系统adapter，是实际蓝牙api组接口
                mBTAdapter = BluetoothAdapter.getDefaultAdapter();
                AnFileLog.e("wlhw-ble","mBTAdapter=" + mBTAdapter);
            }
            if ((mBTAdapter == null) || (!mBTAdapter.isEnabled())) {
                AnFileLog.e("wlhw-ble","error bluetooth not open!!!!");
                listener.onError();
                return;
            }
            //--> step 2.添加gatt ble服务
            addGattServer();
        }else{
            AnFileLog.e("wlhw-ble","error do not support BLE!!" );
        }

        //--> step 3对外开启蓝牙广播
        start();
    }


    /**
     * 核心逻辑
     * GattServer事件回调，涉及蓝牙信息的读写相关逻辑
     */
    BluetoothGattServerCallback mBluetoothGattServerCallback = new BluetoothGattServerCallback() {
        @Override
        public void onConnectionStateChange(BluetoothDevice device, int status, int newState) {

            super.onConnectionStateChange(device, status, newState);
            AnFileLog.e("wlhw-ble","onConnectionStateChange status=" + status + ",newState=" + newState);
//            writeBLE=false;
            readCnt=0;
            if(newState == BluetoothProfile.STATE_CONNECTED){
                //--> ble已连接
                mBluetoothDevice.add(device);
                AnFileLog.e("wlhw-ble","onConnectionStateChange mBluetoothDevice=" + device);
                AnFileLog.e("she-debug","0 无感互联 蓝牙ble 手机端连接ble到车机端：发起握手 start  -->" + System.currentTimeMillis());
                bleConnected =true;
                //ble只添加一个超时任务
                if (timeout != null && mBluetoothDevice.size() == 1) {
                    timeout.postDelayed(timeoutRunnable, ACCESS_POINT_TIMEOUT);
                }
                // stop();
            }else if (newState==BluetoothProfile.STATE_DISCONNECTED){
                //--> ble已断开
                mBluetoothDevice.remove(device);
                AnFileLog.e("wlhw-ble","onConnectionStateChange STATE_DISCONNECTED mBluetoothDevice=" + device);
                bleConnected =false;
                //ble连接设备全部移除时 才移除超时
                if (timeout != null && mBluetoothDevice.size() == 0) {
                    timeout.removeCallbacks(timeoutRunnable);
                    start();
                }
            }
        }

        @Override
        public void onCharacteristicReadRequest(BluetoothDevice device, int requestId, int offset, BluetoothGattCharacteristic characteristic) {
            super.onCharacteristicReadRequest(device, requestId, offset, characteristic);
            UUID cur = characteristic.getUuid();
            readCnt++;
            AnFileLog.e("wlhw-ble","onCharacteristicReadRequest bleConnected="+bleConnected
                    + ",\n requestId = " + requestId
                    + ",\n offset = " + offset
                    + ",\n uuid = " + cur
                    + ",\n characteristic value = " + characteristic.getStringValue(0)
                    + ",\n device = " + device
                    + ",\n readCnt="+readCnt);
//            if (!writeBLE && readCnt>=2 && listener!=null){
//                listener.onReadTwiceWithoutWrite();
//                readCnt=0;
//            }
            if (timeout != null) {
                timeout.removeCallbacks(timeoutRunnable);
                timeout.postDelayed(timeoutRunnable, ACCESS_POINT_TIMEOUT);
            }

            byte[] v1=characteristic.getValue();
            int len=v1.length;
            if (offset==0){
                mBluetoothGattServer.sendResponse(device, requestId, BluetoothGatt.GATT_SUCCESS, offset, v1);
            } else if (offset<len){
                byte[] v2= Arrays.copyOfRange(v1, offset, len);
                mBluetoothGattServer.sendResponse(device, requestId, BluetoothGatt.GATT_SUCCESS, offset, v2);
            }else{
                mBluetoothGattServer.sendResponse(device, requestId, BluetoothGatt.GATT_SUCCESS, offset, null);
            }
            AnFileLog.e("wlhw-ble","BluetoothGattServerCallback::onCharacteristicReadRequest --> sendResponse：" + new String(v1));
        }

        @Override
        public void onCharacteristicWriteRequest(BluetoothDevice device, int requestId, BluetoothGattCharacteristic characteristic, boolean preparedWrite, boolean responseNeeded, int offset, byte[] value) {
            super.onCharacteristicWriteRequest(device, requestId, characteristic, preparedWrite, responseNeeded, offset, value);
            UUID cur = characteristic.getUuid();
            String stringValue = new String(value);
            AnFileLog.e("wlhw-ble","BluetoothGattServerCallback::onCharacteristicWriteRequest bleConnected="+bleConnected+", requestId=" + requestId + ",value=" + stringValue + "," + cur);
//            writeBLE=true;
            if (timeout != null) {
                timeout.removeCallbacks(timeoutRunnable);
                timeout.postDelayed(timeoutRunnable, ACCESS_POINT_TIMEOUT);
            }

            if (listener != null) {
                if (stringValue==null)return;
                if (stringValue.length()<1)return;
                String[] ss1 =  stringValue.split(",");
                for (int i=0;i<ss1.length;i++){
                    AnFileLog.e("wlhw-ble","onWriteData "+i+"="+ss1[i]);
                }
                if (ss1[0].equals("o")){

                    // 数据握手流程完毕，开启wifi direct连接
                    listener.onAccess(0,ss1[1],getTimeout(ss1[2]),null,null);
                }else if (ss1[0].equals("w")){
                    // 当前ios 长度第五传递签名
                      if(ss1.length > 4){
                          listener.onAccess(1,ss1[4],getTimeout(ss1[3]),ss1[1],ss1[2]);
                       }else{
                          listener.onAccess(1,null,getTimeout(ss1[3]),ss1[1],ss1[2]);
                      }
                }else if(ss1[0].equals("a")){
                    //走android ap热点连接
                    listener.onAccess(1,ss1[4],getTimeout(ss1[3]),ss1[1],ss1[2]);
                }
            }
            characteristic.setValue(value);
            mBluetoothGattServer.sendResponse(device, requestId, BluetoothGatt.GATT_SUCCESS, offset, value);
            AnFileLog.e("wlhw-ble","BluetoothGattServerCallback::onCharacteristicWriteRequest sendResponse value = " + new String(value) );
			mBluetoothGattServer.notifyCharacteristicChanged(device,characteristic,false);
        }

        @Override
        public void onServiceAdded(int status, BluetoothGattService service) {
            super.onServiceAdded(status, service);
            AnFileLog.e("wlhw-ble","onServiceAdded status=" + status + ",uuid=" + service.getUuid());
        }
    };
    private int getTimeout(String  ss){
        int go_timeout=50;
        if(TextUtils.isEmpty(ss)){
            return go_timeout;
        }
        try{
            go_timeout=Integer.parseInt(ss);
        }catch (Exception e){
            go_timeout=50;
        }
        if (go_timeout<2 || go_timeout>300 ){//排除异常值,最大允许5分钟
            go_timeout=50;
        }
        return go_timeout;
    }
    /**
     * ble广播setting参数，具体查阅系统AdvertiseSettingsapi
     * @param connectable
     * @param timeoutMillis
     * @return
     */
    public AdvertiseSettings createAdvertiseSettings(boolean connectable, int timeoutMillis) {
        AdvertiseSettings.Builder builder = new AdvertiseSettings.Builder();
        builder.setAdvertiseMode(AdvertiseSettings.ADVERTISE_MODE_LOW_LATENCY);
        builder.setConnectable(connectable);
        builder.setTimeout(timeoutMillis);
        builder.setTxPowerLevel(AdvertiseSettings.ADVERTISE_TX_POWER_HIGH);
        AdvertiseSettings advertiseSettings = builder.build();
        AnFileLog.e("wlhw-ble","AccessPoint::createAdvertiseSettings --> advertiseSettings =" + advertiseSettings);
        return advertiseSettings;
    }


    /**
     * 设置广播数据，用于被感知
     * @return
     */
    private AdvertiseData createAdvertiseData() {
        AdvertiseData.Builder builder = new AdvertiseData.Builder();
        builder.addServiceUuid(new ParcelUuid(bleAdvertisetUuid));
//        builder.setIncludeDeviceName(true);
        AdvertiseData adv = builder.build();
        AnFileLog.e("wlhw-ble","AccessPoint::createAdvertiseData adv=" + adv);
        return adv;
    }

    private void removeBTReceiver(){
        AnFileLog.printStackTraceString("wlhw-ble", "AccessPoint::removeBTReceiver");
        if (bleListenerReceiver!=null && mContext!=null){
            AnFileLog.e("wlhw-ble","unregisterReceiver ble");
            mContext.unregisterReceiver(bleListenerReceiver);
        }
        bleListenerReceiver=null;
    }

    /**
     * 添加蓝牙状态广播Receiver
     * 如下Action：
     *  BluetoothAdapter.ACTION_STATE_CHANGED :  蓝牙 开启/关闭 状态 有变化
     *  BluetoothDevice.ACTION_ACL_CONNECTED|ACTION_ACL_DISCONNECTED：监视蓝牙设备与APP连接的状态经典蓝牙
     */
    private void addBTReceiver(){
        if (bleListenerReceiver==null) {
            // 初始化广播
            bleListenerReceiver = new BluetoothMonitorReceiver();
            IntentFilter intentFilter = new IntentFilter();
            // 监视蓝牙关闭和打开的状态
            intentFilter.addAction(BluetoothAdapter.ACTION_STATE_CHANGED);
            // 监视蓝牙设备与APP连接的状态
            intentFilter.addAction(BluetoothDevice.ACTION_ACL_DISCONNECTED);
            intentFilter.addAction(BluetoothDevice.ACTION_ACL_CONNECTED);
            // 注册广播
            mContext.registerReceiver(bleListenerReceiver, intentFilter);
            AnFileLog.e("wlhw-ble","注册蓝牙状态广播：registerReceiver = " + bleListenerReceiver);
        }
    }
    private void removeGattServer() {
        AnFileLog.e("wlhw-ble","AccessPoint::removeGattServer GattService="+mBluetoothGattService+",GattServer="+mBluetoothGattServer);
            if (mBluetoothGattService!=null && mBluetoothGattServer!=null){
                if (!mBluetoothGattServer.removeService(mBluetoothGattService)) {
                    AnFileLog.e("wlhw-ble", "error removeGattServer fail!!!!" + mBluetoothGattService);
                }
                mBluetoothGattServer.close();
                mBluetoothGattServer=null;
            }
        mBluetoothGattService=null;
    }

    /**
     * 核心方法，添加用于交互的ble gatt服务
     *
     * --BluetoothGattServer
     *  --gatt service
     *   -- mBluetoothGattCharacteristic_version
     *   -- mBluetoothGattCharacteristic_type
     *   -- mBluetoothGattCharacteristic_ap
     *
     *   -- mBluetoothGattDescriptor
     */
    private void addGattServer() {
        AnFileLog.e("wlhw-ble","AccessPoint::addGattServer::welink 注册gatt service "+mBluetoothGattService);
        welinkVersion="v,2,"+Frequency+","+DeviceBrand;
        if (mBluetoothGattService==null) {
            //--> 初始化 BluetoothGattService
            mBluetoothGattService = new BluetoothGattService(bleUUID_Service, BluetoothGattService.SERVICE_TYPE_PRIMARY);

            //--> mBluetoothGattCharacteristic_version 版本信息
            mBluetoothGattCharacteristic_version = new BluetoothGattCharacteristic(bleUUID_Version,
                    BluetoothGattCharacteristic.PROPERTY_WRITE | BluetoothGattCharacteristic.PROPERTY_NOTIFY | BluetoothGattCharacteristic.PROPERTY_READ,
                    (BluetoothGattCharacteristic.PERMISSION_WRITE | BluetoothGattCharacteristic.PERMISSION_READ));
            mBluetoothGattCharacteristic_version.setValue(welinkVersion.getBytes());
            //--> 初始化描述（she tips:最新协议暂未实际启用，保留旧版本兼容）
            mBluetoothGattDescriptor = new BluetoothGattDescriptor(bleUUID_Version, BluetoothGattDescriptor.PERMISSION_READ | BluetoothGattDescriptor.PERMISSION_WRITE);
            mBluetoothGattDescriptor.setValue(welinkVersion.getBytes());

            //--> mBluetoothGattCharacteristic_type
            mBluetoothGattCharacteristic_type = new BluetoothGattCharacteristic(bleUUID_Type,
                    BluetoothGattCharacteristic.PROPERTY_WRITE | BluetoothGattCharacteristic.PROPERTY_NOTIFY | BluetoothGattCharacteristic.PROPERTY_READ,
                    (BluetoothGattCharacteristic.PERMISSION_WRITE | BluetoothGattCharacteristic.PERMISSION_READ));
            mBluetoothGattCharacteristic_type.setValue(welinkType.getBytes());

            //--> mBluetoothGattCharacteristic_ap （ios）
            // SSID
            String apInfo = "w," + WiFiUtil.getApName(mContext) + "," + WiFiUtil.getApPassword(mContext);
            AnFileLog.e("wlhw-ble","热点信息mBluetoothGattCharacteristic_ap:value --> apInfo " + apInfo);
            mBluetoothGattCharacteristic_ap = new BluetoothGattCharacteristic(bleUUID_AP,
                    BluetoothGattCharacteristic.PROPERTY_WRITE | BluetoothGattCharacteristic.PROPERTY_NOTIFY | BluetoothGattCharacteristic.PROPERTY_READ,
                    (BluetoothGattCharacteristic.PERMISSION_WRITE | BluetoothGattCharacteristic.PERMISSION_READ));
            mBluetoothGattCharacteristic_ap.setValue(apInfo.getBytes());

            //--> Service添加特征值
            mBluetoothGattService.addCharacteristic(mBluetoothGattCharacteristic_version);
            mBluetoothGattService.addCharacteristic(mBluetoothGattCharacteristic_type);
            mBluetoothGattService.addCharacteristic(mBluetoothGattCharacteristic_ap);

            //--> 添加服务
            AnFileLog.e("wlhw-ble", "before openGattServer"+mBluetoothManager);
            if (mBluetoothManager != null) {
                mBluetoothGattServer = mBluetoothManager.openGattServer(mContext, mBluetoothGattServerCallback);
                AnFileLog.e("wlhw-ble", "before addService"+mBluetoothGattServer);
                if (mBluetoothGattServer != null) {
                    if (!mBluetoothGattServer.addService(mBluetoothGattService)) {
                        AnFileLog.e("wlhw-ble", "error addService fail!!!!!");
                        if (isBTOn()) listener.onError();
                    }else{
                        AnFileLog.e("wlhw-ble", "addService succ!!!!!");
                    }
                } else {
                    AnFileLog.e("wlhw-ble", "error openGattServer fail!!!!!");
                    if (isBTOn()) listener.onError();
                }
            }
        }
    }

    /**
     * 蓝牙ble广播回调，开启成功、失败
     */
    private final AdvertiseCallback mAdvCallback = new AdvertiseCallback() {
        public void onStartSuccess(android.bluetooth.le.AdvertiseSettings settingsInEffect) {
            AnFileLog.e("wlhw-ble","AccessPoint::AdvertiseCallback::onStartSuccess settingsInEffect=" + settingsInEffect);
            if (settingsInEffect != null) {
                AnFileLog.e("wlhw-ble","AccessPoint::AdvertiseCallback::onStartSuccess TxPowerLv=" + settingsInEffect.getTxPowerLevel() + " mode=" + settingsInEffect.getMode() + " timeout=" + settingsInEffect.getTimeout());
            } else {
                AnFileLog.e("wlhw-ble","AccessPoint::AdvertiseCallback::onStartSuccess, settingInEffect is null");
            }
        }

        public void onStartFailure(int errorCode) {
            AnFileLog.e("wlhw-ble","onStartFailure errorCode=" + errorCode);
            if (errorCode == ADVERTISE_FAILED_DATA_TOO_LARGE) {
                AnFileLog.e("wlhw-ble","Failed to start advertising as the advertise data to be broadcasted is larger than 31 bytes.");
            } else if (errorCode == ADVERTISE_FAILED_TOO_MANY_ADVERTISERS) {
                AnFileLog.e("wlhw-ble","Failed to start advertising because no advertising instance is available.");
            } else if (errorCode == ADVERTISE_FAILED_ALREADY_STARTED) {
                AnFileLog.e("wlhw-ble","Failed to start advertising as the advertising is already started");
            } else if (errorCode == ADVERTISE_FAILED_INTERNAL_ERROR) {
                AnFileLog.e("wlhw-ble","Operation failed due to an internal error");
            } else if (errorCode == ADVERTISE_FAILED_FEATURE_UNSUPPORTED) {
                AnFileLog.e("wlhw-ble","This feature is not supported on this platform");
            }
        }
    };

    public boolean isBLESupported() {
        return mContext.getPackageManager().hasSystemFeature(PackageManager.FEATURE_BLUETOOTH_LE);
    }

    public Boolean isBTOn() {
        if(mBTAdapter==null){
            AnFileLog.e("wlhw-ble","bt is off");
            return false;
        }
        return mBTAdapter.isEnabled();
    }

    //开始BLE广播
    public void startAdvertise() {
        if (!isBLESupported()) return;
        if (!isBTOn()) return;
        if (mBTAdapter == null) return;
        if (mBTAdvertiser == null) {
            mBTAdvertiser = mBTAdapter.getBluetoothLeAdvertiser();
            AnFileLog.e("wlhw-ble", "startAdvertise mBTAdvertiser=" + mBTAdvertiser+", use name="+mBTAdapter.getName());
            if (mBTAdvertiser != null) {
//                mBTAdapter.setName(DeviceName);
                //--> 发起广播,使其被发现
                mBTAdvertiser.startAdvertising(createAdvertiseSettings(true, 0),
                        createAdvertiseData(),
                        mAdvCallback);
            }
        }
    }




    /**
     * 结束BLE广播
     */
    synchronized private void stop() {
        AnFileLog.printStackTraceString("wlhw-ble","AccessPoint::stop() bleConnected="+bleConnected);
        stopAdvertise();
    }

    //停止BLE广播
    public void stopAdvertise() {
        AnFileLog.printStackTraceString("wlhw-ble","stopAdvertising");
        if (mBTAdvertiser != null) {
            mBTAdvertiser.stopAdvertising(mAdvCallback);
        }
        mBTAdvertiser = null;
    }

    /**
     * 释放资源
     */
    public void release() {
        AnFileLog.printStackTraceString("wlhw-ble","AccessPoint::release bleConnected="+bleConnected);
        stop();
        bleConnected =false;
        mBluetoothDevice.clear();
        if (timeout != null) {
            timeout.removeCallbacks(timeoutRunnable);
        }
        removeGattServer();
        removeBTReceiver();
    }

    /**
     * 断开已有的BLE连接
     */
    synchronized public void cancelBLEConnection() {
        AnFileLog.printStackTraceString("wlhw-ble","AccessPoint::cancelBLEConnection bleConnected="+bleConnected+",mBTAdvertiser="+mBTAdvertiser+",mBluetoothDevice.size="+mBluetoothDevice.size());
        if (mBTAdapter == null) return;
        if (mBluetoothGattServer != null) {
            for (BluetoothDevice device : mBluetoothDevice) {
                AnFileLog.e("wlhw-ble","cancelConnection mBluetoothDevice=" + device+",mBluetoothGattServer="+mBluetoothGattServer);
                mBluetoothGattServer.cancelConnection(device);
            }
            mBluetoothDevice.clear();
        }
    }

    /**
     * 蓝牙广播receiver
     */
    public class BluetoothMonitorReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (action != null) {
                switch (action) {
                    //--> 蓝牙 开启/关闭 状态 有变化
                    case BluetoothAdapter.ACTION_STATE_CHANGED:
                        int blueState = intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, 0);
                        AnFileLog.e("wlhw-ble","onReceive ACTION_STATE_CHANGED blueState = " + blueState);
                        switch (blueState) {
                            case BluetoothAdapter.STATE_TURNING_ON:
                                AnFileLog.e("wlhw-ble","onReceive === 蓝牙正在打开");
                                break;
                            //--> 开启
                            case BluetoothAdapter.STATE_ON:
                                AnFileLog.e("wlhw-ble","onReceive === 蓝牙已经打开");
                                new Handler(MainHandler.getInstance().getLooper()).post(new Runnable() {
                                    @Override
                                    public void run() {
                                        //重新开启BLE操作
                                        AnFileLog.e("wlhw-ble","AccessPoint::reopen ble broadcast bleConnected=" + bleConnected + ",WLHardware.isConnected = " + WLHardware.isConnected);
                                        new Handler(MainHandler.getInstance().getLooper()).post(() -> {
                                            //--> todo:在连接完成后，不用开启了
                                            if(!WLHardware.isConnected){
                                                initBLE();
                                            }
                                        });
                                    }
                                });
                                break;
                            case BluetoothAdapter.STATE_TURNING_OFF:
                                AnFileLog.e("wlhw-ble","onReceive === 蓝牙正在关闭");
                                break;
                            //--> 蓝牙已经关闭
                            case BluetoothAdapter.STATE_OFF:
                                AnFileLog.e("wlhw-ble","onReceive === 蓝牙已经关闭");
                                new Handler(MainHandler.getInstance().getLooper()).post(new Runnable() {
                                    @Override
                                    public void run() {
                                        //释放部分资源
                                        new Handler(MainHandler.getInstance().getLooper()).post(() -> {
                                            stopAdvertise();
                                            removeGattServer();
                                        });
                                    }
                                });
                                break;
                        }
                        break;
                    //--> 经典蓝牙，暂时没用到，后续扩展保留
                    case BluetoothDevice.ACTION_ACL_CONNECTED:
                        AnFileLog.e("wlhw-ble","蓝牙设备已连接");
                        break;
                    case BluetoothDevice.ACTION_ACL_DISCONNECTED:
                        AnFileLog.e("wlhw-ble","蓝牙设备已断开");
                        break;
                }
            }
        }
    }
}
