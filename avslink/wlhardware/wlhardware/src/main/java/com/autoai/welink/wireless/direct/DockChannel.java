package com.autoai.welink.wireless.direct;

import static com.autoai.welink.wireless.direct.DockChannelListener.DOCK_CHANNEL_DISCONNECTED_REASON_INNER_DISCONNECT;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.NetworkInfo;
import android.net.wifi.WifiManager;
import android.net.wifi.p2p.WifiP2pConfig;
import android.net.wifi.p2p.WifiP2pDeviceList;
import android.net.wifi.p2p.WifiP2pInfo;
import android.net.wifi.p2p.WifiP2pManager;
import android.net.wifi.p2p.nsd.WifiP2pDnsSdServiceRequest;
import android.os.Handler;

import com.autoai.welink.wireless.WLHardware;
import com.autoai.welink.wireless.utiliy.AnFileLog;
import com.autoai.welink.wireless.utiliy.MainHandler;
import com.autoai.welink.wireless.utiliy.WiFiUtil;

/**
 * 负责完成Direct连接，及通过心跳保证连接有效
 */
@SuppressLint("MissingPermission")
public class DockChannel extends BroadcastReceiver {
    /**
     * 搜索go的间隔时间  缩短为 500ms 提升扫描频率
     */
    private static final long SEARCH_GO_INTERVAL = 1000;

    /**
     * 对外模块状态位通知
     */
    private final DockChannelListener listener;
    /**
     * 上下文：目前是主进程上下文
     */
    private final Context context;

    /**
     * wifi - api 的引用
     */
    private WifiP2pManager manager = null;
    private WifiP2pManager.Channel channel = null;

    /**
     * 搜索go线程
     * fixme：跟一下是否有停止
     */
    private Thread searchThread = null;

    /**
     * ble身份认证阶段，传过来的go校验信息
     */
    private String goSignature = null; //不为null则表示正在连接
    private final Object goSignatureLock = new Object();

    /**
     * 用于wifi-p2p连接成功后，
     * 心跳包服务的port，来自gc discover到go后 在p2p信息中携带
     */
    private int goPort = 0; //不为0则表示正在获取连接信息
    private final Object goPortLock = new Object();

    /**
     * gc连接成功后，go的地址
     */
    private String goDeviceAddress = null;
    private final Object goDeviceAddressLock = new Object();

    /**
     * gc连接到go后 --> true
     */
    private boolean isConnecting = false;
    private boolean isConnected = false;

    /**
     * 暂未启用
     */
    @Deprecated
    private String lastGoDeviceAddress;

    /**
     * wifi p2p监听
     */
    @SuppressLint("NewApi")
    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        AnFileLog.e("wlhw-gc", "DockChannel::onReceive: action =" + action);

        switch (action) {
            //--> 连接状态有变化
            case WifiP2pManager.WIFI_P2P_CONNECTION_CHANGED_ACTION:
                //连接状态发生变化：处理
                new Handler(MainHandler.getInstance().getLooper()).post(() -> {
                    requestConnectionInfo(intent);
                });
                break;
            //--> WifiP2pManager 功能开启、关闭回调
            case WifiP2pManager.WIFI_P2P_STATE_CHANGED_ACTION:
                int status = intent.getIntExtra(WifiP2pManager.EXTRA_WIFI_STATE, -1);
                AnFileLog.e("wlhw-gc", "DockChannel::onReceive: action--> WIFI_P2P_STATE_CHANGED_ACTION  status = " + status + ",WLHardware.isConnected = " + WLHardware.isConnected);
                if (!WLHardware.isConnected) {
                    new Handler(MainHandler.getInstance().getLooper()).post(() -> {
                        if (status == WifiP2pManager.WIFI_P2P_STATE_ENABLED) {
                            // WiFi Direct功能已启用：进行初始化
                            if (manager == null) {
                                initGC();
                            }
                            searchGO();
                        } else {
                            // WiFi Direct功能未启用 | 变为不可用，做disconnect处理
                            if (manager != null) {
                                AnFileLog.e("wlhw-gc", "DockChannel::onReceive WIFI_P2P_STATE_CHANGED_ACTION status = WIFI_P2P_STATE_ENABLED");
                                disconnect(DOCK_CHANNEL_DISCONNECTED_REASON_INNER_DISCONNECT);
                                //--> fixme:she tips : 这里不要直接用 {@link #release},的原因是要回收掉
                                if (channel != null) {
                                    AnFileLog.e("wlhw-gc", "disconnect1  - channel.close");
                                    channel.close();
                                }
                                channel = null;
                                manager = null;
                            }
                            onError();
                        }
                    });
                }
                break;


            //--> 监测到周边WifiP2pDeviceList 有变化（比如感知到有新的go）
            case WifiP2pManager.WIFI_P2P_PEERS_CHANGED_ACTION:
                //互联设备列表发生变化
                new Handler(MainHandler.getInstance().getLooper()).post(() -> {
                    if (goPort == 0) {
                        AnFileLog.e("wlhw-gc", "onReceive === goPort=" + goPort);
                        return;
                    }

                    if (goDeviceAddress != null) {
                        //--> 取出最新的WifiP2pDeviceList
                        final WifiP2pDeviceList deviceList = intent.getParcelableExtra(WifiP2pManager.EXTRA_P2P_DEVICE_LIST);
                        //--> 已经连接的goDeviceAddress不在列表中了，发起断联
                        if (deviceList == null || deviceList.get(goDeviceAddress) == null) {
                            AnFileLog.e("wlhw-gc", "disconnect2");
                            disconnect(DOCK_CHANNEL_DISCONNECTED_REASON_INNER_DISCONNECT);
                        }
                    }
                });
                break;
            //--> 暂未使用
            case WifiP2pManager.WIFI_P2P_THIS_DEVICE_CHANGED_ACTION:
            case WifiP2pManager.WIFI_P2P_DISCOVERY_CHANGED_ACTION:
            default:
                break;
        }
    }

    public DockChannel(Context context, String lastGoDeviceAddress, DockChannelListener listener) {
        this.context = context;
        this.listener = listener;
        this.lastGoDeviceAddress = lastGoDeviceAddress;
    }

    public void updateLastGO(String lastGoDeviceAddress) {
        this.lastGoDeviceAddress = lastGoDeviceAddress;
    }

    /**
     * 开始连接Wi-Fi Direct GO
     *
     * @param signature GO对应的签名
     */
    public void connect(String signature) {
        AnFileLog.e("she-debug", "1.5  DockChannel::connect()  signature=" + signature);
        //--> 打开车机热点
        if (WiFiUtil.getWifiStatus(context) != WifiManager.WIFI_STATE_ENABLED) {
            WiFiUtil.enableWifi(context, true);
        }

//        if (manager == null || channel == null) {
//            return;
//        }
//
//        if (manager != null && channel != null) {
//            manager.cancelConnect(channel, null);
//            manager.removeGroup(channel, null);
//        }

        synchronized (goSignatureLock) {
            goSignature = null;
        }

        synchronized (goPortLock) {
            goPort = 0;
        }

        synchronized (goDeviceAddressLock) {
            goDeviceAddress = null;
        }

        synchronized (goSignatureLock) {
            goSignature = signature;
        }

        isConnecting = false;

        //初始化 group client
        initGC();
//        searchGO();
    }


    private void initGC() {
        AnFileLog.printStackTraceString("wlhw-gc", "DockChannel::initGc()");
        //--> 释放掉所有相关状态
        release();

        manager = (WifiP2pManager) context.getSystemService(Context.WIFI_P2P_SERVICE);
        channel = manager.initialize(context, MainHandler.getInstance().getLooper(), new WifiP2pManager.ChannelListener() {
            @Override
            public void onChannelDisconnected() {
                new Handler(MainHandler.getInstance().getLooper()).post(new Runnable() {
                    @Override
                    public void run() {
                        AnFileLog.e("wlhw-gc", "DockChannel::manager.initialize onChannelDisconnected");
//                        DockChannel.this.disconnect();
                    }
                });
            }
        });


        //--> wifi p2p 状态通知通过广播回调进行通知
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(WifiP2pManager.WIFI_P2P_STATE_CHANGED_ACTION);
        intentFilter.addAction(WifiP2pManager.WIFI_P2P_CONNECTION_CHANGED_ACTION);
        intentFilter.addAction(WifiP2pManager.WIFI_P2P_PEERS_CHANGED_ACTION);
//        intentFilter.addAction(WifiP2pManager.WIFI_P2P_THIS_DEVICE_CHANGED_ACTION);
//        intentFilter.addAction(WifiP2pManager.WIFI_P2P_DISCOVERY_CHANGED_ACTION);
        //--> she tips: 注意 ！！！ 在注册后，会立即返回 action：WIFI_P2P_STATE_CHANGED_ACTION 当前是否可用
        context.registerReceiver(this, intentFilter);

//        if (manager == null || channel == null) {
//            return;
//        }
        //--> 搜索group owner
//        searchGO();
        AnFileLog.e("wlhw-gc", "DockChannel::initGc() --> func finish");
    }


    /**
     * initGO启动调用，以便提早搜索，加快获取GO信息
     * 某些手机需要定时调用，以防止搜索无效
     */
    private void searchGO() {
        AnFileLog.printStackTraceString("wlhw-gc", "searchGO  signature=" + goSignature);
        if (searchThread != null) {
            //BLE获取签名后，主动调用加快搜索
            AnFileLog.e("wlhw-gc", "discoverServices manager: " + manager + ", channel: " + channel);
            if (manager == null || channel == null) {
                return;
            }
            manager.discoverServices(channel, null);
            AnFileLog.e("she-debug", "2.无感互联 discoverServices ing 无感互联 -->" + System.currentTimeMillis());
            return;
        }

        manager.setDnsSdResponseListeners(channel, null, (fullDomainName, txtRecordMap, srcDevice) -> {
            AnFileLog.e("she-debug", "3.无感互联 发现了wifi go -->" + System.currentTimeMillis());
            AnFileLog.e("wlhw-gc", "DockChannel::setDnsSdResponseListeners goSignature=" + goSignature + ",txtRecordMap=" + txtRecordMap);
            //todo 暂时屏蔽此代码，防止互联过的设备 无签名验证也能发起互联
            /*if (!isConnected && lastGoDeviceAddress != null && lastGoDeviceAddress.equals(srcDevice.deviceAddress)) {
                String forced_ble = txtRecordMap.get("forced_ble");
                if (forced_ble == null || !forced_ble.equals("true")) {
                    String server_name = txtRecordMap.get("server");
                    if (server_name != null && server_name.equals("welink.hardware-register")) {
                        String http_port = txtRecordMap.get("http_port");
                        if (http_port != null) {
                            AnFileLog.e("wlhw-gc", "last go");
                            lastGoDeviceAddress = null;
                            synchronized (goSignatureLock) {
                                goSignature = null;
                            }
                            new Handler(MainHandler.getInstance().getLooper()).post(() -> connectGO(srcDevice.deviceAddress, Integer.parseInt(http_port)));
                        }
                    }

                    return;
                }
            }*/

            if (goSignature == null) {
                return;
            }

            //--> 校验服务是否是welink 在 hu车机主机创建的go相匹配
            //--> fixme：后续server welink.hardware-register；signature 字段全局化
            String server_name = txtRecordMap.get("server");
            if (server_name != null && server_name.equals("welink.hardware-register")) {
                String signature = txtRecordMap.get("signature");

                //-->签名校准，是否是当前设备
                if (signature != null && signature.equals(goSignature)) {
                    String http_port = txtRecordMap.get("http_port");
                    if (http_port != null) {
                        AnFileLog.e("wlhw-gc", "DockChannel::searchGO 找到go");
                        lastGoDeviceAddress = null;
                        synchronized (goSignatureLock) {
                            goSignature = null;
                        }
                        //-->与go发起连接
                        // action = WIFI_P2P_CONNECTION_CHANGED_ACTION
                        new Handler(MainHandler.getInstance().getLooper()).post(() -> connectGO(srcDevice.deviceAddress, Integer.parseInt(http_port)));
                    }
                }
            }
        });

        //--> 添加搜索条件 （与手机端group owner是成对的）
        // instanceName：welink
        // serviceType：hardware-register
        WifiP2pDnsSdServiceRequest serviceRequest = WifiP2pDnsSdServiceRequest.newInstance("welink", "hardware-register");
        manager.addServiceRequest(channel, serviceRequest, null);

        //--> 开启线程，搜索go
        searchThread = new Thread() {
            @Override
            public void run() {
                while (!isInterrupted()) {
                    AnFileLog.e("wlhw-gc", "DockChannel::searchThread run::start");
                    new Handler(MainHandler.getInstance().getLooper()).post(() -> {
                        if (manager == null || channel == null) {
                            return;
                        }

                        if (!isConnecting && !isConnected) {
                            AnFileLog.e("wlhw-gc", "DockChannel::searchThread run::discoverServices");
                            // --> 搜索go
                            // --> 搜索到会回调{@Link onReceive(Context context, Intent intent) }
                            manager.discoverServices(channel, null);
                        }
                    });

                    try {
                        // --> 1s/次
                        sleep(SEARCH_GO_INTERVAL);
                    } catch (InterruptedException e) {
                        return;
                    }
                }
            }
        };
        searchThread.start();
    }

    private void connectGO(final String deviceAddress, final int port) {
        if (isConnecting) {
            return;
        }
        AnFileLog.e("wlhw-gc", "DockChannel::connectGO deviceAddress =" + deviceAddress + ",port=" + port);
        isConnecting = true;
        onConnecting(deviceAddress, port);

        synchronized (goSignatureLock) {
            goSignature = null;
        }

        synchronized (goPortLock) {
            goPort = port;
        }

        synchronized (goDeviceAddressLock) {
            goDeviceAddress = deviceAddress;
        }
        WifiP2pConfig config = new WifiP2pConfig();
        config.deviceAddress = deviceAddress;
        //fixme：具体查下资料
        // 组所有者意图值，范围0-15，值越大越倾向于成为GO
//        config.groupOwnerIntent = 0;
        manager.cancelConnect(channel, null);
        //--> 结果回调到 {@link @onReceive}:action --> WIFI_P2P_CONNECTION_CHANGED_ACTION
        manager.connect(channel, config, null);
        AnFileLog.e("wlhw-gc", "DockChannel::connectGO WifiP2pManager.connect 连接go");
    }

    /**
     * 核心方法
     *
     * @param intent
     */
    private void requestConnectionInfo(final Intent intent) {
        NetworkInfo netInfo = intent.getParcelableExtra(
                WifiP2pManager.EXTRA_NETWORK_INFO);
        AnFileLog.e("wlhw-gc", "DockChannel::requestConnectionInfo goPort=" + goPort + ",P2PisConnected:" + netInfo.isConnected());

        //--> 如果是null，说明异常go，放弃
        if (goPort == 0) {
            return;
        }

        //--> 从 Intent中获取到 p2pInfo
        final WifiP2pInfo p2pInfo = intent.getParcelableExtra(WifiP2pManager.EXTRA_WIFI_P2P_INFO);
        if (p2pInfo != null && p2pInfo.groupFormed && p2pInfo.groupOwnerAddress != null) {
            AnFileLog.e("wlhw-gc", "DockChannel::requestConnectionInfo -> onConnected1 =" + p2pInfo.groupOwnerAddress);
            //--> 如果是可用的，p2pInfo.groupOwnerAddress.getHostAddress()即：可用于socket的 Address
            onConnected(p2pInfo.groupOwnerAddress.getHostAddress(), goPort);

            synchronized (goPortLock) {
                goPort = 0;
            }
        } else if (p2pInfo != null && !p2pInfo.groupFormed) {
            AnFileLog.e("wlhw-gc", "DockChannel::requestConnectionInfo !p2pInfo.groupFormed");
//            onDisconnected();
            synchronized (goPortLock) {
                goPort = 0;
            }
        } else {
            //--> info == null
            AnFileLog.e("wlhw-gc", "requestConnectionInfo goPort=" + goPort);
            manager.requestConnectionInfo(channel, info -> {
                if (goPort == 0) {
                    return;
                }

                if (info != null && info.groupFormed && info.groupOwnerAddress != null) {
                    AnFileLog.e("wlhw-gc", "onConnected2 =" + info.groupOwnerAddress);
                    onConnected(info.groupOwnerAddress.getHostAddress(), goPort);

                    synchronized (goPortLock) {
                        goPort = 0;
                    }
                } else if (info != null && !info.groupFormed) {
                    AnFileLog.e("wlhw-gc", "onDisconnected2");
//                    onDisconnected();

                    synchronized (goPortLock) {
                        goPort = 0;
                    }
                }
            });
        }
    }

    private void onConnecting(String address, int port) {
        AnFileLog.e("wlhw-gc", "onConnecting");
        new Handler(MainHandler.getInstance().getLooper()).post(() -> listener.onConnecting(address, port));
    }

    private void onConnected(final String ip, final int port) {
        AnFileLog.e("wlhw-gc", "DockChannel::onConnected ip=" + ip + ",port=" + port);
        if (ip == null || isConnected) {
            return;
        }

        isConnecting = false;
        isConnected = true;

        new Handler(MainHandler.getInstance().getLooper()).post(() -> listener.onConnected(ip, port));
    }


    private void onError() {
        AnFileLog.printStackTraceString("wlhw-gc", "DockChannel::onError");
        isConnected = false;
        if (listener != null) {
            listener.onError();
        }
//        new Handler(MainHandler.getInstance().getLooper()).post(listener::onError);
    }

    /**
     * 停止扫描寻找go
     */
    public void stopSearchGo() {
        if (searchThread != null) {
            searchThread.interrupt();
            searchThread = null;
        }
    }

    /**
     * 断开Wi-Fi Direct GO的连接
     */
    public void disconnect(int reason) {
        AnFileLog.printStackTraceString("wlhw-gc", "DockChannel::disconnect");
        if (searchThread != null) {
            searchThread.interrupt();
            searchThread = null;
        }


        if (manager != null && channel != null) {
            AnFileLog.e("wlhw-gc", "DockChannel::disconnect   -cancelConnect/stopPeerDiscovery/removeGroup");
            manager.cancelConnect(channel, null);
            manager.stopPeerDiscovery(channel, null);
            manager.removeGroup(channel, null);
        }

        synchronized (goSignatureLock) {
            goSignature = null;
        }

        synchronized (goPortLock) {
            goPort = 0;
        }

        synchronized (goDeviceAddressLock) {
            goDeviceAddress = null;
        }

        onDisconnected(reason);
    }

    private void onDisconnected(int reason) {
        AnFileLog.printStackTraceString("wlhw-gc", "DockChannel::onDisconnected isConnecting = " + isConnecting + ";isConnected = " + isConnected);
        if (!isConnected && !isConnecting) {
            return;
        }

        isConnecting = false;
        isConnected = false;

        if (listener != null) {
            listener.onDisconnected(reason);
        }
//        new Handler(MainHandler.getInstance().getLooper()).post(listener::onDisconnected);
    }

    /**
     * 1.释放 wifi相关的 广播Receiver
     * 2.停止寻找discover go
     * 3.回收manager；channel
     */
    @SuppressLint("NewApi")
    public void release() {
        AnFileLog.printStackTraceString("wlhw-gc", "DockChannel::release");

        try {
            context.unregisterReceiver(this);
        } catch (Exception e) {
            AnFileLog.e("wlhw-gc", "error  unregisterReceiver fail!" + e);
        }

        if (searchThread != null) {
            lastGoDeviceAddress = null;
            searchThread.interrupt();
            searchThread = null;
        }
        if (channel != null) {
            AnFileLog.e("wlhw-gc", "DockChannel::release  - channel.close");
            channel.close();
        }
        channel = null;
        manager = null;
    }
}
