package com.autoai.welink.wireless;

import android.content.Context;
import android.os.Build;
import android.os.Handler;

import com.autoai.welink.wireless.ble.AccessPoint;
import com.autoai.welink.wireless.ble.AccessPointListener;
import com.autoai.welink.wireless.direct.DockChannel;
import com.autoai.welink.wireless.direct.DockChannelListener;
import com.autoai.welink.wireless.net.NetInspector;
import com.autoai.welink.wireless.net.NetInspectorListener;
import com.autoai.welink.wireless.utiliy.AnFileLog;
import com.autoai.welink.wireless.ap.WiFiAP;
import com.autoai.welink.wireless.utiliy.MainHandler;

/**
 * 无线互联通信链路层的核心调度类
 */
public class WLHardware {
    private static final long ACCESS_POINT_TIMEOUT = 3600;
    private static long CONNECT_GO_TIMEOUT = 50000;
    private static final long NET_INSPECT_TIMEOUT = 3000;
    private static final String WIRELESS_CONFIG = "WelinkWireless";
    private static final String WIRELESS_CONFIG_LAST_GO = "LastGo";

    private static volatile WLHardware wlHardware = null;

    /**
     * 基于ble封装的接收热点
     */
    private AccessPoint accessPoint = null;

    /**
     * wifi direct
     */
    private DockChannel dockChannel = null;
    private WiFiAP wiFiAP = null;

    /**
     * 负责通道层心跳相关业务
     */
    private NetInspector netInspector = null;

    /**
     * 超时计数器
     */
    private Handler timeout = null;

    /**
     * go 连接超时处理
     */
    private Runnable wifiChannelTimeoutRunnable = null;

    /**
     * 如果在wifi接入步骤，蓝牙心跳断了（一段时间{@link #ACCESS_POINT_TIMEOUT}），那么认为异常,放弃掉档次连接
     */
    private Runnable accessHeartBeatTimeoutRunnable = null;

    /**
     *
     */
    private String curSSID = null, curPasswd = null;

    /**
     * 无感互联状态对外通知
     */
    private WLHardwareListener wlHardwareListener = null;
    private String connectGOAddress = null;
    private String connectSignature = null;
    private long connectGOTimeMillis = 0;



    /**
     * fixme：当前；哦按空耳状态，后续调整完status流转flag
     */
    public static boolean isConnected = false;
    private boolean isConnecting = false;

    /**
     * android 是否处于 无感连接中
     */
    private boolean isAndroidConnecting = false;
    /**
     * ios 是否处于 无感连接中
     */
    private boolean isIOSConnecting = false;

    /**
     * Android go的热点
     */
    private String goIP = null;

    private long lastOpenWiFiTimeMillis = 0;
    private volatile boolean docked = false;//是否已经连接成功了?
    /**
     * 是否 默认打开热点
     */
    private boolean isDefaultOpenAp = false;
    /**
     * 是否是mona车机
     * mona车机 无感 ios 和 android能同时连接
     *
     */
    private boolean mMona = false;
    /**
     * 增加一个回调转发代理,方便库内部处理状态响应
     */
    private WLHardwareListener wlHardwareListenerPoxy = new WLHardwareListener() {

        /**
         * 蓝牙ble 识别到手机设备回调
         * @param type 对接设备的类型：
         *             Android：0
         *             iOS：1
         *             Harmony:2
         */
        @Override
        public void onDocking(int type) {
            docked = false;
            AnFileLog.e("wlhw-wl", "WLHardware::WLHardwareListener --> onDocking type =" + type);
            if (wlHardwareListener != null) {
                wlHardwareListener.onDocking(type);
            }
        }

        /**
         * netInspector 完成网络验证（心跳包）
         * @param ip 对接设备的IP
         */
        @Override
        public void onDocked(String ip) {
            docked = true;
            AnFileLog.e("wlhw-wl", "WLHardware::WLHardwareListener --> onDocked ip =" + ip);
            if (wlHardwareListener != null) {
                wlHardwareListener.onDocked(ip);
            }
            //todo:停掉感知逻辑

            //--> 停掉超时
            //--> 1.蓝牙模块断开
            stopDiscoverAfterConnected();
        }

        /**
         * 断开连接（心跳包超时断开，系统网络关闭）
         */
        @Override
        public void onDisdock() {
            docked = false;
            AnFileLog.e("wlhw-wl", "WLHardware::WLHardwareListener --> onDisdock ");
            if (wlHardwareListener != null) {
                wlHardwareListener.onDisdock();
            }
        }

        @Override
        public void onError(int reason) {
            docked = false;
            AnFileLog.e("wlhw-wl", "WLHardware::WLHardwareListener::onError " + reason);
            if (wlHardwareListener != null) {
                wlHardwareListener.onError(reason);
            }
        }
    };

    /**
     * 停止发现功能
     * 1.听到蓝牙广播
     * 2.停掉wifi go discover
     */
    private void stopDiscoverAfterConnected() {
        AnFileLog.e("wlhw-wl","WLHardware::stopDiscoverAfterConnected() --> accessPoint = " + accessPoint
        + ", dockChannel = " + dockChannel);

        //--> 停止蓝牙广播
        if(accessPoint != null){
//            accessPoint.stopAdvertise();*
            accessPointDisconnect();
            //互联成功后 停止广播移除蓝牙监听，防止被重启启动
            accessPoint.release();
        }

        //--> 停止direct扫描
        if (dockChannel != null){
            dockChannel.stopSearchGo();
        }

        //--> 停止ios广播接收
        if(netInspector != null){
            netInspector.stopIOSUdp();
        }
    }

    /**
     * 获取功能库版本号
     *
     * @return 返回功能库版本号字符串
     */
    public static String getVersion() {
        return BuildConfig.VERSION_NAME + "/" + BuildConfig.BUILD_TYPE;
    }

    Context context;

    /**
     * 单例，禁止包外构建WLHardware对象
     */
    protected WLHardware(Context context) {
        this.context = context;
        AnFileLog.init(this.context);
    }

    /**
     * 获取单例
     *
     * @param context Context
     * @return 单例对象
     */
    public static WLHardware getInstance(Context context) {
        if (wlHardware != null) {
            return wlHardware;
        }

        wlHardware = new WLHardware(context);
        return wlHardware;
    }

    /**
     * 打开无线网络，等待对接
     *
     * @param packageName 对应设备的App包名
     * @param port        设备端口号
     * @param frequency   5G信道频率, 0指全信道, 用于指示手机go创建时指定的信道频率
     *                    以下是中国国内Wi-Fi 5G推荐信道，其他信道是禁止或有DFS要求，DFS有要求的信道不建议单独指定
     *                    信道：频率
     *                    36: 5180
     *                    38: 5190
     *                    40: 5200
     *                    44: 5220
     *                    46: 5230
     *                    48: 5240
     *                    149: 5745
     *                    151: 5755
     *                    153: 5765
     *                    157: 5785
     *                    159: 5795
     *                    161: 5805
     *                    165: 5825
     * @param deviceBrand 设备类型，建议采用AOA usb-accessory中的model字段
     * @param listener    无线连接状态监听
     */
    public void open(String packageName, int port, int frequency, String deviceBrand, WLHardwareListener listener) {
        AnFileLog.printStackTraceString("wlhw-wl", "WLHardware::open()");
        new Handler(MainHandler.getInstance().getLooper()).post(() -> open_imp(packageName, port, frequency, deviceBrand, listener));
    }

    private void open_imp(String packageName, int port, int frequency, String deviceBrand, WLHardwareListener listener) {
        AnFileLog.e("wlhw-wl", "WLHardware::open_imp packageName=" + packageName + ",deviceBrand=" + deviceBrand);
        if (wlHardware == null) {
            return;
        }
        //-->无感互联状态对外通知
        wlHardwareListener = listener;
        //--> 心跳包
        netInspector = new NetInspector(packageName, port, deviceBrand, NET_INSPECT_TIMEOUT, new NetInspectorListener() {

            /**
             * IOS 连接回调
             * @param ip
             * @param conetnt
             */
            @Override
            public void onIOSDeviceConnected(final String ip, final String conetnt) {
                AnFileLog.e("wlhw-wl", "WLHardware::NetInspectorListener:: onConnected " + ip);

                if(accessHeartBeatTimeoutRunnable != null){
                    timeout.removeCallbacks(accessHeartBeatTimeoutRunnable);
                }

                if (wifiChannelTimeoutRunnable != null) {
                    timeout.removeCallbacks(wifiChannelTimeoutRunnable);
                }


//                if (accessPoint != null) {
//                    //无感互联释放资源
//                    accessPoint.release();
//                }
                new Handler(MainHandler.getInstance().getLooper()).post(() -> {
                    if (wlHardwareListenerPoxy != null) {
                        if (conetnt.startsWith("WLServer_Android_1")) {
                            //android 类型
                            wlHardwareListener.onDocking(0);
                        } else if (conetnt.startsWith("WLServer_Harmony")) {
                            wlHardwareListener.onDocking(2);
                        } else {
                            //ios类型
                            wlHardwareListener.onDocking(1);
                        }
                        wlHardwareListenerPoxy.onDocked(ip);
                    }
                });
            }

            @Override
            public void onAndroidDeviceHeartbeat() {
                AnFileLog.e("wlhw-wl", "WLHardware::NetInspectorListener:: onHeartbeat " );
                //--> goIP起到了一个flag作用
                if (goIP != null) {
                    String ip = goIP;
                    goIP = null;

                    if(accessHeartBeatTimeoutRunnable != null){
                        timeout.removeCallbacks(accessHeartBeatTimeoutRunnable);
                    }

                    if (timeout != null) {
                        timeout.removeCallbacks(wifiChannelTimeoutRunnable);
                    }

                    new Handler(MainHandler.getInstance().getLooper()).post(() -> {
                        if (wlHardwareListenerPoxy != null) {
//                            SharedPreferences sharedPreferences = context.getSharedPreferences(WIRELESS_CONFIG, MODE_PRIVATE);
//                            SharedPreferences.Editor editor = sharedPreferences.edit();
//                            editor.putString(WIRELESS_CONFIG_LAST_GO, connectGOAddress);
//                            editor.apply();
//                            dockChannel.updateLastGO(connectGOAddress);
                            //-->互联成功，状态位置回调
                            //传递互联状态0 是android
                            wlHardwareListenerPoxy.onDocking(0);
                            wlHardwareListenerPoxy.onDocked(ip);
                            if (netInspector != null) {
                                //--> 无感互联成功 ，关闭wifi 接收udp广播
                                netInspector.iOSDisconnect();
                            }
                        }
                    });
                }
            }

            @Override
            public void onDisconnected(int reason) {
                AnFileLog.e("wlhw-wl", "WLHardware::NetInspectorListener::onDisconnected isConnecting = " + isConnecting
                + "; isConnected = " + isConnected);

                //--> she tips:已经断开了，不需要进行重复的复归
                if (!isConnected && !isConnecting) {
                    return;
                }

                //--> 复归逻辑
                isConnecting = false;
                isConnected = false;

                //--> 出异常,关闭通道
//                close_imp();
                reset_imp();

                //--> callback通知出去
                if (wlHardwareListenerPoxy != null) {
                    wlHardwareListenerPoxy.onDisdock();
                }


//                if (!isConnected && !isConnecting) {
//                    return;
//                }
//                //更新状态位
//                isConnecting = false;
//                isConnected = false;
//
//                new Handler(MainHandler.getInstance().getLooper()).post(() -> {
//                    if (wlHardwareListenerPoxy != null) {
//                        wlHardwareListenerPoxy.onDisdock();
//                    }
//                });
//
//                AnFileLog.e("wlhw-wl", "WLHardware::NetInspectorListener:: onDisconnected  call reset");
//                reset();
            }
        });
//        由于udp startWithiOS 初始化已调用，顾热点打开后不再次调用
//        wiFiAP = new WiFiAP(context,() -> new Handler(MainHandler.getInstance().getLooper()).post(() -> netInspector.startWithiOS()));
        wiFiAP = new WiFiAP(context, null);

        //mona车机默认打开热点
        String model = Build.MODEL;
        if (model != null) {
            mMona = isDefaultOpenAp = Build.MODEL.contains("Motor");
        }
        AnFileLog.e("wlhw-wl", "WLHardware::open_imp isDefaultOpenAp=" + isDefaultOpenAp + ",Build.MODEL:" + model);
        if (isDefaultOpenAp && !wiFiAP.isEnable()) {
            wiFiAP.open();
        }

        //-->取出sp中上次互联的ip fixme：目前此处逻辑暂时注释
//        SharedPreferences sharedPreferences = context.getSharedPreferences(WIRELESS_CONFIG, MODE_PRIVATE);
//        String lastGo = sharedPreferences.getString(WIRELESS_CONFIG_LAST_GO, null);
        String lastGo = null;
//        Log.i("shecw4","WLHardware::open_imp sharedPreferences.getString WIRELESS_CONFIG_LAST_GO = " + lastGo);
        //--> wifi direct 业务逻辑
        dockChannel = new DockChannel(context, lastGo, new DockChannelListener() {
            @Override
            public void onConnecting(String address, int port) {
                AnFileLog.e("wlhw-wl", "WLHardware::DockChannelListener  --> onConnecting " + address + "," + port);
                connectGOAddress = address;
            }

            @Override
            public void onConnected(String ip, int port) {
                AnFileLog.e("wlhw-wl", "WLHardware::DockChannelListener  --> onConnected " + ip + "," + port + ",docked:" + docked);
                isConnected = true;
                goIP = ip;
                if (!docked) {
                    new Handler(MainHandler.getInstance().getLooper()).post(() -> netInspector.startWithAndroid(ip, port));
                }
            }

            @Override
            public void onDisconnected(int reason) {
                AnFileLog.e("wlhw-wl", "WLHardware::DockChannelListener  --> onDisconnected isConnecting = " + isConnecting
                + ",isConnected = " + isConnected);

                goIP = null;
                //--> she tips:已经断开了，不需要进行重复的复归
                if (!isConnected && !isConnecting) {
                    return;
                }

                //--> 复归逻辑
                isConnecting = false;
                isConnected = false;

                //--> 出异常,关闭通道
                reset_imp();

                //--> 一旦断开，callback通知出去
                if (wlHardwareListenerPoxy != null) {
                    wlHardwareListenerPoxy.onDisdock();
                }


//
//                // --> fixme:后续删掉
//                AnFileLog.e("wlhw-wl", "DockChannel onDisconnected");
//                dockChannel.disconnect();
//                //↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑
//
//                new Handler(MainHandler.getInstance().getLooper()).post(() -> {
//                    // 停掉 netInspector
//                    netInspector.stop();
//                    if (wlHardwareListenerPoxy != null) {
//                        AnFileLog.e("wlhw-wl", "DockChannel onDisconnected onDisdock");
//                        wlHardwareListenerPoxy.onDisdock();
//                    }
//                    AnFileLog.e("wlhw-wl", "DockChannel onDisconnected call reset");
//
//                    //重置整个状态
//                    reset();
//                });
            }

            @Override
            public void onError() {
                new Handler((MainHandler.getInstance().getLooper())).post(() -> {
                    if (wlHardwareListenerPoxy != null) {
                        AnFileLog.e("wlhw-wl", "DockChannel onError");
                        wlHardwareListenerPoxy.onError(0);
                    }
                });
            }
        });

        timeout = new Handler(MainHandler.getInstance().getLooper());
        accessHeartBeatTimeoutRunnable = () -> {
            AnFileLog.e("wlhw-wl", "WLHardware::accessHeartBeatRunnable run");
            connectSignature = null;
            timeout.removeCallbacks(wifiChannelTimeoutRunnable);
            isConnecting = false;
            dockChannel.disconnect(DockChannelListener.DOCK_CHANNEL_DISCONNECTED_REASON_POSITIVE_DISCONNECT);
        };


         wifiChannelTimeoutRunnable = () -> {
            AnFileLog.e("wlhw-wl", "WLHardware::wifiChannelTimeoutRunnable run");
            connectSignature = null;
            isConnecting = false;
            //停掉蓝牙心跳超时
            timeout.removeCallbacks(accessHeartBeatTimeoutRunnable);
        };

//         wifiAPTimeoutRunnable = () -> {
//            AnFileLog.e("wlhw-wl", "WLHardware::wifiAPTimeoutRunnable run");
//             connectSignature = null;
//             isConnecting = false;
//        };

        //-->fixme:新的互联开始之前，对之前做一次清理，目前看有点多余，日志跟踪后续优化
        accessPointDisconnect();
        accessPoint = new AccessPoint(context, deviceBrand, frequency, new AccessPointListener() {
            @Override
            public void onAccess(int type, String signature, int go_timeout, String ssid, String passwd) {
                AnFileLog.e("she-debug", "1.无感互联 蓝牙ble 握手完成 -->" + System.currentTimeMillis());
                AnFileLog.e("wlhw-wl", "WLHardware::onAccess type=" + type + ",signature=" + signature + ",go_timeout=" + go_timeout + ",ssid=" + ssid + ",passwd=" + passwd +
                        ",docked=" + docked + ",connectGOSignature=" + connectSignature+",isConnecting="+isConnecting);
                //如果已经连接上了,则不要再触发连接中,避免中断已连接的状态
                if (docked) {
                    return;
                }

                if(!isConnecting) {
                    isConnecting = true;
                    //重新进入连接流程 ，重置 ios android 互联状态
                    isAndroidConnecting = false;
                    isIOSConnecting = false;
                    //--> 获取超时时间
                    CONNECT_GO_TIMEOUT = go_timeout * 1000;
                    timeout.postDelayed(wifiChannelTimeoutRunnable, ACCESS_POINT_TIMEOUT);
                    //--> 设置隐藏接入点超时
//                    timeout.removeCallbacks(timeoutRunnable);
                    timeout.removeCallbacks(wifiChannelTimeoutRunnable);
                }else{
                    if(connectSignature != null && signature != null && connectSignature.equals(signature)){
                        AnFileLog.e("wlhw-wl", "WLHardware::onDocking 重置断开超时");
                        timeout.removeCallbacks(accessHeartBeatTimeoutRunnable);
                        timeout.postDelayed(accessHeartBeatTimeoutRunnable,ACCESS_POINT_TIMEOUT);
                    }
                    if(!mMona && isConnecting){
                        AnFileLog.e("wlhw-wl", "WLHardware::onAccess 当前只有mona车机支持同时打开热点和wifi");
                        return;
                    }
                }

                    if (type == 0 && !isAndroidConnecting) {
                            isAndroidConnecting = true;
                            timeout.postDelayed(wifiChannelTimeoutRunnable, CONNECT_GO_TIMEOUT);
                            //--> 通过ble通信，手机端提供 go 信息
                            connectSignature = signature;
                            connectGOTimeMillis = System.currentTimeMillis();
                            new Handler(MainHandler.getInstance().getLooper()).post(() -> {
                                //-->准备wifi-p2p直连，关闭热点
                               if(!mMona) {
                                   //不支持同时打开热点和wifi时 ，wifi-direct互联关闭热点
                                   wiFiAP.close();
                               }
                                AnFileLog.e("wlhw-wl", "WLHardware::onAccess dockChannel.connect signature = " + signature);
                                //--> 车机端连接手机端 group owner
                                dockChannel.connect(signature);

                                AnFileLog.e("wlhw-wl", "onDocking " + type);
                                //传递给native层 要互联的设备类型， 由于mona车机 能够同时打开热点和wifi，所以这里先不传递互联类型
                                if (!mMona && wlHardwareListenerPoxy != null) {
                                    wlHardwareListenerPoxy.onDocking(type);
                                }
                            });
                    }
//                    else if (type == 2) {//给第三方接入使用, 准备加入指定的热点
//                        AnFileLog.e("wlhw-wl", "onAccess tbd join wifiAP");
//                    }
                    else if (type == 1 && !isIOSConnecting) {
                        isIOSConnecting = true;
                        //------>
                        connectSignature = signature;
                        //todo 添加互斥机制
                        //IOS-无感互联创建指定ssid的热点给iphone手机使用
                        new Handler(MainHandler.getInstance().getLooper()).post(() -> {
                            AnFileLog.e("wlhw-wl", "WLHardware::onAccess tbd join wifiAP");
                            //--> 互斥逻辑：断开之前正在连接的dock channel
                            if(!mMona) {
                                //不支持同时打开热点和wifi时 ，热点互联关闭 wifi-direct
                                dockChannel.disconnect(DockChannelListener.DOCK_CHANNEL_DISCONNECTED_REASON_POSITIVE_DISCONNECT);
                                dockChannel.release();
                            }
                            AnFileLog.e("wlhw-wl", "WLHardware::onAccess wiFiAP.open");

                            boolean needReOpenAP = false;
                            boolean isEnable = wiFiAP.isEnable();

                            //--> 再重新获取一次curSSID 和 curPasswd
                            if (curPasswd == null) {
                                curPasswd = wiFiAP.getPassword();
                            }
                            if (curSSID == null) {
                                curSSID = wiFiAP.getSSID();
                            }

                            if (!isEnable) {
                                needReOpenAP = true;
                            }
                            if (ssid != null && !ssid.equals(curSSID)) {
                                needReOpenAP = true;
                            }
                            if (passwd != null && !passwd.equals(curPasswd)) {
                                needReOpenAP = true;
                            }

                            AnFileLog.e("wlhw-wl", "onAccess needReOpenAP: " + needReOpenAP);
                            //-->  只在ap未开启,或ssid/密码变化了,才重启AP
                            //-->  添加10s的延时,防止频繁重启AP
                            //  case1：ssid 表示
                            //  case2: 密码变了 ,重新开启下热点
                            // fixme:shecw tips:修改系统热点.....不太理想
                            if (needReOpenAP && System.currentTimeMillis() - lastOpenWiFiTimeMillis > 10 * 1000) {
                                curSSID = ssid;
                                curPasswd = passwd;
                                if (!isEnable) {
                                    wiFiAP.open();
                                }
                                lastOpenWiFiTimeMillis = System.currentTimeMillis();
                            }

                            //todo @张光诚 这里加一个时序
//                             netInspector.startWithiOS 在这里调用
                            AnFileLog.e("wlhw-wl", "onDocking " + type);
                            //传递给native层 要互联的设备类型， 由于mona车机 能够同时打开热点和wifi，所以这里先不传递互联类型
                            if (!mMona && wlHardwareListenerPoxy != null) {
                                wlHardwareListenerPoxy.onDocking(type);
                            }
                            //
                        });
                    }

            }

            @Override
            public void onError() {
                if (wlHardwareListenerPoxy != null) {
                    wlHardwareListenerPoxy.onError(1);
                }
            }

            @Override
            public void onReadTwiceWithoutWrite() {
//                AnFileLog.e("wlhw-wl", "onReadTwiceWithoutWrite docked=" + docked);
//                if (docked) {
//                    return;
//                }
//                accessPointDisconnect();
            }
        });

        AnFileLog.e("wlhw-wl", "open call appear ");
        //不支持无感互联，启动wifi互联
        netInspector.startWithiOS();
    }

    /**
     * 重置无线网络：关闭之前对接，等待新的对接
     */
    public void reset() {
        AnFileLog.printStackTraceString("wlhw-wl", "WLHardware::reset do nothing -->  wlHardware" + wlHardware);
        new Handler(MainHandler.getInstance().getLooper()).post(this::reset_imp);
    }

    /**
     * 重置网络通道状态，准备下次连接
     */
    private void reset_imp() {
        AnFileLog.e("wlhw-wl", "WLHardware::reset_imp --> wlHardware = " + wlHardware);
        if (wlHardware == null) {
            return;
        }
        //签名保存为空，防止同一个设备wifi-direct 互联异常失败再次互联
//        connectSignature = null;
//        connectGOTimeMillis = 0;
////        wiFiAP.close();
//        //--> dock channel 重置
        if (dockChannel != null) {
            dockChannel.disconnect(DockChannelListener.DOCK_CHANNEL_DISCONNECTED_REASON_POSITIVE_DISCONNECT);
            dockChannel.release();
        }

        //--> ble accessPoint 断联
        if (accessPoint != null) {
            accessPointDisconnect();
        }

        if(timeout != null){
            timeout.removeCallbacks(wifiChannelTimeoutRunnable);
            timeout.removeCallbacks(accessHeartBeatTimeoutRunnable);
        }

    }

    /**
     * 关闭无线网络
     * open、close涉及硬件服务资源的分配、释放，因此避免频繁调用
     */
    public void close() {
        AnFileLog.printStackTraceString("wlhw-wl", "WLHardware::close()  wlHardware = " + wlHardware);
        new Handler(MainHandler.getInstance().getLooper()).post(this::close_imp);
    }

    /**
     * 依次关闭互联模块各个模组
     */
    private void close_imp() {
        AnFileLog.e("wlhw-wl", "WLHardware::close_imp() wlHardware = " + wlHardware);
        if (wlHardware == null) {
            return;
        }

        //--> 网络心跳包模块
        if (netInspector != null) {
            netInspector.stop();
        }

        /*if (wiFiAP != null) {
            wiFiAP.close();
        }*/

        //--> wifi direct 断联释放
        if (dockChannel != null) {
            dockChannel.disconnect(DockChannelListener.DOCK_CHANNEL_DISCONNECTED_REASON_POSITIVE_DISCONNECT);
            dockChannel.release();
        }

        //--> ble accessPoint 断联释放
        if (accessPoint != null) {
            accessPointDisconnect();
            accessPoint.release();
        }

        if(timeout != null){
            timeout.removeCallbacks(wifiChannelTimeoutRunnable);
            timeout.removeCallbacks(accessHeartBeatTimeoutRunnable);
        }

        //--> ble accessPoint 断联释放
        wlHardware = null;
    }

    /**
     * 显示无线网络接入点，用于断开互联后，使其他设备可以再发现
     */
    private void accessPointDisconnect() {
        AnFileLog.e("wlhw-wl", "WLHardware::accessPointDisconnect"
                + "wlHardware == null? " + (wlHardware == null)
                + "timeout == null? " + (timeout == null)
                + "accessPoint == null? " + (accessPoint == null));
        if (wlHardware == null) {
            return;
        }

//        if (timeout != null) {
//            timeout.removeCallbacks(timeoutRunnable);
//        }

        if (accessPoint != null) {
            accessPoint.cancelBLEConnection();
        }
    }

    /**
     * 开关Logcat日志
     */
    public static void enableLogCat(boolean enable) {
        AnFileLog.enableLogCat(enable);
    }

    /**
     * 开关日志文件
     */
    public static void enableLogFile(boolean enable) {
        AnFileLog.enableLogFile(enable);
    }

}
