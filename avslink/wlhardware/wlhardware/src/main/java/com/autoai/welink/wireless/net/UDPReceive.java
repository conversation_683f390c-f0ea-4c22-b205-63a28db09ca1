package com.autoai.welink.wireless.net;

import android.os.Handler;
import android.os.Looper;

import com.autoai.welink.wireless.utiliy.AnFileLog;
import com.autoai.welink.wireless.utiliy.MainHandler;

import java.io.IOException;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.net.SocketException;

public class UDPReceive {
    boolean doNotNeedOnError=false;
    private Thread thread;
    DatagramSocket ds = null;

    public UDPReceive(final int port, UDPReceiveListener listener) {
        thread = new Thread() {
            @Override
            public void run() {
                try {
                    AnFileLog.e("wlhw-net","UDPReceive DatagramSocket(port)"+port);
                    ds = new DatagramSocket(port);
                } catch (SocketException e) {
                    AnFileLog.e("wlhw-net","UDPReceive DatagramSocket(port)onError doNotNeedOnError="+doNotNeedOnError+","+e);
                    if (!doNotNeedOnError) {
                        new Handler(MainHandler.getInstance().getLooper()).post(listener::onError);
                    }
                }

                if (ds == null) {
                    AnFileLog.e("wlhw-net","UDPReceive DatagramSocket(port)ds == null doNotNeedOnError="+doNotNeedOnError);
                    if (!doNotNeedOnError) {
                        new Handler(MainHandler.getInstance().getLooper()).post(listener::onError);
                    }
                    return;
                }

                byte[] data = new byte[1024];
                DatagramPacket dp = new DatagramPacket(data, data.length);

                while (!isInterrupted()) {
                    AnFileLog.e("wlhw-net","UDPReceive  before ds.receive(dp)");
                    try {
                        if (ds!=null) {
                            ds.receive(dp);
                        }else{
                            break;
                        }
                    } catch (Exception e) {
                        AnFileLog.e("wlhw-net","UDPReceive ds.receive(dp) onError doNotNeedOnError="+doNotNeedOnError+","+e);
                        if (!doNotNeedOnError) {
                            new Handler(MainHandler.getInstance().getLooper()).post(listener::onError);
                        }
                        try {
                            if (ds != null) {
                                ds.close();
                                ds = null;
                            }
                        }catch (Exception e1){
                            AnFileLog.e("wlhw-net","ds is null e:"+e.getMessage());
                        }
                        break;
                    }
                    AnFileLog.e("wlhw-net","UDPReceive  after ds.receive(dp)");

                    int length = dp.getLength();
                    if (length > 0) {
                        InetAddress inetAddress = dp.getAddress();
                        final String udpAddress = inetAddress.getHostAddress();
                        if (udpAddress != null) {
                            AnFileLog.e("wlhw-net","UDPReceive  heartbeat");
                            new Handler(MainHandler.getInstance().getLooper()).post(() -> listener.onReceive(udpAddress, new String(data, 0, length)));
                        }
                    }
                }
            }
        };

        thread.start();
    }

    public void release() {
        AnFileLog.e("wlhw-net","UDPReceive release()");
        doNotNeedOnError=true;
        if (thread != null) {
            thread.interrupt();
            thread = null;
        }

        if (ds != null) {
            ds.close();
            ds = null;
        }
    }
}
