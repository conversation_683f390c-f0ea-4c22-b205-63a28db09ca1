package com.autoai.welink.wireless.utiliy;

import android.content.Context;
import android.net.wifi.SoftApConfiguration;
import android.net.wifi.WifiManager;
import android.util.Log;
import com.autoai.welink.wireless.ap.WiFiAP;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Random;

public class WiFiUtil {
    private static String apName;

    public static String getApName(Context context) {
        if(apName != null){
            return apName;
        }
        String name = null;
        if (android.os.Build.VERSION.SDK_INT >= 30) {
            WifiManager wifiManager = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
            try {
                SoftApConfiguration softApConfiguration = getSoftApConfiguration(wifiManager);
                if (softApConfiguration != null) {
                    Log.i("shecw4", "apname: " + softApConfiguration.getSsid() + ", psw: " + softApConfiguration.getPassphrase());
                    name = softApConfiguration.getSsid();
                    if(name == null || !name.startsWith(WiFiAP.apName)){
                        name =  WiFiAP.apName;
                    }else {
                        apName = name;
                        return apName;
                    }
                } else {
                    Log.d("WiFiUtil", "apname: " + WiFiAP.apName);
                    name =  WiFiAP.apName;
                }
            } catch (Exception e) {
                AnFileLog.e("WifiUtil", "getSoftApConfiguration fail!!!  " + e);
            }
        } else {
            name =  WiFiAP.apName;
        }
        apName = name+"_" + getRandom4DigitNumber();
        return apName;
    }


    public static String getApPassword(Context context) {
        if (android.os.Build.VERSION.SDK_INT >= 30) {
            WifiManager wifiManager = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
            try {
                SoftApConfiguration softApConfiguration = getSoftApConfiguration(wifiManager);
                if (softApConfiguration != null) {
                    Log.d("WiFiUtil", "apname: " + softApConfiguration.getSsid() + ", psw: " + softApConfiguration.getPassphrase());
                    return softApConfiguration.getPassphrase();
                } else {
                    Log.d("WiFiUtil", "softApConfiguration is null ");
                    return WiFiAP.passwd;
                }
            } catch (Exception e) {
                AnFileLog.e("WiFiUtil", "getSoftApConfiguration fail!!! " + e);
            }
        } else {
            return WiFiAP.passwd;
        }
        return WiFiAP.passwd;
    }

    public static SoftApConfiguration getSoftApConfiguration(WifiManager wifiManager) {
        try {
            Method method = WifiManager.class.getDeclaredMethod("getSoftApConfiguration");
            method.setAccessible(true); // 允许访问私有方法
            return (SoftApConfiguration) method.invoke(wifiManager);
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            e.printStackTrace();
        }
        return null;
    }
    /**
     * 查询wifi状态
     */
    public static int getWifiStatus(Context context) {
        WifiManager wifiManager = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
        int wifiState=WifiManager.WIFI_STATE_UNKNOWN;
        if (wifiManager != null) {
            wifiState = wifiManager.getWifiState();
        }
        AnFileLog.e("wlhw-wl","getWifiStatus  "+wifiState);
        return wifiState;
    }

    /**
     * 车机上实测, 无需系统签名就能开关wifi
     */
    public static void enableWifi(Context context, boolean enable) {
        WifiManager wm = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
        boolean ret=wm.setWifiEnabled(enable);
        AnFileLog.e("wlhw-wl","WiFiUtil::enableWifi  "+ret);
    }
    public static int getRandom4DigitNumber() {
        Random random = new Random();
        // 生成 1000 到 9999 之间的随机数
        return random.nextInt(9000) + 1000;
    }
}
