pluginManagement {
    repositories {
        gradlePluginPortal()
        // Use official Google Maven repository for Android Gradle Plugin metadata
        google()
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/central' }
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        maven { url 'https://wdnexus.autoai.com/content/repositories/autoai-AVS/' }
        maven { url "https://wdnexus.autoai.com/content/repositories/releases/" }
        maven { url 'https://artifact.bytedance.com/repository/Volcengine/' }
        mavenCentral()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/central' }
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        maven { url 'https://wdnexus.autoai.com/content/repositories/autoai-AVS/' }
        maven { url "https://wdnexus.autoai.com/content/repositories/releases/" }
        maven { url 'https://artifact.bytedance.com/repository/Volcengine/' }
        mavenCentral()
        google()
        maven { url "https://jitpack.io" }

    }
}
rootProject.name = "a55haiwai"
include ':app'
if (file("./avslink/linkHost").exists() && SDK_TEST != "true") {
    include ':linkHost'
    project(':linkHost').projectDir = file("./avslink/linkHost")
    include ':linkSdk'
    project(':linkSdk').projectDir = file("./avslink/linkSdk")

    include ':linkCommon'
    project(':linkCommon').projectDir = file("./avslink/linkCommon")
// submodule 工作方式
    include ':wlhardware'
    if (file("./avslink/wlhardware/wlhardware").exists()) {
        project(':wlhardware').projectDir = file("./avslink/wlhardware/wlhardware")
    } else {
        project(':wlhardware').projectDir = file("./avslink/localLibs/wlhardware")
    }

    include ':app-scrcpy'
    project(':app-scrcpy').projectDir = file("./avslink/app_scrcpy")
}
