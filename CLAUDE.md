# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an Android automotive application (a55haiwai) for car infotainment systems that enables wireless phone-to-car connectivity. It supports iOS, Android, and HarmonyOS devices with features like screen mirroring, touch control calibration, and Bluetooth management.

## Build Commands

### Building the Application
```bash
# Build all variants
./gradlew build

# Build specific product flavors
./gradlew assembleBaselineDebug    # Baseline debug build
./gradlew assembleBaselineRelease  # Baseline release build
./gradlew assembleDatongDebug      # Datong debug build
./gradlew assembleDatongRelease    # Datong release build
./gradlew assemble_24LDebug        # 24L system debug build
./gradlew assemble_24LRelease      # 24L system release build

# Clean build
./gradlew clean
./gradlew clean build
```

### Running Tests
```bash
# Run all tests
./gradlew test

# Run connected Android tests
./gradlew connectedAndroidTest

# Run tests for specific flavor
./gradlew connectedBaselineDebugAndroidTest
./gradlew connectedDatongDebugAndroidTest
./gradlew connected_24LDebugAndroidTest
```

### Code Quality
```bash
# Run lint checks
./gradlew lint
./gradlew lintDebug
./gradlew lintRelease

# Check all code quality
./gradlew check
```

## Architecture Overview

### Multi-Module Structure
- **app/**: Main application module with UI and business logic
  - Product-specific customizations in `/src/product_customized/`
  - Separate resources for horizontal (`res-h`) and vertical (`res-v`) layouts
  
- **avslink/** (Git submodule): Core connectivity SDK
  - `linkHost/`: Host-side connectivity implementation
  - `linkSdk/`: SDK interfaces and utilities
  - `linkCommon/`: Shared components
  - `app_scrcpy/`: Screen mirroring functionality
  - `localLibs/wlhardware/`: Hardware abstraction layer

- **link-hid/**: Touch control calibration module (v1.0.2.36)

### Key Components

1. **Connectivity Management** (`com.autoai.wdlink`):
   - `LinkSdkModel`: Core SDK integration
   - `MainActivity`: Main entry point
   - `CastWindowStateManager`: Screen casting state management

2. **Car Integration** (`com.autoai.car`):
   - `CarInfoProxy`: Interface to car system information
   - OS-specific adapters for different car platforms

3. **Touch Calibration** (link-hid module):
   - Platform-specific calibration (iOS requires special handling)
   - State management via `HidConstants`
   - Logging via `LinkHIDLogUtil`

### Platform Support
- **Product Flavors**: baseline, datong, _24L (for different car models)
- **Signing Configs**: a20, sibalu, mona (platform-specific keys)
- **Min SDK**: 27, Target SDK: 34

## Development Guidelines

### State Management
- Bluetooth states: Always check current connection state before UI updates
- Calibration states: Follow the flow defined in `docs/ios_calibration_flow_sequence.md`
- Use proper state encapsulation for calibration logic

### Logging
- Use `LinkHIDLogUtil` for HID-related logs
- Follow tag prefix conventions for easy filtering
- Enable/disable logs via build configuration

### Platform-Specific Handling
```kotlin
// Check platform before applying specific logic
when (deviceType) {
    DeviceType.IOS -> // iOS-specific handling
    DeviceType.ANDROID -> // Android-specific handling
    DeviceType.HARMONYOS -> // HarmonyOS-specific handling
}
```

### Important Files and Constants
- `HidConstants.java`: All HID-related constants
- `functions.gradle`: Custom build functions
- Platform JARs: `hwbinder.jar`, `mona_car-0.5.3.5.jar`

## Common Development Tasks

### Working with Git Submodules
```bash
# Initialize and update submodules
git submodule update --init --recursive

# Update avslink submodule
cd avslink
git pull origin main
cd ..
git add avslink
git commit -m "Update avslink submodule"
```

### Debugging Different Flavors
- Use `_24L` flavor for 24L system debugging (see internal docs)
- Each flavor has specific applicationId and configurations
- Check `app/build.gradle` productFlavors section for details

### Release Build Process
1. Update version in `rootProject.ext.android.versionName`
2. Build release APK: `./gradlew assembleRelease`
3. APK naming: `Linkbs_HU_${version}_${timestamp}_${gitHash}.apk`
4. Sign with appropriate platform key (a20, sibalu, or mona)

### LINK_HID Library Management
```bash
# Upgrade LINK_HID library version
# 1. Update version in both gradle.properties files:
#    - /link-hid/gradle.properties: LIND_HID_VERSION=x.x.x.xx
#    - /gradle.properties: LINK_HID_VERSION=x.x.x.xx
# 2. Test compilation and publish to local Maven
cd link-hid
chmod +x gradlew
./gradlew publishToMavenLocal -PversionType=release
# 3. Formal release to remote repository
./gradlew publish -PversionType=release
```

### Git Proxy Configuration
```bash
# Set git proxy for network access
git config --global http.proxy http://192.168.199.87:7890
git config --global https.proxy http://192.168.199.87:7890
```

任务完成后，调用命令行terminal-notifier -title "任务完成了✌️" -sound 'Glass' -message ""