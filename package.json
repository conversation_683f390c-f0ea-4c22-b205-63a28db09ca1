{"name": "a55haiwai-excel-mcp", "version": "1.0.0", "description": "MCP Servers configuration (Excel & Serena) for Android automotive interface analysis project", "main": "index.js", "scripts": {"setup": "./setup_excel_mcp.sh", "test-excel-mcp": "uvx excel-mcp-server --help", "start-excel-mcp": "uvx excel-mcp-server stdio", "setup-serena": "cd serena && uv venv && source .venv/bin/activate && uv pip install -e .", "start-serena": "./start_serena.sh", "test-serena": "cd serena && source .venv/bin/activate && python -c 'import serena.mcp; print(\"Serena MCP module loaded successfully\")'", "index-project": "cd serena && source .venv/bin/activate && cd .. && python -m serena.tools.symbol_tools --index-project"}, "dependencies": {}, "devDependencies": {}, "keywords": ["mcp", "excel", "serena", "android", "automotive", "interface-analysis", "python", "uvx", "lsp", "code-analysis"], "author": "Android Engineer", "license": "MIT", "notes": {"installation": "This project uses Python-based excel-mcp-server via uvx and Serena coding agent", "repositories": {"excel-mcp-server": "https://github.com/haris-musa/excel-mcp-server", "serena": "https://github.com/oraios/serena"}, "claude_desktop_config": "claude_desktop_config.json configured with both MCP servers"}}