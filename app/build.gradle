plugins {
    id 'com.android.application' version '7.1.3'
    id 'org.jetbrains.kotlin.android' version '1.8.0'

    id 'kotlinx-serialization'
    id 'jacoco'
}
android {
    compileSdkVersion rootProject.ext.android.compileSdk

    defaultConfig {
        applicationId "com.autoai.car.welink3"
        minSdkVersion rootProject.ext.android.minSdk
        targetSdkVersion rootProject.ext.android.targetSdk
        versionName rootProject.ext.android.versionName
        versionCode rootProject.ext.android.versionCode

        buildConfigField "boolean", "DEBUG_MODE", rootProject.ext.getConfigFromLocal('debugMode', 'false')

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary true
        }
        ndk {
            // 指定支持的 ABI，避免依赖系统库
            abiFilters 'armeabi-v7a', 'arm64-v8a'
        }
    }

    signingConfigs {
        a20 {
            //基线抖吧车机系统签名
            storeFile file("../storeFile/a20/toyota.keystore")
            storePassword "android"
            keyAlias "androiddebugkey"
            keyPassword "android"
            /*    storeFile file("../storeFile/a20/debug.keystore")
                storePassword "autoai123"
                keyAlias "dev"
                keyPassword "autoai123"*/
        }
        sibalu {
            storeFile file("../storeFile/sibalu/platform.keystore")
            storePassword "000000"
            keyAlias "desay"
            keyPassword "000000"
        }
        /*mona {
            storeFile file("../storeFile/mona/platform.keystore")
            storePassword "android"
            keyAlias "platformkey"
            keyPassword "android"
            v1SigningEnabled true
            v2SigningEnabled true
        }*/
    }

    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.a20
        }
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.a20
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = rootProject.ext.android.jvmTarget
    }
    buildFeatures {
        viewBinding true
    }
    //打包
//    libraryVariants.all(assembleAAR(project))
    applicationVariants.all(assembleAPK(project))

    // 关闭lint检查、防止正式环境出现异常
    lintOptions {
        abortOnError false
        checkReleaseBuilds false
    }

    flavorDimensions 'poc'
    productFlavors {

        baseline {
//            namespace 'baseline'
            applicationId "com.autoai.car.welink3"
        }

        datong {
//            namespace 'datong'
            applicationId "com.autoai.car.welink3"
        }

        _24L {
//            namespace 'datong'
            applicationId "com.autoai.car.welink3"
        }

    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['src/main/jniLibs']
        }
//        --> 共同
//        main {
//        }
//        --> 基线
        baseline {
            //java 源码
            java.srcDirs += ['src/product_customized/baseline/java']
            //资源路径
            res.srcDirs += ['src/main/res-v']
            res.srcDirs += ['src/product_customized/baseline/res']

            //assets资源文件
            assets.srcDirs += ['src/product_customized/baseline/assets']
            //manifest资源文件
            manifest.srcFile 'src/product_customized/baseline/AndroidManifest.xml'
        }

        //--> 上汽大通
        datong {
            //java 源码
            java.srcDirs += ['src/product_customized/datong/java']
            //资源路径
            res.srcDirs += ['src/main/res-h']
            res.srcDirs += ['src/product_customized/datong/res']
            //assets资源文件
            assets.srcDirs += ['src/product_customized/datong/assets']
            //manifest资源文件
            manifest.srcFile 'src/product_customized/datong/AndroidManifest.xml'
        }

        // --> 丰田24L
        _24L {
            //java 源码
            java.srcDirs += ['src/product_customized/_24L/java']
            //资源路径
            res.srcDirs += ['src/main/res-v']
            res.srcDirs += ['src/product_customized/_24L/res']

            //assets资源文件
            assets.srcDirs += ['src/product_customized/_24L/assets']
            //manifest资源文件
            manifest.srcFile 'src/product_customized/_24L/AndroidManifest.xml'
        }

    }
    testOptions {
        unitTests.all {
            useJUnitPlatform()
            unitTests.returnDefaultValues = true
        }
    }

}

dependencies {
    //noinspection GradlePath
//    implementation files('/libs/dsv-extlink.jar')
    //lib
    if (file("../avslink/linkHost").exists() && SDK_TEST != "true") {
        implementation(project(path: ':linkHost')) {
            exclude group: 'androidx.core'

        }
    } else {
        implementation "com.autoai.avs.link:linkhost:$LINK_HOST_VERSION"
    }
//    implementation 'com.autoai.avs.link:linkhost:*********'
    implementation("com.autoai.link.baselog:baselog:0.0.11") {
        exclude group: 'androidx.core'

    }
    implementation('androidx.core:core:1.8.0+')

    implementation('androidx.appcompat:appcompat:1.4.0') {
        exclude group: 'androidx.core', module: 'core-ktx'

    }
    implementation "com.autoai.avs.link:linkhid:${LINK_HID_VERSION}"
    //24L车机双屏使用updatePadding()方法适配DP席
    implementation 'androidx.core:core-ktx:1.9.0'
//    implementation 'com.ashokvarma.android:bottom-navigation-bar:1.4.1'
    implementation 'androidx.navigation:navigation-fragment:2.4.1'
    implementation 'androidx.navigation:navigation-ui:2.4.1'
//    implementation 'com.google.android.material:material:1.4.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.1'
    //
//    implementation "org.jetbrains.kotlin:kotlin-reflect:1.6.21"
    implementation("org.jetbrains.kotlin:kotlin-reflect:1.6.21") {
        exclude group: 'androidx.core'
    }

    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
//    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.6.1'
//    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1'
//    implementation 'androidx.lifecycle:lifecycle-process:2.3.0'
    implementation('androidx.lifecycle:lifecycle-livedata-ktx:2.6.1') {
        exclude group: 'androidx.core'
    }

    implementation('androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1') {
        exclude group: 'androidx.core'

    }

    implementation('androidx.lifecycle:lifecycle-process:2.3.0') {
        exclude group: 'androidx.core'
    }

    implementation('androidx.fragment:fragment-ktx:1.5.6') {
        exclude group: 'androidx.core'

    }

    implementation('com.blankj:utilcodex:1.31.0') {
        exclude group: 'androidx.core'

    }

    implementation('com.squareup.retrofit2:retrofit:2.11.0') {
        exclude group: 'androidx.core'

    }
    implementation('org.jetbrains.kotlinx:kotlinx-serialization-json:1.6.3') {
        exclude group: 'androidx.core', module: 'core-ktx'

    }
    implementation files('libs\\mona_car-0.5.3.5.jar')
    implementation files('libs\\hwbinder.jar')
//    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.8.6'
//    implementation 'android.car:release:0.5.2.3-RELEASE'

    debugImplementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
    debugImplementation 'com.facebook.stetho:stetho:1.5.0'
    debugImplementation 'com.facebook.stetho:stetho-okhttp3:1.5.0'

    //
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'

    //EAP 包
//    implementation (name: 'EapHidl-ds04r-ds04rSign',ext: 'aar')
//    implementation files('/libs/EapHidl-ds04r-ds04rSign.aar');
    implementation 'com.autoai.avs.common:base:1.9.0.1'
    implementation 'com.autoai.avs.common:sota:1.9.0.1'
    implementation 'com.autoai.avs.common:sign:1.9.0.1'
    implementation 'com.autoai.avs.common:qrcode:1.9.0.1'
    implementation 'com.github.bumptech.glide:glide:4.10.0'
    implementation 'com.youth.banner:banner:1.4.5'
    //回弹效果
    implementation 'io.github.everythingme:overscroll-decor-android:1.1.1'


//    implementation files('/libs/EapHidl-ds04r-ds04rSign.aar');
    implementation fileTree(dir: 'libs', include: ['*.jar', '*.aar'])
//    implementation 'com.tencent.mars:mars-xlog:1.2.6'
//    implementation 'com.tencent.mars:mars-wrapper:2.0.4'

    testImplementation "org.junit.jupiter:junit-jupiter-api:${JUNIT_VERSION}"
    testRuntimeOnly "org.junit.jupiter:junit-jupiter-engine:${JUNIT_VERSION}"
    testImplementation "org.junit.jupiter:junit-jupiter-params:${JUNIT_VERSION}" // 可选，用于参数化测试
    testImplementation "org.mockito:mockito-core:${MOCKITO_VERSION}"
    testImplementation "org.mockito:mockito-inline:${MOCKITO_VERSION}" // 用于模拟 final/static 方法
    testImplementation "org.mockito:mockito-junit-jupiter:${MOCKITO_VERSION}" // 提供 MockitoExtension 支持

}

jacoco {
    toolVersion = "0.8.12" //代码覆盖库jacoco版本号
}
tasks.withType(Test) {
    jacoco.includeNoLocationClasses = true
    jacoco.excludes = ['jdk.internal.*']
}
// 适配多 Flavor 场景， 直接运行 gralde jacocoTestReportT2Debug  即可
android.applicationVariants.configureEach { variant ->
    var tsName = "${variant.flavorName.capitalize()}${variant.buildType.name.capitalize()}"
    task "jacocoTestReport${tsName}"(type: JacocoReport, dependsOn: ["test${tsName}UnitTest"]) {
        group = 'Reporting'
        description = 'Generate Jacoco coverage reports after running tests.'
        reports {
            xml.required = true
            csv.required = false
        }
//        fileTree(dir: "build/intermediates/javac/${tsName}", excludes: ['**/R.class', '**/R$*.class', '**/*$ViewInjector*.*', '**/BuildConfig.*'],
        classDirectories.setFrom(fileTree(dir: "build/intermediates/javac/${tsName}", excludes: ['**/R.class', '**/R$*.class', '**/*$ViewInjector*.*', '**/BuildConfig.*']) + fileTree(dir: "build/tmp/kotlin-classes/${tsName}", excludes: ['**/R.class', '**/R$*.class', '**/*$ViewInjector*.*', '**/BuildConfig.*']))
        sourceDirectories.setFrom(files('src/main/java'))
        executionData.setFrom(fileTree(dir: "build/", includes: ['**/*.exec']))
    }
}