<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.autoai.wdlink.hu"
    >
<!--    android:sharedUserId="android.uid.system"-->

    <uses-sdk tools:overrideLibrary="com.autoai.welinkapp" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    <uses-permission android:name="android.permission.INTERNET" /> <!-- 请求安装APK的权限 -->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" /> <!-- 写如外部存储的权限 -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> <!-- 读取外部存储的权限 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" /> <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CONNECTED_DEVICE"
        android:minSdkVersion="34"/>

    <application
        android:name=".MyApplication"
        android:persistent="true"
        android:allowBackup="false"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:icon="@mipmap/link_ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/link_ic_launcher"
        android:supportsRtl="true"
        android:testOnly="false"
        android:theme="@style/theme_fullScreen"
        tools:ignore="DataExtractionRules"
        tools:replace="android:allowBackup"
        tools:targetApi="s">
        <service
            android:name=".LinkService"
            android:foregroundServiceType="connectedDevice"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com.gn2023oversea.android.launcher.LinkService" />
            </intent-filter>
        </service>

        <activity
            android:name=".castwindow.view.FullscreenActivity"
            android:exported="false"
            android:configChanges="orientation|screenSize|keyboardHidden|layoutDirection|uiMode|smallestScreenSize|screenLayout|density|locale"
            android:theme="@style/Theme.AppCompat.DayNight.NoActionBar" />
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:configChanges="orientation|screenSize|keyboardHidden|layoutDirection|uiMode|smallestScreenSize|screenLayout|density|locale"
            android:launchMode="singleInstance"
            android:screenOrientation="landscape">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <service android:name="com.autoai.wdlink.hu.frame.utils.PopWinService"
            android:foregroundServiceType="connectedDevice"
            />
        <activity android:name=".DevActivity"
            android:exported="false"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|uiMode"
            tools:ignore="Instantiatable" />

        <activity
            android:name=".SettingActivity"
            android:exported="false" />
    </application>

</manifest>
