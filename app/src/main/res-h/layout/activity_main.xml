<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:orientation="vertical"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/relativeLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="84dp"
        android:orientation="vertical">
        <!--main View-->
        <LinearLayout
            android:id="@+id/main_lin"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            tools:visibility="visible"
            android:visibility="gone">

            <!--左侧手机栏-->
            <RelativeLayout
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:id="@+id/phone_lin"
                android:layout_width="322dp"
                android:layout_height="match_parent"
                android:layout_marginStart="120dp"
                android:layout_gravity="center"
                android:background="@drawable/normal"
                android:gravity="center"
                tools:visibility="visible"
                android:orientation="vertical"
                tools:ignore="MissingConstraints">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/jb_phone_bg"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="150dp"
                        android:background="@mipmap/link_phont_adapter_welink_link"
                        android:gravity="center">

                    </LinearLayout>

                    <ImageView
                        android:id="@+id/image_first"
                        android:layout_width="wrap_content"
                        android:layout_height="0dp"
                        android:layout_weight="3"
                        android:src="@mipmap/link_ic_launcher"
                        android:visibility="gone"
                        tools:ignore="ContentDescription" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:layout_marginHorizontal="16dp"
                        android:gravity="center_vertical"
                        android:text="@string/connect_introduce"
                        android:textColor="#99000000"
                        android:textSize="9sp" />


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <RelativeLayout
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginTop="17dp"
                            android:layout_marginStart="16dp">
                            <View
                                android:visibility="gone"
                                android:id="@+id/vdview"
                                android:layout_centerHorizontal="true"
                                android:layout_width="@dimen/base_1_dp"
                                android:layout_marginTop="5dp"
                                android:layout_marginBottom="4dp"
                                android:layout_height="match_parent"
                                android:background="@color/blue"/>
                            <com.autoai.wdlink.hu.ui.VerticalDashLine
                                android:layout_width="1dp"
                                android:id="@+id/vdline"
                                android:layout_marginTop="5dp"
                                android:layout_marginBottom="4dp"
                                android:layout_centerHorizontal="true"
                                android:layout_height="match_parent" />

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:orientation="vertical">

                                <ImageView
                                    android:id="@+id/usb_img"
                                    android:layout_width="16dp"
                                    android:layout_height="16dp"
                                    android:layout_marginTop="17dp"
                                    android:layout_marginBottom="27dp"
                                    android:src="@mipmap/link_blue_dot_circle" />

                                <ImageView
                                    android:id="@+id/wifi_img"
                                    android:layout_width="16dp"
                                    android:layout_height="16dp"
                                    android:layout_marginTop="17dp"
                                    android:layout_marginBottom="28dp"
                                    android:src="@mipmap/link_blue_dot_circle" />

                                <ImageView
                                    android:id="@+id/ic_shouquan"
                                    android:layout_width="16dp"
                                    android:layout_height="16dp"
                                    android:layout_marginTop="17dp"
                                    android:src="@mipmap/link_white_dot_circle" />
                            </LinearLayout>


                        </RelativeLayout>

                        <LinearLayout
                            android:id="@+id/connection_method"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:layout_marginTop="5dp"
                            android:visibility="visible">

                            <LinearLayout
                                android:id="@+id/usb_lin"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="9dp"
                                android:layout_marginRight="17dp"
                                android:layout_marginTop="12dp"
                                android:background="@drawable/install_jb_bg"
                                android:orientation="horizontal"
                                android:paddingLeft="5dp"
                                android:paddingTop="10dp"
                                android:paddingRight="11dp"
                                android:paddingBottom="9dp">
                                <FrameLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content">
                                    <ImageView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center"
                                        android:background="@drawable/shape_icon_bg"/>
                                    <ImageView
                                        android:layout_width="19dp"
                                        android:layout_height="19dp"
                                        android:layout_gravity="center"
                                        android:src="@mipmap/link_usb_prompt"/>
                                </FrameLayout>

                                <TextView
                                    android:id="@+id/usb"
                                    android:layout_width="0dp"
                                    android:layout_height="match_parent"
                                    android:layout_weight="1"
                                    android:gravity="center_vertical"
                                    android:text="@string/connect_usbdatacable"
                                    android:textColor="#222222"
                                    android:layout_marginLeft="8dp"
                                    android:textSize="14sp"/>

                                <ImageView
                                    android:id="@+id/usb_image"
                                    android:layout_width="19dp"
                                    android:layout_height="19dp"
                                    android:src="@mipmap/link_loading"/>

                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/wifi_lin"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="12dp"
                                android:layout_marginLeft="9dp"
                                android:layout_marginRight="17dp"
                                android:background="@drawable/install_jb_bg"
                                android:orientation="horizontal"
                                android:paddingLeft="5dp"
                                android:paddingTop="10dp"
                                android:paddingRight="11dp"
                                android:paddingBottom="9dp">
                                <FrameLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content">
                                    <ImageView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center"
                                        android:background="@drawable/shape_icon_bg"/>
                                    <ImageView
                                        android:layout_width="19dp"
                                        android:layout_height="19dp"
                                        android:layout_gravity="center"
                                        android:src="@mipmap/link_wireless_map"/>
                                </FrameLayout>
                                <TextView
                                    android:id="@+id/wifi"
                                    android:layout_width="0dp"
                                    android:layout_height="match_parent"
                                    android:layout_weight="1"
                                    android:layout_marginLeft="8dp"
                                    android:text="@string/connect_wireless_network"
                                    android:gravity="center_vertical"
                                    android:textColor="#222222"
                                    android:textSize="14sp"/>

                                <ImageView
                                    android:id="@+id/wifi_image"
                                    android:layout_width="19dp"
                                    android:layout_height="19dp"
                                    android:src="@mipmap/link_loading"/>

                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/start_line"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="12dp"
                                android:layout_marginLeft="9dp"
                                android:layout_marginRight="17dp"
                                android:background="@drawable/install_jb_bg"
                                android:orientation="horizontal"
                                android:paddingLeft="5dp"
                                android:paddingTop="10dp"
                                android:paddingRight="11dp"
                                android:paddingBottom="9dp">
                                <FrameLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content">
                                    <ImageView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center"
                                        android:background="@drawable/shape_icon_bg"/>
                                    <ImageView
                                        android:layout_width="19dp"
                                        android:layout_height="19dp"
                                        android:layout_gravity="center"
                                        android:src="@mipmap/link_screen_casting_authorization_diagram"/>
                                </FrameLayout>
                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="match_parent"
                                    android:layout_weight="1"
                                    android:layout_marginLeft="8dp"
                                    android:gravity="center_vertical"
                                    android:text="@string/connect_screen_casting_authorization"
                                    android:textColor="#222222"
                                    android:textSize="14sp" />

                                <ImageView
                                    android:id="@+id/start_image"
                                    android:layout_width="19dp"
                                    android:layout_height="19dp"
                                    android:src="@mipmap/link_loading" />

                            </LinearLayout>
                        </LinearLayout>
                    </LinearLayout>

                    <!--等待连接中-->
                    <LinearLayout
                        android:id="@+id/loading_lin"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:gravity="bottom|right"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:visibility="visible">

                        <ImageView
                            android:id="@+id/loading"
                            android:layout_width="60dp"
                            android:alpha="0.01"
                            android:layout_height="60dp"
                            android:src="@mipmap/link_connection_successful" />

                        <TextView
                            android:id="@+id/loading_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:text="@string/connect_waiting_for_connection"
                            android:textColor="#000"
                            android:visibility="gone" />
                    </LinearLayout>
                </LinearLayout>

                <!-- 校准 -->
                <RelativeLayout
                    android:id="@+id/calibration_lin"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="gone"
                    tools:visibility="gone">

                    <ImageView
                        android:id="@+id/bg_img"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/jb_calibration_phone_bg"
                        android:gravity="center"/>

                    <RelativeLayout
                        android:layout_width="wrap_content"
                        android:layout_centerHorizontal="true"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:layout_marginTop="93dp"
                        android:id="@+id/calibration_related">
                        <ImageView
                            android:visibility="gone"
                            android:id="@+id/calibrationing_img"
                            android:layout_width="199dp"
                            android:layout_height="263dp"/>

                        <ImageView
                            android:visibility="gone"
                            android:id="@+id/scanning_effect"
                            android:layout_width="172dp"
                            android:layout_centerInParent="true"
                            android:layout_height="172dp"
                            android:gravity="center"/>

                        <!--校准失败 icon-->
                        <ImageView
                            android:visibility="gone"
                            android:id="@+id/calibration_failed_img"
                            android:layout_width="184dp"
                            android:layout_centerHorizontal="true"
                            android:layout_marginTop="13dp"
                            android:layout_height="184dp"/>
                    </RelativeLayout>

                    <TextView
                        android:id="@+id/calibration_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:textColor="#17417C"
                        android:layout_marginTop="24dp"
                        android:layout_below="@+id/calibration_related"
                        android:gravity="center"
                        tools:text="校准失败"
                        android:textSize="19sp" />

                    <TextView
                        android:id="@+id/calibration_content"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="36dp"
                        android:layout_below="@+id/calibration_title"
                        android:layout_marginTop="17dp"
                        android:gravity="center"
                        android:textColor="#17417C"
                        android:text="校准过程中请勿触碰手机 校准失败可能会导"
                        android:textSize="12sp"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/recalibrate"
                        android:layout_width="206dp"
                        android:layout_height="49dp"
                        android:layout_centerHorizontal="true"
                        android:layout_gravity="center"
                        android:layout_below="@+id/calibration_content"
                        android:layout_marginTop="29dp"
                        android:background="@drawable/global_selector_btn_bg_normal"
                        android:clickable="true"
                        android:gravity="center"
                        android:text="重新校准"
                        android:textColor="#FFFFFF"
                        android:textSize="16sp"
                        android:visibility="gone"
                        tools:visibility="visible" />

                </RelativeLayout>

            </RelativeLayout>

            <!--右侧窗口和按钮-->
            <LinearLayout
                android:id="@+id/right_lin"
                android:layout_width="match_parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                android:layout_marginStart="32dp"
                android:layout_marginTop="24dp"
                android:layout_height="match_parent"
                android:orientation="horizontal">

                <RelativeLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:layout_marginBottom="50dp"
                    android:layout_marginEnd="20dp"
                    android:background="@drawable/jb_bg">
                    <RelativeLayout
                        android:id="@+id/animation_re"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">
                        <!--引导 1 2 、授权 -->
                        <LinearLayout
                            android:visibility="gone"
                            tools:visibility="gone"
                            android:id="@+id/create_lin"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical">
                            <!--授权-->
                            <ViewStub
                                android:id="@+id/vs_auth"
                                android:layout="@layout/layout_authority"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"/>
                            <!--引导-->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="horizontal">
                                <androidx.constraintlayout.widget.ConstraintLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="120dp">

                                    <TextView
                                        android:id="@+id/main_one"
                                        app:layout_constraintLeft_toLeftOf="parent"
                                        app:layout_constraintTop_toTopOf="parent"
                                        android:layout_marginTop="106dp"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="@string/connect_one"
                                        android:textColor="#A5AAC0"
                                        android:textSize="56sp"
                                        android:textStyle="bold" />

                                    <TextView
                                        android:id="@+id/text_start_link"
                                        app:layout_constraintLeft_toRightOf="@+id/main_one"
                                        app:layout_constraintBottom_toBottomOf="@+id/main_one"
                                        android:layout_width="wrap_content"
                                        android:layout_marginBottom="9dp"
                                        android:layout_marginStart="8dp"
                                        android:layout_height="wrap_content"
                                        android:text="@string/connect_start_connecting"
                                        android:textColor="#2F323E"
                                        android:textSize="28sp"
                                        android:textStyle="bold" />

                                    <TextView
                                        android:id="@+id/main_sec_text"
                                        app:layout_constraintBottom_toBottomOf="@+id/text_start_link"
                                        app:layout_constraintStart_toEndOf="@+id/text_start_link"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginStart="20dp"
                                        android:text="@string/updata_open_the_application_on_your_phone_first"
                                        android:textColor="#686874"
                                        android:textSize="20sp" />

                                    <ImageView
                                        android:id="@+id/ima_tips"
                                        app:layout_constraintLeft_toLeftOf="@+id/main_one"
                                        app:layout_constraintTop_toBottomOf="@+id/main_one"
                                        android:layout_width="219dp"
                                        android:layout_height="41dp"
                                        android:layout_marginTop="21dp"
                                        android:layout_marginStart="8dp"
                                        android:src="@mipmap/link_application_get_tips"
                                        />
                                    <ImageView
                                        android:layout_marginTop="40dp"
                                        app:layout_constraintTop_toBottomOf="@+id/ima_tips"
                                        app:layout_constraintStart_toStartOf="@+id/ima_tips"
                                        android:layout_width="322dp"
                                        android:layout_height="203dp"
                                        android:src="@mipmap/link_connection_prompt_1" />

                                </androidx.constraintlayout.widget.ConstraintLayout>

                                <androidx.constraintlayout.widget.ConstraintLayout
                                    android:id="@+id/connectionPrompt2"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="97dp"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:id="@+id/text_second"
                                        app:layout_constraintLeft_toLeftOf="parent"
                                        app:layout_constraintTop_toTopOf="parent"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="99dp"
                                        android:text="@string/connect_twe"
                                        android:textColor="#A5AAC0"
                                        android:textSize="56sp"
                                        android:textStyle="bold" />

                                    <TextView
                                        android:id="@+id/sc_text"
                                        app:layout_constraintBottom_toBottomOf="@+id/text_second"
                                        app:layout_constraintLeft_toRightOf="@+id/text_second"
                                        android:layout_marginBottom="9dp"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="@string/connect_non_sensory_wired_projection_screen"
                                        android:textColor="#2F323E"
                                        android:textSize="28sp"
                                        android:textStyle="bold" />

                                    <ImageView
                                        app:layout_constraintTop_toBottomOf="@+id/sc_text"
                                        app:layout_constraintLeft_toLeftOf="parent"
                                        android:id="@+id/usb_click"
                                        android:layout_width="219dp"
                                        android:layout_height="41dp"
                                        android:layout_marginTop="21dp"
                                        android:src="@mipmap/link_view_interface_location"/>

                                    <ImageView
                                        android:id="@+id/wireless_imange"
                                        app:layout_constraintTop_toBottomOf="@+id/usb_click"
                                        app:layout_constraintLeft_toLeftOf="parent"
                                        android:layout_width="289dp"
                                        android:layout_height="217dp"
                                        android:layout_marginTop="40dp"
                                        android:src="@mipmap/link_wireless_prompt_diagram" />

                                    <ImageView
                                        app:layout_constraintLeft_toRightOf="@+id/wireless_imange"
                                        app:layout_constraintTop_toTopOf="@+id/wireless_imange"
                                        android:layout_width="289dp"
                                        android:layout_height="217dp"
                                        android:layout_marginStart="19dp"
                                        android:src="@mipmap/link_wired_connection_image" />
                                </androidx.constraintlayout.widget.ConstraintLayout>
                            </LinearLayout>

                        </LinearLayout>
                        <!--环境检测-->
                        <LinearLayout
                            android:id="@+id/checktheenvironment"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical"
                            tools:visibility="gone"
                            android:visibility="visible">

                            <ImageView
                                android:layout_width="295dp"
                                android:layout_height="295dp"
                                android:src="@mipmap/link_check_the_environment"/>

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="20dp"
                                android:gravity="center"
                                android:text="@string/connect_checking_environment"
                                android:textColor="#2F323E"
                                android:textSize="24sp"/>
                        </LinearLayout>
                        <!--授权提示-->
                        <LinearLayout
                            android:id="@+id/windows_lin_tisp"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <TextView
                                android:textStyle="bold"
                                android:gravity="center_horizontal"
                                android:id="@+id/windows_lin_text"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="@string/connect_record_mobile_phone_footage"
                                android:layout_marginTop="70dp"
                                android:textColor="#2F323E"
                                android:textSize="32sp"/>

                            <TextView
                                android:gravity="center_horizontal"
                                android:id="@+id/windows_lin_ftext"
                                android:layout_marginTop="24dp"
                                android:visibility="gone"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="@string/connect_start_screen_mirroring_for_you_immediately"
                                android:textColor="#686874"
                                android:textSize="20sp" />

                            <ImageView
                                android:id="@+id/windows_lin_image"
                                android:layout_width="480dp"
                                android:layout_height="253dp"
                                android:layout_marginTop="50dp"
                                android:layout_gravity="center"
                                android:src="@mipmap/link_android_authorization_screen_casting_prompt_image"/>

                            <TextView
                                android:id="@+id/tv_recording_permission"
                                android:gravity="center_horizontal"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="@string/connect_start_screen_unauthorized_screen_recording_permission"
                                android:textColor="#222222"
                                android:visibility="invisible"
                                android:layout_marginTop="56dp"
                                android:textSize="28sp" />
                        </LinearLayout>
                        <!--IOS提示-->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:gravity="bottom|center"
                            android:id="@+id/ios_webview"
                            android:visibility="gone">

                            <ScrollView
                                android:scrollbars="none"
                                android:id="@+id/scrollView"
                                android:layout_width="wrap_content"
                                android:layout_height="0dp"
                                android:scrollbarThumbVertical="@null"
                                android:fadingEdgeLength="40dp"
                                android:requiresFadingEdge="vertical"
                                android:overScrollMode="never"
                                android:background="@null"
                                android:layout_weight="1">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="vertical">

                                    <ImageView
                                        android:scaleType="centerCrop"
                                        android:id="@+id/tp_ios"
                                        android:layout_gravity="center"
                                        android:layout_width="wrap_content"
                                        android:layout_height="0dp"
                                        android:layout_weight="1"
                                        android:src="@mipmap/link_ios_calibration_prompt_long_image"/>

                                </LinearLayout>
                            </ScrollView>

                            <TextView
                                android:id="@+id/close_text"
                                android:layout_width="324dp"
                                android:layout_height="65dp"
                                android:layout_marginTop="19dp"
                                android:text="@string/connect_close_window"
                                android:layout_marginBottom="37dp"
                                android:gravity="center"
                                android:background="@drawable/close_bg"
                                android:textColor="#686874"
                                android:textSize="19sp" />

                        </LinearLayout>

                        <!--录屏未授权-->
                        <LinearLayout
                            android:id="@+id/window_req_record_screen"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:gravity="center"
                            tools:visibility="gone"
                            android:visibility="gone">
                            <com.autoai.wdlink.hu.view.ReqRecordScreenPage
                                android:id="@+id/view_ReqRecordScreenPage"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"/>
                        </LinearLayout>
                    </RelativeLayout>

                    <!--引导视频、二维码-->
                    <RelativeLayout
                        android:visibility="gone"
                        android:id="@+id/video_install_layout"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <!--引导视频-->
                        <LinearLayout
                            android:id="@+id/video_lin"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_centerHorizontal="true"
                            android:layout_centerVertical="true"
                            android:visibility="gone">

                            <FrameLayout
                                android:id="@+id/textureView_frame"
                                android:layout_width="896dp"
                                android:layout_marginVertical="35dp"
                                android:layout_marginHorizontal="206dp"
                                android:layout_height="match_parent">
                                <TextureView
                                    android:id="@+id/guide_textureView"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_gravity="center"
                                    android:visibility="gone" />
                            </FrameLayout>

                        </LinearLayout>

                        <!--二维码-->
                        <LinearLayout
                            android:id="@+id/install_lin"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            android:visibility="gone">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="0dp"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:orientation="vertical">

                                <ImageView
                                    android:id="@+id/qr_code_image_view"
                                    android:layout_width="330dp"
                                    android:layout_height="330dp"
                                    android:layout_gravity="center"
                                    android:gravity="center"
                                    android:background="@drawable/btn_click_c" />
                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:gravity="center"
                                    android:text="@string/connect_scan_qr_code"
                                    android:textColor="#2F323E"
                                    android:textSize="24sp"
                                    android:layout_marginTop="37dp"
                                    android:textStyle="bold"/>
                            </LinearLayout>

                        </LinearLayout>
                    </RelativeLayout>
                </RelativeLayout>

                <!--右侧按钮-->
                <LinearLayout
                    android:id="@+id/vi_lin"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:layout_marginEnd="18dp"
                    android:layout_marginBottom="25dp"
                    android:orientation="vertical">
                    <LinearLayout
                        android:visibility="gone"
                        android:id="@+id/setting_lin"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@mipmap/link_small_image_with_shadow"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@mipmap/link_guide_video_prompt_image"
                            android:textColor="#4765FF" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/connect_guide_video"
                            android:textColor="#222222"
                            android:textSize="20sp" />
                    </LinearLayout>
                    <!--引导视频按钮-->
                    <LinearLayout
                        android:id="@+id/guide_video_lin"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@mipmap/link_small_image_with_shadow"
                        android:layout_marginStart="-12dp"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:id="@+id/guide_video"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@mipmap/link_guide_video_prompt_image"
                            android:textColor="#4765FF"/>

                        <TextView
                            android:id="@+id/video_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/connect_guide_video"
                            android:textColor="#222222"
                            android:textSize="14sp"/>
                    </LinearLayout>

                    <!--下载应用按钮-->
                    <LinearLayout
                        android:id="@+id/install_app_lin"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="-12dp"
                        android:gravity="center"
                        android:background="@mipmap/link_small_image_with_shadow"
                        android:orientation="vertical">

                        <ImageView
                            android:id="@+id/install_app"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@mipmap/link_guide_to_download_app_images"/>

                        <TextView
                            android:id="@+id/install_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/connect_download_the_application"
                            android:textColor="#222222"
                            android:textSize="14sp"/>
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

    <!--bannerView-->
    <LinearLayout
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:id="@+id/banner_lin"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_gravity="center"
        android:layout_marginLeft="130dp"
        android:layout_marginRight="130dp"
        android:layout_marginTop="24dp"
        android:layout_marginBottom="135dp"
        android:background="@drawable/jb_bg"
        android:orientation="horizontal"
        android:visibility="gone">

        <ImageView
            android:id="@+id/close_icon"
            android:layout_marginStart="30dp"
            android:layout_marginTop="30dp"
            android:layout_width="43dp"
            android:layout_height="43dp"
            android:src="@mipmap/link_full_close"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_marginTop="30dp"
            android:layout_marginBottom="17dp"
            android:layout_height="wrap_content">
            <com.youth.banner.Banner
                android:id="@+id/banner"
                android:layout_width="wrap_content"
                android:layout_height="492dp"
                app:image_scale_type="fit_center"
                app:indicator_margin="16dp"
                app:indicator_drawable_selected="@drawable/selected_point"
                app:indicator_drawable_unselected="@drawable/unselect_point"
                app:indicator_width="16dp"
                app:indicator_height="16dp"/>
        </LinearLayout>
    </LinearLayout>

    <!--debug View-->
    <View
        android:id="@+id/view_dev"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:background="#00cccccc"
        android:layout_marginEnd="170dp"
        android:layout_marginBottom="140dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:ignore="MissingConstraints" />

    <FrameLayout
        android:background="#fff"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:id="@+id/setting_frameLayout"
        />
</androidx.constraintlayout.widget.ConstraintLayout>