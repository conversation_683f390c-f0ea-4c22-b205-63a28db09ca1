<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="#00772900">
    <ImageView
        android:id="@+id/cover_shot"
        android:visibility="visible"
        android:layout_width="200dp"
        android:layout_height="400dp"
        android:layout_alignParentRight="true"/>

    <!-- 左下角缩放功能 -->
    <Button
        android:id="@+id/left"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_marginLeft="-30dp"
        android:layout_marginBottom="-30dp"
        android:layout_alignBottom="@+id/cover_shot"
        android:layout_alignStart="@+id/cover_shot"
        android:background="@drawable/btn_resize_move_bg"
        android:visibility="visible"
        tools:visibility="visible"/>

    <!-- 右下角缩放功能 -->
    <Button
        android:id="@+id/right"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_marginRight="-30dp"
        android:layout_marginBottom="-30dp"
        android:layout_alignBottom="@+id/cover_shot"
        android:layout_alignEnd="@+id/cover_shot"
        android:background="@drawable/btn_resize_move_bg"
        android:visibility="visible"
        tools:visibility="visible"/>
<!--    <Button-->
<!--        android:layout_width="60dp"-->
<!--        android:layout_height="60dp"-->
<!--        android:layout_marginRight="-30dp"-->
<!--        android:layout_marginBottom="-30dp"-->
<!--        android:layout_alignParentRight="true"-->
<!--        android:layout_alignParentBottom="true"-->
<!--        android:background="@drawable/btn_resize_move_bg"-->
<!--        android:visibility="visible"-->
<!--        tools:visibility="visible"/>-->


</RelativeLayout>