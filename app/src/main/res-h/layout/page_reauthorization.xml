<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center_horizontal"
    android:background="@drawable/jb_bg">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="90dp"
        android:text="@string/reauthorization_title"
        android:textColor="@color/black"
        android:gravity="center"
        android:textSize="50sp"
        android:textStyle="bold"
        android:layout_marginTop="32dp" />

    <TextView
        android:id="@+id/tv_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="200dp"
        android:layout_marginTop="14dp"
        android:text="@string/reauthorization_content"
        android:textColor="@color/black"
        android:textSize="28sp"
        android:lineHeight="48dp"
        app:layout_constraintBottom_toTopOf="@+id/btn_reauthorization"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        app:layout_constraintWidth_max="852dp" />

    <TextView
        android:id="@+id/btn_reauthorization"
        android:layout_width="376dp"
        android:layout_height="92dp"
        android:layout_marginTop="90dp"
        android:background="@drawable/global_selector_btn_blue_bg"
        android:clickable="true"
        android:gravity="center"
        android:text="@string/reauthorization_go"
        android:textColor="@color/white"
        android:textSize="24sp"
        app:layout_constraintBottom_toTopOf="@+id/btn_exit"
        app:layout_constraintEnd_toEndOf="@+id/tv_content"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="@+id/tv_content"
        app:layout_constraintTop_toBottomOf="@+id/tv_content" />


    <TextView
        android:id="@+id/btn_exit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="22dp"
        android:clickable="true"
        android:gravity="center"
        android:paddingStart="50dp"
        android:paddingTop="10dp"
        android:paddingEnd="50dp"
        android:paddingBottom="10dp"
        android:text="@string/exit_app"
        android:textColor="#ED0000"
        android:textSize="24sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/btn_reauthorization"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="@+id/btn_reauthorization"
        app:layout_constraintTop_toBottomOf="@+id/btn_reauthorization" />
</LinearLayout>