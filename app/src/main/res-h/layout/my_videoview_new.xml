<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#00009929">

    <com.autoai.wdlink.hu.module.hidscreen.MultiTouchView
        android:id="@+id/root_view_parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <!-- 左边扩展区域 -->
        <View
            android:id="@+id/view_1"
            android:layout_width="@dimen/extend_size2"
            android:layout_height="match_parent"
            android:background="#0033bb33" />

        <!-- 右边扩展区域 -->
        <View
            android:id="@+id/view_2"
            android:layout_width="@dimen/extend_size2"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:background="#0033bb33" />

        <!-- 上边扩展区域 -->
        <View
            android:id="@+id/view_3"
            android:layout_height="@dimen/extend_top_size"
            android:layout_width="match_parent"
            android:background="#0033bb33" />

        <!-- 下边扩展区域 -->
        <View
            android:id="@+id/view_4"
            android:layout_height="@dimen/extend_bottom_size"
            android:layout_width="match_parent"
            android:layout_alignParentBottom="true"
            android:background="#0033bb33" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/root_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_above="@+id/view_4"
            android:layout_below="@+id/view_3"
            android:layout_toStartOf="@+id/view_2"
            android:layout_toEndOf="@+id/view_1"
            android:background="@drawable/window_bg">

            <!-- 视频操作区域 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/sv_framelayout"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:padding="@dimen/dimen_10"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent">

                <com.autoai.wdlink.hu.module.hidscreen.InterceptFrameLayout
                    android:id="@+id/sv_framelayout_inner"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <TextureView
                        android:id="@+id/phoneData_sv"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center" />
                    <!-- 封面图 -->
                    <RelativeLayout
                        android:id="@+id/view_shot_cover_parent"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:alpha="0"
                        android:background="#303030">

                        <ImageView
                            android:id="@+id/view_shot_cover"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_gravity="center"
                            android:visibility="visible" />
                    </RelativeLayout>
                    <TextView
                        android:id="@+id/tv_fps"
                        android:layout_width="60dp"
                        android:layout_height="30dp"
                        android:layout_marginLeft="15dp"
                        android:layout_marginTop="20dp"
                        android:textSize="18sp"
                        android:textColor="#ff33bb33"
                        android:visibility="gone"/>
                </com.autoai.wdlink.hu.module.hidscreen.InterceptFrameLayout>

                <!-- 锁屏提示UI -->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/sc_on_off_tip"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <View
                        android:id="@+id/screen_bgve"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/screen_off_phone_bg"
                        />

                    <ImageView
                        android:layout_width="185dp"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        android:layout_marginTop="77dp"
                        android:layout_height="184dp"
                        android:id="@+id/floating_window_image" />

                    <TextView
                        android:id="@+id/screen_off_title"
                        android:layout_width="0dp"
                        android:gravity="center"
                        android:layout_height="wrap_content"
                        android:text="@string/pop_window_phone_screen_off"
                        android:textColor="#17417C"
                        android:textSize="19sp"
                        android:textStyle="bold"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.5"
                        android:layout_marginTop="25dp"
                        android:layout_marginHorizontal="24dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/floating_window_image"
                        app:layout_constraintVertical_bias="0.3"
                        app:layout_constraintVertical_chainStyle="packed" />

                    <TextView
                        android:id="@+id/screen_off_reason"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="66dp"
                        android:gravity="center"
                        android:text="@string/pop_window_unlock_continue_cast"
                        android:textColor="#17417C"
                        android:textSize="12sp"
                        android:layout_marginHorizontal="24dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.5"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/floating_window_image" />

                    <TextView
                        android:id="@+id/recovery_cast"
                        android:layout_width="match_parent"
                        android:layout_height="50dp"
                        android:layout_marginHorizontal="26dp"
                        android:background="@drawable/global_selector_btn_bg_normal"
                        android:clickable="true"
                        android:gravity="center"
                        android:text="@string/pop_window_recovery_cast"
                        android:layout_marginBottom="12dp"
                        android:textColor="#fff"
                        android:textSize="17sp"
                        app:layout_constraintBottom_toTopOf="@+id/btn_exit"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent" />

                    <TextView
                        android:id="@+id/btn_exit"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="39dp"
                        android:clickable="true"
                        android:gravity="center"
                        android:paddingStart="50dp"
                        android:paddingTop="10dp"
                        android:paddingEnd="50dp"
                        android:paddingBottom="10dp"
                        android:text="@string/exit_app"
                        android:textColor="#FF2424"
                        android:textSize="11sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <include layout="@layout/safe_driver_old" />
                <!--走行限制-->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:id="@+id/sc_safe_driver"
                    android:background="@drawable/shape_safe_driver_bg"
                    android:layout_centerInParent="true"
                    android:visibility="visible">

                    <ImageButton
                        android:id="@+id/bt_warning"
                        android:layout_width="48dp"
                        android:layout_height="50dp"
                        android:background="@null"
                        android:src="@mipmap/link_warning"
                        android:layout_marginTop="125dp"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent" />

                    <TextView
                        android:lineSpacingMultiplier="1.5"
                        android:gravity="top|center"
                        android:id="@+id/tv_safe_text_driver"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="@string/safe_viewing"
                        android:textColor="#F8F8F8"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/bt_warning"
                        app:layout_constraintRight_toRightOf="parent"
                        android:layout_marginTop="9dp"
                        android:layout_marginHorizontal="14dp"
                        android:textSize="22sp" />

                    <LinearLayout
                        android:id="@+id/button_combination"
                        android:layout_width="0dp"
                        app:layout_constraintLeft_toLeftOf="parent"
                        android:gravity="center"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        android:layout_marginBottom="45dp"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <Button
                            android:id="@+id/bt_safe_exit"
                            android:layout_width="match_parent"
                            android:layout_height="50dp"
                            android:text="@string/exit_app"
                            android:background="@drawable/global_selector_btn_bg_go_home"
                            android:textColor="#ED0000"
                            android:textSize="17sp"
                            android:gravity="center"/>

                        <Button
                            android:id="@+id/bt_passenger"
                            android:layout_width="match_parent"
                            android:gravity="center"
                            android:layout_height="50dp"
                            android:layout_marginTop="10dp"
                            android:text="@string/passenger"
                            android:textColor="@color/blue_0071FF"
                            android:background="@drawable/global_selector_btn_bg_go_home"
                            android:textSize="17sp"/>
                    </LinearLayout>


                </androidx.constraintlayout.widget.ConstraintLayout>

                <!-- 校准提示UI-->
                <RelativeLayout
                    android:id="@+id/pop_calibration_lin"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="gone"
                    tools:visibility="gone">

                    <ImageView
                        android:id="@+id/pop_bg_img"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/jb_calibration_phone_bg"
                        android:gravity="center"/>

                    <ImageView
                        android:layout_centerHorizontal="true"
                        android:layout_marginTop="79dp"
                        android:id="@+id/calibration_failed_img"
                        android:layout_width="185dp"
                        android:layout_height="184dp"
                        android:gravity="center"/>

                    <TextView
                        android:id="@+id/pop_calibration_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:textColor="#17417C"
                        android:layout_below="@+id/calibration_failed_img"
                        android:layout_marginHorizontal="24dp"
                        android:layout_marginTop="25dp"
                        android:gravity="center"
                        android:text="校准失败"
                        android:textSize="19sp" />

                    <TextView
                        android:id="@+id/pop_calibration_content"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="24dp"
                        android:layout_marginTop="66dp"
                        android:layout_below="@+id/calibration_failed_img"
                        android:gravity="center"
                        android:textColor="#17417C"
                        android:text="校准过程中请勿触碰手机 校准失败可能会导"
                        android:textSize="12sp"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/pop_recalibrate"
                        android:layout_width="207dp"
                        android:layout_height="50dp"
                        android:layout_centerHorizontal="true"
                        android:layout_gravity="center"
                        android:layout_marginTop="157dp"
                        android:layout_below="@+id/calibration_failed_img"
                        android:background="@drawable/global_selector_btn_bg_normal"
                        android:clickable="true"
                        android:gravity="center"
                        android:text="重新校准"
                        android:textColor="#ffffff"
                        android:textSize="17sp"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </RelativeLayout>
                <FrameLayout
                    android:id="@+id/dialog_view"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    tools:visibility="gone" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- 顶部拓展功能条 -->
            <LinearLayout
                android:id="@+id/ll_console"
                android:layout_width="55dp"
                android:layout_height="20dp"
                android:background="@drawable/shape_toolbar_bg"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/sv_framelayout"
                android:gravity="center"
                android:orientation="vertical">
                <ImageButton
                    android:id="@+id/iv_move"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@null"
                    android:src="@drawable/move_button_selector"/>
            </LinearLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/console"
                android:layout_width="wrap_content"
                android:layout_height="30dp"
                android:background="@drawable/shape_toolbar_bg"
                android:orientation="horizontal"
                android:minWidth="80dp"
                android:visibility="gone"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/sv_framelayout">

                <ImageButton
                    android:id="@+id/mini"
                    android:layout_width="18dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="9dp"
                    android:layout_marginEnd="32dp"
                    android:background="@null"
                    android:src="@drawable/mini_button_selector"
                    app:layout_constraintEnd_toStartOf="@id/move"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"/>

                <ImageButton
                    android:id="@+id/move"
                    android:layout_width="18dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@null"
                    android:src="@drawable/move_button_selector"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"/>

                <ImageButton
                    android:id="@+id/calibrate_bluetooth"
                    android:layout_width="18dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="10dp"
                    android:background="@null"
                    app:layout_constraintStart_toEndOf="@id/move"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <ImageButton
                    android:id="@+id/fullscreen"
                    android:layout_width="18dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="38dp"
                    android:layout_marginEnd="9dp"
                    android:background="@null"
                    android:src="@drawable/fullscreen_button_selector"
                    app:layout_constraintStart_toEndOf="@id/move"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- 右上角关闭投影，隐藏的测试功能 -->
            <!--<ImageView
                android:id="@+id/close"
                android:layout_width="45dp"
                android:layout_height="45dp"
                android:src="@mipmap/a7_btn_close_p"
                android:visibility="gone"
                tools:visibility="visible"
                app:layout_constraintLeft_toRightOf="@+id/sv_framelayout"
                app:layout_constraintTop_toTopOf="@+id/sv_framelayout" />-->


        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- 左下角缩放功能 -->
        <Button
            android:id="@+id/resize_left"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_alignStart="@+id/root_view"
            android:layout_alignBottom="@+id/root_view"
            android:layout_marginLeft="-30dp"
            android:layout_marginBottom="-30dp"
            android:background="@drawable/btn_resize_move_bg"
            android:visibility="visible"
            tools:visibility="visible" />

        <!-- 右下角缩放功能 -->
        <Button
            android:id="@+id/resize"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_alignEnd="@+id/root_view"
            android:layout_alignBottom="@+id/root_view"
            android:layout_marginRight="-30dp"
            android:layout_marginBottom="-30dp"
            android:background="@drawable/btn_resize_move_bg"
            android:visibility="visible"
            tools:visibility="visible" />

        <!-- 时间 隐藏的测试功能-->
        <TextView
            android:id="@+id/tv_time"
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:background="#f00"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />
    </com.autoai.wdlink.hu.module.hidscreen.MultiTouchView>

</RelativeLayout>