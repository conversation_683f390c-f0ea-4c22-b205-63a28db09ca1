<?xml version="1.0" encoding="utf-8"?>
<resources>
    <integer name="toast_margin_top">114</integer>
    <integer name="toast_margin_start">58</integer>
    <integer name="toast_width">492</integer>

    <integer name="safe_driver_vertical_passenger_margin_top">10</integer>
    <integer name="safe_driver_vertical_passenger_margin_start">26</integer>
    <integer name="safe_driver_vertical_passenger_margin_bottom">0</integer>
    <integer name="safe_driver_vertical_passenger_margin_end">26</integer>
    <integer name="safe_driver_vertical_exit_margin_top">0</integer>
    <integer name="safe_driver_vertical_exit_margin_start">26</integer>
    <integer name="safe_driver_vertical_exit_margin_bottom">0</integer>
    <integer name="safe_driver_vertical_exit_margin_end">26</integer>

    <integer name="safe_driver_horizontal_passenger_margin_top">0</integer>
    <integer name="safe_driver_horizontal_passenger_margin_start">35</integer>
    <integer name="safe_driver_horizontal_passenger_margin_bottom">0</integer>
    <integer name="safe_driver_horizontal_passenger_margin_end">43</integer>
    <integer name="safe_driver_horizontal_exit_margin_top">0</integer>
    <integer name="safe_driver_horizontal_exit_margin_start">43</integer>
    <integer name="safe_driver_horizontal_exit_margin_bottom">0</integer>
    <integer name="safe_driver_horizontal_exit_margin_end">0</integer>

    <!--fullscreen safe driver -->
    <integer name="safe_driver_linear_layout_sc_margin_bottom">186</integer>


    <integer name="calibrationTitleTextSize">16</integer>
    <integer name="calibrationFailTitleTextSize">19</integer>


</resources>