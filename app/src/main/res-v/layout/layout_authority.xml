<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="360dp"
        android:layout_height="360dp"
        android:layout_marginTop="180dp"
        android:src="@mipmap/link_authentication_prompt_image"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_auth"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:text=""
        android:textColor="#2F323E"
        android:textSize="24sp"
        app:layout_constraintEnd_toEndOf="@+id/imageView"
        app:layout_constraintStart_toStartOf="@+id/imageView"
        app:layout_constraintTop_toBottomOf="@+id/imageView"
        tools:text="激活鉴权错误码：404" />

    <TextView
        android:id="@+id/btn_authconfirm"
        android:background="@drawable/install_jb_bg"
        android:layout_width="400dp"
        android:layout_height="80dp"
        android:layout_marginTop="82dp"
        android:gravity="center"
        android:text="知道了"
        android:textColor="#2F323E"
        android:textSize="24sp"
        app:layout_constraintEnd_toEndOf="@+id/tv_auth"
        app:layout_constraintStart_toStartOf="@+id/tv_auth"
        app:layout_constraintTop_toBottomOf="@+id/tv_auth" />
</androidx.constraintlayout.widget.ConstraintLayout>