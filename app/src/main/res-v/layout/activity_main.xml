<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/relativeLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_lin"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHeight_percent="0.9"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="1.0"
        tools:visibility="visible">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:id="@+id/right_lin"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                app:layout_constraintLeft_toRightOf="@+id/phone_lin">

                <RelativeLayout
                    android:layout_width="1124px"
                    android:layout_height="match_parent">

                    <RelativeLayout
                        android:id="@+id/animation_re"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <LinearLayout
                            android:id="@+id/create_lin"
                            android:paddingTop="84px"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@mipmap/link_shadow_background_image"
                            android:orientation="vertical"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <ViewStub
                                android:id="@+id/vs_auth"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout="@layout/layout_authority" />

                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="226px"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_marginTop="43px"
                                    android:id="@+id/main_one"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/connect_one"
                                    android:textColor="#A5AAC0"
                                    android:textSize="56px"
                                    android:textStyle="bold"
                                    app:layout_constraintLeft_toLeftOf="parent"
                                    app:layout_constraintTop_toTopOf="parent" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/connect_start_connecting"
                                    android:layout_marginTop="66px"
                                    android:textColor="#2F323E"
                                    android:textSize="28px"
                                    android:textStyle="bold"
                                    app:layout_constraintLeft_toRightOf="@+id/main_one"
                                    app:layout_constraintTop_toTopOf="parent" />

                                <TextView
                                    android:id="@+id/main_sec_text"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="7dp"
                                    android:text="@string/updata_open_the_application_on_your_phone_first"
                                    android:textColor="#2F323E"
                                    android:textSize="20px"
                                    app:layout_constraintLeft_toLeftOf="parent"
                                    app:layout_constraintTop_toBottomOf="@+id/main_one" />

                                <ImageView
                                    android:id="@+id/ima_tips"
                                    android:layout_width="219px"
                                    android:layout_height="41px"
                                    android:layout_marginTop="10dp"
                                    android:src="@mipmap/link_application_get_tips"
                                    app:layout_constraintLeft_toLeftOf="parent"
                                    app:layout_constraintTop_toBottomOf="@+id/main_sec_text" />

                                <ImageView
                                    android:layout_width="349px"
                                    android:layout_height="225px"
                                    android:layout_marginLeft="83px"
                                    android:src="@mipmap/link_connection_prompt_1"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintLeft_toRightOf="@+id/ima_tips"
                                    app:layout_constraintTop_toTopOf="parent" />

                            </androidx.constraintlayout.widget.ConstraintLayout>

                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:id="@+id/connectionPrompt2"
                                android:layout_width="wrap_content"
                                android:layout_height="0dp"
                                android:paddingTop="84px"
                                android:layout_marginLeft="226px"
                                android:layout_weight="2"
                                android:orientation="horizontal">

                                <TextView
                                    android:id="@+id/text_second"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/connect_twe"
                                    android:textColor="#A5AAC0"
                                    android:textSize="56px"
                                    android:textStyle="bold"
                                    app:layout_constraintLeft_toLeftOf="parent"
                                    app:layout_constraintTop_toTopOf="parent" />

                                <TextView
                                    android:id="@+id/sc_text"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="31px"
                                    android:text="@string/connect_non_sensory_wired_projection_screen"
                                    android:textColor="#2F323E"
                                    android:textSize="28px"
                                    android:textStyle="bold"
                                    app:layout_constraintLeft_toRightOf="@+id/text_second"
                                    app:layout_constraintTop_toTopOf="parent" />

                                <TextView
                                    android:id="@+id/three_text"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp"
                                    android:text="@string/connect_ensure"
                                    android:textColor="#686874"
                                    android:textSize="20px"
                                    app:layout_constraintLeft_toLeftOf="parent"
                                    app:layout_constraintTop_toBottomOf="@+id/sc_text" />

                                <ImageView
                                    android:id="@+id/wireless_imange"
                                    android:layout_width="320px"
                                    android:layout_height="240px"
                                    android:layout_marginTop="20dp"
                                    android:src="@mipmap/link_wireless_prompt_diagram"
                                    app:layout_constraintLeft_toLeftOf="parent"
                                    app:layout_constraintTop_toBottomOf="@+id/three_text" />

                                <ImageView
                                    android:layout_width="320px"
                                    android:layout_height="240px"
                                    android:layout_marginLeft="10dp"
                                    android:layout_marginTop="20dp"
                                    android:src="@mipmap/link_wired_connection_image"
                                    app:layout_constraintLeft_toRightOf="@+id/wireless_imange"
                                    app:layout_constraintTop_toBottomOf="@+id/three_text" />

                                <ImageView
                                    android:id="@+id/usb_click"
                                    android:layout_width="219px"
                                    android:layout_height="41px"
                                    android:layout_marginTop="13px"
                                    android:src="@mipmap/link_view_interface_location"
                                    app:layout_constraintLeft_toLeftOf="parent"
                                    app:layout_constraintTop_toBottomOf="@+id/wireless_imange" />
                            </androidx.constraintlayout.widget.ConstraintLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/checktheenvironment"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@mipmap/link_shadow_background_image"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:visibility="visible"
                            tools:visibility="gone">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="10dp"
                                android:drawableTop="@mipmap/link_check_the_environment"
                                android:gravity="center"
                                android:text="@string/connect_checking_environment"
                                android:textColor="#686874"
                                android:textSize="24px"></TextView>
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/windows_lin_tisp"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@mipmap/link_shadow_background_image"
                            android:orientation="vertical"
                            android:visibility="gone"
                            android:paddingTop="164px"
                            tools:visibility="gone">

                            <TextView
                                android:id="@+id/windows_lin_text"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center_horizontal"
                                android:text="@string/connect_record_mobile_phone_footage"
                                android:textColor="#2F323E"
                                android:textSize="32px"
                                android:textStyle="bold"></TextView>

                            <TextView
                                android:id="@+id/windows_lin_ftext"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="14px"
                                android:gravity="center_horizontal"
                                android:text="@string/connect_start_screen_mirroring_for_you_immediately"
                                android:textColor="#686874"
                                android:textSize="20px"
                                android:visibility="gone" />

                            <ImageView
                                android:id="@+id/windows_lin_image"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:layout_marginTop="100px"
                                android:src="@mipmap/link_android_authorization_screen_casting_prompt_image"
                                android:textColor="#000"
                                android:textSize="20dp"></ImageView>

                            <TextView
                                android:id="@+id/tv_recording_permission"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="82px"
                                android:gravity="center_horizontal"
                                android:text="@string/connect_start_screen_unauthorized_screen_recording_permission"
                                android:textColor="#222222"
                                android:textSize="28px"
                                android:visibility="invisible" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/ios_webview"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@mipmap/link_shadow_background_image"
                            android:gravity="bottom|center"
                            android:orientation="vertical"
                            android:visibility="gone"
                            tools:visibility="visible">


                            <!--<ImageView
                                android:scaleType="centerCrop"
                                android:adjustViewBounds="true"
                                android:id="@+id/tp_ios"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:src="@mipmap/ios_tp"></ImageView>-->
                            <ScrollView
                                android:id="@+id/scrollView"
                                android:layout_width="match_parent"
                                android:layout_marginTop="48px"
                                android:layout_height="match_parent"
                                android:layout_weight="1"
                                android:scrollbars="none">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:orientation="vertical">

                                    <ImageView
                                        android:id="@+id/tp_ios"
                                        android:layout_width="wrap_content"
                                        android:layout_height="0dp"
                                        android:layout_gravity="center"
                                        android:layout_weight="1"
                                        android:scaleType="centerCrop"
                                        android:src="@mipmap/link_ios_calibration_prompt_long_image"></ImageView>

                                </LinearLayout>
                            </ScrollView>

                            <!-- <com.autoai.wdlink.hu.jswebview.view.ProgressBarWebView
                                 android:layout_width="match_parent"
                                 android:layout_height="0dp"
                                 android:layout_marginTop="24dp"
                                 android:id="@+id/webview"
                                 android:layout_marginLeft="50dp"
                                 android:layout_marginRight="50dp"
                                 android:layout_marginBottom="28dp"
                                 android:layout_weight="1">
                             </com.autoai.wdlink.hu.jswebview.view.ProgressBarWebView>
                            -->
                            <TextView
                                android:id="@+id/close_text"
                                android:layout_width="400px"
                                android:layout_height="wrap_content"
                                android:background="@drawable/close_bg"
                                android:gravity="center"
                                android:paddingLeft="120px"
                                android:paddingTop="28px"
                                android:paddingRight="120px"
                                android:paddingBottom="28px"
                                android:layout_marginBottom="88px"
                                android:layout_marginTop="44px"
                                android:text="@string/connect_close_window"
                                android:textColor="#13224A"
                                android:textSize="24px" />


                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/window_req_record_screen"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@mipmap/link_shadow_background_image"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:visibility="gone"
                            tools:visibility="gone">

                            <com.autoai.wdlink.hu.view.ReqRecordScreenPage
                                android:id="@+id/view_ReqRecordScreenPage"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent" />
                        </LinearLayout>
                    </RelativeLayout>

                    <RelativeLayout
                        android:id="@+id/video_install_layout"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="3"
                        android:visibility="gone">

                        <LinearLayout
                            android:id="@+id/video_lin"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_centerHorizontal="true"
                            android:layout_centerVertical="true"
                            android:background="@mipmap/link_shadow_background_image"
                            android:padding="100dp"
                            android:visibility="gone">

                            <FrameLayout
                                android:id="@+id/textureView_frame"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_marginLeft="60px"
                                android:layout_marginTop="148px"
                                android:layout_marginRight="60px"
                                android:layout_marginBottom="148px">

                                <TextureView
                                    android:id="@+id/guide_textureView"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_gravity="center"
                                    android:visibility="gone" />
                            </FrameLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/install_lin"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@mipmap/link_shadow_background_image"
                            android:orientation="vertical"
                            android:paddingTop="60dp"
                            android:visibility="gone">


                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="0dp"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:orientation="vertical">

                                <ImageView
                                    android:id="@+id/qr_code_image_view"
                                    android:layout_width="420px"
                                    android:layout_height="420px"
                                    android:layout_gravity="center"
                                    android:background="@drawable/btn_click_c"
                                    android:gravity="center" />

                                <!--  <TextView
                                      android:layout_width="match_parent"
                                      android:layout_height="wrap_content"
                                      android:layout_marginTop="20dp"
                                      android:gravity="center"
                                      android:text="扫描下载手机客户端"
                                      android:textColor="#000"
                                      android:textSize="15dp"></TextView>-->

                                <!-- <TextView
                                     android:layout_width="match_parent"
                                     android:layout_height="wrap_content"
                                     android:layout_marginTop="10dp"
                                     android:gravity="center"
                                     android:text="（支持ios、安卓和鸿蒙）"
                                     android:textColor="#000"
                                     android:textSize="10dp"></TextView>-->
                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="86px"
                                    android:gravity="center"
                                    android:text="@string/connect_scan_qr_code"
                                    android:textColor="#2F323E"
                                    android:textSize="24px"
                                    android:textStyle="bold"></TextView>
                            </LinearLayout>


                        </LinearLayout>
                    </RelativeLayout>
                </RelativeLayout>

                <LinearLayout
                    android:id="@+id/vi_lin"
                    android:layout_width="136px"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:layout_marginBottom="48dp"
                    android:orientation="vertical">

                    <LinearLayout
                        android:visibility="gone"
                        android:id="@+id/setting_lin"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@mipmap/link_small_image_with_shadow"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@mipmap/link_guide_video_prompt_image"
                            android:textColor="#4765FF" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/connect_guide_video"
                            android:textColor="#222222"
                            android:textSize="20sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/guide_video_lin"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@mipmap/link_small_image_with_shadow"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:id="@+id/guide_video"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@mipmap/link_guide_video_prompt_image"
                            android:textColor="#4765FF" />

                        <TextView
                            android:id="@+id/video_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/connect_guide_video"
                            android:textColor="#222222"
                            android:textSize="20px"></TextView>
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/install_app_lin"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@mipmap/link_small_image_with_shadow"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:id="@+id/install_app"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@mipmap/link_guide_to_download_app_images"
                            android:textColor="#4765FF"></ImageView>

                        <TextView
                            android:id="@+id/install_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/connect_download_the_application"
                            android:textColor="#222222"
                            android:textSize="20px"></TextView>
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>

            <RelativeLayout
                android:id="@+id/phone_lin"
                android:layout_width="461px"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:background="@drawable/normal"
                android:gravity="center"
                android:orientation="vertical"
                app:layout_constraintLeft_toLeftOf="parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/jb_phone_bg"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="220px"
                        android:background="@mipmap/link_phont_adapter_welink_link"
                        android:gravity="center">

                    </LinearLayout>

                    <ImageView
                        android:id="@+id/image_first"
                        android:layout_width="wrap_content"
                        android:layout_height="0dp"
                        android:layout_weight="3"
                        android:src="@mipmap/link_ic_launcher"
                        android:visibility="gone"
                        tools:ignore="ContentDescription" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_margin="13dp"
                        android:gravity="center_vertical"
                        android:text="@string/connect_introduce"
                        android:textColor="#000"
                        android:textSize="13px"></TextView>


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="12dp"
                        android:orientation="horizontal">

                        <RelativeLayout
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent">

                            <View
                                android:id="@+id/vdview"
                                android:layout_width="@dimen/base_1_dp"
                                android:layout_height="match_parent"
                                android:layout_marginLeft="20dp"
                                android:background="@color/blue"
                                android:visibility="gone"></View>

                            <com.autoai.wdlink.hu.ui.VerticalDashLine
                                android:id="@+id/vdline"
                                android:layout_width="1dp"
                                android:layout_height="match_parent"
                                android:layout_marginLeft="20dp" />

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:orientation="vertical">

                                <ImageView
                                    android:id="@+id/usb_img"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:paddingLeft="8dp"
                                    android:paddingTop="20dp"
                                    android:paddingRight="8dp"
                                    android:paddingBottom="20dp"
                                    android:src="@mipmap/link_blue_dot_circle" />

                                <ImageView
                                    android:id="@+id/wifi_img"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp"
                                    android:paddingLeft="8dp"
                                    android:paddingTop="28dp"
                                    android:paddingRight="8dp"
                                    android:paddingBottom="20dp"
                                    android:src="@mipmap/link_blue_dot_circle" />

                                <ImageView
                                    android:id="@+id/ic_shouquan"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp"
                                    android:paddingLeft="8dp"
                                    android:paddingTop="28dp"
                                    android:paddingRight="8dp"
                                    android:paddingBottom="20dp"
                                    android:src="@mipmap/link_white_dot_circle" />
                            </LinearLayout>


                        </RelativeLayout>

                        <LinearLayout
                            android:id="@+id/connection_method"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:visibility="visible">

                            <LinearLayout
                                android:id="@+id/usb_lin"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="12dp"
                                android:layout_marginRight="12dp"
                                android:background="@drawable/install_jb_bg"
                                android:orientation="horizontal"
                                android:paddingLeft="8dp"
                                android:paddingTop="20dp"
                                android:paddingRight="8dp"
                                android:paddingBottom="20dp">

                                <ImageView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:src="@mipmap/link_usb_prompt"></ImageView>

                                <TextView
                                    android:id="@+id/usb"
                                    android:layout_width="0dp"
                                    android:layout_height="match_parent"
                                    android:layout_marginLeft="20px"
                                    android:layout_weight="1"
                                    android:gravity="center_vertical"
                                    android:text="@string/connect_usbdatacable"
                                    android:textColor="#000"
                                    android:textSize="18px"></TextView>

                                <ImageView
                                    android:id="@+id/usb_image"
                                    android:layout_width="28dp"
                                    android:layout_height="28dp"
                                    android:src="@mipmap/link_loading"></ImageView>

                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/wifi_lin"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="12dp"
                                android:layout_marginTop="16dp"
                                android:layout_marginRight="12dp"
                                android:background="@drawable/install_jb_bg"
                                android:orientation="horizontal"
                                android:paddingLeft="8dp"
                                android:paddingTop="20dp"
                                android:paddingRight="8dp"
                                android:paddingBottom="20dp">

                                <ImageView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:src="@mipmap/link_wireless_map"></ImageView>

                                <TextView
                                    android:id="@+id/wifi"
                                    android:layout_width="0dp"
                                    android:layout_height="match_parent"
                                    android:layout_marginLeft="20px"
                                    android:layout_weight="1"
                                    android:gravity="center_vertical"
                                    android:text="@string/connect_wireless_network"
                                    android:textColor="#000"
                                    android:textSize="18px"></TextView>

                                <ImageView
                                    android:id="@+id/wifi_image"
                                    android:layout_width="28dp"
                                    android:layout_height="28dp"
                                    android:src="@mipmap/link_loading"></ImageView>

                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/start_line"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="12dp"
                                android:layout_marginTop="16dp"
                                android:layout_marginRight="12dp"
                                android:background="@drawable/install_jb_bg"
                                android:orientation="horizontal"
                                android:paddingLeft="8dp"
                                android:paddingTop="20dp"
                                android:paddingRight="8dp"
                                android:paddingBottom="20dp">

                                <ImageView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:src="@mipmap/link_screen_casting_authorization_diagram"></ImageView>

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="match_parent"
                                    android:layout_marginLeft="20px"
                                    android:layout_weight="1"
                                    android:gravity="center_vertical"
                                    android:text="@string/connect_screen_casting_authorization"
                                    android:textColor="#000"
                                    android:textSize="18px" />

                                <ImageView
                                    android:id="@+id/start_image"
                                    android:layout_width="28dp"
                                    android:layout_height="28dp"
                                    android:src="@mipmap/link_loading" />

                            </LinearLayout>
                        </LinearLayout>
                    </LinearLayout>


                    <LinearLayout
                        android:id="@+id/loading_lin"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        android:gravity="bottom|right"
                        android:orientation="vertical"
                        android:visibility="visible">

                        <ImageView
                            android:id="@+id/loading"
                            android:layout_width="60dp"
                            android:layout_height="60dp"
                            android:alpha="0.01"
                            android:src="@mipmap/link_connection_successful" />

                        <TextView
                            android:id="@+id/loading_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:text="@string/connect_waiting_for_connection"
                            android:textColor="#000"
                            android:visibility="gone" />
                    </LinearLayout>
                </LinearLayout>

                <RelativeLayout
                    android:id="@+id/calibration_lin"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <ImageView
                        android:id="@+id/bg_img"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/jb_calibration_phone_bg"
                        android:gravity="center" />

                    <RelativeLayout
                        android:id="@+id/calibration_related"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerHorizontal="true"
                        android:layout_marginTop="156px"
                        android:gravity="center">

                        <ImageView
                            android:id="@+id/calibrationing_img"
                            android:layout_width="290px"
                            android:layout_height="384px"
                            android:visibility="gone" />

                        <ImageView
                            android:id="@+id/scanning_effect"
                            android:layout_width="268px"
                            android:layout_height="268px"
                            android:layout_centerInParent="true"
                            android:gravity="center"
                            android:visibility="gone" />

                        <ImageView
                            android:id="@+id/calibration_failed_img"
                            android:layout_width="268px"
                            android:layout_height="268px"
                            android:layout_centerInParent="true"
                            android:gravity="center"
                            android:visibility="gone" />
                    </RelativeLayout>
                    <!--<TextView
                        android:textStyle="bold"
                        tools:visibility="visible"
                        android:visibility="gone"
                        android:id="@+id/calibration_countdown"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginLeft="38px"
                        android:layout_marginRight="38px"
                        android:layout_marginTop="180px"
                        android:gravity="center"
                        android:text="5"
                        android:textColor="#17417C"
                        android:textSize="48px" />-->
                    <TextView
                        android:id="@+id/calibration_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/calibration_related"
                        android:layout_gravity="center"
                        android:layout_marginLeft="38px"
                        android:layout_marginTop="36px"
                        android:layout_marginRight="38px"
                        android:gravity="center"
                        android:text="校准失败"
                        android:textColor="#17417C"
                        android:textSize="28px" />

                    <TextView
                        android:id="@+id/calibration_content"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/calibration_related"
                        android:layout_marginLeft="60px"
                        android:layout_marginTop="96px"
                        android:layout_marginRight="60px"
                        android:gravity="center"
                        android:text="校准过程中请勿触碰手机 校准失败可能会导"
                        android:textColor="#17417C"
                        android:textSize="18px"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/recalibrate"
                        android:layout_width="300px"
                        android:layout_height="72dp"
                        android:layout_below="@+id/calibration_related"
                        android:layout_centerHorizontal="true"
                        android:layout_gravity="center"
                        android:layout_marginTop="195px"
                        android:background="@drawable/global_selector_btn_bg_normal"
                        android:clickable="true"
                        android:gravity="center"
                        android:text="重新校准"
                        android:textColor="#FFFFFF"
                        android:textSize="24px"
                        android:visibility="gone"
                        tools:visibility="visible" />


                </RelativeLayout>


            </RelativeLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/banner_lin"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:layout_gravity="center"
        android:layout_marginLeft="96dp"
        android:layout_marginTop="54dp"
        android:layout_marginRight="96dp"
        android:layout_marginBottom="54dp"
        android:background="@drawable/jb_bg"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/close_icon"
            android:layout_width="48px"
            android:layout_height="48px"
            android:layout_gravity="left"
            android:layout_marginLeft="36px"
            android:layout_marginTop="36px"
            android:src="@mipmap/link_full_close"></ImageView>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="-24px"
            android:gravity="center">

            <com.youth.banner.Banner
                android:id="@+id/banner"
                android:layout_width="1116px"
                android:layout_height="628px"
                android:layout_gravity="center"
                app:image_scale_type="fit_center" />
        </LinearLayout>
    </LinearLayout>
    <View
        android:id="@+id/view_dev"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:background="#00CCCCCC"
        app:layout_constraintBottom_toBottomOf="@+id/relativeLayout"
        app:layout_constraintEnd_toEndOf="@+id/relativeLayout"
        tools:ignore="MissingConstraints" />

    <FrameLayout
        android:background="#fff"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
       android:id="@+id/setting_frameLayout"
        />
</androidx.constraintlayout.widget.ConstraintLayout>