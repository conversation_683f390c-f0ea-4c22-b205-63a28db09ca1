<?xml version="1.0" encoding="utf-8"?>
<resources>
    <integer name="toast_margin_top">200</integer>
    <integer name="toast_margin_start">102</integer>
    <integer name="toast_width">462</integer>

    <integer name="safe_driver_vertical_passenger_margin_top">16</integer>
    <integer name="safe_driver_vertical_passenger_margin_start">37</integer>
    <integer name="safe_driver_vertical_passenger_margin_bottom">0</integer>
    <integer name="safe_driver_vertical_passenger_margin_end">37</integer>
    <integer name="safe_driver_vertical_exit_margin_top">0</integer>
    <integer name="safe_driver_vertical_exit_margin_start">37</integer>
    <integer name="safe_driver_vertical_exit_margin_bottom">0</integer>
    <integer name="safe_driver_vertical_exit_margin_end">37</integer>

    <integer name="safe_driver_horizontal_passenger_margin_top">0</integer>
    <integer name="safe_driver_horizontal_passenger_margin_start">16</integer>
    <integer name="safe_driver_horizontal_passenger_margin_bottom">0</integer>
    <integer name="safe_driver_horizontal_passenger_margin_end">0</integer>
    <integer name="safe_driver_horizontal_exit_margin_top">0</integer>
    <integer name="safe_driver_horizontal_exit_margin_start">0</integer>
    <integer name="safe_driver_horizontal_exit_margin_bottom">0</integer>
    <integer name="safe_driver_horizontal_exit_margin_end">0</integer>

    <!--fullscreen safe driver -->
    <integer name="safe_driver_linear_layout_sc_margin_bottom">327</integer>


    <integer name="calibrationTitleTextSize">24</integer>
    <integer name="calibrationFailTitleTextSize">28</integer>
</resources>