<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/sc_safe_tip"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/jb_safe_driver_bg"
    android:visibility="gone"
    tools:visibility="gone">

    <TextView
        android:id="@+id/tv_safe_text"
        android:layout_width="320dp"
        android:layout_height="96dp"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:background="@drawable/jb_bg_red"
        android:gravity="center"
        android:text="@string/safe_viewing"
        android:textColor="@color/white_FFFFFF"
        android:textSize="24sp" />

    <TextView
        android:id="@+id/tv_return_to_homepage"
        android:layout_width="300dp"
        android:layout_height="72dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="38dp"
        android:background="@drawable/global_selector_btn_bg_go_home"
        android:clickable="true"
        android:gravity="center"
        android:visibility="gone"
        android:text="@string/return_to_homepage"
        android:textColor="@color/blue_0071FF"
        android:textSize="24sp" />
</RelativeLayout>