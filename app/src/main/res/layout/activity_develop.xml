<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/rl"
    android:background="#ccffffff">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center" >

        <LinearLayout android:id="@+id/ll_authenticate2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="20dp">

            <TextView
                android:layout_width="240dp"
                android:layout_height="wrap_content"
                android:text="行车安全1.2"
                android:textColor="#646464"
                android:textSize="20sp" />

            <Switch
                android:id="@+id/switch_authenticate_version"
                android:layout_width="140dp"
                android:layout_height="20dp"
                android:layout_marginStart="100dp"
                android:checked="false"
                android:thumb="@drawable/switch_develop_thumb"
                android:track="@drawable/switch_develop_track_selector"
                tools:ignore="UseSwitchCompatOrMaterialXml" />

            <TextView
                android:id="@+id/tv_authenticate2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:text="开"
                android:textColor="#646464"
                android:textSize="20sp" />

        </LinearLayout>

        <LinearLayout android:id="@+id/ll_authenticate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="20dp"
            android:layout_below="@+id/ll_authenticate2">

            <TextView
                android:layout_width="240dp"
                android:layout_height="wrap_content"
                android:text="自动录屏授权"
                android:textColor="#646464"
                android:textSize="20sp" />

            <Switch
                android:id="@+id/switch_authenticate"
                android:layout_width="140dp"
                android:layout_height="20dp"
                android:layout_marginStart="100dp"
                android:checked="true"
                android:thumb="@drawable/switch_develop_thumb"
                android:track="@drawable/switch_develop_track_selector"
                tools:ignore="UseSwitchCompatOrMaterialXml" />

            <TextView
                android:id="@+id/tv_authenticate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:text="开"
                android:textColor="#646464"
                android:textSize="20sp" />

        </LinearLayout>

        <LinearLayout android:id="@+id/ll_driving_safety"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:paddingBottom="10dp"
            android:layout_below="@+id/ll_authenticate" >

            <TextView
                android:id="@+id/tv_driving_safety"
                android:layout_width="240dp"
                android:layout_height="wrap_content"
                android:text="行车安全走行规制"
                android:textColor="#646464"
                android:textSize="20sp" />

            <Switch
                android:id="@+id/switch_driving_safety"
                android:layout_width="140dp"
                android:layout_height="20dp"
                android:layout_marginStart="100dp"
                android:checked="true"
                android:thumb="@drawable/switch_develop_thumb"
                android:track="@drawable/switch_develop_track_selector"
                tools:ignore="UseSwitchCompatOrMaterialXml" />

            <TextView
                android:id="@+id/tv_driving_safety_all"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:text="全部打开"
                android:textColor="#646464"
                android:textSize="20sp" />

        </LinearLayout>

        <RelativeLayout
            android:id="@+id/rl_driving_safety_item"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingStart="40dp"
            android:paddingTop="10dp"
            android:paddingEnd="20dp"
            android:paddingBottom="20dp"
            android:visibility="visible"
            android:layout_below="@+id/ll_driving_safety" >

            <Switch
                android:id="@+id/switch_driving_safety_online"
                android:layout_width="140dp"
                android:layout_height="20dp"
                android:layout_marginStart="100dp"
                android:layout_toEndOf="@+id/tv_driving_safety_online"
                android:checked="true"
                android:thumb="@drawable/switch_develop_thumb"
                android:track="@drawable/switch_develop_track_selector"
                tools:ignore="UseSwitchCompatOrMaterialXml" />

            <Switch
                android:id="@+id/switch_driving_safety_offline"
                android:layout_width="140dp"
                android:layout_height="40dp"
                android:layout_below="@+id/tv_driving_safety_online"
                android:layout_alignTop="@+id/tv_driving_safety_offline"
                android:layout_marginStart="100dp"
                android:layout_toEndOf="@+id/tv_driving_safety_online"
                android:checked="true"
                android:thumb="@drawable/switch_develop_thumb"
                android:track="@drawable/switch_develop_track_selector"
                tools:ignore="UseSwitchCompatOrMaterialXml" />

            <TextView
                android:id="@+id/tv_driving_safety_online_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_toEndOf="@+id/switch_driving_safety_online"
                android:text="开"
                android:textColor="#646464"
                android:textSize="20sp" />

            <TextView
                android:id="@+id/tv_driving_safety_offline"
                android:layout_width="220dp"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_driving_safety_online"
                android:layout_marginTop="20dp"
                android:text="离线识别"
                android:textColor="#646464"
                android:textSize="20sp" />

            <TextView
                android:id="@+id/tv_driving_safety_online"
                android:layout_width="220dp"
                android:layout_height="wrap_content"
                android:text="在线识别"
                android:textColor="#646464"
                android:textSize="20sp" />

            <TextView
                android:id="@+id/tv_driving_safety_offline_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_driving_safety_online"
                android:layout_alignTop="@+id/tv_driving_safety_offline"
                android:layout_marginStart="20dp"
                android:layout_toEndOf="@+id/switch_driving_safety_online"
                android:text="开"
                android:textColor="#646464"
                android:textSize="20sp" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_log"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:paddingBottom="20dp"
            android:visibility="visible"
            android:gravity="center"
            android:layout_below="@+id/rl_driving_safety_item" >

            <Switch
                android:id="@+id/switch_log"
                android:layout_width="140dp"
                android:layout_height="20dp"
                android:layout_marginStart="100dp"
                android:layout_toEndOf="@+id/tv_log"
                android:checked="true"
                android:thumb="@drawable/switch_develop_thumb"
                android:track="@drawable/switch_develop_track_selector"
                tools:ignore="UseSwitchCompatOrMaterialXml" />

            <TextView
                android:id="@+id/tv_log_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_toEndOf="@+id/switch_log"
                android:text="开"
                android:textColor="#646464"
                android:textSize="20sp" />

            <TextView
                android:id="@+id/tv_log"
                android:layout_width="240dp"
                android:layout_height="wrap_content"
                android:text="log日志状态"
                android:textColor="#646464"
                android:textSize="20sp" />
            <RadioGroup
                android:id="@+id/rg_log"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_log"
                android:paddingTop="20dp"
                android:orientation="horizontal"
                android:visibility="visible">
                <RadioButton android:id="@+id/rb_log_v"
                    android:layout_width="wrap_content"
                    android:layout_height="30dp"
                    android:text=" log等级 v   "
                    android:textColor="#646464"
                    android:textSize="20sp" />
                <RadioButton android:id="@+id/rb_log_d"
                    android:layout_width="wrap_content"
                    android:layout_height="30dp"
                    android:text=" log等级 d   "
                    android:textColor="#646464"
                    android:textSize="20sp" />
                <RadioButton android:id="@+id/rb_log_i"
                    android:layout_width="wrap_content"
                    android:layout_height="30dp"
                    android:text=" log等级 i   "
                    android:textColor="#646464"
                    android:textSize="20sp" />
                <RadioButton android:id="@+id/rb_log_w"
                    android:layout_width="wrap_content"
                    android:layout_height="30dp"
                    android:text=" log等级 w   "
                    android:textColor="#646464"
                    android:textSize="20sp" />
                <RadioButton android:id="@+id/rb_log_e"
                    android:layout_width="wrap_content"
                    android:layout_height="30dp"
                    android:text=" log等级 e   "
                    android:textColor="#646464"
                    android:textSize="20sp" />
            </RadioGroup>
        </RelativeLayout>
        <RelativeLayout
            android:id="@+id/re_rtt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:paddingBottom="20dp"
            android:visibility="visible"
            android:gravity="center"
            android:layout_below="@+id/re_fps" >

            <Switch
                android:id="@+id/switch_rtt"
                android:layout_width="140dp"
                android:layout_height="20dp"
                android:layout_marginStart="100dp"
                android:layout_toEndOf="@+id/tv_rtt"
                android:checked="true"
                android:thumb="@drawable/switch_develop_thumb"
                android:track="@drawable/switch_develop_track_selector"
                tools:ignore="UseSwitchCompatOrMaterialXml" />

            <TextView
                android:id="@+id/tv_rtt_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_toEndOf="@+id/switch_rtt"
                android:text="开"
                android:textColor="#646464"
                android:textSize="20sp" />

            <TextView
                android:id="@+id/tv_rtt"
                android:layout_width="240dp"
                android:layout_height="wrap_content"
                android:text="RTT开关"
                android:textColor="#646464"
                android:textSize="20sp" />
        </RelativeLayout>
        <RelativeLayout
            android:id="@+id/re_fps"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:paddingBottom="20dp"
            android:visibility="visible"
            android:gravity="center"
            android:layout_below="@+id/rl_log" >

            <Switch
                android:id="@+id/switch_fps"
                android:layout_width="140dp"
                android:layout_height="20dp"
                android:layout_marginStart="100dp"
                android:layout_toEndOf="@+id/tv_fps"
                android:checked="true"
                android:thumb="@drawable/switch_develop_thumb"
                android:track="@drawable/switch_develop_track_selector"
                tools:ignore="UseSwitchCompatOrMaterialXml" />

            <TextView
                android:id="@+id/tv_fps_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_toEndOf="@+id/switch_fps"
                android:text="开"
                android:textColor="#646464"
                android:textSize="20sp" />

            <TextView
                android:id="@+id/tv_fps"
                android:layout_width="240dp"
                android:layout_height="wrap_content"
                android:text="Fps开关"
                android:textColor="#646464"
                android:textSize="20sp" />
        </RelativeLayout>
    </RelativeLayout>

    <View
        android:id="@+id/view_back"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_alignParentStart="true"
        android:layout_alignParentTop="true"
        android:layout_marginStart="20dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="20dp"
        android:background="@drawable/ic_back" />


    <TextView
        android:id="@+id/app_version"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_alignParentEnd="true"
        android:textColor="#000"
        android:gravity="center"
        android:textSize="15dp"
        android:text="123"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="20dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="20dp"
        />
</RelativeLayout>