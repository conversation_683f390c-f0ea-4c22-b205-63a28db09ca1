<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_width="100dp"
        android:layout_height="300dp"
        android:orientation="vertical">

        <Button
            android:id="@+id/tab1"
            android:text="Tab 1"
            android:layout_width="match_parent"
            android:layout_weight="1"
            android:textColor="#000"
            style="@style/TabButtonStyle"
            android:layout_height="0dp" />

        <Button
            android:id="@+id/tab2"
            android:text="Tab 2"
            android:textColor="#000"
            style="@style/TabButtonStyle"
            android:layout_width="match_parent"
            android:layout_weight="1"
            android:layout_height="0dp" />

        <Button
            android:id="@+id/tab3"
            android:textColor="#000"
            android:text="Tab 3"
            style="@style/TabButtonStyle"
            android:layout_width="match_parent"
            android:layout_weight="1"
            android:layout_height="0dp" />
    </LinearLayout>
    <ScrollView
        android:id="@+id/scrollView1"
        android:layout_width="match_parent"
        android:scrollbarSize="8dp"
        android:scrollbarThumbVertical="@drawable/settings_scrollbar"
        android:layout_height="match_parent">

        <LinearLayout
            android:id="@+id/container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
<!--                        <include layout="@layout/section1"></include>-->
<!--                        <include layout="@layout/section2"></include>-->
<!--                        <include layout="@layout/section3"></include>-->
            <!-- 子布局将在这里动态加载 -->
            <!-- 例如： / -->

        </LinearLayout>
    </ScrollView>
</LinearLayout>
