<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_gravity="center"
    android:background="@android:color/transparent"
    android:orientation="horizontal"
    android:id="@+id/vp_framelayout">

    <!-- 顶部拓展功能条 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/console"
        android:layout_marginLeft="104px"
        android:layout_marginRight="104px"
        android:layout_marginTop="45px"
        android:layout_width="match_parent"
        android:layout_height="100px"
        android:gravity="center_horizontal"
        android:background="@drawable/btn_click_f"
        android:orientation="horizontal">
        <ImageButton
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            android:id="@+id/mini"
            android:layout_marginLeft="18px"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:background="@null"
            android:paddingLeft="20dp"
            android:paddingRight="40dp"
            android:src="@drawable/mini_button_selector" />


        <ImageButton
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:id="@+id/move"
            android:layout_width="wrap_content"
            android:background="@null"
            android:layout_weight="1"
            android:paddingLeft="70dp"
            android:paddingRight="70dp"
            android:layout_height="40dp"
            android:src="@drawable/move_button_selector"
            />
        <ImageButton
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:id="@+id/fullscreen"
            android:background="@null"
            android:paddingLeft="40dp"
            android:paddingRight="20dp"
            android:layout_marginRight="18px"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:src="@drawable/fullscreen_button_selector" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 视频操作区域 -->
    <FrameLayout
        android:id="@+id/sv_framelayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center">

        <com.autoai.wdlink.hu.module.hidscreen.InterceptFrameLayout
            android:id="@+id/sv_framelayout_inner"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextureView
                android:id="@+id/phoneData_sv"
                android:layout_width="360dp"
                android:layout_height="360dp"
                android:layout_gravity="center" />
        </com.autoai.wdlink.hu.module.hidscreen.InterceptFrameLayout>

        <!-- 锁屏提示UI -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/sc_on_off_tip"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#EBEBEF"
            android:visibility="gone"
            tools:layout_height="812dp"
            tools:layout_width="375dp"
            tools:visibility="visible">


        <TextView
                android:id="@+id/screen_off_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/pop_window_phone_screen_off"
                android:textColor="#ff2f323e"
                android:textSize="32sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toTopOf="@+id/screen_off_reason"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.3"
                app:layout_constraintVertical_chainStyle="packed" />

            <TextView
                android:id="@+id/screen_off_reason"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="35dp"
                android:layout_marginTop="40dp"
                android:layout_marginEnd="35dp"
                android:gravity="center"
                android:text="@string/pop_window_unlock_continue_cast"
                android:textColor="#ff2f323e"
                android:textSize="24sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/screen_off_title" />

            <TextView
                android:id="@+id/recovery_cast"
                android:layout_width="300dp"
                android:layout_height="72dp"
                android:layout_marginBottom="12dp"
                android:background="@drawable/global_selector_btn_bg_normal"
                android:clickable="true"
                android:gravity="center"
                android:text="@string/pop_window_recovery_cast"
                android:textColor="#0071FF"
                android:textSize="24sp"
                app:layout_constraintBottom_toTopOf="@+id/btn_exit"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:id="@+id/btn_exit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="38dp"
                android:clickable="true"
                android:gravity="center"
                android:paddingStart="50dp"
                android:paddingTop="10dp"
                android:paddingEnd="50dp"
                android:paddingBottom="10dp"
                android:text="@string/exit_app"
                android:textColor="#ffed0000"
                android:textSize="24sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- 行车安全提示 -->
        <RelativeLayout
            android:visibility="gone"
            tools:visibility="gone"
            android:id="@+id/sc_safe_tip"
            android:background="@color/black_80"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:textColor="@color/white_FFFFFF"
                android:textSize="24px"
                android:background="@drawable/jb_bg_red"
                android:layout_centerVertical="true"
                android:layout_centerHorizontal="true"
                android:gravity="center"
                android:id="@+id/tv_safe_text"
                android:text="@string/safe_viewing"
                android:layout_width="320px"
                android:layout_height="96px" />

           <!-- <TextView
                android:clickable="true"
                android:layout_width="300px"
                android:layout_height="72px"
                android:layout_marginBottom="38px"
                android:layout_centerHorizontal="true"
                android:layout_alignParentBottom="true"
                android:background="@drawable/global_selector_btn_bg_go_home"
                android:gravity="center"
                android:visibility="gone"
                android:id="@+id/tv_return_to_homepage"
                android:text="@string/return_to_homepage"
                android:textColor="@color/blue_0071FF"
                android:textSize="24px" />-->
        </RelativeLayout>
        <FrameLayout
            android:id="@+id/dialog_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            tools:visibility="gone"/>

    </FrameLayout>

    <!-- 右上角关闭投影，隐藏的测试功能 -->
    <!--<ImageView
        android:id="@+id/close"
        android:layout_width="45dp"
        android:layout_height="45dp"
        android:layout_gravity="right|top"
        android:layout_marginTop="38dp"
        android:visibility="gone"
        android:layout_marginRight="38dp"
        android:src="@mipmap/a7_btn_close_p"
        android:text="close" />-->

    <!-- 右下角缩放功能 -->
    <Button
        android:id="@+id/resize"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_gravity="bottom|right"
        android:layout_marginRight="88dp"
        android:layout_marginBottom="88dp"
        android:background="@drawable/btn_resize_move_bg"
        android:visibility="visible"
        tools:visibility="visible"
        android:text="resize"/>

    <!-- 时间 隐藏的测试功能-->
    <TextView
        android:layout_width="200dp"
        android:background="#f00"
        tools:visibility="gone"
        android:visibility="gone"
        android:layout_height="wrap_content"
        android:id="@+id/tv_time" />

</FrameLayout>