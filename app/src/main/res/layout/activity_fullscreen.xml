<?xml version="1.0" encoding="utf-8"?>
<com.autoai.wdlink.hu.module.hidscreen.FullScreenRootView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black"
    tools:context=".castwindow.view.FullscreenActivity">


    <com.autoai.wdlink.hu.module.hidscreen.InterceptFrameLayout
        android:id="@+id/hid_touch_screen_parent_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="88dp"
        android:paddingEnd="88dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" >

        <com.autoai.wdlink.hu.module.hidscreen.InterceptFrameLayout
            android:id="@+id/hid_touch_screen_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center" />

    </com.autoai.wdlink.hu.module.hidscreen.InterceptFrameLayout>

    <include layout="@layout/safe_driver_old"/>
    <include layout="@layout/safe_driver"/>

    <!--<ImageView
        android:visibility="gone"
        android:id="@+id/control_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="10dp"
        android:src="@drawable/btn_move_bg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />-->

    <View
        android:layout_width="match_parent"
        android:id="@+id/fullview"
        android:layout_height="match_parent">
    </View>
    <ImageView
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginStart="40dp"
        android:layout_marginTop="40dp"
        android:id="@+id/closeFullScreen"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:src="@mipmap/link_full_close"/>

    <ImageView
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginEnd="40dp"
        android:layout_marginTop="40dp"
        android:id="@+id/setting"
        android:layout_width="48dp"
        android:visibility="gone"
        android:layout_height="48dp"
        android:src="@mipmap/link_full_close"/>


    <FrameLayout
        android:background="#fff"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:id="@+id/setting_frameLayout"
        />
</com.autoai.wdlink.hu.module.hidscreen.FullScreenRootView>