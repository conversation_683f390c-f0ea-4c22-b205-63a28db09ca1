<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="text_color">#FFFFFF</color>
    <color name="text_can_click">#1DB8D5</color>
    <color name="text_i_know">#FFFFFF</color>
    <color name="help_right_bg">#00000000</color>
    <color name="blue">#4765FF</color>
    <color name="blue_0071FF">#0071FF</color>
    <color name="white_FFFFFF">#FFFFFF</color>
    <color name="black">#222222</color>
    <color name="black_80">#E6000000</color>


    <!-- 主界面色彩 -->
    <color name="background">#F3F4F6</color>
    <color name="onBackground">#242424</color>
    <color name="onBackgroundSecond">#636564</color>
    <color name="cardBackground">#FFFFFF</color>
    <color name="onCardBackground">#000000</color>
    <color name="onCardBackgroundSecond">#696969</color>
    <color name="onCardDivider">#32000000</color>

    <!-- 特殊色彩 -->
    <color name="button">#2E2E2E</color>
    <color name="onButton">#E0E0E0</color>
    <color name="alert">#ca5357</color>
    <color name="onAlert">#ded8d8</color>
    <color name="translucent">#000000ff</color>

    <!-- 客户端界面色彩 -->
    <color name="clientBar">#579CF9</color>
    <color name="clientBarSecond">#c0d1d1d1</color>

    <!-- 迷你界面色彩 -->
    <color name="bar1">#e8b621</color>
    <color name="bar2">#3a7f9e</color>
    <color name="bar3">#6eaa84</color>
    <color name="bar4">#0A95FF</color>
</resources>