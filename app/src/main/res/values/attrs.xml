<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="ScalableTextureView">
        <attr name="scalableType">
            <enum name="none" value="0" />

            <enum name="fitXY" value="1" />
            <enum name="fitStart" value="2" />
            <enum name="fitCenter" value="3" />
            <enum name="fitEnd" value="4" />

            <enum name="leftTop" value="5" />
            <enum name="leftCenter" value="6" />
            <enum name="leftBottom" value="7" />
            <enum name="centerTop" value="8" />
            <enum name="center" value="9" />
            <enum name="centerBottom" value="10" />
            <enum name="rightTop" value="11" />
            <enum name="rightCenter" value="12" />
            <enum name="rightBottom" value="13" />

            <enum name="leftTopCrop" value="14" />
            <enum name="leftCenterCrop" value="15" />
            <enum name="leftBottomCrop" value="16" />
            <enum name="centerTopCrop" value="17" />
            <enum name="centerCrop" value="18" />
            <enum name="centerBottomCrop" value="19" />
            <enum name="rightTopCrop" value="20" />
            <enum name="rightCenterCrop" value="21" />
            <enum name="rightBottomCrop" value="22" />

            <enum name="startInside" value="23" />
            <enum name="centerInside" value="24" />
            <enum name="endInside" value="25" />
        </attr>
    </declare-styleable>

    <!-- progress涉及的style -->
    <declare-styleable name="NumberProgressBar">
        <attr name="progress" format="integer" />
        <attr name="max" format="integer" />
        <attr name="progress_unreached_color" format="color" />
        <attr name="progress_reached_color" format="color" />
        <attr name="progress_reached_bar_height" format="dimension" />
        <attr name="progress_unreached_bar_height" format="dimension" />
        <attr name="progress_text_size" format="dimension" />
        <attr name="progress_text_color" format="color" />
        <attr name="progress_text_offset" format="dimension" />
        <attr name="progress_text_visibility" format="enum">
            <enum name="visible" value="0" />
            <enum name="invisible" value="1" />
        </attr>
    </declare-styleable>

        <declare-styleable name="DashView">
            <attr name="dashWidth" format="dimension"/>
            <attr name="lineWidth" format="dimension"/>
            <attr name="lineHeight" format="dimension"/>
            <attr name="lineColor" format="color"/>
            <attr name="dashOrientation" format="integer"/>
        </declare-styleable>

    <!--注意这里的name要和自定义View的名称一致，不然在xml布局中无法引用-->
    <declare-styleable name="HorizontalExpandMenu">
        <attr name="back_color" format="color"></attr>
        <attr name="stroke_size" format="dimension"></attr>
        <attr name="stroke_color" format="color"></attr>
        <attr name="corner_radius" format="dimension"></attr>
    </declare-styleable>
</resources>