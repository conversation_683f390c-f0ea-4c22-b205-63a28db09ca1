<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android" >
    <item>
        <shape>
            <solid android:color="#CC303030" />
            <corners
                android:topRightRadius="16px"
                android:topLeftRadius="16px" />
        </shape>
    </item>
    <!--这里的right和bottom表示的是这一层图片的右边距和下边距，当然还有left和top-->
    <item android:right="0dp" android:bottom="0dp" android:top="0dp">
        <shape>
            <solid android:color="#CC303030" />
            <corners
                android:topRightRadius="16px"
                android:topLeftRadius="16px"  />
        </shape>
    </item>
</layer-list>