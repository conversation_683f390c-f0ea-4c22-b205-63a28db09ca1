package com.autoai.car.os_adapter.interfaces

import android.content.Context

/**
 * car os 信息获取通用接口
 */
interface CarInfoInterface {


    /**
     * 获取当前行车档位
     */
    fun init(context: Context) {
    }


    /**
     * 获取当前行车行驶状态
     */
    fun getCurDriveState(): Int {
        return -1
    }


    /**
     * 获取当前行车档位
     */
    fun getCurGearValue(): Int {
        return -1
    }

    /**
     * fixme:非通用逻辑，后续从通用接口移除
     * she tips: LinkHost.checkIsPlayVideo();的结果会触发onCheckIsPlayVideoResult
     *
     * @param isPlay
     */
    fun onCheckIsPlayVideoResult(isPlay:Int){

    }

    /**
     * 获取当前行车档位
     */
    fun addVehicleGearChangeListener(driveStateChangedListener: DriveStateChangedListener) {
    }


    /**
     * 移除档位监听
     */
    fun removeVehicleGearChangeListener(driveStateChangedListener: DriveStateChangedListener) {
    }


    interface DriveStateChangedListener {
        /**
         * 档位变化回调
         * @param gear 当前档位状态 [android.car.drivingstate.Gear]
         */
        fun onDriveStateChanged(gear: Int)
    }

    companion object {
        /**
         * 行车
         */
        const val DRIVE_STATE_ON: Int = 1

        /**
         * 停车
         */
        const val DRIVE_STATE_OFF: Int = 2


        /**
         * 行车播放视频
         */
        const val DRIVE_STATE_VIDEO: Int = 1

    }
}