package com.autoai.car.os_adapter.proxy

import android.content.Context
import android.util.Log
import com.autoai.car.os_adapter.imp.CarInfoImp
import com.autoai.car.os_adapter.interfaces.CarInfoInterface


object CarInfoProxy : CarInfoInterface {



    /**
     * 获取当前行车档位
     */
    override fun init(context: Context) {
        CarInfoImp.getInstance().init(context)
    }


    /**
     * 获取当前行车档位
     */
    override fun getCurDriveState(): Int {
        return CarInfoImp.getInstance().getCurDriveState()
    }

    /**
     * 获取当前行车档位
     */
    override fun getCurGearValue(): Int {
        return CarInfoImp.getInstance().getCurGearValue()
    }

    override fun onCheckIsPlayVideoResult(isPlay:Int){
        return CarInfoImp.getInstance().onCheckIsPlayVideoResult(isPlay)
    }

    /**
     * 获取当前行车档位
     */
    override fun addVehicleGearChangeListener(driveStateChangedListener: CarInfoInterface.DriveStateChangedListener) {
        CarInfoImp.getInstance()
            .addVehicleGearChangeListener(driveStateChangedListener)
    }


    /**
     * 移除档位监听
     */
    override fun removeVehicleGearChangeListener(driveStateChangedListener: CarInfoInterface.DriveStateChangedListener) {
        CarInfoImp.getInstance()
            .removeVehicleGearChangeListener(driveStateChangedListener)
    }
}
