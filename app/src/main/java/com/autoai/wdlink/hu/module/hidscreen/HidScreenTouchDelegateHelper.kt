package com.autoai.wdlink.hu.module.hidscreen

import android.annotation.SuppressLint
import android.graphics.Rect
import android.util.Log
import android.view.View
import com.autoai.wdlink.hu.module.hidscreen.HidScreenOutsideTouchDelegate.Companion.setTouchDelegateWhenReady



class HidScreenTouchDelegateHelper {
    companion object{
        @SuppressLint("ClickableViewAccessibility")
        @JvmStatic
        @JvmOverloads
        fun setHidScreenTouchDelegate(hidScreenView: InterceptFrameLayout, outsideInsert: Int,outsideTopInsert: Int, outsideBottomInsert: Int, isFullScreen: Boolean = false, touchEnableCallback: (() -> Boolean)? = null){
            val callback = hidScreenDeviceEnableCallback(touchEnableCallback)
            val insets = Rect(-outsideInsert, - outsideTopInsert, -outsideInsert, -outsideBottomInsert)

            hidScreenView.setOnInterceptHandleCallback(callback)
            hidScreenView.setOnTouchListener(HidScreenTouchListener())

            if(!isFullScreen){
//                insets.top = 0
                (hidScreenView.parent?.parent?.parent as? View)?.let { parent ->
                    setTouchDelegateWhenReady(hidScreenView, parent, insets, isFullScreen, callback)
                }
            } else {
//                insets.top = 0
//                insets.bottom = 0
                (hidScreenView.parent as? View)?.let { parent ->
                    Log.i("TouchDelegate", "HidScreenTouchDelegateHelper setTouchDelegateWhenReady")
                    setTouchDelegateWhenReady(hidScreenView, parent, insets, isFullScreen, callback)
                }
            }
        }

        fun hidScreenDeviceEnableCallback(touchEnableCallback: (() -> Boolean)? = null): () -> Boolean = {
//            CommonData.iCurrentConnectType != CommonData.DEVICE_TYPE_AOA
 //                   && HidScreenConnectManager.getInstance().isConnected
//                    &&
                    touchEnableCallback?.invoke() ?: true
        }
    }
}