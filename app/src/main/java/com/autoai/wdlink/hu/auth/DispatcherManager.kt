package com.autoai.wdlink.hu.auth

import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.ExecutorCoroutineDispatcher
import kotlinx.coroutines.asCoroutineDispatcher
import java.util.concurrent.Executors

object DispatcherManager {
    private val FIX_DISPATCHER: ExecutorCoroutineDispatcher by lazy {
        Executors.newFixedThreadPool(8).asCoroutineDispatcher()
    }

    public fun getFixThreadDispatcher():CoroutineDispatcher {
        return FIX_DISPATCHER
    }
}