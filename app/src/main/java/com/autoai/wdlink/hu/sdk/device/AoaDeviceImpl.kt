package com.autoai.wdlink.hu.sdk.device

import com.autoai.welinkapp.aoa.AoaDevice

/**
 * Android 手机车机 USB 直连
 */
class AoaDeviceImpl: AoaDevice {
    override fun getManufacturer(): String {
        return "AutoAi, Inc"
    }

    override fun getModel(): String {
        return "手车互联"
    }

    override fun getDescription(): String {
        return "手车互联"
    }

    override fun getUri(): String {
        return "https://play.google.com/store/apps/details?id=com.gazra9k.android.launcher"
    }

    override fun getSerial(): String {
        return "mySerial"
    }

    override fun getVersion(): String {
        return "1.0"
    }
}