package com.autoai.wdlink.hu.auth

import android.util.Log
import com.autoai.wdlink.hu.Logger
import com.autoai.wdlink.hu.network.helper.DeviceInfoHelper
import com.autoai.wdlink.hu.network.model.LicenseWelinkActivateData
import com.autoai.wdlink.hu.network.params.LicenseWelinkActivateRequest
import com.autoai.wdlink.hu.network.retrofit.LicenseRetrofitNetwork
import com.autoai.wdlink.hu.network.retrofit.ResponseProtocol
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.sample
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

object AuthRepository {
    private val TAG = "Authority"
    private var mJob: Job? = null
    var hasRequestSuccess = false
    /**
     * 加载数据
     */
    val mAuthTrigger: MutableStateFlow<Long> = MutableStateFlow(-1L)

    private val _activationData: MutableStateFlow<ResponseProtocol<LicenseWelinkActivateData>?> =
        MutableStateFlow(null)
    val mActivationData: StateFlow<ResponseProtocol<LicenseWelinkActivateData>?> = _activationData

    init {
        mJob = GlobalScope.launch(DispatcherManager.getFixThreadDispatcher()) {
            mAuthTrigger.filter { it > -1 }.collectLatest {
                delay(1000)
                Logger.d( TAG+":$it,hasRequestSuccess:$hasRequestSuccess")
                if(!hasRequestSuccess) {
                    activationCheck()
                }
            }
        }
    }


    private suspend fun activationCheck() {
        withContext(Dispatchers.IO) {
            try {
                val activationCheck = LicenseRetrofitNetwork.welinkActivate(
                    LicenseWelinkActivateRequest(
                        "autoai",
                        "xiaopeng",
                        DeviceInfoHelper.getVinWithLazyCache,
                        DeviceInfoHelper.getAndroidIdWithLazyCache,
                        DeviceInfoHelper.getMacAddressWithLazyCache
                    )
                )
                hasRequestSuccess = true
                _activationData.emit(activationCheck)
            } catch (e: Exception) {
                Logger.d( TAG+"-exception : ${e.message}")
                e.printStackTrace()
            }
        }
    }

    fun stopActivation() {
        Logger.d( TAG+"-stopActivation")
        mJob?.cancel()
    }
}