package com.autoai.wdlink.hu.module.hidscreen

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.widget.FrameLayout

/**
 * 可拦截的 FrameLayout
 */
class InterceptFrameLayout: FrameLayout {
    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    @Suppress("unused")
    constructor(
        context: Context,
        attrs: AttributeSet?,
        defStyleAttr: Int,
        defStyleRes: Int
    ) : super(context, attrs, defStyleAttr, defStyleRes)

    private var interceptHandleCallback: (()->Boolean)? = null
    private var enableHandleCallback: (()->Boolean)? = null

    fun setOnInterceptHandleCallback(callback: (()->Boolean)?){
        interceptHandleCallback = callback
    }

    fun setEnableHandleCallback(callback: (()-><PERSON><PERSON><PERSON>)?){
        enableHandleCallback = callback
    }

    override fun onInterceptTouchEvent(ev: MotionEvent?): Boolean {
        return interceptHandleCallback?.invoke() ?: super.onInterceptTouchEvent(ev)
    }

    override fun isEnabled(): Boolean {
        return enableHandleCallback?.invoke() ?: super.isEnabled()
    }
}