package com.autoai.wdlink.hu.castwindow.view

/**
 * 屏幕旋转接口
 */
interface IScreenRotationChange {
    /**
     * 当手机切换横竖屏时调用
     */
    fun onScreenRotationChange(rotationChange: ScreenRotationChange){}
    /**
     * @param width 手机宽
     * @param height 手机高
     * @param angle 手机方向
     * @param mode 视频流模式，0:方形，1：长方形
     * @param platform 手机类型，Android，ios
     */
    data class ScreenRotationChange(
        val width: Int,
        val height: Int,
        val angle: Int,
        val mode: Int,
        val platform: String?
    ){
        override fun toString(): String {
            return "ScreenRotationChange(width=$width, height=$height, angle=$angle, mode=$mode, platform=$platform)"
        }
    }
}