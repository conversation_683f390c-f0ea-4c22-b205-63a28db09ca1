package com.autoai.wdlink.hu.frame.utils

import android.app.UiModeManager
import android.content.Context
import android.content.res.Configuration
import android.view.View
import android.view.Window
import com.autoai.wdlink.hu.Logger

object ThemeUtil {

    fun isNightMode(context: Context): Boolean {
        val uiModelManager = context.getSystemService(Context.UI_MODE_SERVICE) as UiModeManager
        return UiModeManager.MODE_NIGHT_YES == uiModelManager.nightMode
    }

    fun nightTheme(context: Context) {
        val uiModelManager = context.getSystemService(Context.UI_MODE_SERVICE) as UiModeManager
        if (Configuration.UI_MODE_TYPE_CAR == uiModelManager.currentModeType) {
            uiModelManager.enableCarMode(0)
            uiModelManager.nightMode = UiModeManager.MODE_NIGHT_YES
        }
    }

    fun dayTheme(context: Context) {
        val uiModelManager = context.getSystemService(Context.UI_MODE_SERVICE) as UiModeManager
        if (Configuration.UI_MODE_TYPE_CAR == uiModelManager.currentModeType) {
            uiModelManager.enableCarMode(0)
            uiModelManager.nightMode = UiModeManager.MODE_NIGHT_NO
        }
    }

    private var systemUiVisibility = -1

    // Shows the system bars by removing all the flags
    // except for the ones that make the content appear under the system bars.
    fun showSystemUI(window: Window) {
        Logger.i("fullScreen：showSystemUI")
//            window.decorView.systemUiVisibility = (View.SYSTEM_UI_FLAG_LAYOUT_STABLE
//                    or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
//                    or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN)
        window.decorView.systemUiVisibility = systemUiVisibility
    }

    fun hideSystemUI(window: Window) {
        Logger.i("fullScreen：hideSystemUI")
        if (systemUiVisibility == -1) {
            systemUiVisibility = window.decorView.systemUiVisibility
        }
        // Enables regular immersive mode.
        // For "lean back" mode, remove SYSTEM_UI_FLAG_IMMERSIVE.
        // Or for "sticky immersive," replace it with SYSTEM_UI_FLAG_IMMERSIVE_STICKY
        window.decorView.systemUiVisibility = (View.SYSTEM_UI_FLAG_IMMERSIVE
                // Set the content to appear under the system bars so that the
                // content doesn't resize when the system bars hide and show.
                or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                // Hide the nav bar and status bar
                or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                or View.SYSTEM_UI_FLAG_FULLSCREEN
                or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY)
    }
}