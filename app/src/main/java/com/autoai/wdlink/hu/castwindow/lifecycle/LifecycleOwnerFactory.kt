package com.autoai.wdlink.hu.castwindow.lifecycle

import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LifecycleRegistry
import com.autoai.wdlink.hu.ComponentName
import com.autoai.wdlink.hu.castwindow.LinkLog
import com.autoai.wdlink.hu.castwindow.ktx.isActive
import com.autoai.wdlink.hu.castwindow.scope.ViewVisibilityScope.Companion.toVisibilityStr

/**
 * 生成 LifecycleOwner
 * <AUTHOR>
 */
class LifecycleOwnerFactory {
    companion object{
        private const val TAG = "${ComponentName.Lifecycle}LifecycleOwnerFactory"
        fun generateLifecycleOwnerByView(view: View, ignoreWindowFocusChange: Boolean = false): LifecycleOwner {
            return object : LifecycleOwner{
                private var mLifecycleRegistry = LifecycleRegistry(this)
                private var mHasFocus = false
                private var mVisibility = View.GONE
                init {
                    LinkLog.i(TAG, "init: ignoreWindowFocusChange = $ignoreWindowFocusChange")
                    if(ignoreWindowFocusChange){
                        mHasFocus = true
                    } else {
                        view.viewTreeObserver.addOnWindowFocusChangeListener {
                            LinkLog.i(TAG, "onWindowFocusChanged: it = $it")
                            mHasFocus = it
                            handLifecycleEvent()
                        }
                    }
                    view.addOnAttachStateChangeListener(object : View.OnAttachStateChangeListener {
                        override fun onViewAttachedToWindow(v: View) {
                            LinkLog.i(TAG, "onViewAttachedToWindow: v = $v")
                            mLifecycleRegistry.currentState = Lifecycle.State.CREATED
                        }

                        override fun onViewDetachedFromWindow(v: View) {
                            LinkLog.i(TAG, "onViewDetachedFromWindow: v = $v, isActive() = ${isActive()}")
                            if(isActive()){
                                mLifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_PAUSE)
                                mLifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_STOP)
                            }
                            mLifecycleRegistry.currentState = Lifecycle.State.DESTROYED
                        }
                    })
                    if(view is ViewGroup){
                        view.addView(object : View(view.context){
                            init {
                                layoutParams = ViewGroup.LayoutParams(0, 0)
                            }
                            override fun onVisibilityChanged(changedView: View, visibility: Int) {
                                LinkLog.i(TAG, "onVisibilityChanged: visibility = ${visibility.toVisibilityStr()}")
                                mVisibility = visibility
                                handLifecycleEvent()
                            }
                        })
                    }
                }

                private fun handLifecycleEvent(){
                    LinkLog.i(TAG, "handLifecycleEvent: mHasFocus = $mHasFocus, mVisibility = ${mVisibility.toVisibilityStr()}, isActive() = ${isActive()}")
                    if(mHasFocus && mVisibility == View.VISIBLE){
                        if(!isActive()){
                            LinkLog.i(TAG, "handLifecycleEvent: set ON_RESUME")
                            mLifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_START)
                            mLifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_RESUME)
                        }
                    } else if(isActive()){
                        LinkLog.i(TAG, "handLifecycleEvent: set ON_PAUSE")
                        mLifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_PAUSE)
                        mLifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_STOP)
                    }
                }

                override val lifecycle: Lifecycle
                    get() = mLifecycleRegistry
            }
        }
    }
}