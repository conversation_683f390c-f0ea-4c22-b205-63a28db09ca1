package com.autoai.wdlink.hu.frame

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.autoai.wdlink.hu.Logger
import kotlinx.coroutines.*
import kotlin.coroutines.CoroutineContext
import kotlin.coroutines.EmptyCoroutineContext

open class BaseViewModel : ViewModel() {
    protected fun coroutineLauncher(
        context: CoroutineContext = EmptyCoroutineContext,
        block: suspend CoroutineScope.() -> Unit
    ): Job {
        return viewModelScope.launch(context = context, block = block)
    }

    protected fun <T> coroutineAsync(
        context: CoroutineContext = EmptyCoroutineContext,
        block: suspend CoroutineScope.() -> T
    ): Deferred<T> {
        return viewModelScope.async(context = context, block = block)
    }

    override fun onCleared() {
        super.onCleared()
        Logger.d("ViewModel onCleared : $this")
    }
}