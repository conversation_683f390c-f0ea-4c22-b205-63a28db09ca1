package com.autoai.wdlink.hu.module.hidscreen;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.ScaleGestureDetector;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import org.jetbrains.annotations.NotNull;

public class FullScreenRootView extends ConstraintLayout {
    private ScaleGestureDetector scaleGestureDetector;
    private ScaleListener scaleListener;

    public FullScreenRootView(@NonNull Context context) {
        super(context);
        init(context);
    }

    public FullScreenRootView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public FullScreenRootView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    public FullScreenRootView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init(context);
    }

    private void init(Context context) {
        scaleListener = new ScaleListener();
        scaleGestureDetector = new ScaleGestureDetector(context, scaleListener);

    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        requestDisallowInterceptTouchEvent(true);
        if (ev.getPointerCount() >= 3) {
            scaleGestureDetector.onTouchEvent(ev);
        }
        return super.dispatchTouchEvent(ev);
    }

    public void setOnScaleListener(@NotNull MultiTouchView.IDragListener iDragListener) {
        scaleListener.setOnScaleListener(iDragListener);
    }
}
