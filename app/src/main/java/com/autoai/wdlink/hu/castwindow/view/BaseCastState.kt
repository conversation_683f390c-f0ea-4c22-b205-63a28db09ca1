package com.autoai.wdlink.hu.castwindow.view

import androidx.annotation.CallSuper
import com.autoai.wdlink.hu.ComponentName
import com.autoai.wdlink.hu.castwindow.LinkLog
import com.autoai.wdlink.hu.castwindow.scope.CastWindowState

/**
 * 状态
 */
abstract class BaseCastState: IScreenRotationChange {
    @Suppress("PropertyName")
    val TAG:String
        get() { return "${ComponentName.WindowSwitcher}${javaClass.simpleName}" }
    /**
     * 状态是否激活
     */
    var isActive = false
        private set

    @get:CastWindowState
    abstract val state: Int

    /**
     * 进入当前状态
     */
    @CallSuper
    open fun onEnterState(args: Any? = null) {
        LinkLog.i(TAG, "onEnterState: args = $args")
        isActive = true
    }

    /**
     * 重新进入当前状态
     */
    open fun onReenterState() {
        LinkLog.i(TAG, "onReenterState: invoke")
    }

    /**
     * 退出当前状态
     */
    @CallSuper
    open fun onExitState() {
        LinkLog.i(TAG, "onExitState: invoke")
        isActive = false
    }
}