package com.autoai.wdlink.hu

/**
 * 功能模块组件名
 * <AUTHOR>
 * @date 2024/09/22
 */
object ComponentName {
    //业务组件
    //全屏，浮窗，最小化切换功能
    const val WindowSwitcher = "WindowSwitcher."
    //鉴权
    const val DeviceAuth = "DeviceAuth."
    //界面相关 Activity，引导，浮窗等
    const val LinkView = "LinkView."

    //基础组件
    const val Lifecycle = "Lifecycle."
    //网络库
    const val Network = "Network."
    //基础链接服务
    const val Link = "Link."
}