package com.autoai.wdlink.hu.network.retrofit

import androidx.core.os.trace
import com.autoai.wdlink.hu.BuildConfig
import com.autoai.wdlink.hu.castwindow.LinkLog
import com.autoai.wdlink.hu.network.LicenseNetworkDataSource
import com.autoai.wdlink.hu.network.converter.FieldConverterFactory
import com.autoai.wdlink.hu.network.di.NetworkModule
import com.autoai.wdlink.hu.network.model.LicenseWelinkActivateData
import com.autoai.wdlink.hu.network.params.IPostObjectToFieldMap
import com.autoai.wdlink.hu.network.params.LicenseWelinkActivateRequest
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.http.Body
import retrofit2.http.POST


private interface LicenseRetrofitNetworkApi {
    /**
     * @see <a href="https://navinfo.feishu.cn/docx/HwiSdFKZIou699xFsAoc046nnzh#share-JVEKd0Mmoo5kRcxHjmgc5HRYnjs">WeLink鉴权检查</a>
     */
    @POST("agent/license/licenseManage/welinkActivate")
    suspend fun welinkActivate(@Body request: LicenseWelinkActivateRequest): ResponseProtocol<LicenseWelinkActivateData>
}

const val LICENSE_TEST_URL = "http://test-wecockpit.autoai.com/"
const val LICENSE_RELEASE_URL = "http://beta-wecockpit-api.autoai.com/"
val LICENSE_BASE_URL = if(BuildConfig.DEBUG_MODE or BuildConfig.DEBUG) LICENSE_TEST_URL else LICENSE_RELEASE_URL

object LicenseRetrofitNetwork : LicenseNetworkDataSource {
    private val licenseNetworkApi = trace("RetrofitLicenseNetwork") {
        Retrofit.Builder()
            .baseUrl(LICENSE_BASE_URL)
            .callFactory(NetworkModule.licenseOkHttpCallFactory())
            .addConverterFactory(FieldConverterFactory.create(IPostObjectToFieldMap::class.java)) //正常用不到了
            .addConverterFactory(GsonConverterFactory.create())
            .build()
            .create(LicenseRetrofitNetworkApi::class.java)
    }

    override suspend fun welinkActivate(request: LicenseWelinkActivateRequest): ResponseProtocol<LicenseWelinkActivateData> {
        LinkLog.i(TAG, "welinkActivate: request = $request")
        return licenseNetworkApi.welinkActivate(request)
            .also { LinkLog.i(TAG, "welinkActivate: response = $it") }
    }
}