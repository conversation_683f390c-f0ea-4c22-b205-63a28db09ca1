package com.autoai.wdlink.hu

import android.Manifest
import android.annotation.SuppressLint
import android.content.Intent
import android.content.res.Configuration
import android.graphics.Outline
import android.graphics.SurfaceTexture
import android.graphics.drawable.Drawable
import android.media.MediaPlayer
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.CountDownTimer
import android.os.Handler
import android.os.SystemClock
import android.provider.Settings
import android.view.Surface
import android.view.TextureView
import android.view.View
import android.view.View.OnTouchListener
import android.view.ViewOutlineProvider
import android.view.ViewStub
import android.view.WindowManager
import android.view.animation.AlphaAnimation
import android.view.animation.Animation
import android.view.animation.AnimationSet
import android.view.animation.RotateAnimation
import android.view.animation.ScaleAnimation
import android.widget.TextView
import androidx.core.app.ActivityCompat
import androidx.core.view.updatePadding
import androidx.lifecycle.Observer
import com.autoai.avs.common.qrcode.QRCodeUtil
import com.autoai.avslinkhid.api.HidUtil
import com.autoai.wdlink.hu.auth.AuthModel
import com.autoai.wdlink.hu.auth.AuthModel.hasAuthority
import com.autoai.wdlink.hu.castwindow.LinkLog
import com.autoai.wdlink.hu.castwindow.scope.CastWindowState
import com.autoai.wdlink.hu.castwindow.view.CastWindowStateManager
import com.autoai.wdlink.hu.castwindow.view.SmallShowBean
import com.autoai.wdlink.hu.databinding.ActivityMainBinding
import com.autoai.wdlink.hu.dialog.GlideImageLoader
import com.autoai.wdlink.hu.dialog.UIUtil
import com.autoai.wdlink.hu.frame.BaseActivity
import com.autoai.wdlink.hu.frame.utils.AppUtil
import com.autoai.wdlink.hu.frame.utils.PopWinService
import com.autoai.wdlink.hu.frame.utils.PopWinServiceUtils
import com.autoai.wdlink.hu.frame.utils.ThemeUtil
import com.autoai.wdlink.hu.lifecycle.ActivityStateGetter
import com.autoai.wdlink.hu.model.DevicesTypeModel
import com.autoai.wdlink.hu.model.IosHidModel
import com.autoai.wdlink.hu.model.PageModel
import com.autoai.wdlink.hu.sdk.LinkSdkModel
import com.autoai.wdlink.hu.sdk.LinkUtil
import com.autoai.wdlink.hu.utils.ToastUtil
import com.autoai.wdlink.hu.utils.isOverlayPermissionGranted
import com.autoai.wdlink.hu.viewmodel.MainActivityViewModel
import com.autoai.welink.param.HidConstants
import com.autoai.welinkapp.checkconnect.CommonData
import com.autoai.welinkapp.model.MessageEvent
import com.autoai.welinkapp.model.SdkViewModel
import com.autoai.welinkapp.model.UIResponder
import com.blankj.utilcode.util.Utils
import com.bumptech.glide.Glide
import com.youth.banner.BannerConfig
import com.youth.banner.Transformer
import org.json.JSONException
import org.json.JSONObject


/**
 * 首页
 * */
class MainActivity : BaseActivity<MainActivityViewModel, ActivityMainBinding>(ActivityMainBinding::inflate),
    TextureView.SurfaceTextureListener {
    private val TAG = "MainActivity"
    //连接类型
    private var type: String? = null
    private var androidwifi: String? = null
    //连接类型状态
    var state: Int? = -1
    var connectstate: Int? = -1
    var integers = ArrayList<Drawable>()
    private var isVideoImage = true //判断视频是否显示
    private var isInstallImage = true//判断二维码是否显示
    private var bannerIsShow = false// 汽车接口数据入口轮播是否显示
    private var isShowButtonPage = false //点击下载和视频判断是否展示投屏窗口
    private var isconnectionSuccessful = false//是否连接成功
    private var isItDisconnected = false//是否断开连接
    private val handler = Handler()
    private var clickCount = 0 // 点击计数
    private val REQUIRED_CLICKS = 5 // 需要的点击次数
    private val DOUBLE_CLICK_TIME_THRESHOLD: Long = 500 //
    private var lastClickTime: Long = 0
    private var mediaPlayer: MediaPlayer? = null// 引导视频播放
    private var countDownTimer: CountDownTimer?  = null//ios返控引导倒计时
    private var  screenTimer: CountDownTimer?  = null//长时间不确认投屏提示
    private var  skipCalibrationTimer: CountDownTimer?  = null//长时间不确认投屏提示
    private val MIN_CLICK_DELAY_TIME = 300
    private var buttonlastClickTime: Long = 0
    private var clickCountDev = 0 // 点击计数

    private var isSettingFragmentVisible = false//判断当前是否显示fragment
    private val rotateAnimation : RotateAnimation by lazy {
        RotateAnimation(
            0f,
            360f,
            Animation.RELATIVE_TO_SELF,
            0.5f,
            Animation.RELATIVE_TO_SELF,
            0.5f
        )
    }


    //初始化页面状态回调
    private val pageObserver by lazy {
        Observer<PageModel> {
            LinkLog.d(TAG, "MainActivity::pageObserver: onChanged: it = $it")
            when (it.page) {
                UIResponder.INITIAL_PAGE -> {
                    viewModel.bgVisibility.value = View.VISIBLE
                    //如果有 INIT 状态，再次显示布局
                    initview()
                    if (null == it.param) {
                        /*if (viewModel.currentFragment != InitFragment::class.java.simpleName) {
                            LinkLog.d(TAG,"MainActivity::uiResponder: popBackStack - > initFragment")
                        }*/
                    }
                }

                UIResponder.HELP_PAGE -> {
                    LinkLog.d(TAG,"MainActivity::uiResponder: navigate - > helpFragment")
                    viewModel.bgVisibility.value = View.VISIBLE
                    val bundle = Bundle()
                    if (null != it.param) {
                        bundle.putInt(CommonData.MSG_KEY_TYPE, it.param as Int)
                    }
                }

                UIResponder.CONNECTING_PAGE -> {
                    LinkLog.d(TAG,"MainActivity::uiResponder: navigate - > connectingFragment")
                    viewModel.bgVisibility.value = View.VISIBLE
                }

                //TODO:bruce 超时异常（有线拉起 app 推荐 15s 未同意触发）
                // : 测试结果，超过 15s 也没有回调这个，取消录制或投射内容也没有断开回调
                UIResponder.ERR_PAGE_CONNECT -> {
                    LinkLog.d(TAG,"MainActivity::uiResponder: navigate - > connectTimeoutFragment")
                    viewModel.bgVisibility.value = View.VISIBLE
                }
                //TODO:bruce 异常断开（杀手机 app 可触发）
                // ：测试可触发
                UIResponder.ERR_PAGE_HEART,
                UIResponder.ERR_PAGE_SDK -> {
                    LinkLog.d(TAG,"MainActivity::uiResponder: navigate - > heartbeatErrorFragment")
                    viewModel.bgVisibility.value = View.VISIBLE
                    //如果有杀进程退出投屏，再次显示布局
                    initview()
                }
                //连接完成
                UIResponder.RUNNING_PAGE -> {
                    LinkLog.d(TAG,"MainActivity::uiResponder: navigate - > runningFragment")
                }

                // 录屏成功
                UIResponder.SCREEN_PAGE_MIRRORING ->{
                    connectionCompleted()
                    LinkLog.d(TAG,"MainActivity::uiResponder: navigate222 - > runningFragment")
                }
                //请求录屏手机侧取消后 重新发起录屏请求页面
                UIResponder.SCREEN_REQ_RECORD_SCREEN -> {
                    LinkLog.d(TAG,"MainActivity::uiResponder: navigate - > reqRecordScreenPage   == "  + UIResponder.SCREEN_REQ_RECORD_SCREEN)
                    handler.post {
                        binding.windowReqRecordScreen.visibility = View.VISIBLE
                    }
                }
                else -> {
                    viewModel.bgVisibility.value = View.VISIBLE
                }
            }
        }
    }
    //连接设备类型回调
    private val devicetypeObserver by lazy {
        Observer<DevicesTypeModel> {
            LinkLog.d( TAG,"MainActivity::devicetypeObserver ${it.devicetype}")
            when (it.devicetype) {
                CommonData.DEVICE_TYPE_AOA-> {
                    type = "android"
                    androidwifi = "android_aoa"
                    connectstate = CommonData.DEVICE_TYPE_AOA
                    androidUsbLin("android")
                }
                CommonData.DEVICE_TYPE_WIFI_ANDROID, CommonData.DEVICE_TYPE_WIFI_HARMONY-> {
                    type = "android"
                    androidWifiLin("android")
                    connectstate = CommonData.DEVICE_TYPE_WIFI_ANDROID
                    androidwifi = "android_wifi"
                }
                CommonData.DEVICE_TYPE_WIFI_IOS-> {
                    type = "ios"
                    androidwifi = "ios_wifi"
//                    iosWifiLin("ios")
                    val deviceSet = HidUtil.getConnectedBluetoothDevices();
                    LinkLog.d(TAG,"MainActivity::MiandeviceSet  = ${deviceSet.size}")
                    if (deviceSet.isEmpty()){
                        iosWifiLin2("ios")
                    }else{
                        // 检查蓝牙设备是否与互联手机匹配
                        val isBTPairedWithPhone = SdkViewModel.getInstance().isBTPairedWithPhone();
                        if (isBTPairedWithPhone) {
                            handler.postDelayed({iosCalibrationLin()}, 100)
                        } else {
                            iosWifiLin2("ios")
                        }
                    }
                    LinkLog.d(TAG,"MainActivity::permission stateios = ${CommonData.DEVICE_TYPE_WIFI_IOS}")
                }
                CommonData.DEVICE_TYPE_EAP-> {
                    type = "ios"
                    androidwifi = "ios_eap"
                    iosUsbLin("ios")
                }
                CommonData.DEVICE_TYPE_NONE -> { //回到初始开始连接页面
                    type = "none"
                    resetDeviceTypeView("none")
                }
                CommonData.LINK_STATUS_SCREEN_MIRRORING-> {
                    // guideDialog?.dismiss()
                }
            }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun iosCalibrationLin() {
        // 配置 BlurView
        binding.vdline.visibility = View.GONE
        binding.vdview.visibility = View.VISIBLE
        binding.wifiLin.visibility = View.VISIBLE
        binding.usbLin.visibility = View.GONE
        binding.usbImg.visibility = View.GONE
        binding.calibrationingImg.visibility = View.VISIBLE
        binding.calibrationFailedImg.visibility = View.GONE
        binding.scanningEffect.visibility = View.VISIBLE
        binding.calibrationingImg.setImageResource(R.mipmap.link_reverse_control_calibration)
        binding.scanningEffect.setImageResource(R.mipmap.link_scanning_effect)

        rotateAnimation.duration = 2000
        rotateAnimation.repeatCount = Animation.INFINITE // 设置动画无限重复
        binding.scanningEffect.startAnimation(rotateAnimation)

        binding.createLin.visibility = View.GONE
        binding.calibrationLin.visibility = View.VISIBLE
        binding.calibrationTitle.textSize = resources.getInteger(R.integer.calibrationTitleTextSize).toFloat()
        binding.calibrationTitle.text = Utils.getApp().getString(R.string.now_do_not_touch_your_phone)
        binding.calibrationContent.visibility = View.GONE
        binding.windowsLinTisp.visibility = View.VISIBLE
        binding.windowsLinText.text = Utils.getApp().getString(R.string.touch_your_phone)
        binding.windowsLinFtext.visibility = View.VISIBLE
        binding.windowsLinFtext.text = Utils.getApp().getString(R.string.now_your_phone)
        binding.windowsLinImage.setImageResource(R.mipmap.link_no_touch)
        binding.tvRecordingPermission.visibility = View.INVISIBLE
        screenTimer?.cancel()
        handler.postDelayed({ binding.wifiImage.setImageResource(R.mipmap.link_connection_completion_prompt_diagram) }, 100)

    }

    private val iosHidObserver by lazy {
        Observer<IosHidModel> {
            LinkLog.d( TAG,"MainActivity::iosHidObserver ${"iosHidState = $it"}")
            //ios 校准回调 0成功，  1校准失败，  2蓝牙未连接, 3 手机app未在前台，无法校准
            when (it.iosHidState) {
                HidConstants.ON_HID_SUCCESS -> {
                    binding.scanningEffect.clearAnimation()
                    handler.postDelayed({iosWifiLin("ios")}, 100)
                }

                HidConstants.ON_HID_FAIL,
                HidConstants.ON_HID_APP_BACKGROUND_FAIL  -> {
                    binding.scanningEffect.clearAnimation()
                    binding.calibrationingImg.visibility = View.GONE
                    binding.scanningEffect.visibility = View.GONE
                    binding.calibrationFailedImg.visibility = View.VISIBLE
                    binding.calibrationTitle.textSize = resources.getInteger(R.integer.calibrationFailTitleTextSize).toFloat()
                    binding.calibrationFailedImg.setImageResource(R.mipmap.link_calibration_failed_img)
                    binding.calibrationTitle.text = Utils.getApp().getString(R.string.calibration_failed)
//                    binding.calibrationCountdown.visibility = View.VISIBLE
                     skipCalibrationTimer = skipCalibrationTimer()
                    binding.calibrationContent.visibility = View.VISIBLE
                    binding.calibrationContent.text = Utils.getApp().getString(R.string.do_not_touch_your_phone)
                    binding.recalibrate.visibility = View.VISIBLE
                    binding.recalibrate.setOnClickListener {
                        LinkSdkModel.startVerification()
                        binding.calibrationFailedImg.visibility = View.GONE
                        binding.scanningEffect.visibility = View.VISIBLE
                        binding.calibrationingImg.visibility = View.VISIBLE

                        rotateAnimation.duration = 2000
                        rotateAnimation.repeatCount = Animation.INFINITE // 设置动画无限重复
                        binding.scanningEffect.startAnimation(rotateAnimation)

                        binding.calibrationingImg.setImageResource(R.mipmap.link_reverse_control_calibration)
                        binding.calibrationTitle.text = Utils.getApp().getString(R.string.now_do_not_touch_your_phone)
                        binding.recalibrate.visibility = View.GONE
                        binding.calibrationContent.visibility = View.GONE
//                        binding.calibrationCountdown.visibility = View.GONE
                        skipCalibrationTimer!!.cancel()
                    }
                }
                HidConstants.ON_HID_BT_PAIRED_WITH_PHONE -> {
                    handler.postDelayed({iosCalibrationLin()}, 100)
                }
            }
        }
    }
    private val msgObserver by lazy {
        Observer<MessageEvent> {
            when (it.what) {
                MessageEvent.RECEIVE_MESSAGE -> {
                    LinkLog.d(TAG,"MainActivity::MsgCallback Type = MessageEvent.RECEIVE_MESSAGE")
                    var key = ""
                    try {
                        val obj = it.obj.toString()
                        LinkLog.d(TAG,"MainActivity::MsgCallback obj = $obj")
                        val json = JSONObject(obj)
                        key = json.get("key").toString()
                        val extra = json.optString("extra")
                        LinkLog.d(TAG,"MainActivity::MsgCallback key = $key")
                        when (key) {
                            "reauthorize_record_screen" -> {
                                when (extra){
                                    "foreground" -> {
                                        when (type) {
                                            "android" -> {
                                                LinkLog.d(TAG,"MainActivity::MsgCallback connectstate = $connectstate")
                                                when(connectstate){
                                                    CommonData.DEVICE_TYPE_AOA -> androidUsbLin(type)
                                                    CommonData.DEVICE_TYPE_WIFI_ANDROID -> androidWifiLin(type)
                                                }
                                            }
                                            "ios" -> iosUsbLin(type) //理论上不可达
                                            else -> resetDeviceTypeView("none")
                                        }
                                    }
                                    "background" -> ToastUtil.showToast(Utils.getApp().getString(R.string.connect_cancel_cast_tips),"","","",false,false,-1);
                                }
                            }
                            "actionCarHome" -> {
                                //回到车机主页
                                moveTaskToBack(false)
                            }
                            "onOpenHuBTSetting" -> {
                                //前往车机蓝牙设置界面
                                AppUtil.startBluetoothCard(this)
                            }
                        }
                    } catch (e: JSONException) {
                        LinkLog.e(TAG, "MainActivity::消息解析错误: $e")
                    }

                }

                MessageEvent.GO_TO_MENU -> {
                    LinkLog.d(TAG,"MainActivity::MsgCallback Type = MessageEvent.GO_TO_MENU")
//                    moveTaskToBack(false)
                }

                else -> {
                    LinkLog.d(TAG,"MainActivity::MsgCallback Type = others --> ${it.what}")
                }
            }
        }
    }

    private val activationResult by lazy {
        Observer<Pair<Int, String?>> {
            when (it.first) { //0未激活、1已激活、2激活已过期
                0 , 2-> {
                    showAuthMsg("car", it.second)
                }
                1 -> {
                    LinkLog.d(TAG, "MainActivity::activationResult: onAuthResult success")
                }
                else -> {
                    LinkLog.d(TAG, "MainActivity::activationResult: onAuthResult ${it.first}")

                }
            }
        }
    }

    private fun showAuthMsg(type: String, msg: String?) {
        LinkLog.d(TAG, "MainActivity::showAuthMsg: type = $type, msg = $msg")
        showAuthErr(msg)
    }

    override fun getViewModelClass() = MainActivityViewModel::class

    override fun getViewBindingClass() = ActivityMainBinding::class
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        ThemeUtil.hideSystemUI(window)
        LinkLog.d(TAG,"MainActivity::LinkUtil.isConnected() ============ ${LinkUtil.isConnected()},init:${LinkUtil.isInit()}")
        //如果没有链接或没有浮窗，显示页面
        onObserve()
    }

    private fun onObserve() {
        if(!LinkUtil.isConnected() && CastWindowStateManager.getCurrentState() == CastWindowState.NONE) {
            //防止异常崩溃 app不能重启，避免不重新初始化
            if(!LinkUtil.isInit()) {
//                LinkService.stopLinkService(this)
                LinkService.startLinkService(this)
            }
            initview()
            viewModel.pageEvent.observe(this, pageObserver)
            viewModel.devicesEvent.observe(this, devicetypeObserver)
            viewModel.iosHidEvent.observe(this, iosHidObserver)
            AuthModel.activateResult.observe(this, activationResult)
            //接受手机端发送的消息
            LinkUtil.interiorMsgEvent.observe(this, msgObserver)
            val density = resources.displayMetrics.density
            LinkLog.d(TAG,"MainActivity::density ============ $density")

            LinkLog.d(TAG,"MainActivity::权限检查:" + checkSelfPermission(Manifest.permission.ACCESS_FINE_LOCATION)+","
                    +checkSelfPermission(Manifest.permission.BLUETOOTH_ADMIN)+","+
                    checkSelfPermission(Manifest.permission.BLUETOOTH)+","
                    +checkSelfPermission(Manifest.permission.BLUETOOTH_CONNECT) + ","
                    +checkSelfPermission(Manifest.permission.BLUETOOTH_ADVERTISE) + ","
                    +checkSelfPermission(Manifest.permission.ACCESS_COARSE_LOCATION))
            ActivityCompat.requestPermissions(this,arrayOf(
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.BLUETOOTH_CONNECT, Manifest.permission.BLUETOOTH_ADMIN, Manifest.permission.BLUETOOTH,
                Manifest.permission.BLUETOOTH_ADVERTISE,
                Manifest.permission.ACCESS_COARSE_LOCATION),0X101)
        }else{
            if (LinkSdkModel.connectStatus == CommonData.LINK_STATUS_SCREEN_MIRRORING){
                binding.mainLin.visibility = View.GONE
            }
        }

    }

    private fun initview() {
        if (CastWindowStateManager.getCurrentState() != CastWindowState.NONE){
            return
        }
        //24LD席初始化时适配屏幕尺寸
       if (BuildConfig.FLAVOR == "_24L"){
           binding.createLin.apply {
               updatePadding(top = 84)
           }
           binding.connectionPrompt2.apply {
               updatePadding(top = 34)
           }
       }
        integers.clear()
        binding.mainLin.visibility = View.VISIBLE
        binding.rightLin.visibility = View.VISIBLE
        binding.phoneLin.visibility = View.VISIBLE
        integers.add(UIUtil.getDrawable(R.mipmap.link_usb_location_map, this))
        Glide.with(this).asGif().load(R.drawable.loading)
            .placeholder(R.drawable.loading).into(binding.usbImage)
        Glide.with(this).asGif().load(R.drawable.loading)
            .placeholder(R.drawable.loading).into(binding.wifiImage)
        Glide.with(this).asGif().load(R.drawable.loading)
            .placeholder(R.drawable.loading).into(binding.startImage)
        initClickListener()
        handler.postDelayed({
            binding.imageFirst.visibility = View.GONE
            binding.createLin.visibility = View.VISIBLE
            binding.usbClick.isClickable = true
            binding.mainLin.visibility = View.VISIBLE
            binding.checktheenvironment.visibility = View.GONE
            binding.connectionMethod.visibility = View.VISIBLE
            binding.tvRecordingPermission.visibility = View.INVISIBLE
        }, 1000)
        binding.qrCodeImageView.setOutlineProvider(object : ViewOutlineProvider() {
            override fun getOutline(view: View, outline: Outline) {
                outline.setRoundRect(0, 0, view.width, view.height, 30f)
            }
        })
        // 设置圆角
        binding.qrCodeImageView.setClipToOutline(true)

        binding.windowsLinTisp.visibility = View.GONE
        binding.windowReqRecordScreen.visibility = View.GONE
    }


    @SuppressLint("MissingSuperCall")
    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        LinkLog.d(TAG,"MainActivity::onNewIntent-----------getCurrentState:"+CastWindowStateManager.getCurrentState())
        if( CastWindowStateManager.getCurrentState() == CastWindowState.NONE || LinkSdkModel.isConnected()) {
            viewModel.pageEvent.observe(this, pageObserver)
            viewModel.devicesEvent.observe(this, devicetypeObserver)
            AuthModel.activateResult.observe(this, activationResult)
            viewModel.iosHidEvent.observe(this, iosHidObserver)
            //接受手机端发送的消息
            LinkUtil.interiorMsgEvent.observe(this, msgObserver)
        }else{
            if (LinkSdkModel.connectStatus == CommonData.LINK_STATUS_SCREEN_MIRRORING){
                binding.mainLin.visibility = View.GONE
            }
        }
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        LinkLog.d(TAG,"MainActivity::onWindowFocusChanged ：hasFocus = $hasFocus")
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        LinkLog.d(TAG,"MainActivity::onConfigurationChanged ：$this")
        //如果为24L设备区分主副驾
        if(BuildConfig.FLAVOR.contains("_24L")){
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                val display = display ?: return

                val displayId = display.displayId
                if (displayId == 0) {
                    // 主屏逻辑
                    binding.createLin.apply {
                        updatePadding(top = 64)
                    }
                    binding.connectionPrompt2.apply {
                        updatePadding(top = 24)
                    }
                } else {
                    val deviceProductInfo = display.deviceProductInfo
                    if (deviceProductInfo != null && deviceProductInfo.name?.contains("secondary") == true) {
                        // 副屏逻辑
                        binding.createLin.apply {
                            updatePadding(top = 84)
                        }
                        binding.connectionPrompt2.apply {
                            updatePadding(top = 84)
                        }
                        LinkLog.d(TAG,"MainActivity::Mainactivity副屏逻辑")
                    }
                }
            } else {
                // 对于低于 API 30 的设备，可选择忽略或提供替代处理
                LinkLog.d(TAG,"MainActivity::API < 30, display property not available")
            }
        }


    }

    override fun onStart() {
        super.onStart()
        ActivityStateGetter.isActivityInForeground = true
        //超时后 重新打开MainActivity时 是能拉起互联
        if (viewModel.isStratPop() && !LinkUtil.isConnected()) {
            PopWinServiceUtils.startPopWinService(this)
        }
    }

    override fun onResume() {
        LinkLog.d(TAG,"MainActivity::生命周期——onResume:")
        super.onResume()
        LinkLog.d(TAG,"MainActivity::onResume-----------getCurrentState:"+CastWindowStateManager.getCurrentState())
        if (LinkSdkModel.isConnected() && CastWindowStateManager.getCurrentState() == CastWindowState.NONE){

            determineDeviceStatus()

            viewModel.pageEvent.observe(this, pageObserver)
            viewModel.devicesEvent.observe(this, devicetypeObserver)
            AuthModel.activateResult.observe(this, activationResult)
            viewModel.iosHidEvent.observe(this, iosHidObserver)
            //接受手机端发送的消息
            LinkUtil.interiorMsgEvent.observe(this, msgObserver)
            initClickListener()
        }
        if (CastWindowStateManager.getCurrentState() == CastWindowState.MINI){
            CastWindowStateManager.switchState(CastWindowState.SMALL, SmallShowBean(-1, PopWinService.getLastPosition()))
        }
        if (!hasAuthority) {
            showAuthErr("未激活")
        }
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        if (!isOverlayPermissionGranted(this)) {
            val intent = Intent(
                Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                Uri.parse("package:$packageName")
            )
            startActivityForResult(intent, 10)
        } else {
            if (!hasAuthority) {
                PopWinServiceUtils.removePopWinService(this)
            }
            viewModel.onForeground(true)
        }

        LinkLog.d(TAG,"MainActivity::iCurrentConnectType:"+CommonData.iCurrentConnectType)
    }


    override fun onPause() {
        super.onPause()
        binding.scanningEffect.clearAnimation()
        binding.rightLin.clearAnimation()
        LinkLog.d(TAG,"MainActivity::生命周期——onPause:")
        window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }

    override fun onStop() {
        super.onStop()
        viewModel.onForeground(false)
        state = -1
        isItDisconnected = false
        LinkLog.d(TAG,"MainActivity::生命周期——onStop:")
        isShowButtonPage = false
        isconnectionSuccessful = false
        ActivityStateGetter.isActivityInForeground = false
      /*  //将投屏window居中
        if (LinkUtil.isConnected()) {
            PopWinServiceUtils.showPopWinLayout(this)
        }*/
    }

    override fun onBackPressed() {
        LinkLog.d(TAG,"MainActivity::onBackPressed------------")
        if(isSettingFragmentVisible){
            hideSettingFragment()
        }else{
            finish()
        }
        //停止services
        //PopWinServiceUtils.destroyPopWin(this)
//        viewModel.onBackPressed {
//            moveTaskToBack(true)
//        }
    }

    override fun onDestroy() {
        viewModel.pageEvent.removeObserver(pageObserver)
        viewModel.devicesEvent.removeObserver(devicetypeObserver)
        viewModel.iosHidEvent.removeObserver(iosHidObserver)
        AuthModel.activateResult.removeObserver(activationResult)
        super.onDestroy()
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        LinkLog.d(TAG,"MainActivity::onRequestPermissionsResult code = $requestCode  permission = $permissions grantResults = $grantResults")
        for (permission in permissions) {
            LinkLog.d(TAG,"MainActivity::permission = $permission")
        }

        for(result in grantResults){
            LinkLog.d(TAG,"MainActivity::permission result = $result")
        }
    }

    @SuppressLint("CommitTransaction")
    private fun showSettingFragment() {
        binding.settingFrameLayout.visibility = View.VISIBLE
        val fragment = SettingFragment.newInstance("", "")
        supportFragmentManager.beginTransaction()
            .add(R.id.setting_frameLayout, fragment)
            .commit()
        isSettingFragmentVisible = true
    }
    @SuppressLint("CommitTransaction")
    private fun hideSettingFragment() {
        binding.settingFrameLayout.visibility = View.GONE
        val fragment = supportFragmentManager.findFragmentById(R.id.setting_frameLayout)
        if (fragment != null) {
            supportFragmentManager.beginTransaction()
                .remove(fragment)
                .commit()
        }
        isSettingFragmentVisible = false
    }

    @SuppressLint("CommitTransaction")
    private fun initClickListener() {
        binding.settingLin.setOnClickListener {
            showSettingFragment()
        }
        //ios 不会显示录屏未授权的页面
        binding.viewReqRecordScreenPage.setButton1ClickListener{
            LinkLog.d(TAG,"MainActivity::viewReqRecordScreenPage + 去授权")
            handler.post { SdkViewModel.getInstance().sendReqRecordScreenOp() }
        }
        binding.loading.setOnClickListener(View.OnClickListener {handleButtonClick()})

        binding.viewDev.setOnClickListener({startDevClick()})

        binding.usbClick.setOnClickListener(View.OnClickListener { view: View? ->
            bannerIsShow = true
            binding.mainLin.visibility = View.GONE
            binding.bannerLin.visibility = View.VISIBLE
            binding.banner.setBannerStyle(BannerConfig.CIRCLE_INDICATOR_TITLE)
            //设置图片加载器
            binding.banner.setImageLoader(GlideImageLoader())
            //设置图片集合
            binding.banner.setImages(integers)
            //设置banner动画效果
            binding.banner.setBannerAnimation(Transformer.DepthPage)
            //设置自动轮播，默认为true
            binding.banner.isAutoPlay(false)
            //设置轮播时间
            binding.banner.setDelayTime(3000)
            //设置指示器位置（当banner模式中有指示器时）
            binding.banner.setIndicatorGravity(BannerConfig.CENTER)
            //banner设置方法全部调用完毕时最后调用
            binding.banner.start()
        })
        binding.closeIcon.setOnClickListener(View.OnClickListener { view: View? ->
            binding.mainLin.visibility = View.VISIBLE
            binding.bannerLin.visibility = View.GONE
            bannerIsShow = false
              if (type.equals("ios")){
                    if (isconnectionSuccessful){
                        binding.installAppLin.isClickable = false
                        IosAnima()
                    }
                }else if (type.equals("android")){
                    if (isconnectionSuccessful){
                        binding.installAppLin.isClickable = false
                        androidAnimation()
                    }
                }
        })
        //引导视频
        binding.guideVideoLin.setOnClickListener(View.OnClickListener { view: View? ->
            if (isFastClick()){
                if (isVideoImage) {
                    if (!isInstallImage) {
                        isInstallImage = true
                        binding.installApp.setImageResource(R.mipmap.link_guide_to_download_app_images)
                        binding.installText.text = this.getText(R.string.connect_download_the_application)
                        binding.installText.setTextColor(this.getResources().getColor(R.color.black))
                        binding.installAppLin.background = this.getResources().getDrawable(R.mipmap.link_small_image_with_shadow)
                    }
                    isShowButtonPage = true
                    binding.videoInstallLayout.visibility = View.VISIBLE
                    binding.animationRe.visibility = View.GONE
                    binding.guideTextureView.visibility = View.VISIBLE
                    binding.videoLin.visibility = View.VISIBLE
                    binding.installLin.visibility = View.GONE
                    if (state == -1){
                        binding.createLin.visibility = View.GONE
                    }
                    if (state == 1){
                        binding.windowsLinTisp.visibility = View.GONE
                    }
                    if (state == 2){
                        binding.iosWebview.visibility = View.GONE
                        screenTimer!!.cancel()
                        binding.closeText.text = getText(R.string.connect_close_window)
                    }
                    binding.textureViewFrame.setOutlineProvider(object : ViewOutlineProvider() {
                        override fun getOutline(view: View, outline: Outline) {
                            outline.setRoundRect(0, 0, view.width, view.height, 20f)
                        }
                    })
                    // 设置圆角
                    binding.textureViewFrame.setClipToOutline(true)
                    binding.guideTextureView.surfaceTextureListener = this
                    prepareAndStartPlayback()
                    isVideoImage = false
                    binding.videoText.text = this.getText(R.string.connect_close_guidance)
                    binding.videoText.setTextColor(this.getResources().getColor(R.color.blue))
                    binding.guideVideo.setImageResource(R.mipmap.link_close_prompt)
                    binding.guideVideoLin.background = this.getResources().getDrawable(R.mipmap.link_prompt_installation_background)
                } else {
                    isShowButtonPage = false
                    binding.videoInstallLayout.visibility = View.GONE
                    binding.animationRe.visibility = View.VISIBLE
                    binding.guideTextureView.visibility = View.INVISIBLE
                    binding.videoLin.visibility = View.GONE
                    mediaPlayer?.let {
                        it.release()
                    }
                    binding.installLin.visibility = View.GONE
                    if (state == -1){
                        binding.createLin.visibility = View.VISIBLE
                        binding.usbClick.isClickable = true
                    }
                    if (state == 1){
                        binding.windowsLinTisp.visibility = View.VISIBLE
                    }
                    if (state == 2){
                        binding.iosWebview.visibility = View.VISIBLE
                    }
                    isVideoImage = true
                    binding.guideVideo.setImageResource(R.mipmap.link_guide_video_prompt_image)
                    binding.videoText.text = this.getText(R.string.connect_guide_video)
                    binding.videoText.setTextColor(this.getResources().getColor(R.color.black))
                    binding.guideVideoLin.background = this.getResources().getDrawable(R.mipmap.link_small_image_with_shadow)
                    if (type.equals("ios")){
                        if (isconnectionSuccessful){
                            binding.installAppLin.isClickable = false
                            IosAnima()
                        }
                    }else if (type.equals("android")){
                        if (isconnectionSuccessful){
                            binding.installAppLin.isClickable = false
                            androidAnimation()
                        }
                    }
                    if (isItDisconnected){
                        if (state == 1){
                            binding.windowsLinTisp.visibility = View.VISIBLE
                            binding.iosWebview.visibility = View.GONE
                            binding.createLin.visibility = View.GONE

                        }else if (state == 2){
                            binding.windowsLinTisp.visibility = View.GONE
                            binding.iosWebview.visibility = View.VISIBLE
                            binding.createLin.visibility = View.GONE
                        }else{
                            binding.windowsLinTisp.visibility = View.GONE
                            binding.iosWebview.visibility = View.GONE
                            binding.createLin.visibility = View.VISIBLE
                            binding.usbClick.isClickable = true
                        }
                    }
                    if (LinkSdkModel.connectStatus == CommonData.LINK_STATUS_SCREEN_MIRRORING){
                        binding.windowReqRecordScreen.visibility = View.GONE
                        binding.tvRecordingPermission.visibility = View.GONE
                        binding.createLin.visibility = View.GONE
                        binding.windowsLinTisp.visibility  = View.VISIBLE
                        binding.windowsLinText.text = this.getText(R.string.connect_connection_completed)
                        binding.startLine.visibility = View.VISIBLE
                        binding.windowsLinFtext.visibility = View.VISIBLE
                        handler.postDelayed(Runnable {Glide.with(this).load(R.mipmap.link_connection_completion_prompt_diagram).into(binding.startImage) }, 200)
                        if (isconnectionSuccessful){
                            if ("android" == type){
                                androidAnimation()
                            }
                            if ("ios" == type){
                                IosAnima()
                            }
                        }
                    }
                }
            }

        })
        //引导安装app
        binding.installAppLin.setOnClickListener(View.OnClickListener { view: View? ->
           if (isFastClick()){
               if (isInstallImage) {
                   if (!isVideoImage) {
                       isVideoImage = true
                       binding.guideVideo.setImageResource(R.mipmap.link_guide_video_prompt_image)
                       binding.videoText.text = this.getText(R.string.connect_guide_video)
                       binding.videoText.setTextColor(this.getResources().getColor(R.color.black))
                       binding.guideVideoLin.background = this.getResources().getDrawable(R.mipmap.link_small_image_with_shadow)
                   }
                   isShowButtonPage = true
                   binding.videoInstallLayout.visibility = View.VISIBLE
                   binding.animationRe.visibility = View.GONE
                   binding.guideTextureView.visibility = View.INVISIBLE
                   binding.videoLin.visibility = View.GONE
                   binding.createLin.visibility = View.GONE
                   binding.installLin.visibility = View.VISIBLE
                   if (state == -1){
                       binding.createLin.visibility = View.GONE
                   }
                   if (state == 1){
                       binding.windowsLinTisp.visibility = View.GONE
                   }
                   if (state == 2){
                       binding.iosWebview.visibility = View.GONE
                       screenTimer!!.cancel()
                       binding.closeText.text = getText(R.string.connect_close_window)
                   }
                   binding.qrCodeImageView.setImageBitmap(
                       QRCodeUtil.createQRCodeBitmap(
                           "https://www.baidu.com",
                           200
                       )
                   )
                   isInstallImage = false
                   binding.installText.setTextColor(this.getResources().getColor(R.color.blue))
                   binding.installText.text = this.getText(R.string.connect_close_download)
                   binding.installApp.setImageResource(R.mipmap.link_close_prompt)
                   binding.installAppLin.background = this.getResources().getDrawable(R.mipmap.link_prompt_installation_background)


               } else {
                   isShowButtonPage = false
                   binding.videoInstallLayout.visibility = View.GONE
                   binding.animationRe.visibility = View.VISIBLE
                   binding.guideTextureView.visibility = View.INVISIBLE
                   binding.videoLin.visibility = View.GONE
                   mediaPlayer?.let {
                       it.release()
                   }
                   binding.installLin.visibility = View.GONE
                   if (state == -1){
                       binding.createLin.visibility = View.VISIBLE
                       binding.usbClick.isClickable = true
                   }
                   if (state == 1){
                       binding.windowsLinTisp.visibility = View.VISIBLE
                   }
                   if (state == 2){
                       binding.iosWebview.visibility = View.VISIBLE
                   }
                   isInstallImage = true
                   binding.installApp.setImageResource(R.mipmap.link_guide_to_download_app_images)
                   binding.installText.text = this.getText(R.string.connect_download_the_application)
                   binding.installText.setTextColor(this.getResources().getColor(R.color.black))
                   binding.installAppLin.background = this.getResources().getDrawable(R.mipmap.link_small_image_with_shadow)
                   if (type.equals("ios")){
                       if (isconnectionSuccessful){
                           binding.installAppLin.isClickable = false
                           IosAnima()
                       }
                   }else if (type.equals("android")){
                       if (isconnectionSuccessful){
                           binding.installAppLin.isClickable = false
                           androidAnimation()
                       }
                   }
                   if (isItDisconnected){
                       if (state == 1){
                           binding.windowsLinTisp.visibility = View.VISIBLE
                           binding.iosWebview.visibility = View.GONE
                           binding.createLin.visibility = View.GONE

                       }else if (state == 2){
                           binding.windowsLinTisp.visibility = View.GONE
                           binding.iosWebview.visibility = View.VISIBLE
                           binding.createLin.visibility = View.GONE
                       }else{
                           binding.windowsLinTisp.visibility = View.GONE
                           binding.iosWebview.visibility = View.GONE
                           binding.createLin.visibility = View.VISIBLE
                           binding.usbClick.isClickable = true
                       }
                   }
                   if (LinkSdkModel.connectStatus == CommonData.LINK_STATUS_SCREEN_MIRRORING){
                       binding.windowReqRecordScreen.visibility = View.GONE
                       binding.tvRecordingPermission.visibility = View.GONE
                       binding.createLin.visibility = View.GONE
                       binding.windowsLinTisp.visibility  = View.VISIBLE
                       binding.windowsLinText.text = this.getText(R.string.connect_connection_completed)
                       binding.startLine.visibility = View.VISIBLE
                       binding.windowsLinFtext.visibility = View.VISIBLE
                       handler.postDelayed(Runnable {Glide.with(this).load(R.mipmap.link_connection_completion_prompt_diagram).into(binding.startImage) }, 200)
                       if (isconnectionSuccessful){
                           if ("android" == type){
                               androidAnimation()
                           }
                           if ("ios" == type){
                               IosAnima()
                           }
                       }
                   }
               }
           }
        })
        binding.vsAuth.setOnInflateListener(ViewStub.OnInflateListener { _, view ->
            val tv = view.findViewById<TextView>(R.id.btn_authconfirm)
            tv.setOnClickListener {
//                Process.killProcess(Process.myPid())
//                exitProcess(0)
                performAction();
            }
        })
    }

    fun androidWifiLin(type: String?) {
        this.type = type
        state = 1
         screenTimer = getScreenTimer()
        binding.windowsLinText.text = this.getText(R.string.connect_record_mobile_phone_footage)
        binding.windowsLinFtext.visibility = View.GONE
        binding.mainLin.visibility = View.VISIBLE
        binding.createLin.visibility = View.GONE
        binding.usbClick.isClickable = false
        //binding.viLin.visibility = View.INVISIBLE
        binding.windowReqRecordScreen.visibility = View.GONE
        binding.vdline.visibility = View.GONE
        binding.vdview.visibility = View.VISIBLE
        binding.wifiLin.visibility = View.VISIBLE
        binding.usbLin.visibility = View.GONE
        binding.usbImg.visibility = View.GONE
        binding.windowsLinTisp.visibility = View.VISIBLE
        binding.windowsLinImage.setImageResource(R.mipmap.link_android_authorization_screen_casting_prompt_image)
        handler.postDelayed(Runnable { binding.wifiImage.setImageResource(R.mipmap.link_connection_completion_prompt_diagram) }, 100)
        binding.icShouquan.setImageResource(R.mipmap.link_blue_dot_circle)
    }

    fun iosWifiLin(type: String?) {
        this.type = type
        state = 1
        screenTimer = getScreenTimer()
        binding.calibrationLin.visibility = View.GONE
        binding.windowsLinText.text = this.getText(R.string.connect_record_mobile_phone_footage)
        binding.windowsLinFtext.visibility = View.GONE
        binding.mainLin.visibility = View.VISIBLE
        binding.createLin.visibility = View.GONE
        binding.iosWebview.visibility = View.GONE
        binding.usbClick.isClickable = false
        binding.windowsLinTisp.visibility = View.VISIBLE
        binding.windowsLinImage.setImageResource(R.mipmap.link_ios_authorization_screen_recording_prompt_image)
        binding.icShouquan.setImageResource(R.mipmap.link_blue_dot_circle)
    }
    fun iosWifiLin2(type: String?) {
        this.type = type
        state = 1
        screenTimer = getScreenTimer()
        binding.windowsLinText.text = this.getText(R.string.connect_record_mobile_phone_footage)
        binding.windowsLinFtext.visibility = View.GONE
        binding.mainLin.visibility = View.VISIBLE
        binding.usbClick.isClickable = false
        //binding.viLin.visibility = View.INVISIBLE
        binding.windowReqRecordScreen.visibility = View.GONE
        binding.vdline.visibility = View.GONE
        binding.createLin.visibility = View.GONE
        binding.iosWebview.visibility = View.GONE
        binding.icShouquan.setImageResource(R.mipmap.link_blue_dot_circle)
        binding.vdview.visibility = View.VISIBLE
        binding.wifiLin.visibility = View.VISIBLE
        binding.usbLin.visibility = View.GONE
        binding.usbImg.visibility = View.GONE
        binding.windowsLinTisp.visibility = View.VISIBLE
        binding.windowsLinImage.setImageResource(R.mipmap.link_ios_authorization_screen_recording_prompt_image)
        handler.postDelayed(Runnable { binding.wifiImage.setImageResource(R.mipmap.link_connection_completion_prompt_diagram) }, 100)

    }

    fun androidUsbLin(type: String?) {
        this.type = type
        state = 1
        screenTimer = getScreenTimer()
        binding.windowsLinText.text = this.getText(R.string.connect_record_mobile_phone_footage)
        binding.windowsLinFtext.visibility = View.GONE
        binding.mainLin.visibility = View.VISIBLE
        binding.usbClick.isClickable = false
        binding.windowReqRecordScreen.visibility = View.GONE
        binding.createLin.visibility = View.GONE
        //binding.viLin.visibility = View.INVISIBLE
        binding.vdline.visibility = View.GONE
        binding.vdview.visibility = View.VISIBLE
        binding.wifiLin.visibility = View.GONE
        binding.wifiImg.visibility = View.GONE
        binding.windowsLinTisp.visibility = View.VISIBLE
        binding.windowsLinImage.setImageResource(R.mipmap.link_android_authorization_screen_casting_prompt_image)
        binding.usbLin.visibility = View.VISIBLE
        handler.postDelayed({ binding.usbImage.setImageResource(R.mipmap.link_connection_completion_prompt_diagram) }, 100)
        handler.postDelayed({ binding.icShouquan.setImageResource(R.mipmap.link_blue_dot_circle) }, 100)
    }

    fun resetDeviceTypeView(type: String?) {
        this.type = type
        state  = -1
        isconnectionSuccessful = false
        isItDisconnected = true
        countDownTimer?.cancel()
        skipCalibrationTimer?.cancel()
//        binding.calibrationCountdown.visibility = View.GONE
        binding.recalibrate.visibility = View.GONE
        binding.calibrationLin.visibility = View.GONE
        binding.calibrationContent.visibility = View.GONE
        binding.windowReqRecordScreen.visibility = View.GONE
        binding.tvRecordingPermission.visibility = View.GONE
        //binding.viLin.visibility = View.INVISIBLE
        binding.vdline.visibility = View.VISIBLE
        binding.vdview.visibility = View.GONE
        binding.wifiLin.visibility = View.VISIBLE
        binding.wifiImg.visibility = View.VISIBLE
        binding.usbLin.visibility = View.VISIBLE
        binding.usbImg.visibility = View.VISIBLE
        binding.icShouquan.setImageResource(R.mipmap.link_white_dot_circle)
        binding.windowsLinTisp.visibility = View.GONE
        binding.createLin.visibility = View.VISIBLE
        binding.usbClick.isClickable = true
        binding.iosWebview.visibility = View.GONE
    }

    fun iosUsbLin(type: String?) {
        this.type = type
        screenTimer = getScreenTimer()
        binding.windowsLinText.text = this.getText(R.string.connect_record_mobile_phone_footage)
        binding.windowsLinFtext.visibility = View.GONE
        binding.mainLin.visibility = View.VISIBLE
        binding.usbClick.isClickable = false
        binding.windowReqRecordScreen.visibility = View.GONE
        //binding.viLin.visibility = View.INVISIBLE
        binding.vdline.visibility = View.GONE
        binding.vdview.visibility = View.VISIBLE
        binding.wifiLin.visibility = View.GONE
        binding.wifiImg.visibility = View.GONE
        binding.windowsLinTisp.visibility = View.VISIBLE
        binding.windowsLinImage.setImageResource(R.mipmap.link_ios_authorization_screen_recording_prompt_image)
        binding.usbLin.visibility = View.VISIBLE
        handler.postDelayed({ binding.usbImage.setImageResource(R.mipmap.link_connection_completion_prompt_diagram) }, 100)
        handler.postDelayed({ binding.icShouquan.setImageResource(R.mipmap.link_blue_dot_circle) }, 100)
    }
    private fun connectionCompleted() {
        screenTimer?.cancel()
        binding.windowReqRecordScreen.visibility = View.GONE
        binding.tvRecordingPermission.visibility = View.GONE
        binding.windowsLinText.text = this.getText(R.string.connect_connection_completed)
        binding.startLine.visibility = View.VISIBLE
        binding.windowsLinFtext.visibility = View.VISIBLE
        handler.postDelayed(Runnable {Glide.with(this).load(R.mipmap.link_connection_completion_prompt_diagram).into(binding.startImage) }, 200)
        animation()
    }

    private fun determineDeviceStatus() {
        //todo 如果当前连接状态设备类型是null 从新获取连接类型（档位切换时会有问题）
        if (null == androidwifi) {
            Glide.with(this).asGif().load(R.drawable.loading)
                .placeholder(R.drawable.loading).into(binding.usbImage)
            Glide.with(this).asGif().load(R.drawable.loading)
                .placeholder(R.drawable.loading).into(binding.wifiImage)
            Glide.with(this).asGif().load(R.drawable.loading)
                .placeholder(R.drawable.loading).into(binding.startImage)
            when (LinkSdkModel.devicesEventstatus) {
                CommonData.DEVICE_TYPE_AOA-> {
                    type = "android"
                    androidwifi = "android_aoa"
                    connectstate = CommonData.DEVICE_TYPE_AOA
                    androidUsbLin("android")
                }
                CommonData.DEVICE_TYPE_WIFI_ANDROID-> {
                    type = "android"
                    androidWifiLin("android")
                    connectstate = CommonData.DEVICE_TYPE_WIFI_ANDROID
                    androidwifi = "android_wifi"
                }
                CommonData.DEVICE_TYPE_WIFI_IOS-> {
                    type = "ios"
                    androidwifi = "ios_wifi"
//                    iosWifiLin("ios")
                    val deviceSet = HidUtil.getConnectedBluetoothDevices();
                    if (deviceSet.isEmpty()){
                        iosWifiLin2("ios")
                    }else{
                        // 检查蓝牙设备是否与互联手机匹配
                        val isBTPairedWithPhone = SdkViewModel.getInstance().isBTPairedWithPhone();
                        if (isBTPairedWithPhone) {
                            handler.postDelayed({iosCalibrationLin()}, 100)
                        } else {
                            iosWifiLin2("ios")
                        }
                    }
                }
                CommonData.DEVICE_TYPE_EAP-> {
                    type = "ios"
                    androidwifi = "ios_eap"
                    iosUsbLin("ios")
                }
            }
        }

    }

    private var vsInflated: View? = null

    private fun showAuthErr(msg: String?) {
        if (vsInflated == null) {
            vsInflated = binding.vsAuth.inflate()
        }
        val tv: TextView = vsInflated!!.findViewById<TextView>(R.id.tv_auth)
        tv.text = msg
    }

    fun animation() {
        when (type) {
            "android" -> {
                isconnectionSuccessful = true
                Glide.with(this).load(R.mipmap.link_connection_successful)
                    .into(binding.windowsLinImage)
                if (!isShowButtonPage){
                    androidAnimation()
                }
            }

            "ios" -> {
                state = 2
                binding.closeText.isClickable = true
                LinkLog.d(TAG,"permission state3333 = $state")
                binding.windowsLinTisp.visibility = View.GONE
                binding.scrollView.scrollTo(0,0)
                binding.iosWebview.visibility = View.VISIBLE
                 countDownTimer = getCountDownTimer()
                binding.scrollView.setOnTouchListener(OnTouchListener { view, motionEvent ->
                    countDownTimer!!.cancel()
                    binding.closeText.text = getText(R.string.connect_close_window)
                    false
                })
                binding.closeText.setOnClickListener(View.OnClickListener { view: View? ->
                    binding.closeText.isClickable = false
                    countDownTimer!!.cancel()
                    if (!isShowButtonPage){
                        IosAnima()
                    }

//                    devicesEvent.postValue(DevicesType("ios"))
                })
            }
        }
    }
    private fun getCountDownTimer(): CountDownTimer {
        val countDownTimer: CountDownTimer = object : CountDownTimer(5100, 1000) {
            override fun onTick(l: Long) {
                state == 2
                binding.closeText.text = "关闭窗口 (" + l / 1000 + ")"
            }
            override fun onFinish() {
                binding.closeText.text = getText(R.string.connect_close_window)
                isconnectionSuccessful = true
                if (!isShowButtonPage){
                    IosAnima()
                }
            }
        }
        countDownTimer.start()
        return countDownTimer
    }

    private fun IosAnima() {
           val animationSet = getAnimationSet()
           animationSet.setAnimationListener(object : Animation.AnimationListener {
               override fun onAnimationStart(animation: Animation) {}
               override fun onAnimationEnd(animation: Animation) {
                   if (!bannerIsShow){
                       binding.rightLin.visibility = View.INVISIBLE
                      handler.postDelayed({
                           if (LinkUtil.isConnected()){
                               PopWinServiceUtils.showPopWin(this@MainActivity,androidwifi)
                               handler.postDelayed({
                                   binding.phoneLin.visibility = View.INVISIBLE
                                   finish()},50)
                           }else{
                             initview()
                           }

                       }, 100)
                   }
               }

               override fun onAnimationRepeat(animation: Animation) {}
           })
           // 开始动画
           binding.rightLin.startAnimation(animationSet)
    }

    private fun getAnimationSet(): AnimationSet {
        val scaleAnimation = ScaleAnimation(
            1f,
            0f,
            1f,
            0f,
            Animation.RELATIVE_TO_SELF,
            -0.2f,
            Animation.RELATIVE_TO_SELF,
            0.9f
        )
        val alphaAnimation = AlphaAnimation(1.0f, 0.0f)
        val animationSet = AnimationSet(true)
        animationSet.addAnimation(scaleAnimation)
        animationSet.addAnimation(alphaAnimation)
        animationSet.duration = 500
        return animationSet
    }

    private fun androidAnimation() {
        val animationSet = getAnimationSet()
        animationSet.setAnimationListener(object : Animation.AnimationListener {
            override fun onAnimationStart(animation: Animation) {}
            override fun onAnimationEnd(animation: Animation) {
                 if (!bannerIsShow){
                 binding.rightLin.visibility = View.INVISIBLE
                 handler.postDelayed(Runnable {  PopWinServiceUtils.showPopWin(this@MainActivity,androidwifi)
                     handler.postDelayed({
                         binding.phoneLin.visibility = View.INVISIBLE
                         finish()},50)
                 }, 100)

                 }
            }

            override fun onAnimationRepeat(animation: Animation) {}
        })
        // 开始动画
        binding.rightLin.startAnimation(animationSet)
    }
    private fun handleButtonClick() {
        val clickTime = SystemClock.uptimeMillis()
        // 检测点击次数是否达到目标
        if (clickTime - lastClickTime < DOUBLE_CLICK_TIME_THRESHOLD) { // 间隔小于1秒认为是连续点击
            clickCount++
        } else {
            clickCount = 0
        }
        lastClickTime = clickTime

        if (clickCount == REQUIRED_CLICKS) {
            performAction()
        }
    }

    private fun performAction() {
        // 在这里执行连续点击达到后的逻辑
        // 例如：显示信息或开始一个新活动
        finish()
        LinkUtil.destoryServices(this)
//        PopWinServiceUtils.destroyPopWin(this)
//        Process.killProcess(Process.myPid());
//        exitProcess(0);
    }

    override fun onSurfaceTextureAvailable(surface: SurfaceTexture, width: Int, height: Int) {
        // 创建 MediaPlayer 并设置数据源
        prepareAndStartPlayback()
    }

    override fun onSurfaceTextureSizeChanged(surface: SurfaceTexture, width: Int, height: Int) {
        // 处理 SurfaceTexture 尺寸变化
    }

    override fun onSurfaceTextureDestroyed(surface: SurfaceTexture): Boolean {
        // 释放 MediaPlayer 资源
        mediaPlayer?.release()
        mediaPlayer = null
        return true
    }

    override fun onSurfaceTextureUpdated(surface: SurfaceTexture) {
        // 处理 SurfaceTexture 更新
    }
    private fun prepareAndStartPlayback() {
        // 创建 MediaPlayer 并设置数据源
        val videoPath = "android.resource://${packageName}/${R.raw.welinkvideo}"
        mediaPlayer = MediaPlayer().apply {
            try {
                setDataSource(this@MainActivity, Uri.parse(videoPath))
                setSurface(Surface(binding.guideTextureView.surfaceTexture))
                prepareAsync()
                setOnPreparedListener {
                    // 视频准备完成后，可以开始播放
                    start()
                }
                setOnCompletionListener {
                    // 视频播放完毕后重新开始播放
                    start()
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    private fun getScreenTimer(): CountDownTimer {
        screenTimer?.cancel()
        val countDownTimer: CountDownTimer = object : CountDownTimer(15000, 1000) {
            override fun onTick(l: Long) {
            }
            override fun onFinish() {
               binding.tvRecordingPermission.visibility = View.VISIBLE
            }
        }
        countDownTimer.start()
        return countDownTimer
    }
    fun isFastClick(): Boolean {
        val flag: Boolean
        val curClickTime = System.currentTimeMillis()
        flag = (curClickTime - buttonlastClickTime) >= MIN_CLICK_DELAY_TIME
        buttonlastClickTime = curClickTime
        return flag
    }

    fun startDevActivity(){
        this.startActivity(Intent(this, DevActivity::class.java))
    }


    private fun skipCalibrationTimer(): CountDownTimer {
        skipCalibrationTimer?.cancel()
        val countDownTimer: CountDownTimer = object : CountDownTimer(5100, 1000) {
            override fun onTick(l: Long) {
//                binding.calibrationCountdown.text = "${l/1000}"
                binding.recalibrate.text = " 重新校准（${l/1000}）"
            }
            override fun onFinish() {
                iosWifiLin("ios")
            }
        }
        countDownTimer.start()
        return countDownTimer
    }

    private fun startDevClick() {
        val clickTime = SystemClock.uptimeMillis()
        // 检测点击次数是否达到目标
        if (clickTime - lastClickTime < DOUBLE_CLICK_TIME_THRESHOLD) { // 间隔小于1秒认为是连续点击
            clickCountDev++
        } else {
            clickCountDev = 0
        }
        lastClickTime = clickTime

        if (clickCountDev == REQUIRED_CLICKS) {
            startDevActivity()
        }
    }
}