package com.autoai.wdlink.hu.network.converter

import com.autoai.wdlink.hu.ComponentName
import com.autoai.wdlink.hu.castwindow.LinkLog
import com.google.gson.Gson
import com.google.gson.TypeAdapter
import okhttp3.FormBody
import okhttp3.RequestBody
import okio.Buffer
import retrofit2.Converter
import java.io.IOException
import java.io.OutputStreamWriter
import java.io.Writer
import java.nio.charset.Charset

internal class FieldRequestBodyConverter<T>(
    private val gson: Gson,
    private val adapter: TypeAdapter<T>
) : Converter<T, RequestBody> {
    val TAG = "${ComponentName.Network}FieldRequestBodyConverter"
    @Throws(IOException::class)
    override fun convert(value: T): RequestBody {
        val buffer = Buffer()
        val writer: Writer = OutputStreamWriter(buffer.outputStream(), UTF_8)
        val jsonWriter = gson.newJsonWriter(writer)
        adapter.write(jsonWriter, value)
        jsonWriter.close()

        val params = toMapWithGson(buffer.readString(UTF_8))

        val builder = FormBody.Builder()
        params?.forEach { (t, u) ->
            builder.add(t, u.toString())
        }
        return builder.build()
    }

    private fun toMapWithGson(gsonStr: String): Map<String, Any?>? {
        return try {
            LinkLog.i(TAG, "toMapWithGson: gsonStr = $gsonStr")
            gson.fromJson<Map<String, Any?>>(gsonStr, Map::class.java)
        } catch (e: Exception) {
            LinkLog.e(TAG, e, "toMapWithGson: ")
            null
        }.also { LinkLog.i(TAG, "toMapWithGson: return: $it") }
    }

    companion object {
        private val UTF_8: Charset = Charset.forName("UTF-8")
    }
}
