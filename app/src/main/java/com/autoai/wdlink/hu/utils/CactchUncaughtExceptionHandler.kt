package com.autoai.wdlink.hu.utils

import android.R.attr.tag
import android.content.Context
import android.util.Log
import com.autoai.wdlink.hu.sdk.LinkUtil
import java.io.File
import java.io.FileWriter
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale


/**
 * <AUTHOR> zhanggc
 * @description : java类作用描述
 * @date : 2025/4/9 10:40
 * @version : 1.0
 */
class CactchUncaughtExceptionHandler {
    var isSaveFile = false

    /**
     * 功能描述
     *
     *
     * java层捕获全局未捕获的日志信息
     * 该日志一直打开，不进行关闭，方便出现异常后获取日志信息
     */
    fun registerUncaughtExceptionHandler(
        context: Context,
        saveFile: Boolean
    ) {
        isSaveFile = saveFile
        val defaultUncaughtExceptionHandler = Thread.getDefaultUncaughtExceptionHandler()
        var customHandler =
            Thread.UncaughtExceptionHandler { thread: Thread?, exception: Throwable? ->
                uncaughtException(
                    context, thread,
                    exception,
                    defaultUncaughtExceptionHandler
                )
            }

        Thread.setDefaultUncaughtExceptionHandler(customHandler)
    }

    fun uncaughtException(
        context: Context,
        thread: Thread?,
        exception: Throwable?,
        defaultUncaughtExceptionHandler: Thread.UncaughtExceptionHandler?
    ) {
        e("应用崩溃", exception)
        if(isSaveFile) {
            val ExternalFilesDir = context.applicationContext.getExternalFilesDir(null)
            val df = SimpleDateFormat("yyyyMMdd_HH_mm_ss", Locale.getDefault())
            val date = df.format(Date())
            val filePath =
                ExternalFilesDir!!.absolutePath + File.separator + "exception_" + date + ".txt"
            val writer: FileWriter
            try {
                writer = FileWriter(filePath)
                writer.write("应用崩溃: " + Log.getStackTraceString(exception))
                writer.flush()
                writer.close()
            } catch (e: IOException) {
                e("UncaughtExceptionHandler", e)
            }
        }
        try {
            LinkUtil.destoryServices(context)
        } catch (e: Exception) {
            e("UncaughtExceptionHandler", e)
        }
        defaultUncaughtExceptionHandler?.uncaughtException(thread, exception)
    }

    private fun addStackTrace(msg: String): String? {
        val elements = Thread.currentThread().stackTrace
        for (index in 5 downTo 0) {
            if (elements.size <= index) {
                continue
            }
//            if (logName == elements[index].fileName) {
            val element = elements[index + 1]
            return "(" + element.fileName + ":" + element.lineNumber + ")" + msg
//            }
        }
        return msg
    }

    fun e(msg: String?, t: Throwable?) {
        var msg = msg
        msg = addStackTrace(msg!!)
        Log.e("Carsh", msg, t)
    }
}