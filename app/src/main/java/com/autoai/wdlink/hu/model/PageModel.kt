package com.autoai.wdlink.hu.model

import com.autoai.welink.macro.WL_API_PAGE_TYPE
import com.autoai.welinkapp.model.UIResponder

data class PageModel(val page: Int, val param: Any?){
    override fun toString(): String {
        return "PageModel(page=${page.toPageStr()}, param=$param)"
    }

    companion object{
        fun Int.toPageStr(): String {
            @Suppress("DUPLICATE_LABEL_IN_WHEN")
            return "$this: " + when (this){
                UIResponder.SOURCE_OFF, WL_API_PAGE_TYPE.SOURCE_OFF -> "SOURCE_OFF: Page source off(background)"
                UIResponder.INITIAL_PAGE, WL_API_PAGE_TYPE.INITIAL_PAGE -> "INITIAL_PAGE: Page none"
                UIResponder.HELP_PAGE, WL_API_PAGE_TYPE.HELP_PAGE -> "HELP_PAGE: Page help"
                UIResponder.CONNECTING_PAGE_OFFICIAL_ACCOUNT, WL_API_PAGE_TYPE.CONNECTING_PAGE_OFFICIAL_ACCOUNT -> "CONNECTING_PAGE_OFFICIAL_ACCOUNT: Page official account"
                UIResponder.CONNECTING_PAGE, WL_API_PAGE_TYPE.CONNECTING_PAGE -> "CONNECTING_PAGE: Page connecting"
                UIResponder.ERR_PAGE_SDK, WL_API_PAGE_TYPE.ERR_PAGE_SDK -> "ERR_PAGE_SDK: Page sdk error"
                UIResponder.ERR_PAGE_CONNECT, WL_API_PAGE_TYPE.ERR_PAGE_CONNECT -> "ERR_PAGE_CONNECT: Page connect error"
                UIResponder.ERR_PAGE_HEART, WL_API_PAGE_TYPE.ERR_PAGE_HEART -> "ERR_PAGE_HEART: Page heart error"
                UIResponder.RUNNING_PAGE_SAFE_DRIVING, WL_API_PAGE_TYPE.RUNNING_PAGE_SAFE_DRIVING -> "RUNNING_PAGE_SAFE_DRIVING: Page safe driving"
                UIResponder.RUNNING_PAGE_WECHAT_QRCODE, WL_API_PAGE_TYPE.RUNNING_PAGE_WECHAT_QRCODE -> "RUNNING_PAGE_WECHAT_QRCODE: Page wechat qrcode"
                UIResponder.RUNNING_PAGE_WECHAT_CONFIRM_LOGIN, WL_API_PAGE_TYPE.RUNNING_PAGE_WECHAT_CONFIRM_LOGIN -> "RUNNING_PAGE_WECHAT_CONFIRM_LOGIN: Page wechat login confirm"
                UIResponder.RUNNING_PAGE_WECHAT_LOGIN_SUCCESS, WL_API_PAGE_TYPE.RUNNING_PAGE_WECHAT_LOGIN_SUCCESS -> "RUNNING_PAGE_WECHAT_LOGIN_SUCCESS: Page wechat login success"
                UIResponder.RUNNING_PAGE_LIMIT, WL_API_PAGE_TYPE.RUNNING_PAGE_LIMIT -> "RUNNING_PAGE_LIMIT: Page limit"
                UIResponder.RUNNING_PAGE, WL_API_PAGE_TYPE.RUNNING_PAGE -> "RUNNING_PAGE: Page running"
                UIResponder.NUM, WL_API_PAGE_TYPE.NUM -> "NUM: Page max num, do not care"
                UIResponder.SHOW_USB_INTERFACE_DIALOG -> "SHOW_USB_INTERFACE_DIALOG: usb 互联切换  无线到有线弹框 显示"
                UIResponder.DISMISS_USB_INTERFACE_DIALOG -> "DISMISS_USB_INTERFACE_DIALOG: usb 互联切换  无线到有线弹框  关闭"
                UIResponder.SCREEN_PAGE_MIRRORING -> "SCREEN_PAGE_MIRRORING: 开始投屏页面"
                UIResponder.SCREEN_REQ_RECORD_SCREEN -> "SCREEN_REQ_RECORD_SCREEN: 请求录屏，手机侧取消状态"
                else -> "NA"
            }
        }
    }
}