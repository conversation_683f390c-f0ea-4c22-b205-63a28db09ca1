package com.autoai.wdlink.hu.module.hidscreen;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;

import android.view.MotionEvent;
import android.view.ScaleGestureDetector;
import android.view.ViewConfiguration;
import android.widget.RelativeLayout;

import com.autoai.wdlink.hu.frame.utils.PopWinService;

public class MultiTouchView extends RelativeLayout {
    private PopWinService mPopWinService;
    private int originX;
    private int originY;
//    private int originWidth;
//    private int originHeight;
    private IDragListener iDragListener;

    public MultiTouchView(Context context) {
        super(context);
        init(context);
    }

    public MultiTouchView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public MultiTouchView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    public MultiTouchView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init(context);
    }

    private ScaleGestureDetector scaleGestureDetector;
    private ScaleListener scaleListener;
    private void init(Context context) {
        scaleListener = new ScaleListener();
        scaleGestureDetector = new ScaleGestureDetector(context, scaleListener);

    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        return super.onInterceptTouchEvent(ev);
    }

    private int startX;
    private int startY;
    private Boolean isLeft = null;
    private boolean isDrag = false;
    private boolean isScale = false;
    private boolean isMultiTouch = false;
//    ViewConfiguration.getMultiPressTimeout();

    @SuppressLint("NewApi")
    @Override
    public boolean dispatchTouchEvent(MotionEvent event) {
        requestDisallowInterceptTouchEvent(true);
        if (event.getPointerCount() > 3){
            scaleGestureDetector.onTouchEvent(event);
        }
        // 第一个手指
        if (event.getActionMasked() == MotionEvent.ACTION_DOWN) {
            originX = mPopWinService.getCurrentX();
            originY = mPopWinService.getCurrentY();

            startX = (int) event.getRawX(0);
            startY = (int) event.getRawY(0);
            return super.dispatchTouchEvent(event);
        }
        // 第2，3，4个手指
        if (event.getActionMasked() == MotionEvent.ACTION_POINTER_DOWN) {
            if (event.getPointerCount() > 3) {
                isMultiTouch = true;
            }
            if (event.getPointerCount() > 3) {
                isScale = true;
                if (iDragListener != null) {
                    iDragListener.onDown();
                }
            }


        }
        if (event.getActionMasked() == MotionEvent.ACTION_MOVE) {
            int deltaX = (int) (event.getRawX(0) - startX);
            int deltaY = (int) (event.getRawY(0) - startY);
            if (event.getPointerCount() == 2 && !isScale) {
                // 拖拽与缩放只能保留一种操作
                if (Math.abs(deltaX) > 50 && Math.abs(deltaY) <= 10) {
                    isDrag = true;
                    isMultiTouch = true;
                }
                // 拖拽
                if (iDragListener != null && isDrag) {
                    int currentX = originX + deltaX;
                    int currentY = originY + deltaY;
                    isLeft = deltaX <= 0;
                    iDragListener.onDrag(isLeft, currentX, currentY);
                }
            }
        }

        // 手指离开
        if (event.getActionMasked() == MotionEvent.ACTION_POINTER_UP || event.getActionMasked() == MotionEvent.ACTION_UP || event.getActionMasked() == MotionEvent.ACTION_CANCEL) {
            // 拖拽
            if (iDragListener != null && isDrag) {
                iDragListener.onDragUp(isLeft);
            }

            isDrag = false;
            isScale = false;
            isMultiTouch = false;
            return super.dispatchTouchEvent(event);
        }
        if (!isMultiTouch) {
            return super.dispatchTouchEvent(event);
        } else {
            return true;
        }
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        return super.onTouchEvent(event);
    }

    public void setOnDragWindowListener(PopWinService popWinService, IDragListener dragListener) {
        mPopWinService = popWinService;
        iDragListener = dragListener;
        scaleListener.setOnScaleListener(iDragListener);
    }

    public interface IDragListener {
        void onDrag(Boolean isLeft, int currentX, int currentY);

        void onDragUp(Boolean isLeft);

        void onDown();
        void scale(boolean isBig, float spanX, float spanY);

        void onCancel(float spaX, float spanY);
    }
}
