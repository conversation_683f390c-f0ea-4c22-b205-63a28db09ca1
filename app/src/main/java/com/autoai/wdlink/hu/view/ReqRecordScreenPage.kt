package com.autoai.wdlink.hu.view

import android.content.Context
import android.os.Process
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import android.widget.TextView
import com.autoai.wdlink.hu.R
import com.autoai.wdlink.hu.sdk.LinkUtil
import kotlin.system.exitProcess


class ReqRecordScreenPage @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    private val button1: TextView
    private val button2: TextView
    init {
        orientation = VERTICAL
        LayoutInflater.from(context).inflate(R.layout.page_reauthorization, this, true)
        button1 = findViewById(R.id.btn_reauthorization)
        button2 = findViewById(R.id.btn_exit)
        button2.setOnClickListener {
           button2.post {
               LinkUtil.destoryServices(context) }
        }
    }

    fun setButton1ClickListener(listener: OnClickListener) {
        button1.setOnClickListener(listener)
    }

}