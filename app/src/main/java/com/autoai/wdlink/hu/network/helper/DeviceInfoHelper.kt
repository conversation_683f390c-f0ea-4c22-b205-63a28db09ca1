package com.autoai.wdlink.hu.network.helper

import android.content.Context
import com.blankj.utilcode.util.DeviceUtils
import com.blankj.utilcode.util.Utils
import java.util.UUID

/**
 * 获取网络请求需要的信息
 */
class DeviceInfoHelper {
    companion object {
        val getVinWithLazyCache: String by lazy { SystemPropertyUtils.getSystemProperty("persist.vehicle.version.vin") }

        val getAndroidIdWithLazyCache: String by lazy { DeviceUtils.getAndroidID() }

        val getMacAddressWithLazyCache: String by lazy { DeviceUtils.getMacAddress() }

        fun getCidFromVinOrAndroidIdOrMacAdd(): String {
            if(getVinWithLazyCache.isNotEmpty()){
                return getVinWithLazyCache
            }
            if(getAndroidIdWithLazyCache.isNotEmpty()){
                return getAndroidIdWithLazyCache
            }
            if(getMacAddressWithLazyCache.isNotEmpty()){
                return getMacAddressWithLazyCache
            }
            return getUuidOrCreateAndCache()
        }

        /**
         * 生成唯一值
         */
        @Suppress("LocalVariableName")
        private fun getUuidOrCreateAndCache(): String{
            val SP_NAME_DEVICE_INFO_HELPER = "device_info_helper"
            val SP_KEY_DEVICE_UUID = "device_unique_identification"
            val sharedPreferences = Utils.getApp().getSharedPreferences(SP_NAME_DEVICE_INFO_HELPER, Context.MODE_PRIVATE)
            var uuid = sharedPreferences.getString(SP_KEY_DEVICE_UUID, "") ?: ""
            if(uuid.isEmpty()){
                uuid = UUID.randomUUID().toString()
                sharedPreferences.edit().putString(SP_KEY_DEVICE_UUID, uuid).apply()
            }
            return uuid
        }
    }
}