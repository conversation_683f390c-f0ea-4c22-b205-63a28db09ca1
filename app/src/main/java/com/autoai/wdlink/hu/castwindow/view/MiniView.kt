package com.autoai.wdlink.hu.castwindow.view

import android.annotation.SuppressLint
import android.graphics.PixelFormat
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import com.autoai.wdlink.hu.ComponentName
import com.autoai.wdlink.hu.MyApplication
import com.autoai.wdlink.hu.R
import com.autoai.wdlink.hu.castwindow.LinkLog
import com.autoai.wdlink.hu.castwindow.framework.windowManager
import com.autoai.wdlink.hu.castwindow.scope.CastWindowState
import com.autoai.wdlink.hu.castwindow.scope.MiniPositionMode
import com.autoai.wdlink.hu.castwindow.scope.MiniPositionMode.Companion.toMiniModeStr
import com.autoai.wdlink.hu.castwindow.view.CastWindowStateManager.switchState
import com.autoai.wdlink.hu.databinding.ModuleMiniViewBinding
import java.util.concurrent.atomic.AtomicInteger
import kotlin.math.max

class MiniView {
    private val TAG = "${ComponentName.WindowSwitcher}MiniView"

    private val miniView = ModuleMiniViewBinding.inflate(LayoutInflater.from(MyApplication.application))
    private val miniViewParams = WindowManager.LayoutParams(
        WindowManager.LayoutParams.WRAP_CONTENT,
        WindowManager.LayoutParams.WRAP_CONTENT,
        WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
        WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN
                or WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
                or WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                or WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH
                or WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON,
        PixelFormat.TRANSLUCENT
    )
    var position : Int = -1
    var mode : Int = -1

    init {
        miniViewParams.gravity = Gravity.START or Gravity.TOP
        miniViewParams.x = 0
        // Bar颜色
        val colorNum = num++ % 4
        var barColor = R.color.bar1
        when (colorNum) {
            1 -> barColor = R.color.bar2
            2 -> barColor = R.color.bar3
            3 -> barColor = R.color.bar4
        }
//        miniView.bar.backgroundTintList = ColorStateList.valueOf(miniView.root.context.resources.getColor(barColor))
    }

    fun show(@MiniPositionMode mode: Int, miniY: Int, position: Int) {
        LinkLog.i(TAG, "MiniCastState::show: mode = " + mode.toMiniModeStr())
        LinkLog.i(TAG, "MiniCastState::show: miniY = $miniY")
        miniViewParams.y = miniY
        this.position = position
        this.mode = mode
        if (mode == MiniPositionMode.LEFT) {
            miniView.barView.setImageResource(R.mipmap.link_mini_image_left)
            miniViewParams.gravity = Gravity.START or Gravity.TOP
        } else {
            miniView.barView.setImageResource(R.mipmap.link_mini_image_right)
            miniViewParams.gravity = Gravity.END or Gravity.TOP
        }
        // 设置监听控制
        setBarListener()
        // 显示
        kotlin.runCatching { windowManager?.addView(miniView.root, miniViewParams) }
            .onSuccess { LinkLog.i(TAG, "MiniCastState::show: addView success") }
            .onFailure { LinkLog.e(TAG, it, "MiniCastState::show addView Error: ") }
    }

    fun hide() {
        LinkLog.i(TAG, "hide: invoke")
        kotlin.runCatching { windowManager?.removeView(miniView.root) }
            .onSuccess { LinkLog.i(TAG, "MiniCastState::hide: removeView success") }
            .onFailure { LinkLog.e(TAG, it, "MiniCastState::hide: removeView success") }
    }

    // 设置监听控制
    @SuppressLint("ClickableViewAccessibility")
    private fun setBarListener() {
        val yy = AtomicInteger()
        val oldYy = AtomicInteger()
        miniView.root.setOnTouchListener { v: View?, event: MotionEvent ->
            when (event.actionMasked) {
                MotionEvent.ACTION_DOWN -> {
                    yy.set(event.rawY.toInt())
                    oldYy.set(miniViewParams.y)
                }

                MotionEvent.ACTION_MOVE -> {
                    miniViewParams.y = max((oldYy.get() + event.rawY.toInt() - yy.get()).toDouble(), 100.0).toInt()
                    windowManager?.updateViewLayout(miniView.root, miniViewParams)
                }

                MotionEvent.ACTION_UP -> {
                    val flipY = (yy.get() - event.rawY).toInt()
                    if (flipY * flipY < 16) {
                        switchState(CastWindowState.SMALL, SmallShowBean(mode,  position))
                    }
                }
            }
            true
        }
    }

    companion object {
        private var num = 0
    }
}
