package com.autoai.wdlink.hu.lifecycle

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.os.Build
import android.util.Log
import android.widget.Toast
import com.autoai.wdlink.hu.Logger
import com.autoai.wdlink.hu.R
import com.autoai.wdlink.hu.auth.AuthRepository


class NetworkMonitor(context: Context) {
    private val connectivityManager: ConnectivityManager?
    private val networkCallback: ConnectivityManager.NetworkCallback?
    private final val DISCONNECT = -2L
    private final val WIFI_CONNECT = 1L
    private final val CELLULAR_CONNECT = 2L
    private var tips:String? = null
    init {
        connectivityManager =
            context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

        // 创建一个NetworkRequest，监听所有网络类型
        val networkRequest = NetworkRequest.Builder().build()
        networkCallback = object : ConnectivityManager.NetworkCallback() {
            override fun onAvailable(network: Network) {
                Logger.d("NetworkMonitor:网络连接可用")
            }

            override fun onLost(network: Network) {
                AuthRepository.mAuthTrigger.value = DISCONNECT
                var toast = context.resources.getString(R.string.network_disconnect)
                Logger.d("NetworkMonitor:$toast")
                tips = toast
                Toast.makeText(context, toast, Toast.LENGTH_SHORT).show()
            }

            override fun onCapabilitiesChanged(
                network: Network, networkCapabilities: NetworkCapabilities
            ) {
                if (networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)) {
                    Logger.d("NetworkMonitor:Wi-Fi已连接")
                   /* var toast = context.resources.getString(R.string.network_connect_wifi)
                    if(toast != tips) {
                        tips = toast
                        Toast.makeText(
                            context,
                            context.resources.getString(R.string.network_connect_wifi),
                            Toast.LENGTH_SHORT
                        ).show()
                    }*/
                    AuthRepository.mAuthTrigger.value = WIFI_CONNECT
                } else if (networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)) {
                    Logger.d("NetworkMonitor:移动网络连接")
                   /* var toast = context.resources.getString(R.string.network_connect_wifi)
                    if(toast != tips) {
                        tips = toast
                        Toast.makeText(
                            context,
                            context.resources.getString(R.string.network_connect_4G),
                            Toast.LENGTH_SHORT
                        ).show()
                    }*/
                    AuthRepository.mAuthTrigger.value = CELLULAR_CONNECT
                }
            }
        }

        // 注册网络回调
        connectivityManager.registerNetworkCallback(networkRequest, networkCallback)
    }

    private fun getNetworkType(networkCapabilities: NetworkCapabilities?): String {
        return when {
            networkCapabilities == null -> "未知网络类型"
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> "Wi-Fi"
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> "4G"
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> "以太网"
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_BLUETOOTH) -> "蓝牙网络"
            else -> "未知网络类型"
        }
    }

    // 在不需要监听时，取消注册
    fun unregisterNetworkCallback() {
        if (connectivityManager != null && networkCallback != null) {
            connectivityManager.unregisterNetworkCallback(networkCallback)
        }
    }
}