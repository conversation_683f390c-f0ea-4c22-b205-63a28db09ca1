package com.autoai.wdlink.hu.viewmodel

import com.autoai.wdlink.hu.frame.BaseViewModel
import com.autoai.wdlink.hu.sdk.LinkSdkModel
import kotlinx.coroutines.Dispatchers

class TimeoutViewModel : BaseViewModel() {

    fun help() {
        coroutineLauncher(context = Dispatchers.IO) {
            LinkSdkModel.help()
        }
    }

    fun iKnow() {
        coroutineLauncher(context = Dispatchers.IO) {
            LinkSdkModel.iKnow()
        }
    }
}