package com.autoai.wdlink.hu.network.retrofit

import com.google.gson.annotations.SerializedName
import kotlinx.serialization.Serializable

/**
 * 网络请求返回基础数据结构
 */
@Serializable
data class ResponseProtocol<T>(
    @SerializedName("code")
    val code: Int,
    @SerializedName("message")
    val message: String? = "",
    @SerializedName("data")
    val data: T?,
){
    override fun toString(): String {
        return "ResponseProtocol(code='$code', message=$message, data=$data)"
    }

    fun isSuccess(): Boolean{
        return code == 200
    }
}
