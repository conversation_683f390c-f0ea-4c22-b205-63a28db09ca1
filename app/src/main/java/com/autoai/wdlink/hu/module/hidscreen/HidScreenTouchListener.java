package com.autoai.wdlink.hu.module.hidscreen;

import android.annotation.SuppressLint;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;

import com.autoai.avslinkhid.api.HidUtil;


/**
 * <AUTHOR>
 */
public class HidScreenTouchListener implements View.OnTouchListener{
    private static final String TAG = "HidScreenTouchListener";

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouch(View view, MotionEvent motionEvent) {
        if(!view.isEnabled()){
            return false;
        }
        Log.i(TAG, "onTouch: motionEvent.getActionMasked() = "+ motionEvent.getActionMasked() + ", x =" + motionEvent.getX() + ", y = " + motionEvent.getY());
        HidUtil.onTouch(view,motionEvent);
//        if (motionEvent.getActionMasked() == MotionEvent.ACTION_UP
//                || motionEvent.getActionMasked() == MotionEvent.ACTION_CANCEL) {
//            LinkHost.checkIsPlayVideo(CarDriveSafeRuleControl.getInstance().getCurDriveState());
//        }
        return true;
    }
}
