package com.autoai.wdlink.hu.sdk

import android.graphics.Matrix
import android.graphics.SurfaceTexture
import android.util.Log
import android.view.Surface
import android.view.TextureView
import android.view.View
import com.autoai.wdlink.hu.Logger
import com.autoai.wdlink.hu.castwindow.LinkLog
import com.autoai.wdlink.hu.model.DevelopModel
import com.autoai.wdlink.hu.sdk.device.OnVideoFrameListener
import com.autoai.welinkapp.model.SdkViewModel

/**
 * 用于管理渲染视图的核心管理器
 */
class SurfaceHelper() {
    private val TAG = "SurfaceHelper"

    /**
     * 视图层传入的TextureView控件
     */
    private var curTextureView: TextureView? = null
    private var currSurface: Surface? = null

    /**
     * TextureView 刷新率回调
     */
    private var frameListener: OnVideoFrameListener? = null
    private var mFpsEnabled = false
    private var mRttEnabled = false
    private var mRtt = 0

    /**
     * 切换 Surface,投屏选人的surface 的size 发生变化
     */
    fun resetSurface(textureView: TextureView) {
        //-->从布局中获取负责投屏渲染的Texture View
        curTextureView = textureView
        LinkLog.i(
            TAG,
            "SurfaceHelper::resetSurface :" + curTextureView!!.surfaceTexture + ",surfaceTextureListener:" + curTextureView!!.surfaceTextureListener
        )
        //--> 做一次反注册容错
        unRegisterLinkSurface()

        if (curTextureView!!.surfaceTexture != null) {
            registerLinkSurface(curTextureView!!)
        }
        //重新注册监听 ，ios 视频流横竖平切换
        curTextureView!!.surfaceTextureListener = object : TextureView.SurfaceTextureListener {
            override fun onSurfaceTextureAvailable(
                surface: SurfaceTexture,
                width: Int,
                height: Int
            ) {
                LinkLog.i(
                    TAG,
                    "onSurfaceTextureAvailable: surface = $surface, width = $width, height = $height"
                )
                registerLinkSurface(curTextureView!!)
                SdkViewModel.getInstance().isPaused = false
            }

            override fun onSurfaceTextureSizeChanged(
                surface: SurfaceTexture,
                width: Int,
                height: Int
            ) {
                LinkLog.i(
                    TAG,
                    "onSurfaceTextureSizeChanged: surface = $surface, width = $width, height = $height"
                )
            }

            override fun onSurfaceTextureDestroyed(surface: SurfaceTexture): Boolean {
                LinkLog.i(
                    TAG,
                    "SurfaceHelper::resetSurface -> onSurfaceTextureDestroyed: surface = $surface"
                )
                SdkViewModel.getInstance().isPaused = true
                return false
            }

            override fun onSurfaceTextureUpdated(surface: SurfaceTexture) {
                if (mFpsEnabled) {
                    getFps()
                }
            }
        }
    }
    private var mFps = 0
    private var mLastTime: Long = 0

    /**
     * 调试方法，跟踪实际刷新率
     */
    private fun getFps() {
        frameListener?.let {
            mFps++
            val timeStamp = System.currentTimeMillis()
            if (timeStamp - mLastTime >= 1000) {
                LinkLog.i(TAG, "fps ==>$mFps")
                it.onVideoFrame(mFps)
                mFps = 0
                mLastTime = timeStamp
            }
        }
    }

    fun clearSurface() {
        LinkLog.i(TAG, "clearSurface")
        curTextureView?.surfaceTextureListener = null
        unRegisterLinkSurface()
    }

    /**
     * she tips:这个方法将texture view 封装成Surface 会触发解码器开启，currSurface作为解码器的output Surface
     */
    private fun registerLinkSurface(textureView: TextureView) {
        LinkLog.i(TAG, "registerLinkSurface")

        currSurface = Surface(textureView.surfaceTexture)

        //--> she tips:这个方法会触发解码器开启，currSurface作为解码器的output Surface
        LinkSdkModel.registerLinkSurface(currSurface!!, textureView.width, textureView.height)
        LinkLog.i(TAG, "registerLinkSurface: width = " + textureView.width)
        LinkLog.i(TAG, "registerLinkSurface: height = " + textureView.height)

    }

    /**
     * 1.release 掉当前编码器持有的surface，
     * 2.release 掉预先站位的surface
     */
    private fun unRegisterLinkSurface() {
        LinkLog.i(TAG, "unRegisterLinkSurface: currSurface = $currSurface")
        if (currSurface != null) {
            //--> //--> she tips:这个方法会触发解码器关闭
            LinkSdkModel.unRegisterLinkSurface(currSurface!!)
            currSurface = null
        }
    }
    fun setOnVideoFrameListener(listener: OnVideoFrameListener) {
        this.frameListener = listener
    }

    init {
        LinkLog.i(TAG, "SurfaceHelper: init")
        DevelopModel.getInstance().setFpsListener {
            mFpsEnabled = it
            LinkLog.i(TAG, "SurfaceHelper: mFpsEnabled = $mFpsEnabled")
        }
        DevelopModel.getInstance().setRttListener { rttEnabled, rtt ->
            run {
                this.mRttEnabled = rttEnabled
                this.mRtt = rtt
            }
        }
    }
}