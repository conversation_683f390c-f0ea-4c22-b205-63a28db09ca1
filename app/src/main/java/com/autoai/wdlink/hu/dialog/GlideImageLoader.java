package com.autoai.wdlink.hu.dialog;

import android.content.Context;
import android.widget.ImageView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.bumptech.glide.request.RequestOptions;
import com.youth.banner.loader.ImageLoader;

public class G<PERSON><PERSON>mageLoader extends ImageLoader {
    @Override
    public void displayImage(Context context, Object path, ImageView imageView) {
        //Glide
        Glide.with(context).load(path).apply(RequestOptions.bitmapTransform(new RoundedCorners(20))).into(imageView);
    }
}
