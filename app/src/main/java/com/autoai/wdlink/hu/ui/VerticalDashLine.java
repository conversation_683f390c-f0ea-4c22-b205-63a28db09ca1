package com.autoai.wdlink.hu.ui;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.autoai.wdlink.hu.R;


public class VerticalDashLine extends View {

    private Paint paint;
    private Path path;

    public VerticalDashLine(Context context) {
        super(context);
        initViews(context, null, 0, 0);
    }

    public VerticalDashLine(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initViews(context, attrs, 0, 0);
    }

    public VerticalDashLine(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initViews(context, attrs, defStyleAttr, 0);
    }

    public VerticalDashLine(Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initViews(context, attrs, defStyleAttr, defStyleRes);
    }

    private void initViews(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        this.setLayerType(View.LAYER_TYPE_SOFTWARE, null);
        paint = new Paint();
        paint.setColor(ContextCompat.getColor(context, R.color.blue));
//        paint.setColor(Color.RED);
        paint.setStrokeWidth(context.getResources().getDimension(R.dimen.base_1_dp));
        paint.setStyle(Paint.Style.STROKE);
        paint.setAntiAlias(true);
        path = new Path();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }

    @Override
    protected void onDraw(@NonNull Canvas canvas) {
        super.onDraw(canvas);
        int y = 0;
        int pathSegmentLength = (int) getContext().getResources().getDimension(R.dimen.base_7_dp);
        int pathCounts = (int) (getMeasuredHeight() / pathSegmentLength);
        for (int i = 0; i < pathCounts; i++) {
            if (i % 2 != 0) {
                path.moveTo(getMeasuredWidth() / 2, y);
                path.lineTo(getMeasuredWidth()/2,y+pathSegmentLength);
                canvas.drawPath(path,paint);
            }
            y = y +pathSegmentLength;
        }

    }
}