

import android.annotation.SuppressLint
import android.os.Environment
import android.text.TextUtils
import android.util.Log
import java.io.*
import java.util.*

object SystemPropertyUtils {
    var TAG = "SystemPropertyUtils"

    fun getSystemProperty(name: String): String {
        Log.i(TAG, "getSystemProperty: name = $name")
        var prop = getSystemPropertyByShell(name)
        Log.i(TAG, "getSystemProperty: getSystemPropertyByShell = $prop")
        if (!TextUtils.isEmpty(prop)) return prop
        prop = getSystemPropertyByStream(name)
        Log.i(TAG, "getSystemProperty: getSystemPropertyByStream = $prop")
        if (!TextUtils.isEmpty(prop)) return prop
        prop = getSystemPropertyByReflect(name)
        Log.i(TAG, "getSystemProperty: getSystemPropertyByReflect = $prop")
        return prop
    }

    private fun getSystemPropertyByShell(propName: String): String {
        var input: BufferedReader? = null
        try {
            val p = Runtime.getRuntime().exec("getprop $propName")
            input = BufferedReader(InputStreamReader(p.inputStream), 1024)
            val ret = input.readLine()
            if (ret != null) {
                return ret
            }
        } catch (ignore: IOException) {
        } finally {
            if (input != null) {
                try {
                    input.close()
                } catch (ignore: IOException) { /**/
                }
            }
        }
        return ""
    }

    private fun getSystemPropertyByStream(key: String): String {
        try {
            val prop = Properties()
            val `is` = FileInputStream(
                File(Environment.getRootDirectory(), "build.prop")
            )
            prop.load(`is`)
            return prop.getProperty(key, "")
        } catch (ignore: Exception) { /**/
        }
        return ""
    }

    private fun getSystemPropertyByReflect(key: String): String {
        try {
            @SuppressLint("PrivateApi") val clz = Class.forName("android.os.SystemProperties")
            val getMethod = clz.getMethod("get", String::class.java, String::class.java)
            return getMethod.invoke(clz, key, "") as String
        } catch (e: Exception) { /**/
        }
        return ""
    }
}