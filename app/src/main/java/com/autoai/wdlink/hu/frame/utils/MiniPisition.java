package com.autoai.wdlink.hu.frame.utils;

public class MiniPisition {
    int MiniPositionMode;
    int MiniPositionHeight;
    int position;

    public int getPosition() {
        return position;
    }

    public void setPosition(int position) {
        this.position = position;
    }

    public MiniPisition(int miniPositionMode, int miniPositionHeight, int position) {
        MiniPositionMode = miniPositionMode;
        MiniPositionHeight = miniPositionHeight;
        this.position = position;
    }

    public int getMiniPositionMode() {
        return MiniPositionMode;
    }

    public void setMiniPositionMode(int miniPositionMode) {
        MiniPositionMode = miniPositionMode;
    }

    public int getMiniPositionHeight() {
        return MiniPositionHeight;
    }

    public void setMiniPositionHeight(int miniPositionHeight) {
        MiniPositionHeight = miniPositionHeight;
    }
}
