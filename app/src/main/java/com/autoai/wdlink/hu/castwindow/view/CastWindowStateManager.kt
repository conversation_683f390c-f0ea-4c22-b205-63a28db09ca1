package com.autoai.wdlink.hu.castwindow.view

import android.app.ActivityOptions
import android.content.Intent
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.TextureView
import androidx.annotation.MainThread
import com.autoai.wdlink.hu.ComponentName
import com.autoai.wdlink.hu.MyApplication
import com.autoai.wdlink.hu.R
import com.autoai.wdlink.hu.castwindow.LinkLog
import com.autoai.wdlink.hu.castwindow.scope.CastWindowState
import com.autoai.wdlink.hu.castwindow.scope.CastWindowState.Companion.toCastWindowStateStr
import com.autoai.wdlink.hu.sdk.LinkUtil
import com.autoai.wdlink.hu.sdk.SurfaceHelper
import com.autoai.welinkapp.model.HidCallback
import com.autoai.welinkapp.model.SdkViewModel
import java.lang.ref.WeakReference

/**
 * shecw has checked
 * 投屏窗口状态管理，在 mini，small，full 之间切换
 */
object CastWindowStateManager : HidCallback {
    private const val TAG = "${ComponentName.WindowSwitcher}CastWindowStateManager"

    private lateinit var textViewWeakReference: WeakReference<TextureView>

    private val stateMap = mutableMapOf<Int, BaseCastState>()
    private var curState: BaseCastState? = null
    private var rotationChangeInfo: IScreenRotationChange.ScreenRotationChange? = null

    @JvmStatic
    @CastWindowState
    fun getCurrentState(): Int {
        return curState?.state ?: CastWindowState.NONE
    }

    fun getTextureView(): TextureView? = textViewWeakReference.get()

    @JvmStatic
    fun init(textView: TextureView) {
        LinkLog.i(TAG, "CastWindowStateManager::init: textView = $textView")
        this.textViewWeakReference = WeakReference(textView)
        putState(CastWindowState.MINI, MiniCastState())
    }

    @JvmStatic
    fun putState(@CastWindowState state: Int, castState: BaseCastState) {
        LinkLog.i(TAG, "CastWindowStateManager::putState: state = ${state.toCastWindowStateStr()}, castState = $castState")
        if (stateMap.containsKey(state)) {
            LinkLog.e(TAG, "CastWindowStateManager::putState: has containsKey ${state.toCastWindowStateStr()}")
        }
        stateMap[state] = castState
    }

    @JvmStatic
    fun <T : BaseCastState> getState(@CastWindowState state: Int): T? {
        @Suppress("UNCHECKED_CAST")
        return stateMap[state] as? T
    }

    fun removeState(@CastWindowState state: Int): BaseCastState? {
        if (stateMap[state] === curState) {
            LinkLog.e(TAG, "CastWindowStateManager::remove current state!")
        }
        return stateMap.remove(state).also {
            LinkLog.i(
                TAG,
                "CastWindowStateManager::removeState: state = ${state.toCastWindowStateStr()}, removeValue = $it"
            )
        }
    }

    @JvmStatic
    fun close() {
        LinkLog.i(TAG, "CastWindowStateManager::close: invoke")
        //退出所有状态
        curState?.onExitState()
        curState = null
        stateMap.clear()
        textViewWeakReference.clear()
        rotationChangeInfo = null
    }

    @JvmStatic
    fun switchFullState(position: Int) {
        LinkLog.i(TAG, "CastWindowStateManager::switchFullState: invoke")
        // fixme：shecw 认为冗余调用，移除，验证无误后彻底移除
//        switchState(CastWindowState.FULL)
        //打开全屏页面
        with(MyApplication.application) {
            val options = ActivityOptions.makeCustomAnimation(this, R.anim.fade_in, R.anim.fade_out)
            startActivity(Intent(this, FullscreenActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
                putExtra("position", position)
            }, options.toBundle())
        }
    }

    /**
     * 在发生切换状态后，出发 旧状态 -> 新状态的回调  比如从悬浮窗 -> 全屏
     */
    @JvmStatic
    @JvmOverloads
    fun switchState(@CastWindowState state: Int, args: Any? = null) {
        LinkLog.i(
            TAG,
            "CastWindowStateManager::switchState: curState = ${curState}, state = ${state.toCastWindowStateStr()}, args = $args, rotationChangeInfo = $rotationChangeInfo"
        )
        val preState = curState
        curState = stateMap[state]
        if (preState === curState) {
            curState?.onReenterState()
            return
        }

        preState?.onExitState()
        curState?.onEnterState(args)


        rotationChangeInfo?.run {
            onScreenRotationChange(width, height, angle, mode, platform)
        }
    }

    @MainThread
    override fun onScreenRotationChange(
        width: Int,
        height: Int,
        angle: Int,
        mode: Int,
        platform: String?
    ) {
        LinkLog.i(
            TAG,
            "CastWindowStateManager::onScreenRotationChange: width = $width, height = $height, angle = $angle, mode = $mode, platform = $platform"
        )
        LinkLog.i(
            TAG,
            "CastWindowStateManager::onScreenRotationChange: width = $width, height = $height, angle = $angle, mode = $mode, platform = $platform"
        )
        rotationChangeInfo =
            IScreenRotationChange.ScreenRotationChange(width, height, angle, mode, platform)
        curState?.onScreenRotationChange(rotationChangeInfo!!)
    }

    override fun onCarBtPairedConnected(connected: Boolean) {
        LinkLog.i(
            TAG,
            "CastWindowStateManager::onCarBtConnected: connected = $connected"
        )
    }

}