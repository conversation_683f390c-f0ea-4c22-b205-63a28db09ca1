package com.autoai.wdlink.hu.castwindow

import android.util.Log

/**
 * 日志写文件和 logcat 输出
 * <AUTHOR>
 */
class LinkLog {
    companion object {
        var mDebugModeEnabled: Boolean = false

        /**
         * 该日志级别现在不会写入文件
         */
        @JvmStatic
        fun d(tag: String, msg: String) {
            if (!mDebugModeEnabled) {
                Log.d(tag, applyThreadNameIfEnable(msg))
                com.tencent.mars.xlog.Log.d(tag, applyThreadNameIfEnable(msg))
            }
        }

        @JvmStatic
        fun i(tag: String, msg: String) {
            if (!mDebugModeEnabled) {
                Log.i(tag, applyThreadNameIfEnable(msg))
                com.tencent.mars.xlog.Log.i(tag, applyThreadNameIfEnable(msg))
            }
        }

        @JvmStatic
        fun w(tag: String, msg: String) {
            if (!mDebugModeEnabled) {
                Log.w(tag, applyThreadNameIfEnable(msg))
                com.tencent.mars.xlog.Log.w(tag, applyThreadNameIfEnable(msg))
            }
        }

        @JvmStatic
        fun e(tag: String, msg: String) {
            if (!mDebugModeEnabled) {
                Log.e(tag, applyThreadNameIfEnable(msg))
                com.tencent.mars.xlog.Log.e(tag, applyThreadNameIfEnable(msg))
            }
        }

        @JvmStatic
        @JvmOverloads
        fun e(tag: String, msg: Throwable, msgPrefix: String = "") {
            if (!mDebugModeEnabled) {
                Log.e(tag, msgPrefix + msg.stackTraceToString())
                com.tencent.mars.xlog.Log.e(tag, msgPrefix + msg.stackTraceToString())
            }
        }

        private fun applyThreadNameIfEnable(msg: String): String {
            return "#${Thread.currentThread().name}: $msg"
        }

        @JvmStatic
        fun printStackTraceString(tag: String, msg: String) {
            val stackTraceString =
                Log.getStackTraceString(Throwable(msg))
            Log.e(
                tag,"调用栈： ==》 $stackTraceString"
            )
            com.tencent.mars.xlog.Log.e(
                tag,"调用栈： ==》 $stackTraceString"
            )

        }
    }


}