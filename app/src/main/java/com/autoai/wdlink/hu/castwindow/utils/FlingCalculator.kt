package com.autoai.wdlink.hu.castwindow.utils

import android.content.Context
import android.view.ViewConfiguration
import kotlin.math.abs
import kotlin.math.ln


class FlingCalculator {
    private val FLING_FRICTION = ViewConfiguration.getScrollFriction()
    private val DECELERATION_RATE = (ln(0.78) / ln(0.9)).toFloat() // 内部默认值
    private val GRAVITY_EARTH = 9.80665f // 地球重力加速度

    // 获取系统的减速度
    private fun getDeceleration(context: Context): Float {
        return GRAVITY_EARTH * 39.37f * context.resources.displayMetrics.density * FLING_FRICTION
    }

    // 计算 fling 时长（X 或 Y 轴）
    fun calculateFlingDuration(context: Context, velocity: Float): Long {
        val deceleration = getDeceleration(context) * DECELERATION_RATE
        return (1000 * ln((1 + abs(velocity.toDouble()) / (FLING_FRICTION * deceleration))) / DECELERATION_RATE).toLong()
    }
}