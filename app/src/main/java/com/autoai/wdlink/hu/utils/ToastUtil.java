package com.autoai.wdlink.hu.utils;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.graphics.PixelFormat;
import android.os.Handler;
import android.os.Looper;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.autoai.common.util.LogUtil;
import com.autoai.wdlink.hu.MainActivity;
import com.autoai.wdlink.hu.MyApplication;
import com.autoai.wdlink.hu.R;

import java.lang.ref.WeakReference;

public class ToastUtil {
    private static   WeakReference<View> toastViewRef;
    private  static WeakReference<WindowManager> windowManagerRef;
    private static final Handler handler = new Handler(Looper.getMainLooper());
    private static Runnable removeRunnable;
    private static final String ANDROID_SETTINGS_BLUETOOTH_SETTINGS = "android.settings.BLUETOOTH_SETTINGS";
    private static WindowManager windowManager;
    private static View toastView;

    public static void showToast(String message,String tvContext,String buttonText1, String buttonText2,boolean isShowButton1,boolean isShowButton2,int id) {
        Context context = MyApplication.application;

        // 取消之前的移除任务
        if (removeRunnable != null) {
            handler.removeCallbacks(removeRunnable);
        }

        if (toastViewRef == null || toastViewRef.get() == null) {
            LayoutInflater inflater = LayoutInflater.from(context);
            View view = inflater.inflate(R.layout.custom_toast_layout, null);
            toastViewRef = new WeakReference<>(view);

            WindowManager windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
            windowManagerRef = new WeakReference<>(windowManager);
        }

        toastView = toastViewRef.get();
        windowManager = windowManagerRef.get();

        if (toastView == null || windowManager == null) {
            return;
        }

        // 移除已存在的视图
        if (toastView.getParent() != null) {
            windowManager.removeView(toastView);
        }

        TextView tvText = toastView.findViewById(R.id.toast_text);
        TextView tvContextView = toastView.findViewById(R.id.toast_context);
        TextView button1 = toastView.findViewById(R.id.button1);
        TextView button2 = toastView.findViewById(R.id.button2);
        LinearLayout linearLayout = toastView.findViewById(R.id.button_lin);

        if (linearLayout == null || tvText == null || tvContextView == null || button1 == null || button2 == null) {
            return; // 避免 findViewById 失败后继续执行
        }

        handler.post(() -> {
            if (isShowButton1 || isShowButton2) {
                linearLayout.setVisibility(View.VISIBLE);
            } else {
                linearLayout.setVisibility(View.GONE);
            }

            if (!tvContext.isEmpty()) {
                tvContextView.setVisibility(View.VISIBLE);
            } else {
                tvContextView.setVisibility(View.GONE);
            }

            if (isShowButton1) {
                button1.setVisibility(View.VISIBLE);
                button1.setOnClickListener(v -> {
                    //todo 通过发送广播打开蓝牙页面
                    if (id == 1) {
                        Intent intent = new Intent("android.intent.action.didi.settings");
                        intent.putExtra("action", ANDROID_SETTINGS_BLUETOOTH_SETTINGS);
                        MyApplication.application.sendBroadcast(intent);
                        // 移除已存在的视图
                        if (toastView.getParent() != null) {
                            windowManager.removeView(toastView);
                        }
                    }
                });
            } else {
                button1.setVisibility(View.GONE);
            }

            if (isShowButton2) {
                button2.setVisibility(View.VISIBLE);
                button2.setOnClickListener(v -> {
                    // 处理点击逻辑
                });
            } else {
                button2.setVisibility(View.GONE);
            }

            tvText.setText(message);
            tvContextView.setText(tvContext);
            button1.setText(buttonText1);
            button2.setText(buttonText2);
        });
        WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams(
                context.getResources().getInteger(R.integer.toast_width),
                WindowManager.LayoutParams.WRAP_CONTENT,
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
                WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                PixelFormat.TRANSLUCENT
        );
        layoutParams.gravity = Gravity.START | Gravity.TOP;
        //todo 点击事件不被遮挡
        layoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL;
        layoutParams.x = context.getResources().getInteger(R.integer.toast_margin_start);
        layoutParams.y = context.getResources().getInteger(R.integer.toast_margin_top);

        handler.post(() -> {
            if (toastView.getParent() == null) {
                windowManager.addView(toastView, layoutParams);
            }

            removeRunnable = () -> {
                if (toastView.getParent() != null) {
                    windowManager.removeView(toastView);
                }
                toastViewRef.clear();
                windowManagerRef.clear();
            };

            handler.postDelayed(removeRunnable, 5000);
        });
    }
    public static void removeView(){
        if (null != toastView) {
            if (null != toastView.getParent()) {
                windowManager.removeView(toastView);
            }
            toastViewRef.clear();
            windowManagerRef.clear();
        }
    }
}
