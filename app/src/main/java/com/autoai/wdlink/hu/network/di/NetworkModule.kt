package com.autoai.wdlink.hu.network.di
import androidx.core.os.trace
import com.autoai.wdlink.hu.network.interceptro.LicenseHeaderInterceptor
import com.autoai.wdlink.hu.network.retrofit.NetworkProfiler.Companion.configure
import okhttp3.Call
import okhttp3.ConnectionPool
import okhttp3.OkHttpClient
import java.util.concurrent.TimeUnit

internal object NetworkModule {
    private val connectionPool: ConnectionPool = ConnectionPool()

    fun licenseOkHttpCallFactory(): Call.Factory = trace("LicenseOkHttpClient") {
        OkHttpClient.Builder()
            .addInterceptor(LicenseHeaderInterceptor())
            .callTimeout(3000, TimeUnit.MILLISECONDS)
            .connectionPool(connectionPool)
            .configure()
            .build()
    }
}
