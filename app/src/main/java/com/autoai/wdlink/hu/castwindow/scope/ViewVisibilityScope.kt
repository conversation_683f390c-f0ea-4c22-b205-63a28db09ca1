package com.autoai.wdlink.hu.castwindow.scope

import android.view.View
import androidx.annotation.IntDef

/**
 * View visibility
 * <AUTHOR>
 * @date 6/9/23
 */

@Retention(AnnotationRetention.SOURCE)
@IntDef(View.VISIBLE, View.GONE, View.INVISIBLE)
annotation class ViewVisibilityScope {
    companion object{
        fun Int.toVisibilityStr(): String{
            return "$this:" + when (this){
                View.VISIBLE -> "VISIBLE"
                View.GONE -> "GONE"
                View.INVISIBLE -> "INVISIBLE"
                else -> "NA（Reserved or Invalid）"
            }
        }
    }
}
