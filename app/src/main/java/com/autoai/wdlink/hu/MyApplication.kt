package com.autoai.wdlink.hu

import android.annotation.SuppressLint
import android.app.Application
import android.os.Build
import android.util.Log
import androidx.lifecycle.ProcessLifecycleOwner
import com.autoai.wdlink.hu.model.DevelopModel
import com.autoai.wdlink.hu.network.retrofit.NetworkProfiler
import com.autoai.wdlink.hu.sdk.LinkSdkModel
import com.autoai.wdlink.hu.utils.CactchUncaughtExceptionHandler
import com.autoai.welink.sdk.BT.BtConnectionManager
import com.autoai.welink.sdk.BT.BtMusicFocusManager
import com.autoai.welink.sdk.SdkInfo
import com.tencent.mars.xlog.Xlog
import java.io.File


class MyApplication : Application() {

    companion object {
        init {
            System.loadLibrary("c++_shared")
            System.loadLibrary("marsxlog")
        }
        lateinit var application: MyApplication
    }

    override fun onCreate() {
        super.onCreate()
        application = this@MyApplication
        //--->日志配置相关↓↓↓↓↓↓↓
//        val isDebug = SharePreferenceUtil.getInstance(applicationContext)!!.getIsDebug()
        val isDebug = DevelopModel.getInstance()!!.data.isLogEnabled
        Logger.setIsLoggable(isDebug)
        //日志级别  1 全日志
        var level= if(isDebug)DevelopModel.getInstance()!!.logLevel else 5
        SdkInfo.getInstance().logType = level
        Log.d("A55", "isDebug:$isDebug,logType:$level")
        //logDebug:打开logcat日志输出。fileDebug:打开文件日志输出。
        LinkSdkModel.logAble(logDebug = BuildConfig.DEBUG||isDebug, fileDebug = false)
        LinkSdkModel.logFullLog(isDebug && level == 1)
        Logger.setIsFileLoggable(false)

        //进程crash日志捕获功能
//        Logger.registerUncaughtExceptionHandler(this)
        CactchUncaughtExceptionHandler().registerUncaughtExceptionHandler(this,isDebug)
        //没用到webview 不需要hook
        //hookWebView()

        //启动后台核心服务
        LinkService.startLinkService(this)

        //---> 初始化OkHttp服务，主要用于鉴权author 网络请求相关
        NetworkProfiler.init(this)
        //创建通知渠道
        //--->全局生命周期，主要监听wifi状态相关
//        ProcessLifecycleOwner.get().lifecycle.addObserver(AppLifecycleObserver())
        initXLog()
    }
    @SuppressLint("SoonBlockedPrivateApi")
    //避免使用sharedUserId 和webview 出现Crash
    fun hookWebView() {
        val sdkInt = Build.VERSION.SDK_INT
        try {
            val factoryClass = Class.forName("android.webkit.WebViewFactory")
            val field = factoryClass.getDeclaredField("sProviderInstance")
            field.isAccessible = true
            var sProviderInstance = field.get(null)

            if (sProviderInstance != null) {
                Log.i("TAG", "sProviderInstance isn't null")
                return
            }

            val getProviderClassMethod = when {
                sdkInt > 22 -> factoryClass.getDeclaredMethod("getProviderClass")
                sdkInt == 22 -> factoryClass.getDeclaredMethod("getFactoryClass")
                else -> {
                    Log.i("TAG", "Don't need to Hook WebView")
                    return
                }
            }
            getProviderClassMethod.isAccessible = true
            val factoryProviderClass = getProviderClassMethod.invoke(factoryClass) as Class<*>
            val delegateClass = Class.forName("android.webkit.WebViewDelegate")
            val delegateConstructor = delegateClass.getDeclaredConstructor()
            delegateConstructor.isAccessible = true

            if (sdkInt < 26) { // 低于 Android O 版本
                val providerConstructor = factoryProviderClass.getConstructor(delegateClass)
                if (providerConstructor != null) {
                    providerConstructor.isAccessible = true
                    sProviderInstance = providerConstructor.newInstance(delegateConstructor.newInstance())
                }
            } else {
                val chromiumMethodName = factoryClass.getDeclaredField("CHROMIUM_WEBVIEW_FACTORY_METHOD")
                chromiumMethodName.isAccessible = true
                val chromiumMethodNameStr = chromiumMethodName.get(null)?.toString() ?: "create"
                val staticFactory = factoryProviderClass.getMethod(chromiumMethodNameStr, delegateClass)
                if (staticFactory != null) {
                    sProviderInstance = staticFactory.invoke(null, delegateConstructor.newInstance())
                }
            }

            if (sProviderInstance != null) {
                field.set("sProviderInstance", sProviderInstance)
                Log.i("TAG", "Hook success!")
            } else {
                Log.i("TAG", "Hook failed!")
            }
        } catch (e: Throwable) {
            Log.w("TAG", e)
        }
    }

    override fun onTerminate() {
        super.onTerminate()
        com.tencent.mars.xlog.Log.appenderFlush()
        com.tencent.mars.xlog.Log.appenderClose()
    }

    fun initXLog(){
        val logPath =
            MyApplication.application.getExternalFilesDir(null)!!.absolutePath + File.separator + "Test" + File.separator + "welinklog" + File.separator ;

        val xlog = Xlog()

//        val logConfig = Xlog.XLogConfig()
//        logConfig.mode = Xlog.AppednerModeAsync;
//        logConfig.logdir = logPath;
//        logConfig.nameprefix = "haiwai_log";
//        logConfig.pubkey = "";
//        logConfig.compressmode = Xlog.ZLIB_MODE;
//        logConfig.compresslevel = 0;
//        logConfig.cachedir = "";
//        logConfig.cachedays = 0;
//        if (BuildConfig.DEBUG) {
//            logConfig.level = Xlog.LEVEL_VERBOSE;
//            com.tencent.mars.xlog.Log.setConsoleLogOpen(true);
//        } else {
//            logConfig.level = Xlog.LEVEL_INFO;
//            com.tencent.mars.xlog.Log.setConsoleLogOpen(false);
//        }
//        xlog.setMaxFileSize(Long.MAX_VALUE,10 * 1024 * 1024) // 10M

        val xlogInstancePtr: Long = xlog.openLogInstance(Xlog.LEVEL_DEBUG, Xlog.AppednerModeAsync, logPath, logPath, "screenlink_log", 0)
        Log.e("xlog","xlog :"+xlogInstancePtr);
        xlog.setMaxFileSize(0, 1024 * 1024) // 1M
//        xlog.setMaxAliveTime(0, 1 * 24 * 3600)//日志存储1天，单位 秒
        xlog.setConsoleLogOpen(0, true)
        com.tencent.mars.xlog.Log.setLogImp(xlog)
        if (BuildConfig.DEBUG) {
            com.tencent.mars.xlog.Log.appenderOpen(Xlog.LEVEL_ALL, Xlog.AppednerModeAsync, "", logPath, "screenlink_log", 0);
            com.tencent.mars.xlog.Log.setConsoleLogOpen(true);
        } else {
            com.tencent.mars.xlog.Log.appenderOpen(Xlog.LEVEL_ALL, Xlog.AppednerModeAsync, "", logPath, "screenlink_log", 0);
            com.tencent.mars.xlog.Log.setConsoleLogOpen(true);
        }

    }
}