package com.autoai.wdlink.hu.castwindow.scope

import androidx.annotation.IntDef

/**
 * mini bar 左右位置
 */
@Retention(AnnotationRetention.SOURCE)
@IntDef(MiniPositionMode.LEFT, MiniPositionMode.RIGHT)
annotation class MiniPositionMode {
    companion object {
        const val LEFT = 1
        const val RIGHT = 2

        fun Int.toMiniModeStr(): String{
            return "$this:" + when (this){
                LEFT -> "Left"
                RIGHT -> "Right"
                else -> "NA"
            }
        }

        @MiniPositionMode
        fun Int.resolveMiniMode(): Int{
            return when (this){
                LEFT, RIGHT -> this
                else -> LEFT
            }
        }
    }
}