package com.autoai.wdlink.hu.viewmodel;

import com.autoai.common.util.LogUtil;
import com.autoai.wdlink.hu.frame.BaseViewModel;
import com.autoai.wdlink.hu.model.DevelopModel;

public class DevelopViewModel extends BaseViewModel {
    public static final String TAG = "DevelopViewModel";

    DevelopViewModel() {
    }

    /**
     * 页面初始化获取数据
     */
    public DevelopModel.SwitchData getData() {
        return DevelopModel.getInstance().getData();
    }

    /**
     * 存储鉴权弹窗状态
     */
    public void setAuthWindowStatus(boolean enabled) {
        // 存储数据并通知
        LogUtil.v(TAG, "setAuthWindowStatus : " + enabled);
        DevelopModel.getInstance().switchAuthWindow(enabled);
    }

    /**
     * 存储行车安全1.2开关
     * @param enabled
     */
    public void setDriveSafetyVersion(boolean enabled){
        DevelopModel.getInstance().switchDriveSafetyVersion(enabled);
    }

    /**
     * 存储行车安全走行规制状态
     */
    public void setDriveSafetyStatus(boolean onlineEnabled, boolean offlineEnabled) {
        // 存储数据并通知
        LogUtil.v(TAG, "setDriveSafetyStatus online : " + onlineEnabled + " offline : " + offlineEnabled);
        DevelopModel instance = DevelopModel.getInstance();
        instance.switchDriveSafetyOnline(onlineEnabled);
        instance.switchDriveSafetyOffline(offlineEnabled);
    }

    /**
     * log状态
     *
     * @param level v:1 d:2 i:3 w:4 e:5
     */
    public void setLogStatus(boolean enabled, int level) {
        // 存储数据并通知
        LogUtil.v(TAG, "setLogStatus : " + enabled + " level : " + level);
        DevelopModel.getInstance().switchLogLevel(enabled, level);
    }

    /**
     * fps 开关
     * @param enabled
     */
    public void setFpsStatus(boolean enabled) {
        // 存储数据并通知
        LogUtil.v(TAG, "setFpsStatus : " + enabled );
        DevelopModel.getInstance().switchFps(enabled);
    }
 public void setRttStatus(boolean enabled, int rtt) {
        // 存储数据并通知
        LogUtil.v(TAG, "setFpsStatus : " + enabled );
        DevelopModel.getInstance().switchRtt(enabled,rtt);
    }
}
