package com.autoai.wdlink.hu.castwindow.ktx

import android.graphics.Outline
import android.view.View
import android.view.ViewGroup
import android.view.ViewOutlineProvider
import com.autoai.wdlink.hu.ComponentName
import com.autoai.wdlink.hu.castwindow.LinkLog

private const val TAG = "${ComponentName.WindowSwitcher}ViewKtx"
fun View.removeFromParent(){
    (parent as? ViewGroup)?.also {
        it.removeView(this@removeFromParent)
        LinkLog.i(TAG, "removeFromParent: remove success: ${this@removeFromParent}")
    } ?: LinkLog.w(TAG, "removeFromParent: view parent is empty: ${this@removeFromParent}")
}

fun View.setOutlineProvider(radius: Float){
    setOutlineProvider(object : ViewOutlineProvider() {
        override fun getOutline(view: View, outline: Outline) {
            outline.setRoundRect(0, 0, view.width, view.height, radius)
        }
    })
    setClipToOutline(true)
}
