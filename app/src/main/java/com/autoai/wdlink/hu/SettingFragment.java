package com.autoai.wdlink.hu;

import android.os.Bundle;

import androidx.fragment.app.Fragment;

import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ScrollView;

import me.everything.android.ui.overscroll.OverScrollDecoratorHelper;

public class SettingFragment extends Fragment {

    private static final String ARG_PARAM1 = "param1";
    private static final String ARG_PARAM2 = "param2";

    // TODO: Rename and change types of parameters
    private String mParam1;
    private String mParam2;
    private Button[] tabButtons = new Button[3];
    private ScrollView scrollView;
    int[] sectionHeightsDp = {500, 800, 400}; // 每个子布局的高度（dp）
    private int[] sectionOffsets; // 每个子布局的起始位置（px）

    private long lastTabClickTime = 0;
    private static final long TAB_CLICK_TIMEOUT = 300; // ms
    private View view;

    public static SettingFragment newInstance(String param1, String param2) {
        SettingFragment fragment = new SettingFragment();
        Bundle args = new Bundle();
        args.putString(ARG_PARAM1, param1);
        args.putString(ARG_PARAM2, param2);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            mParam1 = getArguments().getString(ARG_PARAM1);
            mParam2 = getArguments().getString(ARG_PARAM2);
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        view = inflater.inflate(R.layout.fragment_setting, container, false);
        tabButtons[0] = view.findViewById(R.id.tab1);
        tabButtons[1] = view.findViewById(R.id.tab2);
        tabButtons[2] = view.findViewById(R.id.tab3);
        tabButtons[0].setSelected(true);
        scrollView = view.findViewById(R.id.scrollView1);
        //回弹效果
        OverScrollDecoratorHelper.setUpOverScroll(scrollView);
        sectionOffsets = new int[sectionHeightsDp.length];
        for (int i = 0; i < sectionHeightsDp.length; i++) {
            if (i > 0) {
                sectionOffsets[i] = sectionOffsets[i - 1] + dpToPx(sectionHeightsDp[i - 1]);
            }
        }

        setupTabClickListeners();
        setupScrollListener();
        return view;
    }
    private void setupTabClickListeners() {
        for (int i = 0; i < tabButtons.length; i++) {
            final int position = i;
            tabButtons[i].setOnClickListener(v -> {
                lastTabClickTime = System.currentTimeMillis(); // 更新点击时间
                updateTabSelection(position);
                scrollView.smoothScrollTo(0, sectionOffsets[position]);
            });
        }
    }

    private void setupScrollListener() {
        scrollView.setOnScrollChangeListener((v, scrollX, scrollY, oldScrollX, oldScrollY) -> {
            // 如果是点击 tab 后短时间内发生的滚动，不联动 tab
            if (System.currentTimeMillis() - lastTabClickTime < TAB_CLICK_TIMEOUT) {
                return;
            }

            if (isAtBottom()) {
                updateTabSelection(2); // 滚动到底部时强制选中 tab3
            } else {
                updateActiveTab(scrollY); // 否则正常联动
            }
        });
    }

    private boolean isAtBottom() {
        return scrollView.getChildAt(0).getMeasuredHeight() - scrollView.getScrollY() <= scrollView.getHeight();
    }

    private void updateActiveTab(int scrollY) {
        int low = 0;
        int high = sectionOffsets.length - 1;
        int activeTab = 0;

        while (low <= high) {
            int mid = (low + high) / 2;
            if (sectionOffsets[mid] <= scrollY) {
                low = mid + 1;
            } else {
                high = mid - 1;
            }
        }

        activeTab = high >= 0 ? high : 0;
        updateTabSelection(activeTab);
    }

    private void updateTabSelection(int selectedTab) {
        for (int i = 0; i < tabButtons.length; i++) {
            tabButtons[i].setSelected(i == selectedTab);
        }
    }

    private int dpToPx(int dp) {
        return Math.round(TypedValue.applyDimension(
                TypedValue.COMPLEX_UNIT_DIP,
                dp,
                getResources().getDisplayMetrics()
        ));
    }
}