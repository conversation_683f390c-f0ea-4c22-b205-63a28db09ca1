package com.autoai.wdlink.hu.frame.utils;

import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.autoai.wdlink.hu.R;
import com.autoai.wdlink.hu.castwindow.LinkLog;
import com.autoai.wdlink.hu.model.DevelopModel;
import com.autoai.wdlink.hu.sdk.LinkUtil;

/**
 * <AUTHOR>
 */
public class SafeDriverUtil {
  /*  private static final String TAG = "SafeDriverUtil";
    private static ConstraintLayout screenSafeDriver;
    private static Button btSafeExit;
    private static Button btPassenger;
    private static Button btSafeExitH;
    private static Button btPassengerH;
    private static int angle;
    private static int state = 0;
    private static int viewStatus = 0;
    public static final int VIEW_VISIBLE = 1;
    public static final int VIEW_GONE = 2;

    public static void setScreenSafeDriver(ConstraintLayout screenSafeDriver) {
        SafeDriverUtil.screenSafeDriver = screenSafeDriver;
        btSafeExit = screenSafeDriver.findViewById(R.id.bt_safe_exit);
        btPassenger = screenSafeDriver.findViewById(R.id.bt_passenger);
        btSafeExitH = screenSafeDriver.findViewById(R.id.bt_safe_exit_h);
        btPassengerH = screenSafeDriver.findViewById(R.id.bt_passenger_h);
        btSafeExit.setOnClickListener(v -> {
            LinkUtil.destoryServices(btSafeExit.getContext());
//            System.exit(0);
        });
        btPassenger.setOnClickListener(v -> {
            SafeDriverUtil.setState(SafeDriverUtil.VIEW_GONE);
            screenSafeDriver.setVisibility(View.GONE);
            viewStatus = VIEW_GONE;
        });

        btSafeExitH.setOnClickListener(v -> {
            LinkUtil.destoryServices(btSafeExitH.getContext());
        });
        btPassengerH.setOnClickListener(v -> {
            SafeDriverUtil.setState(SafeDriverUtil.VIEW_GONE);
            screenSafeDriver.setVisibility(View.GONE);
            viewStatus = VIEW_GONE;
        });
    }

    public static void setAngle(int angle) {
        SafeDriverUtil.angle = angle;
    }

    public static void setState(int state) {
        SafeDriverUtil.state = state;
    }
    public static int getState() {
        return state;
    }

    public static void cleanCache(){
        viewStatus = 0;
    }

    public static void notifySafeTip(int state) {
        if(!DevelopModel.getInstance().getDriveSafetyVersion()){
            return;
        }
        LinkLog.i(TAG, "notifySafeTip: state = " + state + ", viewStatus = " + viewStatus);
        SafeDriverUtil.state = state;
        if(VIEW_GONE == viewStatus){
            return;
        }

        if(screenSafeDriver != null){
            if (state == VIEW_VISIBLE) {
                if(angle == 90 || angle == 270){
                    LinkLog.i(TAG, "notifySafeTip: 1setOrientation = " + LinearLayout.HORIZONTAL);

                    btSafeExitH.setVisibility(View.VISIBLE);
                    btPassengerH.setVisibility(View.VISIBLE);
                    btSafeExit.setVisibility(View.GONE);
                    btPassenger.setVisibility(View.GONE);
                }else{
                    btSafeExitH.setVisibility(View.GONE);
                    btPassengerH.setVisibility(View.GONE);
                    btSafeExit.setVisibility(View.VISIBLE);
                    btPassenger.setVisibility(View.VISIBLE);
                    LinkLog.i(TAG, "notifySafeTip: 2setOrientation = " + LinearLayout.VERTICAL);
                }
                screenSafeDriver.setVisibility(View.VISIBLE);
            } else {
                screenSafeDriver.setVisibility(View.GONE);
            }
        }
    }
*/

}
