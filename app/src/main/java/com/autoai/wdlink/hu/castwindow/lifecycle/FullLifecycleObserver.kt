package com.autoai.wdlink.hu.castwindow.lifecycle

import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.LifecycleOwner

/**
 * 生命周期接口
 * <AUTHOR>
 * @date
 */
interface FullLifecycleObserver : LifecycleObserver {
    fun onCreate(owner: LifecycleOwner)
    fun onStart(owner: LifecycleOwner)
    fun onResume(owner: LifecycleOwner)
    fun onPause(owner: LifecycleOwner)
    fun onStop(owner: LifecycleOwner)
    fun onDestroy(owner: LifecycleOwner)
}

/**
 * 生命周期接口简单实现
 */
open class SimpleFullLifecycleObserver: FullLifecycleObserver {
    override fun onCreate(owner: LifecycleOwner) {}
    override fun onStart(owner: LifecycleOwner) {}
    override fun onResume(owner: LifecycleOwner) {}
    override fun onPause(owner: LifecycleOwner) {}
    override fun onStop(owner: LifecycleOwner) {}
    override fun onDestroy(owner: LifecycleOwner) {}
}
