package com.autoai.wdlink.hu.view

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import android.webkit.WebView
import com.autoai.wdlink.hu.R
import com.autoai.wdlink.hu.databinding.FragmentWebHtmlBinding
import com.autoai.wdlink.hu.frame.BaseFragment
import com.autoai.wdlink.hu.jswebview.browse.JsWeb.CustomWebViewClient
import com.autoai.wdlink.hu.viewmodel.WebHtmlViewModel


class WebHtmlFragment : BaseFragment<WebHtmlViewModel, FragmentWebHtmlBinding>(FragmentWebHtmlBinding::inflate) {

    override fun getViewModelClass() = WebHtmlViewModel::class

    override fun getViewBindingClass() = FragmentWebHtmlBinding::class

    @SuppressLint("SetJavaScriptEnabled")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val mywebview = binding.mywebview

        val webView = mywebview.webView

        mywebview.progressBar.setReachedBarColor(resources.getColor(R.color.text_can_click))
        mywebview.setWebViewClient(object : CustomWebViewClient(webView) {
            override fun shouldOverrideUrlLoading(view: WebView, url: String): Boolean {
                if (url.contains("app://chart")) {
                    jsCallJava(url)
                    return true // 添加webview和scrollview冲突判断
                }
                return false
            }

            override fun onPageError(url: String): String? {
                return null
            }

            override fun onPageHeaders(url: String): Map<String, String> {
                return HashMap()
            }

            override fun onPageFinished(view: WebView, url: String) {
                super.onPageFinished(view, url)
            }
        })
        webView.settings.javaScriptEnabled = true;
        webView.settings.useWideViewPort = false;
        webView.settings.setSupportZoom(false);
        webView.settings.textZoom = 100;
        //解决加载某些h5页面不出来问题
        webView.settings.domStorageEnabled = true;
        //清空缓存
        webView.clearCache(true);

        //reSizeWebview(300);
        webView.loadUrl("http://www.baidu.com");
    }

    private fun jsCallJava(url: String) {


    }

}