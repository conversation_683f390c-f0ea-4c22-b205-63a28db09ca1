//package com.autoai.wdlink.hu.sdk
//
//import android.view.MotionEvent
//import com.autoai.wdlink.hu.Logger
//
//
//object HidRegionHelper {
////    private const val isFullScreen = true
////    private var phoneWidth = 0
////    private var phoneHeight = 0
////    private var phoneAngle = 0
////    private var videoMode = -1
//
//    /**
//     * 触屏传输
//     * */
////    fun motionEventTrans(motionEvent: MotionEvent) {
////        Logger.v("motionEventTrans: motionEvent.x = ${motionEvent.x}, motionEvent.y = ${motionEvent.y}")
////        LinkSdkModel.screenTouch(motionEvent)
////    }
//
////    fun getHidCallback(phoneSurfaceParent: FrameLayout) =
////        HidCallback { width, height, angle, mode ->
////            if (width != 0 && height != 0) {
////                if (width != phoneWidth || height != phoneHeight || phoneAngle != angle || videoMode != mode) {
////                    phoneWidth = width
////                    phoneHeight = height
////                    phoneAngle = angle
////                    videoMode = mode
////                    Logger.d("HidRegionHelper-> ======================================================================================================================")
////                    Logger.d("HidRegionHelper-> HidCallback, phoneWidth = $phoneWidth, phoneHeight = $phoneHeight, phoneAngle = $phoneAngle, videoMode = $videoMode")
////                    phoneSurfaceParent.post {
////                        fitSurface(phoneSurfaceParent)
////                    }
////                }
////            }
////        }
//
////    /**
////     * 适配不同的视频尺寸模式
////     *
////     * videoMode 0 表示固定的方形视频, 1 手机实际的宽高比尺寸
////     */
////    private fun fitSurface(phoneSurfaceParent: FrameLayout) {
////        val dm = phoneSurfaceParent.resources.displayMetrics
////        val containerW: Float
////        val containerH: Float
////        // 计算出当前播放器所需的屏幕宽高,
////        if (!isFullScreen) {
////            containerW = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 320f, dm)
////            containerH =
////                dm.heightPixels - TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 20f, dm)
////        } else {
////            containerW = dm.widthPixels.toFloat()
////            containerH = dm.heightPixels.toFloat()
////        }
////        val base = Math.min(containerW / phoneWidth, containerH / phoneHeight)
////        val videoW = (phoneWidth * base).toInt()
////        val videoH = (phoneHeight * base).toInt()
////        val txW: Int
////        val txH: Int
////        if (videoMode == 0) {
////            // 方形的视频, 需要固定 SurfaceView 尺寸为方形, 通过 hid 传入的实际缩放 parent 以阻止黑色边缘渲染
////            txH = Math.max(videoH, videoW) / 2 * 2
////            txW = txH
////        } else {
////            txW = Math.min(videoH, videoW) / 2 * 2
////            txH = Math.max(videoH, videoW) / 2 * 2
////        }
////        val tx: View = (phoneSurfaceParent.getChildAt(0) as TextureView)
////        var lp = tx.layoutParams
////        if (lp == null) {
////            lp = FrameLayout.LayoutParams(txW, txH)
////        } else {
////            lp.width = txW
////            lp.height = txH
////        }
////        tx.layoutParams = lp
////        val parent: FrameLayout = phoneSurfaceParent
////        val plp = parent.layoutParams
////        plp.width = videoW
////        plp.height = videoH
////        parent.layoutParams = plp
////        if (videoMode == 1) {
////            when (phoneAngle) {
////                Surface.ROTATION_90 -> tx.rotation = 270F
////                Surface.ROTATION_270 -> tx.rotation = 90F
////                else -> tx.rotation = 0F
////            }
////        }
////        parent.invalidate()
////        tx.invalidate()
////    }
//}