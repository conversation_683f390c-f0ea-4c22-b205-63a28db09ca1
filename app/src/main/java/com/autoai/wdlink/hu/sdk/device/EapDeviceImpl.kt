package com.autoai.wdlink.hu.sdk.device

//import com.autoai.common.util.LogUtil
//import com.autoai.welinkapp.eap.EapDevice
//import com.autoai.welinkapp.eap.OnLinkEapListener
//import com.desaysv.eaphidl.EapHidl
//import com.desaysv.eaphidl.IapConfig
//import com.desaysv.eaphidl.PhoneLinkDeviceType
//import java.lang.reflect.Method


//class EapDeviceImpl : EapDevice {
//    companion object {
//        private const val TAG = "EapDevice"
//
//        private const val EAP_ACCESSORY_NAME = "com.navinfo.WeLinkVehicleProtocol"
//        private const val EAP_BUNDLE_ID = "com.fullScreen.weDriveLauncher"
////        private const val EAP_BUNDLE_ID = "com.gn2023.ios.launcher"
//        private const val EAP_NODE_NAME = "/dev/bulk_eap_welink"
////        private const val EAP_BUNDLE_ID = "com.fullScreen.weDriveLauncher"
////        private const val EAP_BUNDLE_ID = "com.fullScreen.weDriveLauncher"
//    }
//
//    private var linkEapListener: OnLinkEapListener? = null
//    override fun initMfiI2cPath() {
//        LogUtil.e(TAG, "initMfiI2cPath")
////        JniEapMethod.eAPInit(EAP_ACCESSORY_NAME, EAP_NODE_NAME)
//        EapHidl.getInstance().init("/dev/i2c-1")
//        var mIapConfig = IapConfig.Builder()
//        mIapConfig.setName("Desaysv GN2023")
//        mIapConfig.setModelNumber("NV2233/BF")
//        mIapConfig.setCarPlayProductPlanUUID("49638baf5e4447dd")
//        mIapConfig.setCurrentForDevice("3000")
//        mIapConfig.setManufacturer("Huizhou Desay SV Automotive Co., Ltd")
//        //车机软硬件版本
//        //车机软硬件版本
//        var serialNumber = getVersion("sys.vehicle.hardware.serial.number")
//        var firmwareVersion =  getVersion("ro.build.display.id")
//        var hardwareVersion =  getVersion("sys.vehicle.hardware.version")
//        LogUtil.e(TAG,"serialNumber:"+serialNumber+",firmwareVersion:"+firmwareVersion+",hardwareVersion:"+hardwareVersion)
//       //序列号
//        mIapConfig.setSerialNumber(serialNumber)
//        //软件版本号
//        mIapConfig.setFirmwareVersion(firmwareVersion)
//        //硬件版本号
//        mIapConfig.setHardwareVersion(hardwareVersion)
//        EapHidl.getInstance().setIapConfig(mIapConfig.build())
//    }
//      fun getVersion(key:String,  defaultValue:String=""):String {
//        var value : String = ""
//        try {
//            var SysProp = Class.forName("android.os.SystemProperties")
//            var method: Method = SysProp.getMethod("get", String::class.java, String::class.java)
//            value =  method.invoke(null, key, defaultValue) as String
//        } catch (e : Exception) {
//            LogUtil.e(TAG,"read SystemProperties error:"+"e:"+e);
//        }
//        return value;
//    }
//    override fun eapInit() {
//        LogUtil.e(TAG, "eapInit")
////        JniEapMethod.eAPInit(EAP_ACCESSORY_NAME, EAP_NODE_NAME)
//        //("com.navinfo.WeLinkVehicleProtocol", "/dev/bulk_eap");
//        EapHidl.getInstance().eapInit(EAP_ACCESSORY_NAME, EAP_NODE_NAME)
//    }
//
//    override fun eapDeinit() {
//        LogUtil.e(TAG, "eapDeinit")
////        JniEapMethod.eAPDeinit()
//        EapHidl.getInstance().eapDeInit()
//    }
//    override fun setUsbMode(usbModeType : Boolean):Boolean {
//        LogUtil.e(TAG, "setUsbMode  usbModeType:$usbModeType")
//        val result = EapHidl.getInstance().setUsbMode(
//            if (usbModeType) PhoneLinkDeviceType.UsbModeType.USBMODE_DEVICE else PhoneLinkDeviceType.UsbModeType.USBMODE_HOST,
//            PhoneLinkDeviceType.LinkDeviceType.DEVICE_TYPE_EXTERNAL_LINK
//        )
//        LogUtil.e(TAG, "setUsbMode  result:$result")
//        return result;
//    }
//    override fun activeEap() {
//        LogUtil.e(TAG, "activeEap")
////        JniEapMethod.activeEAP()
//        EapHidl.getInstance().activeIap(PhoneLinkDeviceType.LinkDeviceType.DEVICE_TYPE_EXTERNAL_LINK)
//    }
//
//    override fun deactiveEap() {
//        LogUtil.e(TAG, "deactiveEap")
////        JniEapMethod.deactiveEAP()
//        EapHidl.getInstance().deActiveIap()
//    }
//
//    override fun eapLaunchApp() {
//        LogUtil.e(TAG, "eapLaunchApp")
////        JniEapMethod.eAPLaunchApp(EAP_BUNDLE_ID, EAPType.EAPLaunchMethod.EAP_Launch_With_User_Alert)
//        EapHidl.getInstance().eapLaunchApp(EAP_BUNDLE_ID, PhoneLinkDeviceType.EAPLaunchMethod.EAP_Launch_With_User_Alert)
//    }
//
//    override fun linkDeviceCallbackRegister(linkEapListener: OnLinkEapListener?) {
//        LogUtil.e(TAG, "linkDeviceCallbackRegister")
//      /*  JniEapMethod.linkDeviceCBRegister { i: Int ->
//            LogUtil.e(TAG, "linkDeviceCBRegister: $i")
//            linkEapListener?.onLinkDeviceCallbackType(i)
//        }*/
//        this.linkEapListener = linkEapListener
//       /* EapHidl.getInstance().registerPhoneLinkEapStatusListener{
//                i: Int ->
//            LogUtil.e(TAG, "linkDeviceCBRegister: $i")
//            linkEapListener?.onLinkDeviceCallbackType(i)
//        }*/
//        EapHidl.getInstance().registerPhoneLinkEapStatusListener(iPhoneLinkEapListner)
//    }
//
//    var iPhoneLinkEapListner: EapHidl.IPhoneLinkEapListner = EapHidl.IPhoneLinkEapListner{
//            i: Int ->
//        LogUtil.e(TAG, "linkDeviceCBRegister: $i")
//        linkEapListener?.onLinkDeviceCallbackType(i)
//    }
//    override fun eapBulkOpen(): Boolean {
//        LogUtil.e(TAG, "eapBulkOpen")
////        return JniEapMethod.eapBulkOpen()
//        return  EapHidl.getInstance().eapBulkOpen()
//    }
//
//    override fun eapBulkClose() {
//        LogUtil.e(TAG, "eapBulkClose")
////        JniEapMethod.eapBulkClose()
//        EapHidl.getInstance().eapBulkClose()
//    }
//
//    override fun eapBulkRead(len: Int): ByteArray {
//        if (LogUtil.isFullLog())
//            LogUtil.e(TAG, "eapBulkRead")
////        return JniEapMethod.eapBulkRead(buffer, len)
//        return EapHidl.getInstance().eapBulkRead(len).toByteArray()
//    }
//
//    override fun eapBulkWrite(buffer: ByteArray?, len: Int): Int {
//        if (LogUtil.isFullLog())
//            LogUtil.e(TAG, "eapBulkWrite")
////        return JniEapMethod.eapBulkWrite(buffer, len)
//        return return EapHidl.getInstance().eapBulkWrite(buffer, len)
//    }
//    override fun getEapStatus(): Int {
//        //获取Eap 状态
//        if (LogUtil.isFullLog())
//            LogUtil.e(TAG, "getEapStatus")
//        return EapHidl.getInstance().eapStatus
//    }
//    override fun resetUsb(): Boolean {
//        LogUtil.e(TAG, "resetUsb")
//        return EapHidl.getInstance()
//            .resetUsb("/sys/devices/platform/soc/13010000.cdncusb/vbus", "0", "1")
//    }
//    override fun unLinkDeviceCallbackRegister() {
//        LogUtil.e(TAG, "unlinkDeviceCallbackRegister")
//        EapHidl.getInstance().unRegisterPhoneLinkEapStatusListener(iPhoneLinkEapListner)
//    }
//
//}