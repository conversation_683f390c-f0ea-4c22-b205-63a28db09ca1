package com.autoai.wdlink.hu.dialog;


import android.content.Context;
import android.content.Intent;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;


import com.autoai.wdlink.hu.MainActivity;
import com.autoai.wdlink.hu.R;
import com.autoai.welinkapp.checkconnect.CommonData;



/**
 * <AUTHOR>
 */
public class BasePopDialog {
    private Context mAppActivity;
    AOADialog popDialog;
    private int width = 1024;
    private int height = 534;
    View viewRoot;

    public void showTwoButtonDialog(Context mAppActivity, FrameLayout frameLayout) {
        this.mAppActivity = mAppActivity;
        width = frameLayout.getWidth();
        height = frameLayout.getHeight();
        Log.d("BasePopDialog", "showTwoButtonDialog width:" + width + ",height:" + height);
        LayoutInflater inflater = LayoutInflater.from(mAppActivity);
        // 得到加载view
        viewRoot = inflater.inflate(R.layout.welink_basemodule_confirm_dialog, null);
        TextView tvTitle = viewRoot.findViewById(R.id.tv_title);
        //if (currHome.dialogTitleResourceId != 0) {
        tvTitle.setVisibility(View.VISIBLE);
        tvTitle.setText("无线互联切换为USB互联");
          /*  } else if (!TextUtils.isEmpty(currHome.dialogTitleStr)) {
                tvTitle.setVisibility(View.VISIBLE);
                tvTitle.setText(currHome.dialogTitleStr);
            } else {
                tvTitle.setVisibility(View.GONE);
            }
        }*/
        popDialog = new AOADialog(mAppActivity, frameLayout);
        setDialogSize(viewRoot);
        popDialog.setContentView(viewRoot);

        ImageView icon = viewRoot.findViewById(R.id.confirm_dialog_icon);
      /*  if (currHome.backgroundResourceId > 0) {
            icon.setVisibility(View.VISIBLE);
            icon.setBackgroundResource(currHome.backgroundResourceId);
        } else {
            icon.setVisibility(View.GONE);
        }*/

        TextView tvDesc = viewRoot.findViewById(R.id.tv_desc);
        tvDesc.setText("无线互联是否切换为USB有线互联？");


        TextView btnCancel = viewRoot.findViewById(R.id.btn_cancel);

        btnCancel.setText("取消");

        btnCancel.setOnClickListener(v -> {

        });

        TextView btn_ok = viewRoot.findViewById(R.id.btn_ok);

        btn_ok.setText("确定");

        btn_ok.setOnClickListener(v -> {
            popDialog.dismiss();
//            closeDialog(flagId);
            CommonData.sendMsg(mAppActivity, CommonData.CONNECT_STATUS_OUT, CommonData.iCurrentConnectType, null);
//            CommonData.sendMsg(mAppActivity, CommonData.CONNECT_STATUS_IN, CommonData.DEVICE_TYPE_AOA, null);
            //回调app 显示互联画面
            Intent intent = new Intent(mAppActivity, MainActivity.class);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            mAppActivity.startActivity(intent);

        });

        popDialog.setOnCancelListener(() -> {
            popDialog.dismiss();
//            closeDialog(flagId);
        });
        popDialog.setCanceledOnTouchOutside(true);
        popDialog.setCancelable(false);
        popDialog.show();
    }


    private void setDialogSize(View view) {
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(1, 1);
        setDialogSize(params);
        params.gravity = Gravity.CENTER;
        view.setLayoutParams(params);
    }

    public void refreshDialogSize(int width, int height) {
        Log.d("BasePopDialog", "refreshDialogSize");
        if (viewRoot != null) {
            this.width = width;
            this.height = height;
            setDialogSize(viewRoot);
        }
    }

    public void setDialogSize(ViewGroup.LayoutParams aParams) {

        int w, h;
        w = (int) (width * 0.45);
        h = (int) (height * 0.6);
        if(w < 400){
            w = width;
        }
        if(h > 250){
            h = 250;
        }
        aParams.width = w;
        aParams.height = h;
        /*if (w > h) {
            aParams.width = w;
            aParams.height = h;
        } else {
            aParams.width = h;
            aParams.height = w;
        }*/
    }

    /**
     * 创建AOADialog时绑定对应的AppActivity
     */
    public void setAppActivity(Context appActivity) {
        mAppActivity = appActivity;
    }

    public Context getAppActivity() {
        return mAppActivity;
    }


    public void closeDialog() {
        if (popDialog != null) {
            popDialog.dismiss();
        }
    }

    public boolean isShow() {
        if (popDialog != null) {
            return popDialog.isShowing();
        }
        return false;
    }


    public static int dip2px(Context context, float dpValue) {
        final float scale = context.getResources().getDisplayMetrics().density;
        return (int) (dpValue * scale + 0.5f);
    }


}
