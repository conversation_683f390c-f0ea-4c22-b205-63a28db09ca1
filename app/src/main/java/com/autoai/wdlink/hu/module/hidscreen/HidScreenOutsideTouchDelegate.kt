package com.autoai.wdlink.hu.module.hidscreen

import android.graphics.Rect
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Color
import android.view.MotionEvent
import android.view.TouchDelegate
import android.view.View
import com.autoai.common.util.LogUtil
import com.autoai.wdlink.hu.MyApplication
import com.autoai.wdlink.hu.R
import com.autoai.wdlink.hu.castwindow.view.CastWindowStateManager

/**
 * 触摸投屏区域外的部分，处理边缘滑动返回，上滑回桌面
 * parentView ──┐                 （Activity 根布局 / Window 内容）
 *              ├── delegateView  （包裹 TextureView 的容器，如 FrameLayout）
 *              │      └── TextureView   ← 真正显示投屏画面
 *              └── BoundaryVisualizationView
 */
class HidScreenOutsideTouchDelegate(
    private val expandDelegateViewBound: Rect,          // 扩展后的触摸代理区域边界（delegateView经过insets调整后的区域，用于判断是否需要处理触摸事件）
    private val originalTextureViewBound: Rect,         // TextureView的原始边界区域（originalBound或经过matrixTransfor变换的delegate，用于区分触摸在投屏内还是扩展区域内）
    private val delegateView: View,    // 被代理的目标视图（接收dispatchTouchEvent的视图，通常是InterceptFrameLayout）
    private val callback: (() -> Boolean)? // 回调函数，用于检查触摸代理功能是否启用（返回true表示启用触摸代理）
) : TouchDelegate(expandDelegateViewBound, delegateView) {
    private val originalPaint = Paint().apply {
        color = Color.BLUE
        style = Paint.Style.STROKE
        strokeWidth = 6f
    }
    
    private val textPaint = Paint().apply {
        color = Color.WHITE
        textSize = 32f
        isAntiAlias = true
        style = Paint.Style.FILL
        setShadowLayer(4f, 2f, 2f, Color.BLACK)
    }
    
    private val delegateViewPaint = Paint().apply {
        color = Color.MAGENTA
        style = Paint.Style.STROKE
        strokeWidth = 6f
        pathEffect = android.graphics.DashPathEffect(floatArrayOf(20f, 10f), 0f)
    }
    
    private val parentViewPaint = Paint().apply {
        color = Color.CYAN
        style = Paint.Style.STROKE
        strokeWidth = 4f
        pathEffect = android.graphics.DashPathEffect(floatArrayOf(15f, 5f), 0f)
    }
    companion object {
        private const val TAG = "HidScreenOutsideTouchDelegate"
        // 区分全屏和浮窗的边缘位置
        private var topMargin = run {
            val application = MyApplication.application
            if (application != null) {
                application.resources.getDimensionPixelSize(R.dimen.extend_size2) +
                        application.resources.getDimensionPixelSize(R.dimen.dimen_45)
            } else {
                LogUtil.e(TAG, "MyApplication.application is null during initialization, using default topMargin")
                133 // 默认值：88 + 45
            }
        }

        private var leftMargin = 88

        private var viewWidth = 0
        /**
         * delegateView --> texture 外部 parent
         * parentView  -->  window  主布局
         */
        @JvmStatic
        fun setTouchDelegateWhenReady(
            delegateView: View?,
            parentView: View?,
            insets: Rect?,
            isFullScreen: Boolean,
            callback: (() -> Boolean)?
        ) {
            // 空值检查
            if (delegateView == null) {
                LogUtil.e(TAG, "HidScreenOutsideTouchDelegate::setTouchDelegateWhenReady: delegateView is null")
                return
            }
            if (parentView == null) {
                LogUtil.e(TAG, "HidScreenOutsideTouchDelegate::setTouchDelegateWhenReady: parentView is null")
                return
            }
            if (insets == null) {
                LogUtil.e(TAG, "HidScreenOutsideTouchDelegate::setTouchDelegateWhenReady: insets is null")
                return
            }

            LogUtil.i(TAG, "HidScreenOutsideTouchDelegate::setTouchDelegateWhenReady: isFullScreen=$isFullScreen, insets=$insets")
            delegateView.post {
                //代理区域矩阵
                val expandDelegateViewBound = Rect()
                delegateView.getHitRect(expandDelegateViewBound)

                // 获取textureView 原始边界Rect
                val textureViewBoundRect = Rect()
                delegateView.getHitRect(textureViewBoundRect)

                if (isFullScreen) {
                    expandDelegateViewBound.apply {
                        left += (insets.left * 2)
                        top += insets.top
                        right -= insets.right
                        bottom -= insets.bottom
                    }
                } else {
                    expandDelegateViewBound.apply {
                        left += insets.left
                        top += insets.top
                        right -= (insets.right * 2)
                        bottom -= insets.bottom - 100
                    }
                }

                topMargin = if (isFullScreen) {
                    leftMargin = 88
                    0
                } else {
                    // 空值检查 MyApplication.application
                    val application = MyApplication.application
                    if (application == null) {
                        LogUtil.e(TAG, "HidScreenOutsideTouchDelegate::setTouchDelegateWhenReady: MyApplication.application is null")
                        leftMargin = 88 // 使用默认值
                        0 // 使用默认值
                    } else {
                        leftMargin = application.resources.getDimensionPixelSize(R.dimen.extend_size2)
                        application.resources.getDimensionPixelSize(R.dimen.extend_size2) +
                                application.resources.getDimensionPixelSize(R.dimen.dimen_45)
                    }
                }
                
                viewWidth = delegateView.width

                LogUtil.i(TAG, "HidScreenOutsideTouchDelegate::setTouchDelegateWhenReady: setupComplete expandBound=${expandDelegateViewBound.width()}x${expandDelegateViewBound.height()}, textureViewBound=${textureViewBoundRect.width()}x${textureViewBoundRect.height()}")
                
                val touchDelegate = HidScreenOutsideTouchDelegate(expandDelegateViewBound, textureViewBoundRect, delegateView, callback)
                parentView.touchDelegate = touchDelegate
            }
        }
    }

    private var isDelegateTargeted = false
    private var isInDelegate = false

    /**
     * 发现这个点就在被代理view自身区域 ，标记flag
     */
    private var isInOriginalView = false
    override fun onTouchEvent(event: MotionEvent): Boolean {
        val enable = callback?.invoke()
        LogUtil.i(TAG, "HidScreenOutsideTouchDelegate::onTouchEvent: enable=$enable, action=${event.actionMasked}")

        if (enable != true) return false

        val x = event.x.toInt()
        val y = event.y.toInt()
        var locX = x - leftMargin
        var locY = y - topMargin
        LogUtil.i(TAG, "HidScreenOutsideTouchDelegate::onTouchEvent: transform ($x,$y) -> ($locX,$locY)")

        var sendToDelegate = false
        var handled = false
        when (event.actionMasked) {
            MotionEvent.ACTION_DOWN -> {
                //--> 标记为需要分发
                isDelegateTargeted = expandDelegateViewBound.contains(locX, locY)
                sendToDelegate = isDelegateTargeted
                isInDelegate = false
                isInOriginalView = false
                LogUtil.i(TAG, "HidScreenOutsideTouchDelegate::onTouchEvent: ACTION_DOWN targeted=$isDelegateTargeted")
            }
            
            MotionEvent.ACTION_POINTER_DOWN,
            MotionEvent.ACTION_POINTER_UP -> {
                sendToDelegate = isDelegateTargeted
            }
            //move 事件跟着down 事件标识的情况走
            MotionEvent.ACTION_MOVE,
            MotionEvent.ACTION_UP -> {
                val inBounds = !(locX < 0 || locX > viewWidth)
                sendToDelegate = if (inBounds) isDelegateTargeted else false
            }
            
            MotionEvent.ACTION_CANCEL -> {
                sendToDelegate = isDelegateTargeted
                isDelegateTargeted = false
            }
        }

        //--> 需要处理
        if (sendToDelegate) {
            //--> 发现这个点x,y就在自身区域
            if (originalTextureViewBound.contains(locX, locY)) {
                //派发一次边缘 ACTION_DOWN 事件

                //--> 发现这个点就在被代理view自身区域 ，标记flag
                isInOriginalView = true
                
                if (isInDelegate) {
                    isInDelegate = false
                    val downEvent = MotionEvent.obtain(event)
                    downEvent.action = MotionEvent.ACTION_DOWN
                    // 左侧触摸点在外部拓展区域拓展区域
                    if (locX < 0) locX = 0
                    if (locX > viewWidth) locX = viewWidth
                    downEvent.setLocation(locX.toFloat(), locY.toFloat())
                    delegateView.dispatchTouchEvent(downEvent)
                }

                // 左侧触摸点在外部拓展区域
                if (locX < 0) locX = 0
                if (locX > viewWidth) locX = viewWidth
                event.setLocation(locX.toFloat(), locY.toFloat())
                delegateView.dispatchTouchEvent(event)
            } else {
                isInDelegate = true
                if (isInOriginalView) {
                    isInOriginalView = false
                    //从范围内划出边界，发送取消事件
                    val upEvent = MotionEvent.obtain(event)
                    upEvent.action = MotionEvent.ACTION_UP
                    delegateView.dispatchTouchEvent(upEvent)
                    //不再响应后续的划入事件了
                    isDelegateTargeted = false
                }
            }
            handled = true
        }

        LogUtil.i(TAG, "HidScreenOutsideTouchDelegate::onTouchEvent: handled=$handled")
        return handled
    }
    
    fun drawBoundaries(canvas: Canvas?, view: View?) {
        // 空值检查
        if (canvas == null) {
            LogUtil.e(TAG, "HidScreenOutsideTouchDelegate::drawBoundaries: canvas is null")
            return
        }
        if (view == null) {
            LogUtil.e(TAG, "HidScreenOutsideTouchDelegate::drawBoundaries: view is null")
            return
        }

        val adjustedOriginal = Rect(originalTextureViewBound)
        adjustedOriginal.offset(leftMargin, topMargin)
        
        val clippedOriginal = Rect(adjustedOriginal)
        clippedOriginal.left = maxOf(0, clippedOriginal.left)
        clippedOriginal.top = maxOf(0, clippedOriginal.top)
        clippedOriginal.right = minOf(view.width, clippedOriginal.right)
        clippedOriginal.bottom = minOf(view.height, clippedOriginal.bottom)
        
        if (clippedOriginal.width() > 0 && clippedOriginal.height() > 0) {
            canvas.drawRect(clippedOriginal, originalPaint)
        }

        val delegateLocationInParent = IntArray(2)
        val parentLocationInParent = IntArray(2)
        delegateView.getLocationInWindow(delegateLocationInParent)
        view.getLocationInWindow(parentLocationInParent)
        
        val offsetX = delegateLocationInParent[0] - parentLocationInParent[0]
        val offsetY = delegateLocationInParent[1] - parentLocationInParent[1]
        
        val delegateRect = Rect(
            offsetX,
            offsetY,
            offsetX + delegateView.width,
            offsetY + delegateView.height
        )
        
        val clippedDelegateRect = Rect(delegateRect)
        clippedDelegateRect.left = maxOf(0, clippedDelegateRect.left)
        clippedDelegateRect.top = maxOf(0, clippedDelegateRect.top)
        clippedDelegateRect.right = minOf(view.width, clippedDelegateRect.right)
        clippedDelegateRect.bottom = minOf(view.height, clippedDelegateRect.bottom)
        
        if (clippedDelegateRect.width() > 0 && clippedDelegateRect.height() > 0) {
            canvas.drawRect(clippedDelegateRect, delegateViewPaint)
            canvas.drawText("delegateView", clippedDelegateRect.left + 10f, clippedDelegateRect.top + 50f, textPaint)
        }
        
        val parentRect = Rect(0, 0, view.width, view.height)
        canvas.drawRect(parentRect, parentViewPaint)
        canvas.drawText("parentView", 10f, 30f, textPaint)
    }
}

/**
 * 用于绘制边界可视化的自定义视图
 */
class BoundaryVisualizationView(context: android.content.Context?, private val touchDelegate: HidScreenOutsideTouchDelegate?) : View(context) {

    companion object {
        private const val TAG = "BoundaryVisualizationView"
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        touchDelegate?.drawBoundaries(canvas, this)
    }
}

