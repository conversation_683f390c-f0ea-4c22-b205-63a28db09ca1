package com.autoai.wdlink.hu.castwindow.view;

public class SmallShowBean {
    int smallShowMode;
    int position;

    public SmallShowBean(int smallShowMode, int position) {
        this.smallShowMode = smallShowMode;
        this.position = position;
    }

    public int getSmallShowMode() {
        return smallShowMode;
    }

    public void setSmallShowMode(int smallShowMode) {
        this.smallShowMode = smallShowMode;
    }

    public int getPosition() {
        return position;
    }

    public void setPosition(int position) {
        this.position = position;
    }
}
