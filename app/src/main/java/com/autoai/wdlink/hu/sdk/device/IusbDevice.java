package com.autoai.wdlink.hu.sdk.device;


import com.autoai.common.util.LogUtil;
import com.autoai.welinkapp.eap.OnLinkEapListener;


/**
 * 苹果手机和车机 USB 直连
 */
public class IusbDevice implements com.autoai.welinkapp.eap.EapDevice{
    private static final String TAG = "IusbDevice";
    private static String deviceName = "";
    private static long devicePtr = 0;
    private static long connectPtr = 0;
    private static final int PORT = 2345;
    OnLinkEapListener linkEapListener;

    private static  boolean  CONNECT_START = false;

    @Override
    public void initMfiI2cPath() {

    }

    @Override
    public void eapInit() {

    }

    @Override
    public boolean setUsbMode(boolean usbMode) {
        return false;
    }

    @Override
    public void eapDeinit() {

    }

    @Override
    public void activeEap() {
        //激活
    }

    @Override
    public void deactiveEap() {
       //去激活
    }

    @Override
    public void eapLaunchApp() {
        //拉起app
//        LogUtil.i(TAG, "ideviceConnect connectPtr = " + devicePtr);
//        if(devicePtr > 0) {
//            connectPtr = UsbApiInterface.getInstance().ideviceConnect(devicePtr, PORT);
//            LogUtil.i(TAG, "ideviceConnect connectPtr = " + connectPtr);
//            if (connectPtr > 0) {
//               //return true;
//            }
//        }
    }

    @Override
    public void linkDeviceCallbackRegister(OnLinkEapListener linkEapListener) {
    }

    private void iusbInitDevice(int i){
    }

    @Override
    public void unLinkDeviceCallbackRegister() {

    }

    @Override
    public boolean eapBulkOpen() {
        LogUtil.i(TAG, "ideviceConnect devicePtr = " + devicePtr);
        return true;
    }

    @Override
    public void eapBulkClose() {
        LogUtil.i(TAG,"ideviceConnect disconnect");
    }


    public static String byteArrayToHexString(byte[] bytes, int length) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(String.format("%02X ", bytes[i]));
        }
        return sb.toString().trim();
    }

    @Override
    public byte[] eapBulkRead(int len) {
        return new byte[0];
    }

    @Override
    public int eapBulkWrite(byte[] buffer, int len) {
        return len;
    }

    @Override
    public int getEapStatus() {
        return 0;
    }

    @Override
    public boolean resetUsb() {
        return false;
    }
}
