package com.autoai.wdlink.hu.module.hidscreen;

import android.util.Log;
import android.view.ScaleGestureDetector;

import androidx.annotation.NonNull;

public class ScaleListener extends ScaleGestureDetector.SimpleOnScaleGestureListener {
    private MultiTouchView.IDragListener iDragListener;
    private float startSpan;

    @Override
    public boolean onScaleBegin(@NonNull ScaleGestureDetector detector) {
        startSpan = detector.getCurrentSpan();
        return super.onScaleBegin(detector);
    }

    @Override
    public boolean onScale(ScaleGestureDetector detector) {
//        mScaleFactor = detector.getScaleFactor();
        boolean isBig = (detector.getCurrentSpan() - startSpan) >= 0;
        iDragListener.scale(isBig,
                detector.getCurrentSpanX() - detector.getPreviousSpanX(),
                detector.getCurrentSpanY() - detector.getPreviousSpanY());

        return true;
    }

    @Override
    public void onScaleEnd(@NonNull ScaleGestureDetector detector) {
        super.onScaleEnd(detector);
        iDragListener.onCancel(detector.getCurrentSpanX() - detector.getPreviousSpanX(),
                detector.getCurrentSpanY() - detector.getPreviousSpanY());
    }

    public void setOnScaleListener(MultiTouchView.IDragListener dragListener) {
        iDragListener = dragListener;
    }
}
