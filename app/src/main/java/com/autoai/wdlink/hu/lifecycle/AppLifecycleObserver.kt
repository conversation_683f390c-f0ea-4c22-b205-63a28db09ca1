import android.util.Log
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import com.autoai.common.util.AppUtil
import com.autoai.wdlink.hu.lifecycle.NetworkMonitor

class AppLifecycleObserver : LifecycleEventObserver {

    private var networkMonitor: NetworkMonitor? = null
    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        when (event) {
            Lifecycle.Event.ON_RESUME -> {
                Log.d("AppLifecycleObserver", "ON_RESUME")
                networkMonitor = NetworkMonitor(AppUtil.getContext())

            }

            Lifecycle.Event.ON_STOP -> {
                Log.d("AppLifecycleObserver", "ON_STOP")
                networkMonitor?.unregisterNetworkCallback()
            }

            else -> {
                Log.d("AppLifecycleObserver", event.name)
            }
        }
    }
}