package com.autoai.wdlink.hu.auth

import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.autoai.wdlink.hu.network.model.LicenseWelinkActivateData
import com.autoai.wdlink.hu.network.retrofit.ResponseProtocol
import com.autoai.welinkapp.model.MessageEvent
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.json.JSONException
import org.json.JSONObject

object AuthModel {
    private const val TAG = "Authority"

    var hasAuthority = true
    private val _activateResult = MutableLiveData<Pair<Int, String?>>()
    val activateResult: LiveData<Pair<Int, String?>> = _activateResult

    init {
        GlobalScope.launch(DispatcherManager.getFixThreadDispatcher()) {
            AuthRepository.mActivationData.collect {
                val pair = decodeResult(it) ?: return@collect
                Log.d(TAG, "onReceived")
                _activateResult.postValue(pair)
            }
        }
    }


    fun receivePhoneAuthResult(event: MessageEvent) {
        Log.d(TAG, "receivePhoneAuthResult")
        if (event.what == MessageEvent.RECEIVE_MESSAGE) {
            var key = ""
            try {
                val obj = event.obj.toString()
                Log.d(TAG, "MsgCallback obj = $obj")
                val json = JSONObject(obj)
                key = json.get("key").toString()
                Log.d(TAG, "MsgCallback key = $key")
                if ("onAuthResult" == key) {
                    val msg = json.getString("msg")
                    val result = decodeResultFromJson(msg) ?: return
                    _activateResult.postValue(result!!)
                    /*if (result?.first == 0 || result?.first == 2 || result?.first == 1) {
                        goMainAct()
                    } else {
                        Log.d(TAG, "onAuthResult success")
                    }*/

                }
            } catch (e: JSONException) {
                Log.d(TAG, "消息解析错误: ${e.message}")
            }
        }
    }

    private fun decodeResult(result: ResponseProtocol<LicenseWelinkActivateData>?): Pair<Int, String?>? {
        val code = result?.code
        val data = result?.data
        if (code == 200 && data != null) {
            if (data.deviceStatus == 0 || data.deviceStatus == 2) {
                hasAuthority = false
            }
            return Pair(data.deviceStatus, result.message)
        }
        return null
    }

    private fun decodeResultFromJson(json: String): Pair<Int, String?>? {
        try {
            val result = JSONObject(json)
            val code = result.getInt("code")
            // 非200错误 不用处理
            if (code == 200) {
                val data = result.getJSONObject("data")
                val status = data.getInt("deviceStatus")
                val msg = result.getString("message")
                if (status == 0 || status == 2) {
                    hasAuthority = false
                }
                return Pair(status, msg)
            }
        } catch (e: Exception) {
            Log.d(TAG, "${e.message}")
        }
        return null
    }

    /*private fun goMainAct() {
        Log.d(TAG, "goMainAct")
        CommonData.sendMsg(
            AppUtil.getContext(),
            CommonData.CONNECT_STATUS_OUT,
            CommonData.iCurrentConnectType,
            null
        );
        PopWinServiceUtils.stopPopWinService(AppUtil.getContext())

        val intent = Intent(AppUtil.getContext(), MainActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        AppUtil.getContext().startActivity(intent)
    }*/
}