package com.autoai.wdlink.hu.dialog;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothAdapter;
import android.content.Context;
import android.content.Intent;
import android.net.wifi.WifiManager;
import android.widget.Toast;

public class BluetoothWifiUtil {

    @SuppressLint("MissingPermission")
    public static void checkAndOpenBluetooth() {
        BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        if (!bluetoothAdapter.isEnabled()) {
            // todo 通过系统签名后可无感打开系统蓝牙
            bluetoothAdapter.enable();
        }
    }

    public static void checkAndOpenWifi(Context context) {
        WifiManager wifiManager = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
        if (!wifiManager.isWifiEnabled()) {
            wifiManager.setWifiEnabled(true);
            // 可以添加一个监听器来检查Wi-Fi是否真正启动
        }
    }
}