package com.autoai.wdlink.hu.castwindow.utils

import android.os.Looper

/**
 * 输出线程堆栈信息
 * <AUTHOR>
 * @date 2024/09/22
 */
class ThreadInfoUtils {
    companion object{
        @JvmStatic
        fun getStackTraces(appendMainThreadStackTraces: Boolean = false): String {
            val sb = StringBuilder()
            var t = Thread.currentThread()
            sb.append(t.toString()).append('\n')
            val stacks = t.stackTrace
            for (i in 4 until stacks.size) {
                sb.append("\tat ").append(stacks[i].toString()).append('\n')
            }
            sb.append('\n')
            if(appendMainThreadStackTraces && t != Looper.getMainLooper().thread){
                t = Looper.getMainLooper().thread
                sb.append(t.toString()).append('\n')
                for (ste in t.stackTrace) {
                    sb.append("\tat ").append(ste.toString()).append('\n')
                }
                sb.append('\n')
            }
            return sb.toString()
        }
    }
}