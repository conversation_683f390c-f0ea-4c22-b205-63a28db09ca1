package com.autoai.wdlink.hu.castwindow.scope

import androidx.annotation.IntDef

/**
 * 当前窗口状态
 */
@Retention(AnnotationRetention.SOURCE)
@IntDef(CastWindowState.NONE, CastWindowState.MINI, CastWindowState.SMALL, CastWindowState.FULL)
annotation class CastWindowState {
    companion object {
        const val NONE = -1
        const val MINI = 0
        const val SMALL = 1
        const val FULL = 2

        fun Int.toCastWindowStateStr(): String{
            return "$this:" + when (this){
                MINI -> "Mini"
                SMALL -> "Small"
                FULL -> "Fullscreen"
                else -> "NA"
            }
        }
    }
}