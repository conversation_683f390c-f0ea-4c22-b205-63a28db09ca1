package com.autoai.wdlink.hu.frame.utils;

import static com.autoai.Constant.MINI_SCALE_LANDSCAPE;
import static com.autoai.Constant.MINI_SCALE_PORTRAIT;
import static com.autoai.Constant.TEXTURE_VIEW_INITIAL_LANDSCAPE_SCALE;
import static com.autoai.Constant.TEXTURE_VIEW_INITIAL_SCALE;
import static com.autoai.car.os_adapter.interfaces.CarInfoInterface.DRIVE_STATE_OFF;
import static com.autoai.car.os_adapter.interfaces.CarInfoInterface.DRIVE_STATE_ON;
import static com.autoai.welink.param.HidConstants.TAG_IOS_CALIB;
import static com.autoai.welink.param.HidConstants.ON_HID_SUCCESS;
import static com.autoai.welink.param.HidConstants.ON_HID_FAIL;
import static com.autoai.welink.param.HidConstants.ON_HID_CONNECT_FAIL;
import static com.autoai.welink.param.HidConstants.ON_HID_APP_BACKGROUND_FAIL;
import static com.autoai.welink.param.HidConstants.ON_HID_APP_NOT_FOREGROUND;
import static com.autoai.welinkapp.model.SdkViewModel.TAG_BT;
import static com.blankj.utilcode.util.ViewUtils.runOnUiThread;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.annotation.SuppressLint;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.bluetooth.BluetoothDevice;
import android.car.drivingstate.Gear;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ServiceInfo;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.PixelFormat;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.util.Size;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.TextureView;
import android.view.VelocityTracker;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.animation.AccelerateInterpolator;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.MainThread;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.view.ViewKt;

import com.autoai.avslinkhid.api.HidIosCalibrationUtil;
import com.autoai.avslinkhid.api.HidUtil;
import com.autoai.welink.sdk.BT.BtConnectionManager;
import com.autoai.welink.sdk.BT.BlueToothSysBroadcastReceiver;
import com.autoai.car.os_adapter.interfaces.CarInfoInterface;
import com.autoai.car.os_adapter.proxy.CarInfoProxy;
import com.autoai.common.util.LogUtil;
import com.autoai.wdlink.hu.BuildConfig;
import com.autoai.wdlink.hu.ComponentName;
import com.autoai.wdlink.hu.MainActivity;
import com.autoai.wdlink.hu.MyApplication;
import com.autoai.wdlink.hu.R;
import com.autoai.wdlink.hu.auth.AuthModel;
import com.autoai.wdlink.hu.castwindow.LinkLog;
import com.autoai.wdlink.hu.castwindow.ktx.MetricsKtxKt;
import com.autoai.wdlink.hu.castwindow.ktx.ViewKtxKt;
import com.autoai.wdlink.hu.castwindow.scope.CastWindowState;
import com.autoai.wdlink.hu.castwindow.scope.MiniPositionMode;
import com.autoai.wdlink.hu.castwindow.utils.SharePreferenceUtil;
import com.autoai.wdlink.hu.castwindow.view.CastWindowStateManager;
import com.autoai.wdlink.hu.castwindow.view.SmallCastState;
import com.autoai.wdlink.hu.castwindow.view.SmallShowBean;
import com.autoai.wdlink.hu.dialog.BasePopDialog;
import com.autoai.wdlink.hu.model.DevelopModel;
import com.autoai.wdlink.hu.module.hidscreen.HidScreenTouchDelegateHelper;
import com.autoai.wdlink.hu.module.hidscreen.InterceptFrameLayout;
import com.autoai.wdlink.hu.module.hidscreen.MultiTouchView;
import com.autoai.wdlink.hu.sdk.LinkSdkModel;
import com.autoai.wdlink.hu.sdk.LinkUtil;
import com.autoai.wdlink.hu.utils.ToastUtil;
import com.autoai.welinkapp.LinkHost;
import com.autoai.welinkapp.checkconnect.CommonData;
import com.autoai.welinkapp.model.HidCallback;
import com.autoai.welinkapp.model.SdkViewModel;
import com.autoai.welinkapp.util.WindowUtil;
import com.blankj.utilcode.util.Utils;

import java.util.Objects;
import java.util.Set;

/**
 * 投屏悬浮窗
 * service + Window的形式
 */
public class PopWinService extends Service implements HidCallback {
    private static final String TAG = "PopWinService";

    /**
     * ios 清晰度问题 高为1920 宽为1080,
     * android 宽为1920 高为 1080 (ios 无横屏情况全为竖屏 车机端做的本地旋转) |||| 小窗系数
     */
    private static final float SCREEN_TEXTUREVIEW_INITIAL_SCALE = 0.42f;
    private static final float TEXTUREVIEW_INITIAL_SCALE = 0.75f;
    /**
     * 透明度
     */
    private static final float ALPHA_255 = 1.0f;
    private static final float ALPHA_77 = 0.3f;
    
    /**
     * 左下角拖动优化相关常量和变量
     */
    private static final int MIN_WIDTH = 200;
    private static final int MIN_HEIGHT = 150;
    private static final int UPDATE_THROTTLE_MS = 16; // 60fps限制
    
    // 抗抖动优化：不再需要Handler批处理，改用View变换
    
    // 左下角拖动状态记录
    private int initialWidth, initialHeight, initialX, initialY;
    private long lastUpdateTime = 0;
    
    // 抗抖动：View变换状态管理
    private boolean isLeftBottomDragging = false;
    private float tempTranslationX = 0f;

    /**
     * 全局上下文
     */
    private Context context;

    /**
     * 外接通过intent和 PopWindowService进行通信
     * service 会执行{@link #onStartCommand(Intent, int, int)}
     */
    private String action;

    private String streamAbort = null;


    /**
     * TextureView 投屏外渐变边框外透明区域
     */
    private final int transparentFrameWidth = MyApplication.application.getResources().getDimensionPixelSize(R.dimen.extend_size) * 2;
    private final int transparentFrameHeight = MyApplication.application.getResources().getDimensionPixelSize(R.dimen.extend_top_size) +
            MyApplication.application.getResources().getDimensionPixelSize(R.dimen.extend_bottom_size) + MyApplication.application.getResources().getDimensionPixelSize(R.dimen.dimen_10) * 2;
    private final int transparentTopHeight = MyApplication.application.getResources().getDimensionPixelSize(R.dimen.extend_top_size) +
            MyApplication.application.getResources().getDimensionPixelSize(R.dimen.dimen_10);
    private final int transparentBottomHeight = MyApplication.application.getResources().getDimensionPixelSize(R.dimen.extend_bottom_size) +
            MyApplication.application.getResources().getDimensionPixelSize(R.dimen.dimen_10);
    private final int surfaceTopView = MyApplication.application.getResources().getDimensionPixelSize(R.dimen.dimen_45); //+ MyApplication.application.getResources().getDimensionPixelSize(R.dimen.dimen_10);
    private final int surfaceWidthMargin = MyApplication.application.getResources().getDimensionPixelSize(R.dimen.dimen_10) * 2;
    private final int surfaceVerticalMargin = MyApplication.application.getResources().getDimensionPixelSize(R.dimen.dimen_10) * 2;

    boolean isShow = false;
    boolean isNotification = true;

    //↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓ 悬浮窗体Window 相关  start  ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓
    /**
     * wms
     */
    private WindowManager windowManager;

    /**
     * 窗体相关操作
     */
    private WindowUtil windowUtilInstance;

    /**
     * Window的属性 layout params
     */
    private WindowManager.LayoutParams popViewLayoutParams;

    /**
     * 拖动缩放窗口过程中，用于视觉过渡的window的WinLayoutParams
     */
    private WindowManager.LayoutParams coverWinLayoutParams;


    /**
     * 最大的宽高，屏幕宽高
     */
    private final int maxWidth = MetricsKtxKt.getScreenWidthPixels();
    private final int maxHeight = MetricsKtxKt.getScreenHeightPixels();
    //Dialog
    BasePopDialog basePopDialog = new BasePopDialog();
    //↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑ 悬浮窗体Window  end  ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑


    //↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓ 布局 View  start  ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓
    private TextView tvSafeTextDriver;
    private ConstraintLayout textureViewContainer;


    /**
     * 和窗体Window绑定的root 布局
     */
    private View popView;

    /**
     * 整体窗体布局popView的child
     * fixme：后续改名字 rootView
     */
    private MultiTouchView rootViewParent;

    /**
     * 实际可视区域
     * fixme：后续改名字 visibleContentView
     */
    private ConstraintLayout rootView;

    /**
     * 投屏后 【更多】按钮入口
     */
    private ImageButton fullScreenImgBtn;
    /**
     * 投屏后 【切换到全屏】按钮
     */
    private ImageButton mBlueToothOrCalibration;
    /**
     * 投屏后【更多按钮】布局
     */
    private ConstraintLayout console;
    private LinearLayout ll_console;
    private static final long LONG_CLICK_TIME = 150L;

    /**
     * ↓↓↓↓↓↓↓↓↓拖动缩放过程中的fake 过渡图层相关↓↓↓↓↓↓↓↓↓
     * she tips:coverView（fake view），是与主视图在不同window上，用于拖动缩放操作过渡使用
     */
    private View coverView;
    private Button left;
    private ImageView coverShotImageView;

    private RelativeLayout viewShotCoverParent;
    private ImageView viewShotCover;


    /**
     * 实际负责渲染的纹理view
     */
    private TextureView textureView;
    private InterceptFrameLayout textureViewParent;


    /**
     * 用于在浮窗上提示的浮层
     */
    private FrameLayout dialogView;
    private ImageView floatingImage;

    /**
     * ↓↓↓↓↓↓↓↓↓锁屏|回复投屏|重连↓↓↓↓↓↓↓↓↓
     */
    private View screenOffOnLayout;
    private TextView screenOffTitle;
    private TextView screenOffReason;
    private View screenBgve;
    private TextView recoveryCast;
//↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑


    /**
     * 走行安全图才能
     */
    private RelativeLayout screenSafeTip;
    private ConstraintLayout screenSafeDriver;


    /**
     * 刷新帧率
     */
    private TextView tv_fps;

    /**
     * --> ↓↓↓↓↓↓↓↓↓行车安全布局
     */
    private LinearLayout mWalkingButton;
    private ImageButton bt_warning;
    private Button mCoPilot;
    private Button mExitSecurity;

    /**
     * --> ↓↓↓↓↓↓↓↓↓IOS 反控校准
     */
    private RelativeLayout popCalibrationLin;
    private ImageView popCalibrationImg;
    private TextView popCalibrationTitle;
    private TextView popCalibrationContent;
    private TextView popRecalibrate;
    //↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑ 布局 View  end  ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑

    /**
     * 纪录手机端返回的视频[实际投屏区域]宽高
     */
    private Size realVideoSize = new Size(386, 864);

    /**
     * 注册前台服务的notification
     */
    private Notification notification = null;
    /**
     * 设备平台
     */
    private String platform = "";
    private int angle = 0;

    /**
     * 平台判断工具方法
     */

    /**
     * 判断是否为iOS设备
     * @return true if iOS device
     */
    private boolean isIOSDevice() {
        return platform != null && platform.contains("ios");
    }

    /**
     * 判断是否为Android设备
     * @return true if Android device
     */
    private boolean isAndroidDevice() {
        return platform != null && platform.contains("android");
    }

    /**
     * 判断是否为其他设备（主要指鸿蒙系统）
     * @return true if other device (HarmonyOS)
     */
    private boolean isOtherDevice() {
        return platform != null && !platform.isEmpty() && !isIOSDevice() && !isAndroidDevice();
    }
    /**
     * iOS设备校准状态枚举
     * 直接使用HidConstants中定义的值，避免重复定义和转换
     */
    public enum CalibrationStatus {
        SUCCESS(ON_HID_SUCCESS),                    // 0 - 校准成功
        FAILED(ON_HID_FAIL),                        // 1 - 校准失败
        BT_NOT_CONNECTED(ON_HID_CONNECT_FAIL),      // 2 - 蓝牙未连接
        APP_BACKGROUND_FAIL(ON_HID_APP_BACKGROUND_FAIL),  // 3 - 校准过程中手机APP切换到后台
        APP_NOT_FOREGROUND(ON_HID_APP_NOT_FOREGROUND),    // 21 - 请求校准时手机APP已经在后台
        NOT_CALIBRATED(-1),  // 未校准状态
        UNKNOWN(-2);         // 未知状态

        private final int value;

        CalibrationStatus(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }

        public static CalibrationStatus fromValue(int value) {
            for (CalibrationStatus status : values()) {
                if (status.value == value) {
                    return status;
                }
            }
            return UNKNOWN;
        }
    }

    /**
     * 蓝牙异常状态枚举
     * 用于表示各种蓝牙连接异常情况
     */
    public enum BluetoothAbnormalState {
        NORMAL(-1),                    // 正常状态，无异常
        BT_NOT_CONNECTED(1),           // 蓝牙未连接
        BT_DISCONNECTED(2),            // 已经互联，但是蓝牙断开
        BT_DEVICE_MISMATCHED(3),       // 蓝牙设备不匹配（连接到错误的蓝牙设备）
        IOS_NEEDS_CALIBRATION(4);      // iOS设备需要校准

        private final int value;

        BluetoothAbnormalState(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }

        public static BluetoothAbnormalState fromValue(int value) {
            for (BluetoothAbnormalState state : values()) {
                if (state.value == value) {
                    return state;
                }
            }
            return NORMAL;
        }
    }

    private BluetoothAbnormalState bluetoothAbnormalState = BluetoothAbnormalState.NORMAL;

    /**
     * 蓝牙状态管理器
     * 统一处理所有蓝牙状态相关的UI更新逻辑
     */
    private class BluetoothStateManager {

        /**
         * 更新蓝牙状态并刷新UI
         * @param newState 新的蓝牙状态
         * @param showToast 是否显示Toast提示
         */
        private void updateBluetoothState(BluetoothAbnormalState newState, boolean showToast) {
            if (mBlueToothOrCalibration == null) {
                return;
            }

            LogUtil.d(TAG_BT, "PopWinService::updateBluetoothState: " + newState + ", showToast=" + showToast);
            bluetoothAbnormalState = newState;

            runOnUiThread(() -> {
                switch (newState) {
                    case NORMAL:
                        // 蓝牙正常，隐藏警告图标
                        mBlueToothOrCalibration.setVisibility(View.INVISIBLE);
                        break;

                    case BT_NOT_CONNECTED:
                        // 蓝牙未连接
                        mBlueToothOrCalibration.setVisibility(View.VISIBLE);
                        mBlueToothOrCalibration.setImageResource(R.mipmap.link_bluetooth_failure_prompt);
                        if (showToast) {
                            ToastUtil.showToast(
                                Utils.getApp().getString(R.string.bt_not_connected_tips),
                                Utils.getApp().getString(R.string.please_connect_bluetooth),
                                Utils.getApp().getString(R.string.connect_bluetooth),
                                "", true, false, 1
                            );
                        }
                        break;

                    case BT_DISCONNECTED:
                        // 蓝牙断开连接
                        mBlueToothOrCalibration.setVisibility(View.VISIBLE);
                        mBlueToothOrCalibration.setImageResource(R.mipmap.link_bluetooth_failure_prompt);
                        if (showToast && LinkSdkModel.INSTANCE.isConnected()) {
                            ToastUtil.showToast(
                                Utils.getApp().getString(R.string.bt_disconnect_tips),
                                Utils.getApp().getString(R.string.bluetooth_connection_disconnected),
                                Utils.getApp().getString(R.string.connect_bluetooth),
                                "", true, false, 1
                            );
                        }
                        break;

                    case BT_DEVICE_MISMATCHED:
                        // 蓝牙设备不匹配
                        mBlueToothOrCalibration.setVisibility(View.VISIBLE);
                        mBlueToothOrCalibration.setImageResource(R.mipmap.link_bluetooth_failure_prompt);
                        if (showToast) {
                            ToastUtil.showToast(
                                Utils.getApp().getString(R.string.bt_not_connected_occupy),
                                Utils.getApp().getString(R.string.may_cause_abnormal_sound_of_the_anti_control_phone),
                                Utils.getApp().getString(R.string.switchBluetooth),
                                "", true, false, 1
                            );
                        }
                        break;

                    case IOS_NEEDS_CALIBRATION:
                        // iOS需要校准
                        LogUtil.d(TAG_BT, "PopWinService::isCalibrationNeeded iOS calibration needed, isIOSDevice=" + isIOSDevice());
                        if (isIOSDevice()) {
                            mBlueToothOrCalibration.setVisibility(View.VISIBLE);
                            mBlueToothOrCalibration.setImageResource(R.mipmap.link_calibration_icon);
                        } else {
                            mBlueToothOrCalibration.setVisibility(View.INVISIBLE);
                        }
                        break;

                    default:
                        LogUtil.w(TAG_BT, "PopWinService::updateBluetoothState unknown state: " + newState);
                        break;
                }
            });
        }

        /**
         * 检查蓝牙连接状态并更新UI
         */
        private void checkAndUpdateBluetoothState() {
            LogUtil.i(TAG_BT, "PopWinService::checkAndUpdateBluetoothState start, platform=" + PopWinService.this.platform);

            if (!LinkSdkModel.INSTANCE.isConnected()) {
                // 没有互联，那么就隐藏蓝牙图标
                LogUtil.d(TAG_BT, "PopWinService::checkAndUpdateBluetoothState not connected, hide BT icon");
                updateBluetoothState(BluetoothAbnormalState.NORMAL, false);
                return;
            }

            Set<BluetoothDevice> deviceSet = HidUtil.getConnectedBluetoothDevices();
            LogUtil.i(TAG_BT, "PopWinService::checkAndUpdateBluetoothState BT devices count=" + deviceSet.size() + " platform: " + platform  + " calibrationStatus: " + calibrationStatus);

            if (deviceSet.isEmpty()) {
                // 没有连接的蓝牙设备
                updateBluetoothState(BluetoothAbnormalState.BT_NOT_CONNECTED, true);
            } else {

                // iOS设备需要校准检查
                if (isCalibrationNeeded()) {
                    LogUtil.d(TAG_BT, "PopWinService::checkAndUpdateBluetoothState iOS needs calibration, status=" + calibrationStatus);
                    LogUtil.d(TAG_BT + TAG_IOS_CALIB, "BluetoothStateManager:checkAndUpdateBluetoothState iOS needs calibration, status=" + calibrationStatus);
                    updateBluetoothState(BluetoothAbnormalState.IOS_NEEDS_CALIBRATION, false);
                    return;
                }

                // 有连接的蓝牙设备，检查是否匹配
                boolean deviceMatched = false;

                // iOS & Android设备需要检查蓝牙匹配状态
                if (isAndroidDevice() || isIOSDevice()) {
                    boolean isPaired = SdkViewModel.getInstance().isBTPairedWithPhone();
                    LogUtil.i(TAG_BT, "PopWinService::checkAndUpdateBluetoothState iOS/Android BT paired=" + isPaired);

                    if (!isPaired) {
                        // 如果蓝牙未与Link设备匹配，显示蓝牙图标
                        updateBluetoothState(BluetoothAbnormalState.BT_DEVICE_MISMATCHED, true);
                        return;
                    }
                    else {
                        deviceMatched = true;
                    }
                }

                // 鸿蒙系统需要检查设备名称匹配
                if (isOtherDevice()) {
                    for (BluetoothDevice device : deviceSet) {
                        String deviceName = device.getName();
                        LogUtil.d(TAG, "PopWinService::checkAndUpdateBluetoothState device name=" + deviceName);

                        if (deviceName != null && deviceName.equals(blueToothName)) {
                            LogUtil.d(TAG_BT, "PopWinService::checkAndUpdateBluetoothState HarmonyOS device matched");
                            deviceMatched = true;
                            break;
                        }
                    }
                }

                if (deviceMatched) {
                    updateBluetoothState(BluetoothAbnormalState.NORMAL, false);
                } else if (isOtherDevice()){
                    // iOS 和 android 通过btpaired 消息来更新状态
                    LogUtil.d(TAG_BT, "PopWinService::checkAndUpdateBluetoothState HarmonyOS device not matched");
                    updateBluetoothState(BluetoothAbnormalState.BT_DEVICE_MISMATCHED, true);
                }
            }
        }

        /**
         * 处理蓝牙连接状态变化
         * @param connected 是否连接
         */
        private void onBluetoothConnectionChanged(boolean connected) {
            LogUtil.d(TAG_BT, "PopWinService::onBluetoothConnectionChanged: " + connected);

            if (!LinkSdkModel.INSTANCE.isConnected()) {
                return;
            }

            if (connected) {
                // 蓝牙连接成功，重新检查状态
                checkAndUpdateBluetoothState();
            } else {
                // 蓝牙断开连接
                updateBluetoothState(BluetoothAbnormalState.BT_DISCONNECTED, true);
            }
        }

        /**
         * 处理校准状态回调
         * @param calibrationResult 校准结果值
         */
        private void onCalibrationResult(int calibrationResult) {
            CalibrationStatus status = CalibrationStatus.fromValue(calibrationResult);
            LogUtil.d(TAG_BT, "PopWinService::onCalibrationResult: " + calibrationResult + ", status=" + status);
            LogUtil.d(TAG_BT + TAG_IOS_CALIB, "BluetoothStateManager:onCalibrationResult calibration result=" + calibrationResult + ", status=" + status);

            switch (status) {
                case SUCCESS:
                    // 校准成功
                    LogUtil.d(TAG + TAG_IOS_CALIB, "PopWinService:BluetoothStateManager:onCalibrationResult iOS calibration SUCCESS");
                    updateBluetoothState(BluetoothAbnormalState.NORMAL, false);
                    runOnUiThread(() -> {
                        popCalibrationLin.setVisibility(View.GONE);
                        if (countDownTimer != null) {
                            countDownTimer.cancel();
                        }
                    });
                    break;

                case FAILED:
                    // 校准失败 - 显示校准图标
                    LogUtil.d(TAG + TAG_IOS_CALIB, "BluetoothStateManager:onCalibrationResult iOS calibration FAILED");
                    if (SdkViewModel.getInstance().isBTPairedWithPhone()) {
                        updateBluetoothState(BluetoothAbnormalState.IOS_NEEDS_CALIBRATION, false);
                        runOnUiThread(() -> {
                            popCalibrationLin.setVisibility(View.VISIBLE);
                            popCalibrationImg.setImageResource(R.mipmap.link_calibration_failed_img);
                            popCalibrationTitle.setText(Utils.getApp().getString(R.string.calibration_failed));
                        });
                    }
                    else {
                        updateBluetoothState(BluetoothAbnormalState.BT_NOT_CONNECTED, false);
                    }

                    break;

                case APP_NOT_FOREGROUND:
                    // 请求校准时手机app未在前台 - 显示校准图标并提示用户
                    LogUtil.d(TAG + TAG_IOS_CALIB, "PopWinService:BluetoothStateManager:onCalibrationResult iOS app not in foreground");
                    updateBluetoothState(BluetoothAbnormalState.IOS_NEEDS_CALIBRATION, false);
                    runOnUiThread(() -> {
                        ToastUtil.showToast(
                            Utils.getApp().getString(R.string.please_open_the_welink_app_on_your_phone),
                            "", "", "", false, false, -1
                        );
                    });
                    break;

                case APP_BACKGROUND_FAIL:
                    // 校准过程中手机app切换到后台 - 显示校准图标
                    LogUtil.d(TAG + TAG_IOS_CALIB, "PopWinService:BluetoothStateManager:onCalibrationResult iOS app background during calibration");
                    updateBluetoothState(BluetoothAbnormalState.IOS_NEEDS_CALIBRATION, false);
                    break;

                case BT_NOT_CONNECTED:
                    // 蓝牙未连接 - 显示校准图标
                    LogUtil.d(TAG + TAG_IOS_CALIB, "PopWinService:BluetoothStateManager:onCalibrationResult Bluetooth not connected");
                    updateBluetoothState(BluetoothAbnormalState.BT_NOT_CONNECTED, false);
                    break;

                case NOT_CALIBRATED:
                case UNKNOWN:
                    // 未校准或未知状态 - 不显示校准图标，使用普通蓝牙连接检查
                    checkAndUpdateBluetoothState();
                    break;

                default:
                    LogUtil.w(TAG_BT, "PopWinService::onCalibrationResult unknown calibration status: " + status);
                    checkAndUpdateBluetoothState();
                    break;
            }
        }
    }

    private BluetoothStateManager bluetoothStateManager = new BluetoothStateManager();

    /**
     * 处理蓝牙按钮点击事件
     */
    private void handleBluetoothButtonClick() {
        LogUtil.d(TAG_BT, "PopWinService::handleBluetoothButtonClick: " + bluetoothAbnormalState);

        switch (bluetoothAbnormalState) {
            case BT_NOT_CONNECTED:
                ToastUtil.showToast(
                    Utils.getApp().getString(R.string.bt_not_connected_tips),
                    Utils.getApp().getString(R.string.please_connect_bluetooth),
                    Utils.getApp().getString(R.string.connect_bluetooth),
                    "", true, false, 1
                );
                break;

            case BT_DISCONNECTED:
                ToastUtil.showToast(
                    Utils.getApp().getString(R.string.bt_disconnect_tips),
                    Utils.getApp().getString(R.string.bluetooth_connection_disconnected),
                    Utils.getApp().getString(R.string.connect_bluetooth),
                    "", true, false, 1
                );
                break;

            case BT_DEVICE_MISMATCHED:
                ToastUtil.showToast(
                    Utils.getApp().getString(R.string.bt_not_connected_occupy),
                    Utils.getApp().getString(R.string.may_cause_abnormal_sound_of_the_anti_control_phone),
                    Utils.getApp().getString(R.string.switchBluetooth),
                    "", true, false, 1
                );
                break;

            case IOS_NEEDS_CALIBRATION:
                // iOS校准：隐藏按钮并开始校准
                LogUtil.d(TAG + TAG_IOS_CALIB, "PopWinService:handleBluetoothButtonClick user triggered iOS calibration");
                mBlueToothOrCalibration.setVisibility(View.INVISIBLE);
                LinkSdkModel.INSTANCE.startVerification();
                break;

            case NORMAL:
            default:
                // 正常状态或未知状态，无需处理
                break;
        }
    }

    private String blueToothName = "";
    private boolean first = true;
    /**
     * fixme:@光诚：isUpdateUI flag是否需要移除
     * 控制断开互联后重新互联，浮窗UI尺寸是否更新
     * true投屏手机 分辨率发生变化更新，防止变形
     * false 投屏分辨率未发生变化显示原尺寸
     */
    private boolean isUpdateUI = false;

    /**
     * 当前悬浮窗可是区域宽高
     */
    int currentWidth;
    int currentHeight;

    /**
     * 窗口缩放，最小缩放比例
     */
    float miniScalePortrait = 0.71f;
    float miniScaleLandscape = 0.41f;

    /**
     * todo：后续版本移除
     */

    /**
     * 缩放操作时候，rootview的截图
     */
    private Drawable drawable;
    private Handler handler = new Handler();
    private final Runnable runnable = new Runnable() {
        @Override
        public void run() {
            captureWindowToBitmap();
            if (drawable != null) {
                LogUtil.i(TAG, "PopWinService::刷新假小窗图片");
                coverShotImageView.setBackgroundDrawable(drawable);
            }
            handler.postDelayed(runnable, 30);
        }
    };


    private final Runnable runnable2 = new Runnable() {
        @Override
        public void run() {
            rootViewParent.setAlpha(1);
            coverView.setAlpha(0);
        }
    };

    /**
     * 档位监听变化回调
     */
    private CarInfoInterface.DriveStateChangedListener driveStateChangedListener = new CarInfoInterface.DriveStateChangedListener() {
        @Override
        public void onDriveStateChanged(int state) {
            LogUtil.d(TAG, "PopWinService::onDriveStateChanged: state = " + state);
            if (DevelopModel.getInstance().getDriveSafetyVersion()) {
                displayBaselineRunningStatus(state);
            } else {
                if (screenSafeTip != null) {
                    if (state == 1) {
                        screenSafeTip.setVisibility(View.VISIBLE);
                    } else {
                        screenSafeTip.setVisibility(View.GONE);
                    }
                }
            }
        }
    };

    /**
     * 悬浮窗x轴位置
     */
    private int position;

    /**
     * 监听到档位变化，结合当前
     * 1）投屏状态
     * 2）横竖屏状态
     * 3）是否锁屏状态
     * 综合刷新ui
     *
     * @param state
     */
    private void displayBaselineRunningStatus(int state) {
        if (screenSafeDriver != null) {
            boolean aBoolean = SharePreferenceUtil.Companion.getBoolean(MyApplication.application, "isPassenger", true);
            LogUtil.d(TAG, "PopWinService::displayBaselineRunningStatus: state =  " + state + " --  aBoolean =  " + aBoolean);
            if (state == 1 && aBoolean && LinkSdkModel.INSTANCE.getConnectStatus() == CommonData.LINK_STATUS_SCREEN_MIRRORING && !"screen_off".equals(action)) {
                screenSafeDriver.setVisibility(View.VISIBLE);
                if (angle == 0 || angle == 180) {
                    mWalkingButton.setOrientation(LinearLayout.VERTICAL);
                    ViewGroup.MarginLayoutParams lp = (ViewGroup.MarginLayoutParams) mCoPilot.getLayoutParams();
                    lp.width = ViewGroup.LayoutParams.MATCH_PARENT;
                    lp.setMargins(getResources().getInteger(R.integer.safe_driver_vertical_passenger_margin_start), getResources().getInteger(R.integer.safe_driver_vertical_passenger_margin_top), getResources().getInteger(R.integer.safe_driver_vertical_passenger_margin_end), getResources().getInteger(R.integer.safe_driver_vertical_passenger_margin_bottom));
                    mCoPilot.setLayoutParams(lp);
                    ViewGroup.MarginLayoutParams lps = (ViewGroup.MarginLayoutParams) mExitSecurity.getLayoutParams();
                    lps.width = ViewGroup.LayoutParams.MATCH_PARENT;
                    lps.setMargins(getResources().getInteger(R.integer.safe_driver_vertical_exit_margin_start), getResources().getInteger(R.integer.safe_driver_vertical_exit_margin_top), getResources().getInteger(R.integer.safe_driver_vertical_exit_margin_end), getResources().getInteger(R.integer.safe_driver_vertical_exit_margin_bottom));
                    mExitSecurity.setLayoutParams(lps);
                } else {
                    mWalkingButton.setOrientation(LinearLayout.HORIZONTAL);
                    ViewGroup.MarginLayoutParams lp = (ViewGroup.MarginLayoutParams) mCoPilot.getLayoutParams();
                    lp.width = 300;
                    lp.setMargins(getResources().getInteger(R.integer.safe_driver_horizontal_passenger_margin_start), getResources().getInteger(R.integer.safe_driver_horizontal_passenger_margin_top), getResources().getInteger(R.integer.safe_driver_horizontal_passenger_margin_end), getResources().getInteger(R.integer.safe_driver_horizontal_passenger_margin_bottom));
                    mCoPilot.setLayoutParams(lp);
                    ViewGroup.MarginLayoutParams lps = (ViewGroup.MarginLayoutParams) mExitSecurity.getLayoutParams();
                    lps.width = 300;
                    lps.setMargins(getResources().getInteger(R.integer.safe_driver_horizontal_exit_margin_start), getResources().getInteger(R.integer.safe_driver_horizontal_exit_margin_top), getResources().getInteger(R.integer.safe_driver_horizontal_exit_margin_end), getResources().getInteger(R.integer.safe_driver_horizontal_exit_margin_bottom));
                    mExitSecurity.setLayoutParams(lps);

                }
                mExitSecurity.setOnClickListener(v -> {
                    LinkUtil.destoryServices(mExitSecurity.getContext());
                });
                mCoPilot.setOnClickListener(v -> {
                    SharePreferenceUtil.Companion.saveBoolean(MyApplication.application, "isPassenger", false);
                    screenSafeDriver.setVisibility(View.GONE);
                });
            } else {
                screenSafeDriver.setVisibility(View.GONE);

            }
        }
    }

    private CountDownTimer countDownTimer;
    private CalibrationStatus calibrationStatus = CalibrationStatus.NOT_CALIBRATED;
    //单个移动按钮是否隐藏
    private boolean moveIsGone = false;

    @Override
    public void onCreate() {
        super.onCreate();
        LogUtil.i(TAG, "PopWinService onCreate: invoke");

        context = this;
        handler = new Handler(Looper.myLooper());
        windowManager = (WindowManager) getSystemService(WINDOW_SERVICE);
        windowUtilInstance = WindowUtil.getInstance(this);
        //-->前台服务notification
        notification = createNotificationChannel();
        BtConnectionManager.getInstance().registerBluetoothReceiver(btConnectedListener, false);
        //行车安全提示监听
        CarInfoProxy.INSTANCE.addVehicleGearChangeListener(driveStateChangedListener);
        //HidScreenConnectManager.getInstance().startGetHidProfileAndRegisterScreenDigitizer(context, false);
        /*
        * */
        AuthModel.INSTANCE.getActivateResult().observeForever(pair -> {
            int status = pair.component1();
            String msg = pair.component2();
            LogUtil.d(TAG, "PopWinService::getActivateResult: " + status + ", " + msg);
            if (!LinkUtil.isConnected()) {
                return;
            }
            if (status == 0 || status == 2) {
                CommonData.sendMsg(this, CommonData.CONNECT_STATUS_OUT, CommonData.iCurrentConnectType, null);
                destroyThumbWindow();
                LinkHost.registerUiResponder(null);
                LinkUtil.stopForLink(MyApplication.application);

                Intent intent = new Intent(context, MainActivity.class);
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                startActivity(intent);
            }
        });

        LinkUtil.setOnVideoFrameListener(fps -> {
            if (tv_fps != null) {
                LogUtil.d(TAG, "PopWinService::onVideoFrameListener------>fps:" + fps);
                tv_fps.setText("Fps:" + fps);
            }
        });
    }

    /**
     * 外部模块和浮窗Service交互，是通过intent方式，意味着onStartCommand承担交互入口
     *
     * @param intent The Intent supplied to {@link android.content.Context#startService},
     *               action = intent.getAction(); 通过action进行
     * @return
     */
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent == null) {
            return super.onStartCommand(intent, flags, startId);
        }
        action = intent.getAction();
        if (null != action && action.equals("stream_abort")) {
            streamAbort = "stream_abort";
        }
        LogUtil.d(TAG, "PopWinService::onStartCommand------>action：" + action + ",isShow:" + isShow + ",popView=" + popView + "----");
        switch (action != null ? action : "null") {
            case "start":
                if (isNotification) {
                    //FIXME:应该在 onCreate 就调用，这里如果调用晚了会 导致 crash
                    if (notification == null) {
                        notification = createNotificationChannel();
                    }
                    isNotification = false;
                    if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE){
                        startForeground(1, notification, ServiceInfo.FOREGROUND_SERVICE_TYPE_CONNECTED_DEVICE);
                    }else{
                        startForeground(1, notification);
                    }

                }
                //--> 初始化view
                createView();
                //--> 1.初始化互联sdk 2.对渲染视图textureView进行初始化
                LinkUtil.readyForLink(this, textureView);
                break;
            case "show":
                //断开后重新互联成功，隐藏断开重连界面
                notifyScreenState(action);
                //android 手机传入蓝牙信息
                blueToothName = LinkSdkModel.INSTANCE.getAddress();
                LogUtil.d(TAG, "onStartCommand------>blueToothName :" + LinkSdkModel.INSTANCE.getAddress());
                //--> 根据档位刷新ui
                gearInformationAcquisition();
                //--> 蓝牙icon状态
                determineIfBluetoothIsConnected();
                if (!isShow) {
                    showThumbWindow();
                }
                if (null != streamAbort) {
                    notifyScreenState("stream_abort");
                }
                break;
            case "stop":
                //todo 断开后恢复竖屏
                if (angle == 90 || angle == 270) {
                    isScreenChange = true;
                    this.angle = 0;
                    runOnUiThread(() -> CastWindowStateManager.INSTANCE.onScreenRotationChange(503, 1080, 0, -1, platform));
                }
                if (popView != null) {
                    LinkUtil.stopForLink(MyApplication.application);
                }
                handler.removeCallbacksAndMessages(null);
                // fix 断开连接时点击左下角浮窗出现闪烁的问题
                handler.post(runnable);
                handler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        handler.removeCallbacksAndMessages(null);
                    }
                }, 500);
                if (isShow) {
                    isUpdateUI = true;
                    first = true;
//                    destroyThumbWindow();
                }
                streamAbort = null;
                switchSmallCastIfFullscreen(action);
                notifyScreenState(action);
                break;
            case "remove":
                if (popView != null) {
                    LinkUtil.stopForLink(MyApplication.application);
                }
                first = true;
                handler.removeCallbacksAndMessages(null);
                if (isShow) {
                    destroyThumbWindow();
                }
                break;

            case "showDialog":
                showDialog();
                break;
            case "dismissDialog":
                dismissDialog();
                break;
            case "screen_off":
            case "screen_on":
            case "stream_abort":
            case "stream_recovery":
            case "cancel_cast":
            case "connected":
            case "foreground":
            case "background":
                if (action.equals("screen_on")) {
                    gearInformationAcquisition();
                }
                if (action.equals("screen_off")){
                    if (angle == 90 || angle == 270) {
                        isScreenChange = true;
                        this.angle = 0;
                        runOnUiThread(() -> CastWindowStateManager.INSTANCE.onScreenRotationChange(503, 1080, 0, -1, platform));
                    }
                }
                switchSmallCastIfFullscreen(action);
                notifyScreenState(action);
                break;
            default://do nothing
        }
        return START_NOT_STICKY;
    }

    private void windowLayoutAnimator(int startX, int endX, int duration) {
        LogUtil.d(TAG, "PopWinService::windowLayoutAnimator------>startX:" + startX + ",endX:" + endX + ",duration:" + duration);
        if (popViewLayoutParams != null && popView != null && windowManager != null) {
            ValueAnimator an = ValueAnimator.ofFloat(startX, endX);
            an.setDuration(duration);
            an.setInterpolator(new AccelerateInterpolator());
            an.start();
            an.addUpdateListener(animation -> {
                float animatedValue = (float) animation.getAnimatedValue();
                popViewLayoutParams.x = (int) animatedValue;
                updateWindowLayout();
                if (animatedValue >= endX) {
                    if (isViewAttachedToWindow(popView)) {
                        updateCoverView(0);
                    }
                }
            });
        }
    }


    /**
     * Android机制，系统前台服务需要声明notify
     *
     * @return
     */
    @RequiresApi(api = Build.VERSION_CODES.O)
    private Notification createNotificationChannel() {
        NotificationManager mNotificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        // 通知渠道的id
        String id = "my_channel_01";
        // 用户可以看到的通知渠道的名字.
        String name = "Welink";
//      用户可以看到的通知渠道的描述
        String description = "Welink Demo";
        int importance = NotificationManager.IMPORTANCE_HIGH;
        NotificationChannel mChannel = new NotificationChannel(id, name, importance);
//         配置通知渠道的属性
        mChannel.setDescription(description);
//         设置通知出现时的闪灯（如果 android 设备支持的话）
        mChannel.enableLights(true);
        mChannel.setLightColor(Color.RED);
//         设置通知出现时的震动（如果 android 设备支持的话）
        mChannel.enableVibration(true);
        mChannel.setVibrationPattern(new long[]{100, 200, 300, 400, 500, 400, 300, 200, 400});
//         最后在notificationmanager中创建该通知渠道 //
        mNotificationManager.createNotificationChannel(mChannel);

        // 通知渠道的id
        String CHANNEL_ID = "my_channel_01";
        // Create a notification and set the notification channel.
        return new Notification.Builder(this, name)
                .setContentTitle("New Message").setContentText("You've received new messages.")
                .setSmallIcon(R.mipmap.link_ic_launcher)
                .setChannelId(CHANNEL_ID)
                .build();
    }

    /**
     * 初始化布局相关
     */
    private void createView() {
        if (popView != null) {
            LinkLog.i( TAG, "createView: popView != null");
            return;
        }
        //--> 初始化Window属性
        setPopViewWindowLayoutParams();
        //--> 初始化layout
        inflateView();
        //--> 初始化view 触控事件
        setViewListener();
        //--> 设置横竖屏监听
        setPhoneScreenRotationChange();
        //--> 反控校准状态回调
        CalibrationMonitoringCallback();
    }

    /**
     * 设置窗口属性
     */
    private void setPopViewWindowLayoutParams() {
        popViewLayoutParams = new WindowManager.LayoutParams(windowUtilInstance.getScreenWidth(), windowUtilInstance.getScreenHeight(), 0, 0, PixelFormat.TRANSPARENT);
        popViewLayoutParams.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
        popViewLayoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                | WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
                | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
                | WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN;
        //对齐方式
        popViewLayoutParams.gravity = Gravity.START | Gravity.TOP;
        popViewLayoutParams.x = 125;
        popViewLayoutParams.y = 0;

        //悬浮窗 coverWinLayoutParams
        coverWinLayoutParams = new WindowManager.LayoutParams(200, 200, 0, 0, PixelFormat.TRANSPARENT);
        coverWinLayoutParams.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
        coverWinLayoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE
                | WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
                | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
                | WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN;
        coverWinLayoutParams.gravity = Gravity.START | Gravity.TOP;
    }

    /**
     * 初始化view
     */
    private void inflateView() {
        //--> 和窗体Window绑定的root 布局
        popView = LayoutInflater.from(this).inflate(R.layout.my_videoview_new, null);

        rootViewParent = popView.findViewById(R.id.root_view_parent);
        rootView = popView.findViewById(R.id.root_view);

        coverView = LayoutInflater.from(this).inflate(R.layout.my_videoview_cover, null);
        left = coverView.findViewById(R.id.left);
        coverShotImageView = coverView.findViewById(R.id.cover_shot);
        tv_fps = popView.findViewById(R.id.tv_fps);
        viewShotCoverParent = popView.findViewById(R.id.view_shot_cover_parent);
        viewShotCover = popView.findViewById(R.id.view_shot_cover);
        textureView = popView.findViewById(R.id.phoneData_sv);
        textureViewParent = popView.findViewById(R.id.sv_framelayout_inner);
        textureViewContainer = popView.findViewById(R.id.sv_framelayout);
        dialogView = popView.findViewById(R.id.dialog_view);

        screenOffOnLayout = popView.findViewById(R.id.sc_on_off_tip);

        screenOffTitle = popView.findViewById(R.id.screen_off_title);
        screenOffReason = popView.findViewById(R.id.screen_off_reason);
        recoveryCast = popView.findViewById(R.id.recovery_cast);
        screenBgve = popView.findViewById(R.id.screen_bgve);
        floatingImage = popView.findViewById(R.id.floating_window_image);
        console = popView.findViewById(R.id.console);
        ll_console = popView.findViewById(R.id.ll_console);

        if (ll_console.getVisibility() == View.GONE) {
            moveIsGone = true;
            console.setVisibility(View.VISIBLE);
        } else {
            console.setVisibility(View.GONE);
        }

        screenSafeTip = popView.findViewById(R.id.sc_safe_tip);
        screenSafeDriver = popView.findViewById(R.id.sc_safe_driver);
        mWalkingButton = screenSafeDriver.findViewById(R.id.button_combination);
        mCoPilot = screenSafeDriver.findViewById(R.id.bt_passenger);
        mExitSecurity = screenSafeDriver.findViewById(R.id.bt_safe_exit);

        tvSafeTextDriver = popView.findViewById(R.id.tv_safe_text_driver);
        fullScreenImgBtn = popView.findViewById(R.id.fullscreen);
        popCalibrationLin = popView.findViewById(R.id.pop_calibration_lin);
        popCalibrationImg = popView.findViewById(R.id.calibration_failed_img);
        popCalibrationTitle = popView.findViewById(R.id.pop_calibration_title);
        popCalibrationContent = popView.findViewById(R.id.pop_calibration_content);
        popRecalibrate = popView.findViewById(R.id.pop_recalibrate);
        mBlueToothOrCalibration = popView.findViewById(R.id.calibrate_bluetooth);
        //初始化 TextureView
        CastWindowStateManager.init(textureView);
        //设置 textureView 圆角
        ViewKtxKt.setOutlineProvider(popView.findViewById(R.id.sv_framelayout_inner), MetricsKtxKt.dpToPx(30));
        ViewKtxKt.setOutlineProvider(screenOffOnLayout, MetricsKtxKt.dpToPx(30));
        ViewKtxKt.setOutlineProvider(screenSafeTip, MetricsKtxKt.dpToPx(30));
        ViewKtxKt.setOutlineProvider(screenSafeDriver, MetricsKtxKt.dpToPx(30));
        ViewKtxKt.setOutlineProvider(viewShotCover, MetricsKtxKt.dpToPx(30));
    }


    @SuppressLint("ClickableViewAccessibility")
    private void setViewListener() {
        //-->右上角关闭投影，隐藏的测试功能
      /*  popView.findViewById(R.id.close).setOnClickListener(view -> {
            destroyThumbWindow();
            CommonData.sendMsg(this, CommonData.CONNECT_STATUS_OUT, CommonData.iCurrentConnectType, null);
        });*/

        // --> 最小化
        popView.findViewById(R.id.mini).setOnTouchListener((view, motionEvent) -> {
            if (motionEvent.getAction() == MotionEvent.ACTION_DOWN) {
                view.setSelected(!view.isSelected());
            } else if (motionEvent.getAction() == MotionEvent.ACTION_UP) {
                view.setSelected(!view.isSelected());
                LogUtil.d(TAG, "PopWinService::mini  MINI_LEFT = " + popViewLayoutParams.height + " ----  " + popViewLayoutParams.y);
                position = popViewLayoutParams.x;
                updatePosition(position);
                if ((popViewLayoutParams.x + (popViewLayoutParams.width + transparentFrameWidth) / 2) < maxWidth / 2) {
                    //贴边的视觉过渡动画
                    windowLayoutAnimator(position, 0, 100);
                    popView.postDelayed(() -> {
                        CastWindowStateManager.switchState(CastWindowState.MINI, new MiniPisition(MiniPositionMode.LEFT, ((popViewLayoutParams.height + surfaceTopView) / 2) + popViewLayoutParams.y, position));
                    }, 110);

                } else {
                    int endX = MetricsKtxKt.getScreenWidthPixels() - currentWidth - transparentFrameWidth;
                    windowLayoutAnimator(position, endX, 100);
                    popView.postDelayed(() -> {
                        CastWindowStateManager.switchState(CastWindowState.MINI, new MiniPisition(MiniPositionMode.RIGHT, ((popViewLayoutParams.height + surfaceTopView) / 2) + popViewLayoutParams.y, position));
                    }, 110);
                }
            }
            return false;
        });

        // --> 切换全屏
        popView.findViewById(R.id.fullscreen).setOnClickListener(view -> {
            if (lockViewVisible()) {
                LogUtil.d(TAG, "PopWinService::lockViewVisible");
                //锁屏或断流情况下不可全屏
                return;
            }

            //-->视觉过渡动画：全屏的情况下（FullActivity），视频居中显示，做一个视觉平移，切换流程
            handler.post(() -> {
                position = popViewLayoutParams.x;
                updatePosition(position);
                int width = getResources().getDisplayMetrics().widthPixels;
                int endX = width / 2 - (popViewLayoutParams.width / 2);
                windowLayoutAnimator(position, endX, 80);
            });
            //--> 触发全屏切换
            handler.postDelayed(() -> {
                CastWindowStateManager.switchFullState(position);
            }, 85);
        });

        popView.findViewById(R.id.calibrate_bluetooth).setOnClickListener(view -> {
            if (lockViewVisible()) {
                //锁屏或断流情况下不可全屏
                LogUtil.d(TAG, "PopWinService::lockViewVisible");
                return;
            }
            // 处理蓝牙按钮点击事件
            handleBluetoothButtonClick();
        });

        /**
         * moveView touch监听
         */
        View.OnTouchListener moveTouchListener = new View.OnTouchListener() {
            private final int maximumFlingVelocity = ViewConfiguration.get(context).getScaledMaximumFlingVelocity();
            private final int minimumFlingVelocity = ViewConfiguration.get(context).getScaledMinimumFlingVelocity();
            private int windowX, windowY, clampX, clampY, downX, downY;
            private VelocityTracker mVelocityTracker = null;
            private boolean isOverLimit = false;
            private long downTime = 0L;

            @Override
            public boolean onTouch(View moveView, MotionEvent event) {
                int rawX = (int) event.getRawX();
                int rawY = (int) event.getRawY();
                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        downTime = System.currentTimeMillis();
                        moveView.setSelected(!moveView.isSelected());
                        windowX = popViewLayoutParams.x;
                        windowY = popViewLayoutParams.y;
                        clampX = MetricsKtxKt.getScreenWidthPixels() - popViewLayoutParams.width;
                        clampY = MetricsKtxKt.getScreenHeightPixels() - popViewLayoutParams.height;
                        downX = rawX;
                        downY = rawY;
                        LinkLog.d(ComponentName.WindowSwitcher + TAG, "PopWinService::move onTouch:Down: "
                                + "layoutParams.width = " + popViewLayoutParams.width + ", layoutParams.height = " + popViewLayoutParams.height
                                + ", windowX = " + windowX + ", windowY = " + windowY
                                + ", clampX = " + clampX + ", clampY = " + clampY
                                + ", downX = " + downX + ", downY = " + downY);
                        isOverLimit = false;
                        //速度计算
                        if (mVelocityTracker == null) {
                            mVelocityTracker = VelocityTracker.obtain();
                        } else {
                            mVelocityTracker.clear();
                        }
                        mVelocityTracker.addMovement(event);
                        break;
                    case MotionEvent.ACTION_MOVE:
                        popViewLayoutParams.x = windowX + rawX - downX;
                        popViewLayoutParams.y = Math.min(Math.max(0, windowY + rawY - downY), clampY);
                        updateWindowLayout();
                        //当划出屏幕时 alpha 渐变
                        int x = Math.min(Math.max(0, popViewLayoutParams.x), clampX);
                        if (x != popViewLayoutParams.x) {
                            int offsetX = Math.abs(x - popViewLayoutParams.x);
                            float alpha = Math.max(1f - (float) offsetX * 2 / popViewLayoutParams.width, 0);
                            LinkLog.d(ComponentName.WindowSwitcher + TAG, "PopWinService::move onTouch:Move: offsetX = " + offsetX + ", alpha = " + alpha + ", layoutParams.width = " + popViewLayoutParams.width);
                            popView.setAlpha(alpha);
                            isOverLimit = true;
                        } else {
                            popView.setAlpha(1);
                            isOverLimit = false;
                        }
                        mVelocityTracker.addMovement(event);
                        break;
                    case MotionEvent.ACTION_UP:
                    case MotionEvent.ACTION_CANCEL:
                        updateCoverView(0);
                        moveView.setSelected(!moveView.isSelected());

                        if (System.currentTimeMillis() - downTime <= LONG_CLICK_TIME) {
                            if (moveIsGone) return true;

                            if (ll_console.getVisibility() == View.GONE) {
                                ll_console.setVisibility(View.VISIBLE);
                                console.setVisibility(View.GONE);
                            } else {
                                ll_console.setVisibility(View.GONE);
                                console.setVisibility(View.VISIBLE);
                            }
                            downTime = System.currentTimeMillis();
                            return false;
                        }

                        if (isOverLimit) {//边界外处理
                            mVelocityTracker.computeCurrentVelocity(1000, maximumFlingVelocity);
                            float xVelocity = mVelocityTracker.getXVelocity();
                            LinkLog.d(ComponentName.WindowSwitcher + TAG, "PopWinService::move onTouch:up|cancel: xVelocity = " + xVelocity + ", minimumFlingVelocity = " + minimumFlingVelocity);
                            if (mVelocityTracker != null) {
                                mVelocityTracker.recycle();
                                mVelocityTracker = null;
                            }
                            if (popViewLayoutParams.x < 0) { //超出左侧边界
                                //回弹到屏幕内
                                popViewLayoutParams.x = 0;
                                if (xVelocity < -minimumFlingVelocity * 5 || popView.getAlpha() < 0.5f) { //向左 fling 或超出投屏宽度一半
                                    CastWindowStateManager.switchState(CastWindowState.MINI, new MiniPisition(MiniPositionMode.LEFT, ((popViewLayoutParams.height + surfaceTopView) / 2) + popViewLayoutParams.y, popViewLayoutParams.x));

                                    //TODO: 先简化处理，不做动画
                                } else {
                                    popView.setAlpha(1f);
                                    updateWindowLayout();
                                }
                            } else { //超出右侧边界
                                //回弹到屏幕内
                                popViewLayoutParams.x = clampX;
                                if (xVelocity > minimumFlingVelocity * 5 || popView.getAlpha() < 0.5f) { //向右 fling 或超出投屏宽度一半
                                    CastWindowStateManager.switchState(CastWindowState.MINI, new MiniPisition(MiniPositionMode.RIGHT, ((popViewLayoutParams.height + surfaceTopView) / 2) + popViewLayoutParams.y, popViewLayoutParams.x));

                                } else {
                                    popView.setAlpha(1f);
                                    updateWindowLayout();
                                }
                            }
                        }
                        break;
                }
                return false;
            }
        };

        //工具条
        popView.findViewById(R.id.iv_move).setOnTouchListener(moveTouchListener);
        popView.findViewById(R.id.move).setOnTouchListener(moveTouchListener);
        //-->右下角窗体缩放
        popView.findViewById(R.id.resize).setOnTouchListener(new View.OnTouchListener() {
            private float mStartX, mStartY;
            private float mMoveX, mMoveY;

            @SuppressLint("UseCompatLoadingForDrawables")
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                int width = textureViewParent.getWidth();
                int height = textureViewParent.getHeight();
                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        v.setBackground(Utils.getApp().getDrawable(R.drawable.btn_resize_move_bg2));
                        popView.findViewById(R.id.resize_left).setEnabled(false);
                        LogUtil.d(TAG, "PopWinService::resize transparentFrameWidth " + transparentFrameWidth
                                + ", surfaceWidthMargin " + surfaceWidthMargin);
                        LogUtil.d(TAG, "PopWinService::resize transparentTopHeight " + transparentTopHeight
                                + ", transparentBottomHeight " + transparentBottomHeight
                                + ", transparentFrameHeight " + transparentFrameHeight);
                        mStartX = event.getX();
                        mStartY = event.getY();
                        //--> 计算可拖动区域最大范围{@link popViewWidthMax}  {@link popViewHeightMax}
                        //防止点击缩放左侧时快速点击右侧滑动偏移问题
                        updatePopViewMaxSize(1);
                        changePopViewSize(1);
                        break;
                    case MotionEvent.ACTION_MOVE:
                        v.setBackground(Utils.getApp().getDrawable(R.drawable.btn_resize_move_bg2));
                        left.setBackground(Utils.getApp().getDrawable(R.drawable.btn_resize_move_bg2));
                        mMoveX = event.getX();
                        mMoveY = event.getY();
                        int dx = (int) (mMoveX - mStartX);
                        int dy = (int) (mMoveY - mStartY);
//                        if (dx == 0 || dy == 0) {
//                            break;
//                        }
                        // ACTION_DOWN有概率会触发ACTION_MOVE 方法，偏离值在2 或 3 ，如因此修改遇到其他问题可切换为上面的=0的判断
                        if (Math.abs(dx) <= 3 || Math.abs(dy) <= 3) {
                            LogUtil.d(TAG, "PopWinService::resize return");
                            break;
                        }
                        int dW = width + dx;
                        int dH = height + dy;
                        scaleView(dW, dH, 1);
                        break;
                    case MotionEvent.ACTION_UP:
                        // 恢复UI状态
                        v.postDelayed(() -> {
                            v.setBackground(Utils.getApp().getDrawable(R.drawable.btn_resize_move_bg));
                        }, 1000);
                        popView.findViewById(R.id.resize_left).setEnabled(true);

                        LogUtil.d(TAG, "PopWinService::resize ACTION UP " + (popView == null));
                        if (popView == null) {
                            break;
                        }

                        // 通知 iOS 窗口大小变化，用于复归重新校准
                        HidIosCalibrationUtil.updateViewSize(currentWidth, currentHeight);

                        // 直接更新WindowManager（如果需要）
                        boolean doIf = false;
                        if ((currentWidth + transparentFrameWidth) < popViewWidthMax) {
                            popViewLayoutParams.width = currentWidth + transparentFrameWidth;
                            popViewLayoutParams.height = Math.min(currentHeight + transparentFrameHeight + surfaceTopView, MetricsKtxKt.getScreenHeightPixels());
                            windowManager.updateViewLayout(popView, popViewLayoutParams);
                            doIf = true;
                        }
                        LogUtil.d(TAG, "PopWinService::resize doIf " + doIf + " " + popViewLayoutParams.width
                                + ", " + popViewLayoutParams.height + "  ccc " + currentWidth + "," + currentHeight);
                        break;
                    default:
                        LogUtil.d(TAG, "PopWinService::resize other Action " + event.getAction());
                }
                return false;
            }
        });

        //-->左下角窗体缩放
        popView.findViewById(R.id.resize_left).setOnTouchListener(new View.OnTouchListener() {
            /**
             * Down的起始x,y
             */
            private float mStartX, mStartY;

            /**
             * Move 的 x,y,随move更新
             */
            private float mMoveX, mMoveY;

            private int rightMargin;

            // 是否显示真小窗
            private boolean showRealView = true;

            // 左下角拖动时的固定锚点坐标（右上角）
            private int fixedRightX, fixedTopY;

            @Override
            public boolean onTouch(View v, MotionEvent event) {
                //--> 获取当前可视区域宽，高
                int width = textureViewParent.getWidth();
                int height = textureViewParent.getHeight();
                LogUtil.i(TAG, "PopWinService::resize_left --------->new event： " + event.getAction());
                LogUtil.i(TAG, "PopWinService::resize_left ACTION_MOVE：rootview:width = " + rootView.getWidth() + ",rootview:height = " + rootView.getHeight());
                LogUtil.i(TAG, "PopWinService::resize_left ACTION_MOVE：textureView:width = " + textureViewParent.getWidth() + ",textureView:height = " + textureViewParent.getHeight());

                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        // 设置视觉反馈
                        v.setBackground(Utils.getApp().getDrawable(R.drawable.btn_resize_move_bg2));
                        left.setBackground(Utils.getApp().getDrawable(R.drawable.btn_resize_move_bg2));
                        popView.findViewById(R.id.resize).setEnabled(false);

                        // 记录起始坐标
                        mStartX = event.getRawX();
                        mStartY = event.getRawY();

                        // 记录右上角的固定锚点坐标
                        fixedRightX = popViewLayoutParams.x + currentWidth + transparentFrameWidth;
                        fixedTopY = popViewLayoutParams.y;

                        // 更新最大尺寸设置
                        updatePopViewMaxSize(2);
                        
                        // 记录初始状态（避免增量计算误差）
                        initialWidth = width;
                        initialHeight = height;
                        initialX = popViewLayoutParams.x;
                        initialY = popViewLayoutParams.y;
                        lastUpdateTime = 0;
                        
                        // 启用硬件加速层，减少重绘开销
                        if (popView.getLayerType() != View.LAYER_TYPE_HARDWARE) {
                            popView.setLayerType(View.LAYER_TYPE_HARDWARE, null);
                        }
                        
                        // 抗抖动：初始化拖动状态
                        isLeftBottomDragging = true;
                        tempTranslationX = 0f;
                        textureViewParent.setTranslationX(0f);

                        LogUtil.i(TAG, "PopWinService::resize_left ACTION_DOWN：固定右上角锚点 = (" + fixedRightX + "," + fixedTopY + ")");
                        break;
                    case MotionEvent.ACTION_MOVE:
                        // 保持视觉反馈
                        left.setBackground(Utils.getApp().getDrawable(R.drawable.btn_resize_move_bg2));
                        v.setBackground(Utils.getApp().getDrawable(R.drawable.btn_resize_move_bg2));

                        // 当前移动坐标
                        mMoveX = event.getRawX();
                        mMoveY = event.getRawY();
                        
                        // 避免频繁的增量更新，直接基于初始状态计算最终位置
                        float totalDeltaX = mMoveX - mStartX;
                        float totalDeltaY = mMoveY - mStartY;
                        
                        // 基于初始状态计算新尺寸（避免累积误差）
                        int newWidth = Math.max(initialWidth - (int)totalDeltaX, MIN_WIDTH);
                        int newHeight = Math.max(initialHeight + (int)totalDeltaY, MIN_HEIGHT);
                        
                        // 边界检查 - 确保窗口不会超出屏幕边界
                        int maxWidth = MetricsKtxKt.getScreenWidthPixels() - (fixedRightX - initialWidth);
                        newWidth = Math.min(newWidth, maxWidth);
                        int maxHeight = MetricsKtxKt.getScreenHeightPixels() - fixedTopY;
                        newHeight = Math.min(newHeight, maxHeight);

                        // 🎯 抗抖动核心：模仿右下角拖动的策略
                        
                        // 1. 只更新内部View尺寸（同步，无延迟）
                        scaleView(newWidth, newHeight, 2);
                        
                        // 2. 用View变换模拟位置调整（同步，流畅）
                        tempTranslationX = totalDeltaX;
                        textureViewParent.setTranslationX(tempTranslationX);
                        
                        // 3. 完全避免WindowManager更新（关键！消除抖动根源）
                        // 注释掉原来的windowManager.updateViewLayout调用
                        
                        LogUtil.i(TAG, "PopWinService::resize_left ACTION_MOVE：totalDelta = (" + totalDeltaX + "," + totalDeltaY + ")");
                        LogUtil.i(TAG, "PopWinService::resize_left ACTION_MOVE：newSize = (" + newWidth + "," + newHeight + ")");
                        LogUtil.i(TAG, "PopWinService::resize_left ACTION_MOVE：tempTranslation = " + tempTranslationX);
                        break;
                    case MotionEvent.ACTION_UP:
                        // 🎯 抗抖动优化：拖动结束后一次性更新真实位置
                        
                        // 恢复正常渲染模式
                        if (popView.getLayerType() == View.LAYER_TYPE_HARDWARE) {
                            popView.setLayerType(View.LAYER_TYPE_NONE, null);
                        }
                        
                        // 恢复UI状态
                        popView.findViewById(R.id.resize).setEnabled(true);
                        v.postDelayed(() -> {
                            v.setBackground(Utils.getApp().getDrawable(R.drawable.btn_resize_move_bg));
                            left.setBackground(Utils.getApp().getDrawable(R.drawable.btn_resize_move_bg));
                        }, 1000);

                        // 通知 iOS 窗口大小变化，用于复归重新校准
                        HidIosCalibrationUtil.updateViewSize(currentWidth, currentHeight);

                        // 🎯 关键优化：一次性更新WindowManager位置和尺寸
                        if (isLeftBottomDragging) {
                            try {
                                // 计算最终的真实位置（基于当前内容尺寸和变换）
                                int finalX = fixedRightX - currentWidth - transparentFrameWidth;
                                int finalY = fixedTopY;
                                
                                // 创建最终参数
                                WindowManager.LayoutParams finalParams = new WindowManager.LayoutParams();
                                finalParams.copyFrom(popViewLayoutParams);
                                finalParams.x = finalX;
                                finalParams.y = finalY;
                                finalParams.width = currentWidth + transparentFrameWidth;
                                finalParams.height = Math.min(currentHeight + transparentFrameHeight + surfaceTopView, 
                                                             MetricsKtxKt.getScreenHeightPixels());
                                
                                // 一次性更新窗口位置和尺寸
                                windowManager.updateViewLayout(popView, finalParams);
                                popViewLayoutParams = finalParams;
                                
                                // 清除临时变换（窗口位置已更新到正确位置）
                                textureViewParent.setTranslationX(0f);
                                
                                LogUtil.i(TAG, "PopWinService::resize_left ACTION_UP：一次性更新WindowManager，位置 = (" + finalX + "," + finalY + ")");
                                
                            } catch (Exception e) {
                                LogUtil.e(TAG, "PopWinService::resize_left ACTION_UP updateViewLayout error: " + e.getMessage());
                                // 发生错误时也要清除变换
                                textureViewParent.setTranslationX(0f);
                            }
                            
                            // 重置拖动状态
                            isLeftBottomDragging = false;
                            tempTranslationX = 0f;
                        }

                        LogUtil.i(TAG, "PopWinService::resize_left ACTION_UP completed，最终位置 = (" + popViewLayoutParams.x + "," + popViewLayoutParams.y + ")");
                        LogUtil.i(TAG, "PopWinService::resize_left ACTION_UP completed，最终尺寸 = (" + popViewLayoutParams.width + "," + popViewLayoutParams.height + ")");
                        break;
                }
                return false;
            }
        });

//        popView.setOnTouchListener(new View.OnTouchListener() {
//            float scaleScreen = 0f;
//
//            @Override
//            public boolean onTouch(View v, MotionEvent event) {
//                LogUtil.d(TAG, "popView onTouch " + event.getAction());
//                if (lockViewVisible()) {
//                    return false;
//                }
//                if (event.getAction() == MotionEvent.ACTION_DOWN) {
//                    scaleScreen = windowUtilInstance.getSurfaceHeight() * 1f / windowUtilInstance.getVideoHeight();
//                }
//                return false;
//            }
//        });

        screenOffTitle.setOnTouchListener((v, event) -> false);

        recoveryCast.setOnClickListener(v -> {
            screenOffReason.setText(R.string.pop_connection_click_on_the_phone_to_record_the_screen);
            SdkViewModel.getInstance().sendReqRecordScreenOp();
        });
        popView.findViewById(R.id.btn_exit).setOnClickListener(view -> LinkUtil.destoryServices(context));

        // 小窗拖拽监听
        rootViewParent.setOnDragWindowListener(this, new MultiTouchView.IDragListener() {
            // 是不是放大操作
            private boolean isBig = false;

            @Override
            public void onDrag(Boolean isLeft, int currentX, int currentY) {
                int maxX = MetricsKtxKt.getScreenWidthPixels() - transparentFrameWidth - currentWidth;
                if (currentX > maxX) {
                    currentX = maxX;
                }
                if (currentX <= 0) {
                    currentX = 0;
                }
                int maxY = MetricsKtxKt.getScreenHeightPixels() - transparentFrameHeight - currentHeight - surfaceTopView;
                if (currentY >= maxY) {
                    currentY = maxY;
                }
                if (currentY <= 0) {
                    currentY = 0;
                }
                popViewLayoutParams.x = currentX;
                popViewLayoutParams.y = currentY;
                updateWindowLayout();
            }

            @Override
            public void onDragUp(Boolean isLeft) {
                int maxX = MetricsKtxKt.getScreenWidthPixels() - transparentFrameWidth - currentWidth;
                if (popViewLayoutParams.x >= maxX && !isLeft) {
                    //右侧最小化
                    CastWindowStateManager.switchState(CastWindowState.MINI, new MiniPisition(MiniPositionMode.RIGHT, ((popViewLayoutParams.height + surfaceTopView) / 2) + popViewLayoutParams.y, popViewLayoutParams.x));
                } else if (popViewLayoutParams.x <= 0 && isLeft) {
                    //左侧最小化
                    CastWindowStateManager.switchState(CastWindowState.MINI, new MiniPisition(MiniPositionMode.LEFT, ((popViewLayoutParams.height + surfaceTopView) / 2) + popViewLayoutParams.y, popViewLayoutParams.x));
                } else {

                    if (isLeft != null && isLeft) {
                        windowLayoutAnimator(popViewLayoutParams.x, 0, 100);
                    } else {
                        int endX = MetricsKtxKt.getScreenWidthPixels() - currentWidth - transparentFrameWidth;
                        windowLayoutAnimator(popViewLayoutParams.x, endX, 100);
                    }
                }
            }

            @Override
            public void onDown() {
//                handler.removeCallbacksAndMessages(null);
//                updateView(5);
//                // 开始刷新假小窗上的图片
//                handler.post(runnable);
//                // 显示假小窗，隐藏真小窗
//                coverView.setAlpha(1);
//                rootViewParent.setAlpha(0);
                isBig = false;
            }

            @Override
            public void onCancel(float spanX, float spanY) {
//                handler.removeCallbacksAndMessages(null);
//                isBig = scaleView((int) (currentWidth + spanX), (int) (currentHeight + spanY), 4);
                if (isBig) {
                    if (lockViewVisible()) {
                        //锁屏或断流情况下不可全屏
                        return;
                    }
                    rootViewParent.setAlpha(1);
                    handler.postDelayed(() -> {
                        CastWindowStateManager.switchFullState(popViewLayoutParams.x);
                    }, 100);
                    isBig = false;
                    coverView.setAlpha(0);
                }
//
            }

            @Override
            public void scale(boolean big, float spanX, float spanY) {
                isBig = big;
            }
        });
    }

    /**
     * 改变真小窗、假小窗位置，大小
     *
     * @param currentDirection 0：左下角缩放
     *                         1:右下角缩放
     */
    private void changePopViewSize(int currentDirection) {
        if (currentDirection != -1) {
            // 左上角不动缩放（右下角缩放）
            if (currentDirection == 1) {
                // 初始化最大宽高；
                float scaleNewWidth = (MetricsKtxKt.getScreenWidthPixels() - popViewLayoutParams.x - transparentFrameWidth) / (float) realVideoSize.getWidth();
                float scaleNewHeight = (MetricsKtxKt.getScreenHeightPixels() - popViewLayoutParams.y - transparentFrameHeight - surfaceTopView) / (float) realVideoSize.getHeight();
                float scaleTemp = Math.min(scaleNewWidth, scaleNewHeight); // 最大可放大百分比

                // popView 最大可用空间（包括视频 + 边框 + title）
                int popViewWidthMax = (int) (realVideoSize.getWidth() * scaleTemp) + transparentFrameWidth;
                int popViewHeightMax = (int) (realVideoSize.getHeight() * scaleTemp) + transparentFrameHeight + surfaceTopView;
                LogUtil.d(TAG, "PopWinService::changePopViewSize popView wMax: " + popViewWidthMax + ",hMax: " + popViewHeightMax);

                popViewLayoutParams.width = Math.min(popViewWidthMax, MetricsKtxKt.getScreenWidthPixels() - popViewLayoutParams.x);
                popViewLayoutParams.height = Math.min(popViewHeightMax, MetricsKtxKt.getScreenHeightPixels() - popViewLayoutParams.y);
                windowManager.updateViewLayout(popView, popViewLayoutParams);
                LogUtil.d(TAG, "PopWinService::changePopViewSize popView w: " + popViewLayoutParams.width + ",h: " + popViewLayoutParams.height);

                // 动态修改rootView 对齐方式
                RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) rootViewParent.getLayoutParams();
                params.removeRule(RelativeLayout.ALIGN_PARENT_RIGHT);
                params.addRule(RelativeLayout.ALIGN_PARENT_LEFT, RelativeLayout.TRUE);
                params.width = (currentWidth + transparentFrameWidth);
                params.height = (currentHeight + transparentFrameHeight + surfaceTopView);
                rootViewParent.setLayoutParams(params);

            } else if (currentDirection == 0) {
                // 右上角不动缩放（左下角缩放）
                popViewLayoutParams.width = Math.min(popViewWidthMax, popViewLayoutParams.width + popViewLayoutParams.x);
                popViewLayoutParams.height = Math.min(popViewHeightMax, MetricsKtxKt.getScreenHeightPixels() - popViewLayoutParams.y);
                LogUtil.d(TAG, "PopWinService::changePopViewSize 左下角popView popViewWidthMax: " + popViewWidthMax + ",w: " + (popViewLayoutParams.width + popViewLayoutParams.x));
                LogUtil.d(TAG, "PopWinService::changePopViewSize 左下角popView popViewHeightMax: " + popViewHeightMax + ",h: " + (MetricsKtxKt.getScreenHeightPixels() - popViewLayoutParams.y));
                popViewLayoutParams.x -= (popViewWidthMax - currentWidth - transparentFrameWidth);
                if (popViewLayoutParams.x < 0) {
                    popViewLayoutParams.x = 0;
                }
                windowManager.updateViewLayout(popView, popViewLayoutParams);
                // rootViewParent 对齐方式
                RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) rootViewParent.getLayoutParams();
                params.removeRule(RelativeLayout.ALIGN_PARENT_LEFT);
                params.addRule(RelativeLayout.ALIGN_PARENT_RIGHT, RelativeLayout.TRUE);
                rootViewParent.setLayoutParams(params);
            }
        }
    }

    /**
     * fixme:这是一个 异味的代码调用逻辑 在PopWinService监听了sdk的hid{@link SdkViewModel#onRecvMsg(int, Object)}
     * CastWindowStateManager.INSTANCE.通过 onScreenRotationChange 进行状态分发
     */
    private void setPhoneScreenRotationChange() {
        LinkSdkModel.INSTANCE.setHidRegionCallback(new HidCallback() {
            @Override
            public void onScreenRotationChange(int width, int height, int angle, int mode, String platform) {
                LinkLog.i(TAG, "PopWinService::setPhoneScreenRotationChange -> onScreenRotationChange width = " + width + ";"
                        + "height = " + height + ";"
                        + "angle = " + angle + ";"
                        + "mode = " + mode + ";"
                );
                PopWinService.this.platform = platform;
                PopWinService.this.angle = angle;

                if (width == 0 || height == 0) {
                    return;
                }
                //todo:加一个判断，如果last width ！= width || .....再进行回调，避免额外触发转屏消耗
                runOnUiThread(() -> CastWindowStateManager.INSTANCE.onScreenRotationChange(width, height, angle, mode, platform));
            }

            @Override
            public void onCarBtPairedConnected(boolean connected) {
                PopWinService.this.onCarBtPairedConnected(connected);
            }
        });

    }


    private void skipCalibrationTimer() {
        if (null != countDownTimer) {
            countDownTimer.cancel();
        }
        countDownTimer = new CountDownTimer(5100, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                popRecalibrate.setText("重新校准(" + millisUntilFinished / 1000 + ")");
            }

            @Override
            public void onFinish() {
                popCalibrationLin.setVisibility(View.GONE);
            }
        };
        countDownTimer.start();
    }

    /**
     * 横竖屏切换flag
     */
    Boolean isScreenChange = null;

    @MainThread
    @Override
    public void onScreenRotationChange(int width, int height, int angle, int mode, String platform) {
        //横竖屏切换时获取车机档位状态信息改变UI按钮位置
        gearInformationAcquisition();
        //todo 投屏窗口宽高日志
        LogUtil.d(TAG, "PopWinService::onScreenRotationChange: width:" + width + ",height:" + height + ",angle:" + angle + ",mode:" + mode + ",platform:" + platform);
        if (!first) {
            updateShowWindowsScale(currentWidth, currentHeight, width, height);
        } else {
            if (angle == 90 || angle == 270) {
                //--> 横屏
                mScale = TEXTURE_VIEW_INITIAL_LANDSCAPE_SCALE;
            } else {
                //--> 竖屏
                mScale = TEXTURE_VIEW_INITIAL_SCALE;
            }
        }
         if (popView == null || popViewLayoutParams == null) {
            LinkLog.i(TAG, "PopWinService::onScreenRotationChange: popView or popViewLayoutParams is null");
            return;
        }
        if (popView.getWidth() <= 0) {
            LinkLog.i(TAG, "PopWinService::onScreenRotationChange: popView.getWidth() <= 0");
            //在popwin重绘前新乡旋转。再次执行
            ViewKt.doOnPreDraw(popView, view -> {
                onScreenRotationChange(width, height, angle, mode, platform);
                return null;
            });
            return;
        }

        HidScreenTouchDelegateHelper.setHidScreenTouchDelegate(textureViewParent, transparentFrameWidth / 2, transparentTopHeight, transparentBottomHeight, false, () -> !lockViewVisible());
        LogUtil.d(TAG, "PopWinService::setHidScreenTouchDelegate --> realVideoSize:" + realVideoSize.getWidth() + ",height:" + realVideoSize.getHeight() + ",isUpdateUI:" + isUpdateUI);
        //未发生横竖屏切换 不更新UI
        if (width == realVideoSize.getWidth() && height == realVideoSize.getHeight()) {
            LogUtil.d(TAG, "PopWinService::isUpdateUI------->:" + isUpdateUI);
            isUpdateUI = false;
            return;
        }

        realVideoSize = new Size(width, height);


        //fixme: 这里强制用 Screen WH 实际视频帧的 width，height 区分长条屏幕和宽屏
       if (BuildConfig.FLAVOR.contains("datong")){
           windowUtilInstance.setVideoWH(1920, 760);
       }else {
           windowUtilInstance.setVideoWH(1920, 1080);
       }

        //实际视频帧中，显示投屏区域的width, height
        windowUtilInstance.setSurfaceWidth(width);
        windowUtilInstance.setSurfaceHeight(height);

        // 横竖屏切换
        if (angle == 90 || angle == 270) {
            isScreenChange = true;
        }
        LogUtil.d(TAG, "PopWinService::isScreenChange--------->" + isScreenChange);
        if (isScreenChange != null) {
            isScreenChange = true;
        }
        int widthNew;
        int heightNew;

        widthNew = (int) (width * mScale);
        heightNew = (int) (height * mScale);

        updateLayout(widthNew, heightNew);
        updateFirstView();
    }

    /**
     * 处理车机蓝牙连接状态变化, 与互联设备配对
     * @param connected 是否已连接
     */
    @Override
    public void onCarBtPairedConnected(boolean connected) {
        LogUtil.d(TAG_BT, "PopWinService::onCarBtPairedConnected: " + connected);
        if (LinkSdkModel.INSTANCE.isConnected()) {
            SdkViewModel.getInstance().updateBTPairedWithPhone(connected);
            bluetoothStateManager.checkAndUpdateBluetoothState();
        }
        else {
            LogUtil.d(TAG_BT, "PopWinService::onCarBtPairedConnected not connecte: " + connected);
        }
    }

    private float mScale;

    private void updateShowWindowsScale(int windowW, int windowH, int videoW, int videoH) {
        int widthNew;
        int hightNew;

        // 竖屏切换横屏
        if (angle == 90) {
//            mScale = (float)windowW/videoH;
            hightNew = windowW;
//            widthNew = (hightNew / videoH) * videoW;
            mScale = (float) hightNew / videoH;
            if (mScale < TEXTURE_VIEW_INITIAL_LANDSCAPE_SCALE) {
                mScale = TEXTURE_VIEW_INITIAL_LANDSCAPE_SCALE;
            }
            LogUtil.d(TAG, "PopWinService::updateShowWindowsScale  :" + mScale);
        } else {
            // 横屏切竖屏
            widthNew = windowH;
//            hightNew = widthNew / videoW * videoH;
            mScale = (float) widthNew / videoW;

            // 因竖屏最大为0.84(ios 0.47) 估切换的时候不应该大于此值，如竖屏大于0.84(ios 0.47) 可修改此数值
            if (BuildConfig.FLAVOR.contains("datong")) {
                if (isIOSDevice()) {
                    if (mScale > 0.32) {
                        mScale = 0.32f;
                    }
                } else {
                    if (mScale > 0.84) {
                        mScale = 0.84f;
                    }
                }
            } else {
                if (mScale > 0.84) {
                    mScale = 0.84f;
                }
            }
            LogUtil.d(TAG, "PopWinService::updateShowWindowsScale :mScale = " + mScale);
        }
    }

    /**
     * 在对窗口进行【缩放】操作的时候，不断的更新window 的params，会造成整体窗口属性的重置，造成不流畅，尤其低端车机
     * 所以先 更新popView可到达的最大尺寸
     * 注意，该方法只是计算
     *
     * @param from 1:右下角
     *             2：左下角
     */
    private void updatePopViewMaxSize(int from) {
        int popViewY = popViewLayoutParams.y;
        int popViewX = popViewLayoutParams.x;
        LogUtil.d(TAG, "PopWinService::updatePopViewMaxSize -->: popViewX = " + popViewX + ",popViewY:" + popViewY);
        // 右下角放大popview出现尺寸为全屏的问题或者左下角无法放大的问题均为此值不正确导致popViewWidthMax值不对
        int maxWidth = 0;
        // --> height
        int maxHeight = MetricsKtxKt.getScreenHeightPixels() - transparentFrameHeight - popViewY - surfaceTopView;
        if (from == 1) {
            // 右下角
            maxWidth = MetricsKtxKt.getScreenWidthPixels() - popViewX - transparentFrameWidth;
        } else if (from == 2) {
            // 左下角
            maxWidth = popViewX + currentWidth;
        } else if (from == 0) {
            // 横竖屏切换和初始化、move的canle(此设置无异议)
            maxWidth = popViewX + currentWidth;
        } else if (from == 5) {
            maxHeight = MetricsKtxKt.getScreenHeightPixels() - transparentFrameHeight;
        } else {
            maxWidth = MetricsKtxKt.getScreenWidthPixels() - popViewX - transparentFrameWidth;
        }

        // 初始化最大宽高；
        float scaleNewWidth = maxWidth / (float) realVideoSize.getWidth();
        float scaleNewHeight = maxHeight / (float) realVideoSize.getHeight();
        float scaleTemp = Math.min(scaleNewWidth, scaleNewHeight);

        popViewWidthMax = (int) (realVideoSize.getWidth() * scaleTemp) + transparentFrameWidth;
        popViewHeightMax = (int) (realVideoSize.getHeight() * scaleTemp) + transparentFrameHeight + surfaceTopView;
        LogUtil.d(TAG, "PopWinService::updatePopViewMaxSize -->: from" + from + ",popViewWidthMax:" + popViewWidthMax + ",popViewHeightMax:" + popViewHeightMax + ",maxHeight=" + maxHeight + ",maxWidth:" + maxWidth);
    }

    /**
     * "假小窗"是用于缩放过程中,整体操作流程流程，做的视觉蒙层，在缩放过程中会显示
     * 根据实际小窗区域，更新"假小窗"位置，
     *
     * @param from 1:右下角 2：左下角 0：横竖屏切换和初始化
     */
    private void updateCoverView(int from) {
        //--> 获取实际popView的位置
        int popViewY = popViewLayoutParams.y;
        int popViewX = popViewLayoutParams.x;
        updatePopViewMaxSize(from);
        // 更新假小窗位置，大小
        coverWinLayoutParams.width = MetricsKtxKt.getScreenWidthPixels();
        coverWinLayoutParams.height = MetricsKtxKt.getScreenHeightPixels();
        coverWinLayoutParams.x = 0;
        coverWinLayoutParams.y = 0;
        // 判断view是否已经添加到window上
        // from传4，是updateWindowLayout(true) 且第一次调用
        if (from == 4 && coverView.getParent() == null) {
            windowManager.addView(coverView, coverWinLayoutParams);
        } else if (coverView.getParent() != null) {
            windowManager.updateViewLayout(coverView, coverWinLayoutParams);
        }

        // 更新假小窗图片大小,使其与真是的投屏窗口root view 大小，位置保持一致
        ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) coverShotImageView.getLayoutParams();

        //--> 可视区域【宽高】与 实际view 保持同步一致
        layoutParams.width = currentWidth + surfaceWidthMargin;
        layoutParams.height = currentHeight + surfaceTopView + surfaceWidthMargin;

        //--> 可视区域宽高与 实际view 位置保持一致
        // -+ surfaceWidthMargin 原因是因为透明区域view xml中与代码中四个方向各相差 10 （因为内部阴影边距为padding 10），解决反控区域被压缩20dp的问题
        layoutParams.topMargin = popViewY + transparentTopHeight - surfaceWidthMargin / 2;
        layoutParams.rightMargin = MetricsKtxKt.getScreenWidthPixels() - popViewX - (transparentFrameWidth + surfaceWidthMargin) / 2 - currentWidth;
        layoutParams.bottomMargin = transparentBottomHeight - surfaceWidthMargin / 2;
        LogUtil.d(TAG, "PopWinService::updateCoverView --> bottomMargin : " + layoutParams.bottomMargin);
        coverShotImageView.setLayoutParams(layoutParams);
    }

    @SuppressLint("MissingPermission")
    private void showThumbWindow() {
        LogUtil.d(TAG, "PopWinService::showThumbWindow popView:" + (popView == null ? "is null" : popView.getParent()));
        if (popView != null) {
            LogUtil.d(TAG, "PopWinService::showThumbWindow 1width:" + popViewLayoutParams.width + ",1height:" + popViewLayoutParams.height +
                    ",SurfaceHeight:" + windowUtilInstance.getSurfaceHeight() + ",ScreenWidth:" + windowUtilInstance.getScreenWidth());
            SmallCastState smallCastState = CastWindowStateManager.getState(CastWindowState.SMALL);
            if (smallCastState == null) {
                smallCastState = new SmallCastState(popView, this);
                CastWindowStateManager.putState(CastWindowState.SMALL, smallCastState);
            }
            smallCastState.setLayoutParams(popViewLayoutParams);
            //-->fixme she tips:该方法会触发 {@link com.autoai.wdlink.hu.castwindow.view.SmallCastState#onEnterState},从而触发 windowManager?.addView(popView, layoutParams)
            CastWindowStateManager.switchState(CastWindowState.SMALL);
            isShow = true;
            //todo 基线动画从最左侧仪表区域平移到中间，避免出现仪表遮挡浮窗问题。
            // --> fixme 修改为popservicewindow.addVidew后再做渐变平移动画
            ObjectAnimator fadeAnimator = ObjectAnimator.ofFloat(popView, "alpha", 0f, 1f);
            fadeAnimator.setDuration(150);
            fadeAnimator.start();
            LogUtil.i(TAG, "PopWinService:: 窗体 fade 动画start");
            fadeAnimator.addListener(new AnimatorListenerAdapter() {
                @Override
                public void onAnimationEnd(Animator animation) {
                    handler.postDelayed(() -> {
                        int startX = popViewLayoutParams.x;
                        int width = getResources().getDisplayMetrics().widthPixels;
                        int endX = width / 2 - (popViewLayoutParams.width / 2);
                        LogUtil.i(TAG, "PopWinService:: 窗体 fade 动画结束end");
                        windowLayoutAnimator(startX, endX, 500);
                    }, 100);
                }
            });
        }

    }

    /**
     * 根据是否连接蓝牙，刷新蓝牙显示状态
     */
    @SuppressLint("MissingPermission")
    private void determineIfBluetoothIsConnected() {
        LogUtil.d(TAG_BT, "PopWinService::determineIfBluetoothIsConnected");
        handler.postDelayed(() -> {
            bluetoothStateManager.checkAndUpdateBluetoothState();
        }, 500);
    }

    private void gearInformationAcquisition() {
        //投屏成功获取档位
        int curDriveState;
        int vehicleGear = CarInfoProxy.INSTANCE.getCurGearValue();
        LogUtil.d(TAG, "PopWinService::gearInformationAcquisition --> " + vehicleGear);
        if (vehicleGear == Gear.P) {
            curDriveState = DRIVE_STATE_OFF;
        } else {
            curDriveState = DRIVE_STATE_ON;
            //连接投屏不是P档设置显示基线限行
            SharePreferenceUtil.Companion.saveBoolean(MyApplication.application, "isPassenger", true);
        }
        if (DevelopModel.getInstance().getDriveSafetyVersion()) {
            displayBaselineRunningStatus(curDriveState);
        } else {
            LinkHost.checkIsPlayVideo(curDriveState);
            if (screenSafeTip != null) {
                if (curDriveState != 1) {
                    screenSafeTip.setVisibility(View.GONE);
                }
            }
        }
    }

    private void CalibrationMonitoringCallback() {
        //todo 监听返控校准回调
        LogUtil.d(TAG, "PopWinService::CalibrationMonitoringCallback");
        LinkSdkModel.INSTANCE.registerPopHidCheckCallback((isCheck) -> {
            calibrationStatus = CalibrationStatus.fromValue(isCheck);
            int hidCheckStatus = isCheck;
            if (!SdkViewModel.getInstance().isBTPairedWithPhone() && hidCheckStatus != CalibrationStatus.SUCCESS.value) {
                hidCheckStatus = CalibrationStatus.BT_NOT_CONNECTED.value;
            }

            LogUtil.d(TAG + TAG_IOS_CALIB, "PopWinService:CalibrationMonitoringCallback <<< received calibration result=" + isCheck + ", status=" + calibrationStatus);
            //ios 校准回调 0成功，1校准失败，2蓝牙连接失败，3手机app在后台，21手机app不在前台
            bluetoothStateManager.onCalibrationResult(hidCheckStatus);

            // 处理校准失败时的额外UI逻辑
            // 如果手机蓝牙没有匹配，则不需要提示。 只是告诉 iPhone 开始投屏
            if (hidCheckStatus == 1) {
                LogUtil.d(TAG_BT, "PopWinService::CalibrationMonitoringCallback showing calibration UI");
                runOnUiThread(() -> {
                    skipCalibrationTimer();
                    popCalibrationContent.setVisibility(View.VISIBLE);
                    popCalibrationContent.setText(Utils.getApp().getString(R.string.do_not_touch_your_phone));
                    popRecalibrate.setVisibility(View.VISIBLE);
                    popRecalibrate.setOnClickListener(v -> {
                        LogUtil.d(TAG + TAG_IOS_CALIB, "PopWinService:PopHidCheckCallback user clicked recalibrate button");
                        popCalibrationLin.setVisibility(View.INVISIBLE);
                        popRecalibrate.setVisibility(View.GONE);
                        popCalibrationContent.setVisibility(View.GONE);
                        if (countDownTimer != null) {
                            countDownTimer.cancel();
                        }
                        LinkSdkModel.INSTANCE.startVerification();
                    });
                });
            }
        });
    }


    private void destroyThumbWindow() {
        if (windowManager != null) {
            CastWindowStateManager.close();
            if (popView != null && popView.getParent() != null) {
                windowManager.removeView(popView);
            }
            if (coverView != null && coverView.getParent() != null) {
                windowManager.removeView(coverView);
            }
            popView = null;
            coverView = null;
            realVideoSize = new Size(386, 864);
            isShow = false;
        }
    }

    /**
     * 更新window 在窗口缩放过程中，轴线计算出最大可拖动范围
     */
    private int popViewWidthMax;

    private int popViewHeightMax;

    /**
     * she review tips：方法待整理
     */
    private void updateWindowLayout() {
        if (popView == null) {
            LogUtil.d(TAG, "PopWinService::updateWindowLayout popView is null");
            return;
        }
        if (CastWindowStateManager.getCurrentState() != CastWindowState.SMALL) {
            LogUtil.d(TAG, "PopWinService::updateWindowLayout: CastWindowStateManager.INSTANCE.getCurrentState() = "
                    + CastWindowStateManager.getCurrentState());
            return;
        }
            if (windowManager != null && popViewLayoutParams != null) {
                windowManager.updateViewLayout(popView, popViewLayoutParams);
            }
    }


    /**
     * 放大/缩小浮窗时，移动过程中实时更新浮窗的方法
     *
     * @param newWidth
     * @param newHeight
     * @param from      2：左下角拖动缩放
     * @return
     */
    private boolean scaleView(int newWidth, int newHeight, int from) {
        // 如果宽高，其中一个没有变化就不更新大小
        boolean isRight = maxWidth / 2 <= popViewLayoutParams.x;
        // 左下角缩放功能
        if (from == 2) {
            //获取内部显示区域宽高
            //获取内部显示区域宽高
            float scaleNewWidth = (newWidth / (float) realVideoSize.getWidth());
            float scaleNewHeight = (newHeight / (float) realVideoSize.getHeight());
            // 移动时放大/缩小百分比
            float scaleTemp = Math.min(scaleNewWidth, scaleNewHeight);

            int paramsWidth = (int) Math.round((realVideoSize.getWidth() * scaleTemp + 0.5));
            int paramsHeight = (int) Math.round(realVideoSize.getHeight() * scaleTemp + 0.5);
            int popViewHeightMini; // 最小宽高控制

            if (angle == 90 || angle == 270) {
                popViewHeightMini = (int) (maxHeight * MINI_SCALE_LANDSCAPE);
            } else {
                popViewHeightMini = (int) (maxHeight * MINI_SCALE_PORTRAIT);
            }
            if ((paramsHeight + transparentFrameHeight) < popViewHeightMini) {
                // 最小宽高控制
                return false;
            }

            //--> fixme:这个算法怀疑有问题，需要一起review
            if ((paramsHeight + transparentFrameHeight + surfaceTopView) >= popViewHeightMax) {
                // 最大宽高控制
                paramsHeight = popViewHeightMax - transparentFrameHeight - surfaceTopView;
//                return true;
            }
            if ((paramsWidth + transparentFrameWidth) >= popViewWidthMax) {
                paramsWidth = popViewWidthMax - transparentFrameWidth;
            }
            windowUtilInstance.setSurfaceWidth(paramsWidth);
            windowUtilInstance.setSurfaceHeight(paramsHeight);

            LogUtil.d(TAG, "PopWinService::scaleView -->" + "paramsWidth = " + paramsWidth + " paramsHeight = " + paramsHeight);
            updateLayout(paramsWidth, paramsHeight);
        }
        // 右下角缩放功能
        if (from == 1) {
            // 初始化最大宽高；
            float scaleNewWidth1 = (MetricsKtxKt.getScreenWidthPixels() - popViewLayoutParams.x - transparentFrameWidth) / (float) realVideoSize.getWidth();
            float scaleNewHeight1 = (MetricsKtxKt.getScreenHeightPixels() - popViewLayoutParams.y - transparentFrameHeight - surfaceTopView) / (float) realVideoSize.getHeight();
            // 最大可以放大的百分比
            float scaleTemp1 = Math.min(scaleNewWidth1, scaleNewHeight1);
            // popView最大可以宽高值
            int popViewWidthMax = (int) (realVideoSize.getWidth() * scaleTemp1) + transparentFrameWidth;
            int popViewHeightMax = (int) (realVideoSize.getHeight() * scaleTemp1) + transparentFrameHeight + surfaceTopView;

            float scaleNewWidth = (newWidth / (float) realVideoSize.getWidth());
            float scaleNewHeight = (newHeight / (float) realVideoSize.getHeight());
            // 移动时放大/缩小百分比
            float scaleTemp = Math.min(scaleNewWidth, scaleNewHeight);
            int paramsWidth = (int) Math.round((realVideoSize.getWidth() * scaleTemp + 0.5));
            int paramsHeight = (int) Math.round(realVideoSize.getHeight() * scaleTemp + 0.5);
            int popViewHeightMini; // 最小宽高控制

            if (angle == 90 || angle == 270) {
                popViewHeightMini = (int) (maxHeight * MINI_SCALE_LANDSCAPE);
            } else {
                popViewHeightMini = (int) (maxHeight * MINI_SCALE_PORTRAIT);
            }
            if ((paramsHeight + transparentFrameHeight) < popViewHeightMini) {
                // 最小宽高控制
                LogUtil.d(TAG, "PopWinService::scaleView --> return false h " + (paramsHeight + transparentFrameHeight) + " < "
                        + popViewHeightMini);
                return false;
            }
            if ((paramsWidth + transparentFrameWidth) >= popViewWidthMax) {
                // 最大宽高控制
                LogUtil.d(TAG, "PopWinService::scaleView --> return true w "
                        + (paramsWidth + transparentFrameWidth) + " >= " + popViewWidthMax);
                paramsWidth = popViewWidthMax - transparentFrameWidth;
            }
            if ((paramsHeight + transparentFrameHeight + surfaceTopView) >= popViewHeightMax) {
                LogUtil.d(TAG, "PopWinService::scaleView --> return true h "
                        + (paramsHeight + transparentFrameHeight + surfaceTopView) + " >= " + popViewHeightMax);
                paramsHeight = popViewHeightMax - transparentFrameHeight - surfaceTopView;
            }
            windowUtilInstance.setSurfaceWidth(paramsWidth);
            windowUtilInstance.setSurfaceHeight(paramsHeight);
            LogUtil.d(TAG, " PopWinService::scaleView --> " + paramsWidth + ", " + paramsHeight);
            updateLayout(paramsWidth, paramsHeight);
        }
        // 没找到有地方调用scaleView(4)
        if (from == 4) {
            //获取内部显示区域宽高
            //获取内部显示区域宽高
            float scaleNewWidth = (newWidth / (float) realVideoSize.getWidth());
            float scaleNewHeight = (newHeight / (float) realVideoSize.getHeight());

            float scaleTemp = Math.min(scaleNewWidth, scaleNewHeight);

            int paramsWidth = (int) Math.round((realVideoSize.getWidth() * scaleTemp + 0.5));
            int paramsHeight = (int) Math.round(realVideoSize.getHeight() * scaleTemp + 0.5);
            int popViewHeightMini; // 最小宽高控制
            if (newWidth < newHeight) {
                popViewHeightMini = (int) (maxHeight * MINI_SCALE_PORTRAIT);
            } else {
                popViewHeightMini = (int) (maxHeight * MINI_SCALE_LANDSCAPE);
            }
            if ((paramsHeight + transparentFrameHeight) < popViewHeightMini) {
                // 最小宽高控制
                return false;
            }
            if ((paramsHeight + transparentFrameHeight + surfaceWidthMargin) >= popViewHeightMax || (paramsWidth + transparentFrameWidth + surfaceWidthMargin) >= popViewWidthMax) {
                return true;
            }
            windowUtilInstance.setSurfaceWidth(paramsWidth);
            windowUtilInstance.setSurfaceHeight(paramsHeight);
            LogUtil.d(TAG, "PopWinService::scaleView -->"+ paramsWidth + ", " + paramsHeight);
            updateLayout(paramsWidth, paramsHeight);
            currentWidth = paramsWidth;
            currentHeight = paramsHeight;
        }
        // 最小化调用scaleView(3)
        return false;
    }

    /**
     * 更新布局
     * todo：方法优化 @利国 王蕊 {@link #updateWindowLayout()}两个函数的职责分开，
     * todo： updateLayout负责布局layout的计算；updateWindowLayout负责悬浮窗的计算
     * she tips:布局计算的思路是，基于实际悬浮窗的 width，height 进行整个布局的计算
     *
     * @param width  悬浮窗 【实际surface的width】
     * @param height 【实际surface的height】
     */
    private void updateLayout(int width, int height) {
        currentWidth = width;
        currentHeight = height;
        LogUtil.i(TAG, "PopWinService::updateLayout -> currentWidth = " + currentWidth + ";currentHeight = " + currentHeight);

        LogUtil.i(TAG, "PopWinService::updateLayout -> popViewLayoutParams.x = " + popViewLayoutParams.x + ";MetricsKtxKt.getScreenWidthPixels() = " + MetricsKtxKt.getScreenWidthPixels() + "transparentFrameWidth = " + transparentFrameWidth);
        popViewLayoutParams.x = Math.min(Math.max(0, popViewLayoutParams.x), MetricsKtxKt.getScreenWidthPixels() - width - transparentFrameWidth);
        popViewLayoutParams.y = Math.min(Math.max(0, popViewLayoutParams.y), MetricsKtxKt.getScreenHeightPixels() - height - transparentFrameHeight - surfaceTopView);
        LogUtil.i(TAG, "PopWinService::updateLayout -> popViewLayoutParams.x = " + popViewLayoutParams.x + ";popViewLayoutParams.y = " + popViewLayoutParams.y);

        LogUtil.d(TAG, "isScreenChange===" + isScreenChange + ",currentWidth:" + currentWidth + ",currentHeight:" + currentHeight);
        //可能超出屏幕
        if (popViewLayoutParams.y < 0) {
            popViewLayoutParams.y = 0;
        }

        LogUtil.i(TAG, "PopWinService::updateLayout --> isScreenChange=" + isScreenChange + ",isUpdateUI:" + isUpdateUI);
        // 方向改变时         判断浮窗不是第一次互联成功时调用
        if (isScreenChange != null && isScreenChange && !isUpdateUI) {

            isScreenChange = false;
            //--> 更新最外层popView 宽高
            popViewLayoutParams.width = currentWidth + transparentFrameWidth;
            popViewLayoutParams.height = Math.min(currentHeight + transparentFrameHeight + surfaceTopView, MetricsKtxKt.getScreenHeightPixels());
            //--> 更新popView大小
            ViewGroup.LayoutParams layoutParamsPopView = popView.getLayoutParams();
            layoutParamsPopView.width = popViewLayoutParams.width;
            layoutParamsPopView.height = popViewLayoutParams.height;
            popView.setLayoutParams(layoutParamsPopView);

            //-->调整可视区域view（包含padding）
            ViewGroup.LayoutParams layoutParamsRVP = rootViewParent.getLayoutParams();
            layoutParamsRVP.width = (currentWidth + transparentFrameWidth);
            // 不确定高度是不是和上面的获取一样 需要取最小值
            layoutParamsRVP.height = (currentHeight + transparentFrameHeight + surfaceTopView);
            rootViewParent.setLayoutParams(layoutParamsRVP);

            //--> 更新TextureView大小
            //-->根据当前的尺寸，设置Surface view的大小
            ViewGroup.LayoutParams layoutParamsTv = textureView.getLayoutParams();
            layoutParamsTv.width = (int) ((windowUtilInstance.getVideoWidth()) * ((float)currentWidth / (float)realVideoSize.getWidth()));
            layoutParamsTv.height = (int) (windowUtilInstance.getVideoHeight() * ((float)currentHeight / (float)realVideoSize.getHeight()));
            textureView.setLayoutParams(layoutParamsTv);

            //↓↓↓↓↓↓↓↓↓↓↓↓↓ todo：和updateWindowLayout 做下整合
            windowManager.updateViewLayout(popView, popViewLayoutParams); // 更新popView
            LogUtil.i(TAG, "PopWinService::updateLayout -> layoutParamsTv.width = " + layoutParamsTv.width + ";layoutParamsTv.height = " + layoutParamsTv.height);

            // --> 更新假小窗尺寸
            updateCoverView(0);
        } else {
            if (isShow) {
                if (windowManager != null && popViewLayoutParams != null) {
                    // 更新缩放rootView大小
                    ViewGroup.LayoutParams layoutParamsRVP = rootViewParent.getLayoutParams();
                    layoutParamsRVP.width = (currentWidth + transparentFrameWidth);
                    layoutParamsRVP.height = (currentHeight + transparentFrameHeight + surfaceTopView);
                    rootViewParent.setLayoutParams(layoutParamsRVP);

                    //-->根据当前的尺寸，设置Surface view的大小
                    ViewGroup.LayoutParams layoutParamsTv = textureView.getLayoutParams();
                    layoutParamsTv.width = (int) ((windowUtilInstance.getVideoWidth()) * ((float)currentWidth / (float)realVideoSize.getWidth()));
                    layoutParamsTv.height = (int) (windowUtilInstance.getVideoHeight() * ((float)currentHeight / (float)realVideoSize.getHeight()));
                    LogUtil.i(TAG, "PopWinService::updateLayout -> layoutParamsTv.width = " + layoutParamsTv.width + ";layoutParamsTv.height = " + layoutParamsTv.height);
                    textureView.setLayoutParams(layoutParamsTv);
                }
            }
        }

        //textureView 父容器，如果能通过蓝牙 HID 反控，拦截处理
        HidScreenTouchDelegateHelper.setHidScreenTouchDelegate(textureViewParent, transparentFrameWidth / 2, transparentTopHeight, transparentBottomHeight, false, () -> !lockViewVisible());
    }

    /**
     * 第一次互联成功 更新UI初始化调用
     */
    private void updateFirstView() {
        LogUtil.d(TAG, "PopWinService::updateFirstView ->first===" + first + ",currentWidth:" + currentWidth + ",currentHeight:" + currentHeight + ",this.isUpdateUI:" + this.isUpdateUI);
        if (CastWindowStateManager.INSTANCE.getCurrentState() != CastWindowState.SMALL) {
            LogUtil.d(TAG, "PopWinService::updateFirstView: CastWindowStateManager.INSTANCE.getCurrentState() = "
                    + (CastWindowStateManager.INSTANCE.getCurrentState()));
            return;
        }
        if (first) {
            first = false;
            // 更新最外层popView 宽高
            popViewLayoutParams.width = currentWidth + transparentFrameWidth;
            popViewLayoutParams.height = Math.min(currentHeight + transparentFrameHeight + surfaceTopView, MetricsKtxKt.getScreenHeightPixels());

            // 更新popView大小
            ViewGroup.LayoutParams layoutParams = popView.getLayoutParams();
            layoutParams.width = popViewLayoutParams.width;
            layoutParams.height = popViewLayoutParams.height;
            popView.setLayoutParams(layoutParams);
            if (popView.getParent() != null && isUpdateUI) {
                //重新互联，刷新浮框显示
                isUpdateUI = false;
                windowManager.updateViewLayout(popView, popViewLayoutParams);
            }
            coverView.setAlpha(0);

            popView.postDelayed(() -> {
                updateCoverView(4);
            }, 1000);
        }
    }

    private void showDialog() {
        if (dialogView != null && !basePopDialog.isShow()) {
            basePopDialog.showTwoButtonDialog(this, dialogView);
        }
    }

    private void switchSmallCastIfFullscreen(String action) {
        LogUtil.d(TAG, "PopWinService::switchSmallCastIfFullscreen -> action = " + action);
        //如果是全屏情况下触发锁屏或视频断流，切换到浮窗
        if (CastWindowStateManager.getCurrentState() == CastWindowState.FULL
                && (Objects.equals(action, "screen_off") || Objects.equals(action, "stream_abort") || Objects.equals(action, "stop"))) {
            CastWindowStateManager.switchState(CastWindowState.SMALL,new SmallShowBean(-1, position));
        }
    }

    private void notifyScreenState(String action) {
        LogUtil.d(TAG, "PopWinService::notifyScreenState -> action = " + action);
        if (screenOffOnLayout != null) {
            switch (action) {
                case "screen_on":
                    if (isIOSDevice()) {
                        screenSafeDriver.setVisibility(View.GONE);
                        floatingImage.setImageResource(R.mipmap.link_window_stream_abort);
                        screenOffOnLayout.setVisibility(View.VISIBLE);
                        screenOffTitle.setText(R.string.pop_window_stream_abort);
                        screenOffReason.setText(R.string.pop_window_stream_abort_tips);
                        screenOffTitle.setVisibility(View.VISIBLE);
                        screenOffReason.setVisibility(View.VISIBLE);
                        recoveryCast.setVisibility(View.VISIBLE);
                    } else if (isAndroidDevice()) {
                        screenOffOnLayout.setVisibility(View.GONE);
                        fullScreenImgBtn.setAlpha(ALPHA_255);
                    }
                    break;
                case "screen_off":
                    screenSafeDriver.setVisibility(View.GONE);
                    floatingImage.setImageResource(R.mipmap.link_lock_screen_image);
                    screenOffOnLayout.setVisibility(View.VISIBLE);
                    screenOffTitle.setVisibility(View.VISIBLE);
                    screenOffReason.setVisibility(View.VISIBLE);
                    recoveryCast.setVisibility(View.GONE);
                    fullScreenImgBtn.setAlpha(ALPHA_77);
                    screenOffTitle.setText(R.string.pop_window_phone_screen_off);
                    screenOffReason.setText(R.string.pop_window_unlock_continue_cast);
                    /*if (isIOSDevice()) {
                        screenBgve.setAlpha(1f);
                    } else if (isAndroidDevice()) {
                        screenBgve.setAlpha(0.6f);
                    }*/
                    break;
                case "stream_abort"://ios 独有
                    floatingImage.setImageResource(R.mipmap.link_window_stream_abort);
                    screenOffOnLayout.setVisibility(View.VISIBLE);
                    screenOffTitle.setVisibility(View.VISIBLE);
                    screenOffReason.setVisibility(View.VISIBLE);
                    recoveryCast.setVisibility(View.VISIBLE);
                    screenOffTitle.setText(R.string.pop_window_stream_abort);
                    screenOffReason.setText(R.string.pop_window_stream_abort_tips);
                    break;
                case "stream_recovery"://ios 独有
                    //  screenOffOnLayout.postDelayed(() -> {
                    screenOffOnLayout.setVisibility(View.GONE);
                    // }, 1000);
                    break;
                case "stop":
                    //断开时隐藏行车安全提示
                    floatingImage.setImageResource(R.mipmap.link_connection_dropped);
                    screenSafeDriver.setVisibility(View.GONE);
                    screenSafeTip.setVisibility(View.GONE);
                    screenOffOnLayout.setVisibility(View.VISIBLE);
                    screenOffTitle.setVisibility(View.VISIBLE);
                    screenOffReason.setVisibility(View.VISIBLE);
                    recoveryCast.setVisibility(View.GONE);
                    screenBgve.setAlpha(ALPHA_255);
                    fullScreenImgBtn.setAlpha(ALPHA_77);
                    screenOffTitle.setText(R.string.pop_window_disconnect);
                    screenOffReason.setText(R.string.pop_window_disconnect_tips);
                    SharePreferenceUtil.Companion.saveBoolean(MyApplication.application, "isPassenger", true);
                    break;
                case "show":
                    //重新互联成功
                    screenOffOnLayout.setVisibility(View.GONE);
                    fullScreenImgBtn.setAlpha(ALPHA_255);
                    break;
                case "connected":
                    //重新互联成功
                    floatingImage.setImageResource(R.mipmap.link_connected_successfully);
                    screenOffOnLayout.setVisibility(View.VISIBLE);
                    screenOffTitle.setVisibility(View.VISIBLE);
                    screenOffReason.setVisibility(View.VISIBLE);
                    recoveryCast.setVisibility(View.GONE);
                    screenOffTitle.setText(R.string.pop_tonnected_successfully);
                    screenOffReason.setText(R.string.pop_connection_click_on_the_phone_to_record_the_screen);
                    break;
                case "cancel_cast":
                    floatingImage.setImageResource(R.mipmap.link_connected_successfully);
                    screenOffOnLayout.setVisibility(View.VISIBLE);
                    screenOffTitle.setVisibility(View.VISIBLE);
                    screenOffReason.setVisibility(View.VISIBLE);
                    recoveryCast.setVisibility(View.VISIBLE);
                    screenOffTitle.setText(R.string.pop_tonnected_successfully);
                    screenOffReason.setText(R.string.pop_connection_recovery);
                    break;
                case "foreground":
                    recoveryCast.setVisibility(View.GONE);
                    break;
                case "background":
                    ToastUtil.showToast(Utils.getApp().getString(R.string.connect_cancel_cast_tips), "", "", "", false, false, -1);
                    break;
                default:
                    break;
            }
        }
    }

    private boolean lockViewVisible() {
        return screenOffOnLayout != null && screenOffOnLayout.getVisibility() == View.VISIBLE;
    }

    private void refreshDialogSize(int width, int height) {
        if (dialogView != null && popViewLayoutParams != null) {
            basePopDialog.refreshDialogSize(popViewLayoutParams.width, popViewLayoutParams.height);
        }
    }

    /**
     * 无线切有线的弹窗（可能的触发场景：无线连上之后再连有线）
     */
    private void dismissDialog() {
        if (dialogView != null) {
            basePopDialog.closeDialog();
        }
    }
    private boolean isViewAttachedToWindow(View view) {
        return view != null && view.getWindowToken() != null;
    }

    @Override
    public void onDestroy() {
        isNotification = true;
        SharePreferenceUtil.Companion.saveBoolean(MyApplication.application, "isPassenger", true);
        destroyThumbWindow();
        LinkUtil.stopForLink(this);
        LinkUtil.clearMessage();
        // 移除前台服务的通知
        stopForeground(true); // 移除前台服务通知并停止前台服务
        // 停止服务自身
        stopSelf();
        CarInfoProxy.INSTANCE.removeVehicleGearChangeListener(driveStateChangedListener);
        LogUtil.d(TAG, "PopWinService:-->onDestroy--------services");
        //HidScreenConnectManager.getInstance().closeHidProfileProxyIfNeed();
        HidUtil.unregisterHID();
        BtConnectionManager.getInstance().unregisterBluetoothReceiver(btConnectedListener);
        super.onDestroy();
        //todo 当前添加解决断开后 再次打开崩溃问题，后续可删除
        android.os.Process.killProcess(android.os.Process.myPid());
        System.exit(0);
    }

    /**
     * rootView 生成bitmap
     */
    private void captureWindowToBitmap() {
        if (textureView == null || textureViewParent == null || rootView == null) {
            return;
        }

        Bitmap bitmapTexture = textureView.getBitmap();
        if (bitmapTexture == null || bitmapTexture.isRecycled()) {
            return;
        }

        int width_textureView = textureView.getWidth();
        int height_textureView = textureView.getHeight();

        int width_textureViewParent = textureViewParent.getWidth();
        int height_textureViewParent = textureViewParent.getHeight();

        LogUtil.i(TAG, "PopWinService::captureWindowToBitmap --> width_textureView = " + width_textureView +
                ";height_textureView = " + height_textureView +
                ";width_textureViewParent = " + width_textureViewParent +
                ";height_textureViewParent = " + height_textureViewParent);

        // 确保 x、y >= 0
        int x = Math.max(0, (width_textureView - width_textureViewParent) / 2);
        int y = Math.max(0, (height_textureView - height_textureViewParent) / 2);

        int fault_tolerant = 2;

        // 参数合法性检查
        if (x < 0 || y < 0 || width_textureViewParent <= 0 || height_textureViewParent <= 0) {
            LogUtil.e(TAG, "PopWinService::captureWindowToBitmap invalid parameters: x=" + x + ", y=" + y + ", width=" + width_textureViewParent + ", height=" + height_textureViewParent);
            return;
        }

        try {
            bitmapTexture = Bitmap.createBitmap(bitmapTexture, x, y, width_textureViewParent - fault_tolerant, height_textureViewParent - fault_tolerant);
        } catch (Exception e) {
            LogUtil.e(TAG, "PopWinService::captureWindowToBitmap error creating bitmap", e);
            return;
        }

        // 设置封面图
        viewShotCover.setBackground(new BitmapDrawable(getResources(), PopWinServiceUtils.bitmapFillet(bitmapTexture)));
        viewShotCoverParent.setAlpha(1f);

        // 创建 rootView 的截图
        if (rootView.getWidth() <= 0 || rootView.getHeight() <= 0) {
            LogUtil.i(TAG, "PopWinService::captureWindowToBitmap rootView has invalid size");
            return;
        }

        Bitmap bitmap = Bitmap.createBitmap(rootView.getWidth(), rootView.getHeight(), Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        rootView.draw(canvas);
        drawable = new BitmapDrawable(getResources(), bitmap);
        viewShotCoverParent.setAlpha(0f);
    }



    /**
     * 当拖动缩放过程中，开启更新用于视觉过渡的cover
     */
    private void startRefreshCover() {
        handler.postDelayed(runnable, 30);
    }

    /**
     * 停止更新用于视觉过渡的cover {@link #startRefreshCover}
     */
    private void stopRefreshCover() {
        handler.removeCallbacks(runnable);
        drawable = null;
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    public int getCurrentX() {
        return popViewLayoutParams.x;
    }

    public int getCurrentY() {
        return popViewLayoutParams.y;
    }

    public int getCurrentWidth() {
        return popViewLayoutParams.width;
    }

    public int getCurrentHeight() {
        return popViewLayoutParams.height;
    }

    private BlueToothSysBroadcastReceiver btConnectedListener = new BlueToothSysBroadcastReceiver() {
        @Override
        public void onBluetoothStateChanged(int state) {
            LogUtil.d(TAG_BT, "PopWinService::onBluetoothStateChanged: " + state);
            // 可以根据需要处理原始状态值
        }

        @Override
        public void onBluetoothOn() {
            LogUtil.d(TAG_BT, "PopWinService::onBluetoothOn");
            // 蓝牙开启时不需要特殊处理
        }

        @Override
        public void onBluetoothOff() {
            LogUtil.d(TAG_BT, "PopWinService::onBluetoothOff");
            // 蓝牙关闭
            bluetoothStateManager.onBluetoothConnectionChanged(false);
        }

        @Override
        public void onDeviceConnected(BluetoothDevice device) {
            LogUtil.d(TAG_BT, "PopWinService::onDeviceConnected: " + device);
            // 单个设备连接
            bluetoothStateManager.onBluetoothConnectionChanged(true);
        }


        @Override
        public void onDeviceDisconnected(BluetoothDevice device) {
            LogUtil.d(TAG_BT, "PopWinService::onDeviceDisconnected: " + device);
            
            if (isOtherDevice()) {
                // 鸿蒙设备：保持原逻辑，直接触发断开
                LogUtil.i(TAG_BT, "PopWinService::onDeviceDisconnected HarmonyOS device disconnected, updating UI");
                bluetoothStateManager.onBluetoothConnectionChanged(false);
            } else {
                // iOS/Android设备：检查MAC地址是否匹配当前Link设备
                
                // 首先检查是否处于Link互联状态
                if (!LinkSdkModel.INSTANCE.isConnected()) {
                    LogUtil.d(TAG_BT, "PopWinService::onDeviceDisconnected Not in Link session, ignoring disconnect");
                    return;
                }
                
                String disconnectedDeviceMac = device != null ? device.getAddress() : null;
                String currentLinkDeviceMac = null;
                
                // 安全地获取当前Link设备MAC地址
                try {
                    SdkViewModel sdkViewModel = SdkViewModel.getInstance();
                    if (sdkViewModel != null) {
                        currentLinkDeviceMac = sdkViewModel.getCurrentA2DPConnectedDeviceMac();
                    }
                } catch (Exception e) {
                    LogUtil.e(TAG_BT, "PopWinService::onDeviceDisconnected Error getting current Link device MAC: " + e.getMessage());
                }
                
                LogUtil.d(TAG_BT, "PopWinService::onDeviceDisconnected iOS/Android device - disconnectedMAC: " + 
                         disconnectedDeviceMac + ", currentLinkMAC: " + currentLinkDeviceMac);
                
                if (currentLinkDeviceMac == null || currentLinkDeviceMac.isEmpty()) {
                    // 没有连接设备了，触发断开UI更新
                    LogUtil.i(TAG_BT, "PopWinService::onDeviceDisconnected No connected devices remaining, updating UI");
                    bluetoothStateManager.onBluetoothConnectionChanged(false);
                } else if (disconnectedDeviceMac != null && 
                           disconnectedDeviceMac.equalsIgnoreCase(currentLinkDeviceMac)) {
                    // 断开的设备MAC匹配当前Link设备，触发断开UI更新
                    LogUtil.i(TAG_BT, "PopWinService::onDeviceDisconnected Link device MAC matched, updating UI");
                    bluetoothStateManager.onBluetoothConnectionChanged(false);
                } else {
                    // 断开的是其他设备，不触发UI更新
                    LogUtil.d(TAG_BT, "PopWinService::onDeviceDisconnected Non-Link device disconnected, ignoring - " +
                             "disconnectedMAC: " + disconnectedDeviceMac + ", currentLinkMAC: " + currentLinkDeviceMac);
                }
            }
        }


        @Override
        public void onDeviceSwitch(@NonNull Set<BluetoothDevice> devices) {
            LogUtil.d(TAG, "PopWinService::onDeviceSwitch: " + devices.size() + " devices");
        }
    };

    /**
     * 判断iOS设备是否需要校准
     * 检查设备是否为iOS设备且是否满足需要校准的条件
     *
     * @return true if calibration is needed
     */
    private boolean isCalibrationNeeded() {
        // 首先确认是iOS设备
        if (!isIOSDevice()) {
            LogUtil.d(TAG, "PopWinService::isCalibrationNeeded not iOS Device. ");
            return false;
        }

        if (!SdkViewModel.getInstance().isBTPairedWithPhone()) {
            LogUtil.d(TAG, "PopWinService::isCalibrationNeeded not isBTPairedWithPhone. ");
            return false;
        }

        return calibrationStatus == CalibrationStatus.FAILED ||
                   calibrationStatus == CalibrationStatus.APP_BACKGROUND_FAIL ||
                   calibrationStatus == CalibrationStatus.APP_NOT_FOREGROUND ||
                   calibrationStatus == CalibrationStatus.BT_NOT_CONNECTED;

    }
    private static int lastPosition = 0; // 静态变量保存最后一次 position

    public static int getLastPosition() {
        return lastPosition;
    }

    // 在每次 position 更新时更新 lastPosition
    private void updatePosition(int newPosition) {
        this.position = newPosition;
        lastPosition = newPosition;
    }
}
