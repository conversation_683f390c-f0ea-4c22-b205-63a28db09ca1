package com.autoai.wdlink.hu.network

import com.autoai.wdlink.hu.ComponentName
import com.autoai.wdlink.hu.network.model.LicenseWelinkActivateData
import com.autoai.wdlink.hu.network.params.LicenseWelinkActivateRequest
import com.autoai.wdlink.hu.network.retrofit.ResponseProtocol

/**
 * Interface representing network calls to the NIA backend
 */
interface LicenseNetworkDataSource {
    @Suppress("PropertyName")
    val TAG: String
        get() = "${ComponentName.Network}${javaClass.simpleName}"
    /**
     * 2. 设备激活检查接口
     */
    suspend fun welinkActivate(request: LicenseWelinkActivateRequest): ResponseProtocol<LicenseWelinkActivateData>

}
