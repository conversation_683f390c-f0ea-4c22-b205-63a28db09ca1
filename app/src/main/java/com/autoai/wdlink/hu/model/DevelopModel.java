package com.autoai.wdlink.hu.model;

import com.autoai.wdlink.hu.MyApplication;
import com.autoai.wdlink.hu.castwindow.utils.SharePreferenceUtil;
import com.blankj.utilcode.util.GsonUtils;

public class DevelopModel {
    public static volatile DevelopModel mDevelopModel = null;

    public static DevelopModel getInstance() {
        if (mDevelopModel == null) {
            synchronized (DevelopModel.class) {
                if (mDevelopModel == null) {
                    mDevelopModel = new DevelopModel();
                }
            }
        }
        return mDevelopModel;
    }

    DevelopModel() {
    }

    public static class SwitchData {
        private boolean authWindow;
        private boolean driveSafetyOnline;
        private boolean driveSafetyOffline;
        private boolean logEnabled;
        private int logLevel;
        private int rtt;

        private boolean fpsEnabled;
        private boolean rttEnabled;
        private boolean driveSafetyVersion;

        public boolean isAuthWindow() {
            return authWindow;
        }

        public void setAuthWindow(boolean authWindow) {
            this.authWindow = authWindow;
        }

        public boolean isDriveSafetyOnline() {
            return driveSafetyOnline;
        }

        public void setDriveSafetyOnline(boolean driveSafetyOnline) {
            this.driveSafetyOnline = driveSafetyOnline;
        }

        public boolean isDriveSafetyOffline() {
            return driveSafetyOffline;
        }

        public void setDriveSafetyOffline(boolean driveSafetyOffline) {
            this.driveSafetyOffline = driveSafetyOffline;
        }

        public boolean isLogEnabled() {
            return logEnabled;
        }

        public void setLogEnabled(boolean logEnabled) {
            this.logEnabled = logEnabled;
        }

        public int getLogLevel() {
            return logLevel;
        }

        public void setLogLevel(int logLevel) {
            this.logLevel = logLevel;
        }

        public int getRtt() {
            return rtt;
        }

        public void setRtt(int rtt) {
            this.rtt = rtt;
        }

        public boolean isDriveSafetyVersion() {
            return driveSafetyVersion;
        }

        public void setDriveSafetyVersion(boolean driveSafetyVersion) {
            this.driveSafetyVersion = driveSafetyVersion;
        }

        public boolean isFpsEnabled() {
            return fpsEnabled;
        }

        public void setFpsEnabled(boolean fpsEnabled) {
            this.fpsEnabled = fpsEnabled;
        }
        public boolean isRttEnabled() {
            return rttEnabled;
        }

        public void setRttEnabled(boolean rttEnabled) {
            this.rttEnabled = rttEnabled;
        }
    }

    public static final String SHARED_KEY_SWITCHES = "key_switches";

    private AuthWindowListener mAuthWindowListener;

    private LogListener mLogListener;
    private FpsListener mFpsListener;
    private RttListener mRttListener;

    private DriveSafetyListener mDriveSafetyListener;

    private DriveSafetyVersionListener mDriveSafetyVersionListener;

    /**
     * 获取数据
     */
    public SwitchData getData() {
        String json = SharePreferenceUtil.Companion.getString(MyApplication.application, SHARED_KEY_SWITCHES, "");
        SwitchData switchData;
        if (json != null && !json.isEmpty()) {
            switchData = GsonUtils.fromJson(json, SwitchData.class);
        } else {
            // 第一次默认数据
            switchData = new SwitchData();
            switchData.setAuthWindow(false);
            switchData.setDriveSafetyOnline(true);
            switchData.setDriveSafetyOffline(true);
            switchData.setLogEnabled(true);
            //1 为全日志，4，5 为 ERROR级别
            switchData.setLogLevel(1);
            switchData.setFpsEnabled(false);
            switchData.setDriveSafetyVersion(true);
        }
        return switchData;
    }

    /**
     * 存入数据
     */
    public void saveData(SwitchData switchData) {
        SharePreferenceUtil.Companion.saveString(MyApplication.application, SHARED_KEY_SWITCHES, GsonUtils.toJson(switchData));
    }

    public void switchAuthWindow(boolean enabled) {
        SwitchData data = getData();
        data.setAuthWindow(enabled);
        saveData(data);
        if (mAuthWindowListener != null) {
            mAuthWindowListener.onAuthWindowSwitch(enabled);
        }
    }

    public void switchLogLevel(boolean enabled, int level) {
        SwitchData data = getData();
        data.setLogEnabled(enabled);
        data.setLogLevel(level);
        saveData(data);
        if (mLogListener != null) {
            mLogListener.onLogSwitch(enabled, level);
        }
    }
    public void switchFps(boolean enabled) {
        SwitchData data = getData();
        data.setFpsEnabled(enabled);
        saveData(data);
        if (mFpsListener != null) {
            mFpsListener.onFpsSwitch(enabled);
        }
    }

    public void switchRtt(boolean enabled , int rtt) {
        SwitchData data = getData();
        data.setRttEnabled(enabled);
        data.setRtt(rtt);
        saveData(data);
        if (mFpsListener != null) {
            mRttListener.onRttSwitch(enabled,rtt);
        }
    }

    public void switchDriveSafetyVersion(boolean enabled) {
        SwitchData data = getData();
        data.setDriveSafetyVersion(enabled);
        saveData(data);
        if (mDriveSafetyVersionListener != null) {
            mDriveSafetyVersionListener.onDriveSafetyVersionSwitch(data.driveSafetyVersion);
        }
    }

    public void switchDriveSafetyOnline(boolean enabled) {
        SwitchData data = getData();
        data.setDriveSafetyOnline(enabled);
        saveData(data);
        if (mDriveSafetyListener != null) {
            mDriveSafetyListener.onDriveSafetyOnlineSwitch(data.driveSafetyOnline);
        }
    }

    public void switchDriveSafetyOffline(boolean enabled) {
        SwitchData data = getData();
        data.setDriveSafetyOffline(enabled);
        saveData(data);
        if (mDriveSafetyListener != null) {
            mDriveSafetyListener.onDriveSafetyOfflineSwitch(data.driveSafetyOffline);
        }
    }

    /**
     * 获取鉴权弹窗开关状态
     */
    public Boolean getAuthWindowStatus() {
        return getData().isAuthWindow();
    }

    /**
     * 获取行车安全走行规制版本开关状态
     * @return
     */
    public Boolean getDriveSafetyVersion() {
        return getData().isDriveSafetyVersion();
    }

    /**
     * 获取行车安全走行规制在线开关状态
     */
    public Boolean getDriveSafetyOnlineStatus() {
        return getData().isDriveSafetyOnline();
    }

    /**
     * 获取行车安全走行规制离线开关状态
     */
    public Boolean getDriveSafetyOfflineStatus() {
        return getData().isDriveSafetyOffline();
    }

    /**
     * 获取log开关状态
     */
    public Boolean getLogStatus() {
        return getData().isLogEnabled();
    }

    /**
     * 获取log等级
     */
    public int getLogLevel() {
        return getData().getLogLevel();
    }

    public void setAuthWindowListener(AuthWindowListener authWindowListener) {
        this.mAuthWindowListener = authWindowListener;
    }

    public void setLogListener(LogListener logListener) {
        this.mLogListener = logListener;
    }
    public void setFpsListener(FpsListener fpsListener) {
        this.mFpsListener = fpsListener;
        if(this.mFpsListener != null){
            mFpsListener.onFpsSwitch(getData().fpsEnabled);
        }
    }

    public void setRttListener(RttListener rttListener) {
        this.mRttListener = rttListener;
        if (this.mRttListener != null) {
            mRttListener.onRttSwitch(getData().rttEnabled,getData().rtt);
        }
    }


    public void setDriveSafetyListener(DriveSafetyListener driveSafetyListener) {
        this.mDriveSafetyListener = driveSafetyListener;
    }

    public void setDriveSafetyVersionListener(DriveSafetyVersionListener driveSafetyVersionListener) {
        this.mDriveSafetyVersionListener = driveSafetyVersionListener;
    }

    /**
     * 鉴权弹窗开关监听
     */
    public interface AuthWindowListener {
        /**
         * 当鉴权弹窗开关状态改变时触发
         *
         * @param enabled 开关状态，true:打开，false:关闭
         */
        void onAuthWindowSwitch(boolean enabled);
    }

    /**
     * log开关监听
     */
    public interface LogListener {
        /**
         * 当log开关状态改变时触发
         *
         * @param enabled 开关状态
         * @param level   log级别
         */
        void onLogSwitch(boolean enabled, int level);
    }

    /**
     * 行车安全走行规制开关监听
     */
    public interface DriveSafetyListener {
        /**
         * 当行车安全走行规制开关状态改变时触发
         *
         * @param onlineEnabled 在线状态
         */
        void onDriveSafetyOnlineSwitch(boolean onlineEnabled);

        /**
         * 当行车安全开关走行规制状态改变时触发
         *
         * @param offlineEnabled 离线状态
         */
        void onDriveSafetyOfflineSwitch(boolean offlineEnabled);
    }


    public interface DriveSafetyVersionListener {
        /**
         * 安全走行1.2版本是否打开
         *
         * @param versionEnabled 版本状态
         */
        void onDriveSafetyVersionSwitch(boolean versionEnabled);
    }
    public interface FpsListener {
        /**
         * 打开fps显示
         *
         * @param fpsEnabled
         */
        void onFpsSwitch(boolean fpsEnabled);
    }

    public interface RttListener {
        /**
         * 打开Rtt显示
         *
         * @param rttEnabled
         */
        void onRttSwitch(boolean rttEnabled, int rtt);
    }
}
