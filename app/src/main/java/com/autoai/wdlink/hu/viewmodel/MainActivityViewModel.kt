package com.autoai.wdlink.hu.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.autoai.wdlink.hu.Logger
import com.autoai.wdlink.hu.frame.BaseViewModel
import com.autoai.wdlink.hu.frame.utils.UnPeekLiveData
import com.autoai.wdlink.hu.model.DevicesType
import com.autoai.wdlink.hu.model.DevicesTypeModel
import com.autoai.wdlink.hu.model.IosHidModel
import com.autoai.wdlink.hu.model.ShowIosWindow
import com.autoai.wdlink.hu.model.PageModel
import com.autoai.wdlink.hu.sdk.LinkSdkModel
import com.autoai.welinkapp.model.ButtonType

class MainActivityViewModel : BaseViewModel() {

    var currentFragment: String = ""
        set(value) {
            Logger.v("currentFragment = $value")
            field = value
        }


    val pageEvent: LiveData<PageModel>
        get() = LinkSdkModel.exteriorPageEvent

        val iosHidEvent: LiveData<IosHidModel>
        get() = LinkSdkModel.iosHidTypeEvent

    val bgVisibility: MutableLiveData<Int> = MutableLiveData()

    val devicesEvent: UnPeekLiveData<DevicesTypeModel>
        get() = LinkSdkModel.devicesTypeEvent

    val devicestype: LiveData<DevicesType>
        get() = ShowIosWindow.getDevicesTypeEvent()


//    val fullScreenLiveData = MutableLiveData<Boolean>()
//    var fullScreen = false
//    var windowHasFocus = false

//    fun fullScreen() {
//        Logger.i("fullScreen：windowHasFocus = $windowHasFocus, fullScreen = $fullScreen")
//        if (windowHasFocus) {
//            fullScreenLiveData.postValue(fullScreen)
//        } else {
//            fullScreenLiveData.postValue(false)
//        }
//    }
    /**
     * 前台 后台 切换 是否启动start状态保存
     * true 切换回前台启动startpop
     * false 不启动startpop
     */
    fun onForeground(foreground: Boolean) {
        LinkSdkModel.mStratPop.set(foreground)
    }

    fun isStratPop() : Boolean{
        return LinkSdkModel.mStratPop.get()
    }
    fun sendButton(type: Int, param: Int) {
        LinkSdkModel.sendButton(type, param)
    }

    /**
     * 触屏传输
     * */


//    fun setHidRegionCallback(phoneSurfaceParent: FrameLayout) {
//        LinkHost.setHidRegionCallback(HidRegionHelper.getHidCallback(phoneSurfaceParent))
//    }

//    /**
//     * 返回事件拦截处理
//     * */
//    fun onBackPressed(back: () -> Unit) {
//        Logger.d("MainActivityViewModel：onBackPressed --> currentFragment = $currentFragment")
//        when (currentFragment) {
//            RunningFragment::class.java.simpleName -> {
//                back.invoke()
//                sendButton(ButtonType.LAUNCHER, 0)
//            }
//
//            ExitFragment::class.java.simpleName -> {
//                back.invoke()
//                sendButton(ButtonType.GOT_IT, 1)
//            }
//
//            HelpFragment::class.java.simpleName -> {
//                sendButton(ButtonType.HELP_BACK, 0)
//            }
//
//            InitFragment::class.java.simpleName -> {
//                sendButton(ButtonType.INIT_CLOSE, 0)
//                back.invoke()
//            }
//
//            else -> {
//                back.invoke()
//            }
//        }
//    }

    override fun onCleared() {
        super.onCleared()
//        LinkSdkModel.destroy()
    }


}