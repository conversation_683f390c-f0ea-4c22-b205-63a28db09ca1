package com.autoai.wdlink.hu.utils

import android.content.Context
import android.content.ContextWrapper
import android.content.pm.PackageManager
import android.provider.Settings
import androidx.core.content.ContextCompat

fun ContextWrapper.checkPermissions(permissions: Array<String>): Bo<PERSON>an {
    for (permission in permissions) {
        // 如果有任意一个权限未被授予，则返回 false
        if (ContextCompat.checkSelfPermission(
                applicationContext, permission
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            return false
        }
    }
    return false
}

fun isOverlayPermissionGranted(context: Context): Bo<PERSON>an {
    return Settings.canDrawOverlays(context);
}



