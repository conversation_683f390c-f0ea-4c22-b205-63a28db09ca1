package com.autoai.wdlink.hu.castwindow.view

import android.annotation.SuppressLint
import android.car.drivingstate.Gear
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams
import android.widget.Button
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.doOnPreDraw
import androidx.fragment.app.FragmentActivity
import com.autoai.car.os_adapter.interfaces.CarInfoInterface
import com.autoai.car.os_adapter.proxy.CarInfoProxy
import com.autoai.wdlink.hu.ComponentName
import com.autoai.wdlink.hu.MyApplication
import com.autoai.wdlink.hu.R
import com.autoai.wdlink.hu.SettingFragment
import com.autoai.wdlink.hu.castwindow.LinkLog
import com.autoai.wdlink.hu.castwindow.ktx.dpToPx
import com.autoai.wdlink.hu.castwindow.ktx.getScreenHeightPixels
import com.autoai.wdlink.hu.castwindow.ktx.getScreenWidthPixels
import com.autoai.wdlink.hu.castwindow.scope.CastWindowState
import com.autoai.wdlink.hu.castwindow.utils.SharePreferenceUtil.Companion.getBoolean
import com.autoai.wdlink.hu.castwindow.utils.SharePreferenceUtil.Companion.saveBoolean
import com.autoai.wdlink.hu.model.DevelopModel
import com.autoai.wdlink.hu.module.hidscreen.FullScreenRootView
import com.autoai.wdlink.hu.module.hidscreen.HidScreenTouchDelegateHelper
import com.autoai.wdlink.hu.module.hidscreen.HidScreenTouchListener
import com.autoai.wdlink.hu.module.hidscreen.InterceptFrameLayout
import com.autoai.wdlink.hu.module.hidscreen.MultiTouchView
import com.autoai.wdlink.hu.sdk.LinkUtil.destoryServices
import com.autoai.wdlink.hu.utils.ToastUtil
import com.autoai.welinkapp.util.WindowUtil


/**
 * 全屏Activity
 */
class FullscreenActivity : FragmentActivity() {
    @Suppress("PrivatePropertyName")
    private val TAG = "${ComponentName.WindowSwitcher}FullscreenActivity"
    private lateinit var surfaceContainer: FullScreenRootView
    private lateinit var hidScreenView: InterceptFrameLayout
    private lateinit var hidScreenViewParent: InterceptFrameLayout
    private lateinit var screenSafeDriver: ConstraintLayout
    private lateinit var closeFullScreen: ImageView
    private lateinit var setting: ImageView
    private lateinit var settingLayout: FrameLayout
    private lateinit var fullview: View
    private var angle = 0
    private var width = 0
    private var height = 0
    private var curDriveState = 0
    private var linearLayoutsc: LinearLayout? = null
    private var bt_passenger: Button? = null
    private var bt_safe_exit: Button? = null
    private var screenSafeTip: RelativeLayout? = null
    private var position: Int = 0
    private var isSettingFragmentVisible = false//判断当前是否显示fragment
    private val handler = Handler(Looper.getMainLooper())
    private val hideRunnable = Runnable {
        closeFullScreen.visibility = View.INVISIBLE
        setting.visibility = View.INVISIBLE
    }

    private val fullState = object : BaseCastState() {
        override val state: Int
            get() = CastWindowState.FULL

        override fun onEnterState(args: Any?) {
            super.onEnterState(args)
            CastWindowStateManager.getTextureView()
                ?.let {
                    LinkLog.i(<EMAIL>, "FullscreenActivity::onEnterState: it = $it")
                    surfaceContainer.addView(
                        it,
                        0,
                        LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT)
                    )
                    val constraintSet = ConstraintSet()
                    constraintSet.clone(surfaceContainer)
                    // 设置 TextureView 的约束以使其居中（全屏显示）
                    constraintSet.connect(it.id, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP)
                    constraintSet.connect(it.id, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM)
                    constraintSet.connect(it.id, ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START)
                    constraintSet.connect(it.id, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END)
                    // 应用约束
                    constraintSet.applyTo(surfaceContainer)
                }
        }

        override fun onExitState() {
            super.onExitState()
            surfaceContainer.removeAllViews()
            finish()
        }

        override fun onScreenRotationChange(rotationChange: IScreenRotationChange.ScreenRotationChange) {
            LinkLog.i(
                TAG,
                "FullscreenActivity::onScreenRotationChange: rotationChange = $rotationChange"
            )
            angle = rotationChange.angle
            width = rotationChange.width
            height = rotationChange.height
            LinkLog.i(
                <EMAIL>,
                "FullscreenActivity::onScreenRotationChange: rotationChange = $rotationChange"
            )
           /* val outsideInsert = 88.dpToPx().toInt()
            if (rotationChange.angle == 0 || rotationChange.angle == 180) {
                //---> 横屏
                hidScreenViewParent.setPadding(outsideInsert, 0, outsideInsert, 0)
                rotationChange.platform?.let { LinkUtil.setAngleAndPlatform(180, it) }
            } else {
                //---> 竖屏
                rotationChange.platform?.let {
                    LinkUtil.setAngleAndPlatform(
                        rotationChange.angle,
                        it
                    )
                }
                hidScreenViewParent.setPadding(0, outsideInsert, 0, outsideInsert)
            }*/
            hidScreenView.layoutParams = hidScreenView.layoutParams.apply {
                val horizontalScale = rotationChange.width.toFloat() / getScreenWidthPixels()
                val verticalScale = rotationChange.height.toFloat() / getScreenHeightPixels()
                if (horizontalScale < verticalScale) { //长边顶满屏幕
                    width = getScreenHeightPixels() * rotationChange.width / rotationChange.height
                    height = getScreenHeightPixels()
                } else { //短边顶满屏幕
                    width = getScreenWidthPixels()
                    height = width * rotationChange.height / rotationChange.width
                }
                LinkLog.i(
                    TAG,
                    "FullscreenActivity::onScreenRotationChange: layoutParams.width = $width, layoutParams.height = $height"
                )
            }
            //---》根据实际情况 更新texture的params
            surfaceContainer.doOnPreDraw {
                LinkLog.i(<EMAIL>, "FullscreenActivity::onScreenRotationChange:doOnPreDraw: invoke")
                val windowUtil = WindowUtil.getInstance(this@FullscreenActivity)

//                windowUtil.setVideoWH(rotationChange.width, rotationChange.height)

                CastWindowStateManager.getTextureView()?.let {
                    val params: LayoutParams = it.layoutParams
                    val vehicleGear = CarInfoProxy.getCurGearValue()
                    curDriveState = if (vehicleGear == Gear.P) {
                        CarInfoInterface.DRIVE_STATE_OFF
                    } else {
                        CarInfoInterface.DRIVE_STATE_ON
                    }
                    if (DevelopModel.getInstance().driveSafetyVersion) {
                        displayBaselineRunningStatus(curDriveState)
                    }
                    LinkLog.i(
                        <EMAIL>,
                        "FullscreenActivity::carDrivingStateManagerUtil:vehicleGear === : $vehicleGear"
                    )
                    val interceptFrameLayout =
                        hidScreenView.layoutParams as FrameLayout.LayoutParams
                    if (vehicleGear != -1 && vehicleGear != 1) {
                        if (rotationChange.angle == 90 || rotationChange.angle == 270) {
                            params.width = (1920 * 2/3)
                            params.height = (1080 * 2/3)
                            interceptFrameLayout.width = (rotationChange.width * 2/3)
                            interceptFrameLayout.height = (rotationChange.height * 2/3)
                        } else {
                            params.height = (1080)
                            params.width = (1920)
                            interceptFrameLayout.height = rotationChange.height
                            interceptFrameLayout.width = rotationChange.width
                        }
                    }
                    hidScreenView.layoutParams = interceptFrameLayout
                    it.setLayoutParams(params)
                }

                LinkLog.i(
                    <EMAIL>,
                    "FullscreenActivity::onScreenRotationChange:doOnPreDraw: videoWidth:" + windowUtil.videoWidth + ",videoHeight=" + windowUtil.videoHeight
                )
                //大小更新之后需要重新计算
                HidScreenTouchDelegateHelper.setHidScreenTouchDelegate(
                    hidScreenView,
                    88.dpToPx().toInt(),
                    0, 0,
                    true
                )

            }
        }
    }


    @SuppressLint("ClickableViewAccessibility")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_fullscreen)
        ToastUtil.removeView()
        position = intent.getIntExtra("position", 0)
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }

        surfaceContainer = findViewById(R.id.main)
        hidScreenView = findViewById(R.id.hid_touch_screen_view)
        closeFullScreen = findViewById(R.id.closeFullScreen)
        setting = findViewById(R.id.setting)
        hidScreenViewParent = findViewById(R.id.hid_touch_screen_parent_view)
        screenSafeDriver = findViewById(R.id.sc_safe_driver)
        fullview = findViewById(R.id.fullview)
        linearLayoutsc = screenSafeDriver.findViewById(R.id.button_combination)
        bt_passenger = screenSafeDriver.findViewById(R.id.bt_passenger)
        bt_safe_exit = screenSafeDriver.findViewById(R.id.bt_safe_exit)
        screenSafeTip = findViewById(R.id.sc_safe_tip)
        settingLayout = findViewById(R.id.setting_frameLayout)


        CastWindowStateManager.putState(CastWindowState.FULL, fullState)
        CastWindowStateManager.switchState(CastWindowState.FULL)
        handler.postDelayed(hideRunnable, 3000)

        //点击屏幕显示关闭按钮3000ms后隐藏
        fullview.setOnTouchListener { _, event ->
            if (event.action == MotionEvent.ACTION_DOWN) {
                closeFullScreen.visibility = View.VISIBLE
                setting.visibility = View.VISIBLE
                handler.removeCallbacks(hideRunnable)
                handler.postDelayed(hideRunnable, 3000)
            }
            false
        }

        closeFullScreen.setOnClickListener { finish() }
        setting.setOnClickListener { showSettingFragment() }
        //行车安全提示监听
        CarInfoProxy.addVehicleGearChangeListener(driveStateChangedListener)
        surfaceContainer.setOnScaleListener(object : MultiTouchView.IDragListener {
            override fun onDrag(isLeft: Boolean?, currentX: Int, currentY: Int) {
            }

            override fun onDragUp(isLeft: Boolean?) {
            }

            override fun onDown() {
            }

            override fun onCancel(spanX: Float, spanY: Float) {
            }

            override fun scale(isBig: Boolean, spanX: Float, spanY: Float) {
                if (!isBig) {
                    finish()
                }
            }

        })

//        hidScreenViewParent.setOnTouchListener(View.OnTouchListener { v, event ->
//            if (event.pointerCount >= 3) {
//                false
//            } else {
//                true
//            }
//        })

    }


    override fun onStop() {
        super.onStop()
        Log.i(TAG, "FullscreenActivity.kt::onStop")
        CarInfoProxy
            .removeVehicleGearChangeListener(driveStateChangedListener)
        if (CastWindowStateManager.getCurrentState() == CastWindowState.FULL) {
            CastWindowStateManager.switchState(CastWindowState.SMALL, SmallShowBean(-1, position))
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        handler.removeCallbacks(hideRunnable)
        CastWindowStateManager.removeState(CastWindowState.FULL)
    }


    /**
     * 监听挡位，走行规制相关ui
     */
    private val driveStateChangedListener = object : CarInfoInterface.DriveStateChangedListener {
        override fun onDriveStateChanged(state: Int) {
            if (DevelopModel.getInstance().driveSafetyVersion) {
                displayBaselineRunningStatus(state)
            } else {
                if (state == 1) {
                    screenSafeTip?.visibility = View.VISIBLE
                } else {
                    screenSafeTip?.visibility = View.GONE
                }
            }
        }
    }

    /**
     * 根据行车档位，走行状态，刷新ui
     */
    private fun displayBaselineRunningStatus(state: Int) {

        if (state == 2){
            CastWindowStateManager.getTextureView()?.let {
                val params: LayoutParams = it.layoutParams
                params.width = 1920
                params.height = 1080

                val interceptFrameLayout =
                    hidScreenView.layoutParams as FrameLayout.LayoutParams
                interceptFrameLayout.width = width
                interceptFrameLayout.height = height
                hidScreenView.layoutParams = interceptFrameLayout

                it.setLayoutParams(params)
            }
            //大小更新之后需要重新计算
            HidScreenTouchDelegateHelper.setHidScreenTouchDelegate(
                hidScreenView,
                88.dpToPx().toInt(),
                0, 0,
                true
            )
        }
        val aBoolean = getBoolean(MyApplication.application, "isPassenger", true)
        if (state == 1 && aBoolean) {
            screenSafeDriver.visibility = View.VISIBLE

            linearLayoutsc?.orientation = LinearLayout.HORIZONTAL
            val layoutParams = linearLayoutsc?.layoutParams as ViewGroup.MarginLayoutParams
            layoutParams.setMargins(
                0,
                0,
                0,
                resources.getInteger(R.integer.safe_driver_linear_layout_sc_margin_bottom)
            )
            linearLayoutsc!!.layoutParams = layoutParams

            val lp = bt_passenger!!.layoutParams as ViewGroup.MarginLayoutParams
            lp.width = 300
            lp.setMargins(120, 0, 0, 0)
            bt_passenger?.setLayoutParams(lp)
            val lps = bt_safe_exit!!.layoutParams as ViewGroup.MarginLayoutParams
            lps.width = 300
            lps.setMargins(0, 0, 0, 0)
            bt_safe_exit?.setLayoutParams(lps)

            /* if (angle == 0 || angle == 180) {
                 linearLayoutsc?.orientation = LinearLayout.VERTICAL

                 val layoutParams = linearLayoutsc?.layoutParams as ViewGroup.MarginLayoutParams
                 layoutParams.setMargins(0, 0, 0, 62)
                 linearLayoutsc!!.layoutParams = layoutParams

                 val lp = bt_passenger!!.layoutParams as ViewGroup.MarginLayoutParams
                 lp.setMargins(820, 16, 820, 0)
                 bt_passenger?.setLayoutParams(lp)
                 val lps = bt_safe_exit!!.layoutParams as ViewGroup.MarginLayoutParams
                 lps.setMargins(820, 16, 820, 0)
                 bt_safe_exit?.setLayoutParams(lps)
             } else {

             }*/
            bt_safe_exit!!.setOnClickListener {
                destoryServices(
                    bt_safe_exit!!.context
                )
            }
            bt_passenger!!.setOnClickListener {
                saveBoolean(
                    MyApplication.application,
                    "isPassenger",
                    false
                )
                screenSafeDriver.visibility = View.GONE
            }
        } else {
            screenSafeDriver.visibility = View.GONE
        }
    }
    @SuppressLint("CommitTransaction")
    private fun showSettingFragment() {
        settingLayout.visibility = View.VISIBLE
        val fragment = SettingFragment.newInstance("", "")
        supportFragmentManager.beginTransaction()
            .add(R.id.setting_frameLayout, fragment)
            .commit()
        isSettingFragmentVisible = true
    }
    @SuppressLint("CommitTransaction")
    private fun hideSettingFragment() {
        settingLayout.visibility = View.GONE
        val fragment = supportFragmentManager.findFragmentById(R.id.setting_frameLayout)
        if (fragment != null) {
            supportFragmentManager.beginTransaction()
                .remove(fragment)
                .commit()
        }
        isSettingFragmentVisible = false
    }
    override fun onBackPressed() {
        LinkLog.d(TAG,"FullscreenActivity::onBackPressed------------")
        if(isSettingFragmentVisible){
            hideSettingFragment()
        }else{
            finish()
        }
    }
}