package com.autoai.wdlink.hu.castwindow.utils

import android.content.Context
import android.content.SharedPreferences
import android.util.Log

/**
 * <AUTHOR> zhanggc
 * @description : java类作用描述
 * @date : 2024/11/5 15:43
 * @version : 1.0
 */

class SharePreferenceUtil private constructor(context: Context) {
    private val mContext: Context

    /**
     * 保存debug模式
     */
    fun setIsDebug(open: Boolean) {
        try {
            sp!!.edit().putBoolean(IS_DEBUG, open).commit()
        } catch (throwable: Throwable) {
            Log.d("SharePreferenceUtil", "setDrivingAnalyzeOpenDevelopMode:" + throwable.message)
        }
    }

    /**
     * 获取是否是debug模式
     */
    fun getIsDebug(): Boolean {
        return sp!!.getBoolean(IS_DEBUG, false)
    }

    init {
        mContext = context
        sp = mContext.getSharedPreferences("sp_data", Context.MODE_PRIVATE)
    }

    companion object {
        /**
         * 驾驶行为分析 开发者模式测试数据  是否开启
         */
        const val IS_DEBUG = "isDebug"
        private var instance: SharePreferenceUtil? = null
        private var sp: SharedPreferences? = null
        fun getInstance(context: Context): SharePreferenceUtil? {
            if (instance == null) {
                instance = SharePreferenceUtil(context.applicationContext)
            }
            return instance
        }

        private const val SP_NAME = "future_rich"
        fun saveBoolean(context: Context, key: String?, value: Boolean) {
            if (sp == null) sp = context.getSharedPreferences(SP_NAME, 0)
            val edit = sp!!.edit()
            edit.putBoolean(key, value)
            edit.commit()
        }

        fun getBoolean(context: Context, key: String?, value: Boolean): Boolean {
            if (sp == null) sp = context.getSharedPreferences(SP_NAME, 0)
            return sp!!.getBoolean(key, value)
        }

        fun saveString(context: Context, key: String?, value: String) {
            if (sp == null) sp = context.getSharedPreferences(SP_NAME, 0)
            val edit = sp!!.edit()
            edit.putString(key, value)
            edit.commit()
        }

        fun getString(context: Context, key: String?, value: String): String? {
            if (sp == null) sp = context.getSharedPreferences(SP_NAME, 0)
            return sp!!.getString(key, value)
        }
    }
}
