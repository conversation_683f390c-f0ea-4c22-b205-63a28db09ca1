package com.autoai.wdlink.hu.dialog;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.LayoutRes;
import androidx.core.content.ContextCompat;


/**
 * Created by L on 2016/5/10.
 */
public class UIUtil {
    public static final String CONFIRM = "确定";
    public static final String CANCEL = "取消";
    private static Handler mMainHandler;
    /**
     * 获得状态栏的高度
     *
     * @param context
     * @return
     */
    private static int mStatusHeight = -1;

    /**
     * 获取到主线程Handler对象
     **/
    public static Handler getMainThreadHandler() {
        if (mMainHandler == null) {
            mMainHandler = new Handler(Looper.getMainLooper());
        }
        return mMainHandler;
    }

    /**
     * 延时在主线程执行runnable
     */
    public static boolean postDelayed(Runnable runnable, long delayMillis) {
        return getMainThreadHandler().postDelayed(runnable, delayMillis);
    }

    /**
     * 在主线程执行runnable
     */
    public static boolean post(Runnable runnable) {
        return getMainThreadHandler().post(runnable);
    }

    /**
     * 从主线程looper里面移除runnable
     */
    public static void removeCallbacks(Runnable runnable) {
        getMainThreadHandler().removeCallbacks(runnable);
    }

    /**
     * 获取布局
     **/
    public static View inflate(Context context, @LayoutRes int layoutId) {
        return inflate(context, layoutId, null);
    }

    public static View inflate(Context context, @LayoutRes int layoutId, ViewGroup parent) {
        return inflate(context, layoutId, parent, false);
    }

    public static View inflate(Context context, @LayoutRes int layoutId, ViewGroup parent, boolean attatch) {
        return getInflater(context).inflate(layoutId, parent, attatch);
    }

    public static LayoutInflater getInflater(Context context) {
        return LayoutInflater.from(context);
    }

    /**
     * 获取颜色
     */
    public static int getColor(int resId,Context context) {
        return ContextCompat.getColor(context, resId);
    }
    /**
     * 获取drawable
     */
    public static Drawable getDrawable(int resId,Context context) {
        return ContextCompat.getDrawable( context, resId);
    }

}
