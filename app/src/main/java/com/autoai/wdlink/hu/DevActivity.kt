package com.autoai.wdlink.hu

import android.os.Bundle
import android.view.View
import android.widget.TextView
import com.autoai.wdlink.hu.databinding.ActivityDevelopBinding
import com.autoai.wdlink.hu.frame.BaseActivity
import com.autoai.wdlink.hu.model.DevelopModel
import com.autoai.wdlink.hu.model.DevelopModel.DriveSafetyListener
import com.autoai.wdlink.hu.viewmodel.DevelopViewModel

class DevActivity :
    BaseActivity<DevelopViewModel, ActivityDevelopBinding>(ActivityDevelopBinding::inflate) {
    companion object {
        const val TAG = "DevActivity"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // 初始化view
        initView(viewModel.data)
    }

    override fun getViewModelClass() = DevelopViewModel::class
    override fun getViewBindingClass() = ActivityDevelopBinding::class

    private fun initView(data: DevelopModel.SwitchData) {
        binding.appVersion.text = "当前版本信息 : ${BuildConfig.VERSION_NAME}"
        binding.rbLogV.setButtonDrawable(R.drawable.rb_develop_selector)
        binding.rbLogD.setButtonDrawable(R.drawable.rb_develop_selector)
        binding.rbLogI.setButtonDrawable(R.drawable.rb_develop_selector)
        binding.rbLogW.setButtonDrawable(R.drawable.rb_develop_selector)
        binding.rbLogE.setButtonDrawable(R.drawable.rb_develop_selector)
        binding.switchAuthenticateVersion.setOnClickListener {
            val isAuth = binding.switchAuthenticateVersion.isChecked
            if (isAuth) {
                binding.tvAuthenticate2.setText("开")
            } else {
                binding.tvAuthenticate2.setText("关")
            }
            viewModel.setDriveSafetyVersion(isAuth)
        }
        binding.switchAuthenticate.setOnClickListener() {
            val isAuth = binding.switchAuthenticate.isChecked
            if (isAuth) {
                binding.tvAuthenticate.setText("开")
            } else {
                binding.tvAuthenticate.setText("关")
            }
            viewModel.setAuthWindowStatus(isAuth)
        }
        binding.switchDrivingSafety.setOnClickListener() {
            val isDrivingSafety = binding.switchDrivingSafety.isChecked
            if (isDrivingSafety) {
                drivingSafetyAllStatus(isDrivingSafety, "全部打开", "开")
            } else {
                drivingSafetyAllStatus(isDrivingSafety, "全部关闭", "关")
            }
            viewModel.setDriveSafetyStatus(
                isDrivingSafety,
                isDrivingSafety
            )
        }
        binding.switchLog.setOnClickListener() {
            val isLog = binding.switchLog.isChecked
            if (isLog) {
                binding.tvLogStatus.setText("开")
                viewModel.setLogStatus(isLog, 1)
                binding.rgLog.visibility = View.VISIBLE
                binding.rbLogV.isChecked = true
            } else {
                binding.tvLogStatus.setText("关")
                viewModel.setLogStatus(isLog, -1)
                binding.rgLog.visibility = View.INVISIBLE
                binding.rgLog.clearCheck()
            }
        }
        binding.switchFps.setOnClickListener() {
            val isFps = binding.switchFps.isChecked
            if (isFps) {
                binding.tvFpsStatus.setText("开")
                viewModel.setFpsStatus(isFps)
            } else {
                binding.tvFpsStatus.setText("关")
                viewModel.setFpsStatus(isFps)
            }
        }
       binding.switchRtt.setOnClickListener() {
            val isRtt = binding.switchRtt.isChecked
            if (isRtt) {
                binding.tvRttStatus.setText("开")
                viewModel.setRttStatus(isRtt,1)
            } else {
                binding.tvRttStatus.setText("关")
                viewModel.setRttStatus(isRtt, 0)
            }
        }
        binding.switchDrivingSafetyOnline.setOnClickListener() {
            driveSafetyStatus(binding.tvDrivingSafetyOnlineStatus)
        }
        binding.switchDrivingSafetyOffline.setOnClickListener() {
            driveSafetyStatus(binding.tvDrivingSafetyOfflineStatus)
        }
        binding.rbLogV.setOnClickListener() {
            viewModel.setLogStatus(true, 1)
        }
        binding.rbLogD.setOnClickListener() {
            viewModel.setLogStatus(true, 2)
        }
        binding.rbLogI.setOnClickListener() {
            viewModel.setLogStatus(true, 3)
        }
        binding.rbLogW.setOnClickListener() {
            viewModel.setLogStatus(true, 4)
        }
        binding.rbLogE.setOnClickListener() {
            viewModel.setLogStatus(true, 5)
        }
        binding.viewBack.setOnClickListener() {
            finish()
        }

        binding.switchAuthenticate.isChecked = data.isAuthWindow
        if (data.isAuthWindow) {
            binding.tvAuthenticate.setText("开")
        } else {
            binding.tvAuthenticate.setText("关")
        }

        if (data.isDriveSafetyOnline && data.isDriveSafetyOffline) {
            binding.switchDrivingSafety.isChecked = true
            binding.tvDrivingSafetyAll.setText("全部打开")
        } else {
            binding.switchDrivingSafety.isChecked = false
            binding.tvDrivingSafetyAll.setText("全部关闭")
        }
        binding.switchDrivingSafetyOnline.isChecked = data.isDriveSafetyOnline
        if (data.isDriveSafetyOnline) {
            binding.tvDrivingSafetyOnlineStatus.setText("开")
        } else {
            binding.tvDrivingSafetyOnlineStatus.setText("关")
        }
        binding.switchDrivingSafetyOffline.isChecked = data.isDriveSafetyOffline
        if (data.isDriveSafetyOffline) {
            binding.tvDrivingSafetyOfflineStatus.setText("开")
        } else {
            binding.tvDrivingSafetyOfflineStatus.setText("关")
        }

        binding.switchLog.isChecked = data.isLogEnabled
        if (data.isLogEnabled) {
            binding.tvLogStatus.setText("开")
            binding.rgLog.visibility = View.VISIBLE
        } else {
            binding.tvLogStatus.setText("关")
            binding.rgLog.visibility = View.INVISIBLE
        }
        when (data.logLevel) {
            1 -> binding.rbLogV.isChecked = true
            2 -> binding.rbLogD.isChecked = true
            3 -> binding.rbLogI.isChecked = true
            4 -> binding.rbLogW.isChecked = true
            5 -> binding.rbLogE.isChecked = true
        }

        binding.switchAuthenticateVersion.isChecked = data.isDriveSafetyVersion
        if (data.isDriveSafetyVersion) {
            binding.tvAuthenticate2.setText("开")
        } else {
            binding.tvAuthenticate2.setText("关")
        }

        binding.switchFps.isChecked = data.isFpsEnabled
        if (data.isFpsEnabled) {
            binding.switchFps.setText("开")
        } else {
            binding.switchFps.setText("关")
        }
        binding.switchRtt.isChecked = data.isRttEnabled
        if (data.isRttEnabled) {
            binding.switchRtt.text = "开"
        } else {
            binding.switchRtt.text = "关"
        }
    }

    private fun driveSafetyStatus(tv: TextView) {
        val isOnline = binding.switchDrivingSafetyOnline.isChecked
        val isOffline = binding.switchDrivingSafetyOffline.isChecked
        if (tv.equals(binding.tvDrivingSafetyOfflineStatus)) {
            if (isOffline) {
                tv.setText("开")
            } else {
                tv.setText("关")
            }
        } else {
            if (isOnline) {
                tv.setText("开")
            } else {
                tv.setText("关")
            }
        }

        if (isOnline && isOffline) {
            binding.switchDrivingSafety.isChecked = true
            binding.tvDrivingSafetyAll.setText("全部打开")
        }
        if (!isOnline && !isOffline) {
            binding.switchDrivingSafety.isChecked = false
            binding.tvDrivingSafetyAll.setText("全部关闭")
        }
        viewModel.setDriveSafetyStatus(
            isOnline,
            isOffline
        )
    }

    private fun drivingSafetyAllStatus(isDrivingSafety: Boolean, strAll: String, str: String) {
        binding.tvDrivingSafetyAll.setText(strAll)
        binding.switchDrivingSafetyOnline.isChecked = isDrivingSafety
        binding.switchDrivingSafetyOffline.isChecked = isDrivingSafety
        binding.tvDrivingSafetyOnlineStatus.setText(str)
        binding.tvDrivingSafetyOfflineStatus.setText(str)
    }
}