package com.autoai.wdlink.hu.frame.utils;

import android.os.Handler;
import android.os.Message;
import android.view.View;

public class HideControl {
    public final static int MSG_HIDE = 0x01;

    private HideHandler mHideHandler;
    private View view;

    public HideControl(View view) {
        this.view = view;
        mHideHandler = new HideHandler();
    }

    public class HideHandler extends Handler {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            switch (msg.what) {
                case MSG_HIDE:
                    view.setVisibility(View.INVISIBLE);
                    break;
            }

        }
    }

    private Runnable hideRunable = new Runnable() {

        @Override
        public void run() {
            mHideHandler.obtainMessage(MSG_HIDE).sendToTarget();
        }
    };

    public void startHideTimer() {//开始计时,三秒后执行runable
        mHideHandler.removeCallbacks(hideRunable);
        if(view.getVisibility() == View.INVISIBLE){
            view.setVisibility(View.VISIBLE);
        }
        mHideHandler.postDelayed(hideRunable, 10000);
    }

    public void endHideTimer() {//移除runable,将不再计时
        mHideHandler.removeCallbacks(hideRunable);
    }

    public void resetHideTimer() {//重置计时
        mHideHandler.removeCallbacks(hideRunable);
        mHideHandler.postDelayed(hideRunable, 10000);
    }

}