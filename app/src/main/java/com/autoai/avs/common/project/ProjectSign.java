package com.autoai.avs.common.project;

import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.autoai.avs.common.base.log.L;
import com.autoai.avs.common.base.project.SdkInitListener;
import com.autoai.avs.common.base.project.SignAdapter;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

public class ProjectSign implements SignAdapter {
    private static final String TAG = ProjectSign.class.getSimpleName();
    public static final String WECLOUD_API = ".autoai.com";

    //vehicle（车端）（默认）
    public static final String CONFIG_X_CLIENT_VEHICLE = "vehicle";
    //phone（手机）
    public static final String CONFIG_X_CLIENT_PHONE = "phone";
    //backend（后台）
    public static final String CONFIG_X_CLIENT_BACKEND = "backend";

    //云端配置请求头版本号
    public static final String CONFIG_X_V = "1.0";
    //请求服务的终端类型
    public static final String CONFIG_X_CLIENT = CONFIG_X_CLIENT_VEHICLE;

    private static final int BAT = 0;
    private static final int TEST = 1;
    private static final int UAT = 2;
    private static final int PROD = 3;

    private Sign lexusSign, ftmsSign, gtmcSign, defaultSign;

    @NonNull
    public String getX_V() {
        return CONFIG_X_V;
    }

    @NonNull
    public String getX_Cid() {
        return "DeviceInfoUtil.getDeviceId()";
    }

    @NonNull
    public String getX_Token() {
        return "UserUtil.getToken()";
    }

    @NonNull
    public String getX_Guid() {
        return "UserUtil.getUserId()";
    }

    @NonNull
    @Override
    public String getX_Channel() {
        return getSign().getX_Channel();
    }

    @Nullable
    @Override
    public Map<String, String> getExtendHeaderMap(String url) {
        HashMap<String, String> map = new HashMap<>();
        String nickName = "UserUtil.getNickName()";
        try {
            map.put("nickname", URLEncoder.encode(nickName == null ? "" : nickName, "UTF-8"));
        } catch (UnsupportedEncodingException e) {
            L.e(TAG, "e = " + e);
        }
        map.put("product", getSign().getProduct());
        map.put("x-rid", creteRandomString());
        return map;
    }

    /**
     * @return 生成64位随机字符串
     */
    private String creteRandomString() {
        StringBuilder stringBuilder = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < 64; i++) {
            stringBuilder.append(random.nextInt(10));
        }
        return stringBuilder.toString();
    }

    @NonNull
    @Override
    public String getSignAlias(String url) {
        return getSign().getSignAlias();
    }

    @Nullable
    @Override
    public List<Map<String, String>> getSignMaps() {
        List<Map<String, String>> list = new ArrayList<>();
        if (TextUtils.isEmpty(getSign().getSignAlias())) {
            return list;
        }
        Map<String, String> signMap = new HashMap<>();
        signMap.put("SIGN_ALIAS", getSign().getSignAlias());
        signMap.put("SECRET_KEY", getSign().getSecretKey());
        list.add(signMap);
        return list;
    }

    @NonNull
    @Override
    public String getSignKeyPath() {
        return SignAdapter.super.getSignKeyPath();
    }

    @Override
    public boolean isLexus() {
        return getType() == 0;
    }

    @NonNull
    public String getX_Client() {
        return CONFIG_X_CLIENT;
    }

    @Override
    public void init(SdkInitListener sdkInitListener) {
        /*DeviceInfoUtil.addSdkInitListener(new SdkInitListener() {
            @Override
            public void onInitSuccess() {
                sdkInitListener.onInitSuccess();
                DeviceInfoUtil.removeSdkInitListener(this);
            }

            @Override
            public void onInitFailed() {
                sdkInitListener.onInitFailed();
                DeviceInfoUtil.removeSdkInitListener(this);
            }
        });*/
    }

    @Override
    public boolean isNeedSign(String url) {
        return !TextUtils.isEmpty(url) && url.indexOf(WECLOUD_API) > 0;
    }

    @Nullable
    @Override
    public String getApiKey(String url) {
        return getSign().getApiKey(0);
    }

    @Nullable
    @Override
    public String getX_Tenant() {
        return getSign().getX_Tenant();
    }

    /**
     * @return "":异常；"0：lexus；1：一丰；2：广丰"
     */
    private int getType() {
        //return DeviceInfoUtil.getBrands();
        return 3;
    }

    private Sign getSign() {
        int type = getType();
        if (type == 0) {
            //Lexus
            if (lexusSign == null) {
                lexusSign = new LexusSign();
            }
            return lexusSign;
        } else if (type == 1) {
            //一丰
            if (ftmsSign == null) {
                ftmsSign = new FtmsSign();
            }
            return ftmsSign;
        } else if (type == 2) {
            //广丰
            if (gtmcSign == null) {
                gtmcSign = new GtmcSign();
            }
            return gtmcSign;
        } else {
            if (defaultSign == null) {
                defaultSign = new DefaultSign();
            }
            return defaultSign;
        }
    }

    private interface Sign extends SignAdapter {
        String getSignAlias();

        String getSecretKey();

        String getApiKey(int environment);

        String getProduct();

    }

    private static class LexusSign extends ProjectSign implements Sign {

        @Nullable
        @Override
        public String getX_Tenant() {
            return "Lexus";
        }

        @NonNull
        @Override
        public String getX_Channel() {
            return "24MML";
        }

        @Nullable
        @Override
        public Map<String, String> getExtendHeaderMap(String url) {
            Map<String, String> map = super.getExtendHeaderMap(url);
            map.put("product", "24MMLexus");
            return map;
        }

        @Override
        public String getSignAlias() {
            return "SIGN_LEXUS";
        }

        @Override
        public String getSecretKey() {
            return "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";
        }

        @Override
        public String getApiKey(int environment) {
            return "c91a71ec021945ca8d340810ebd2de19";
        }

        @Override
        public String getProduct() {
            return "24MMLexus";
        }
    }

    private static class FtmsSign extends ProjectSign implements Sign {

        @NonNull
        @Override
        public String getX_Channel() {
            return "25MMY";
        }

        @Nullable
        @Override
        public String getX_Tenant() {
            return "FTMS";
        }

        @Override
        public String getSignAlias() {
            return "SIGN_FTMS";
        }

        @Override
        public String getSecretKey() {
            return "gAAAAAEAAQD1rN0fkgMZ642XFlNOjsbAHylbwus+9eq3mGMIvOPFoNA1Xs7/Mi/mfcDpNBcl1+FQNySoU06T5LIWSyCEH9dG5N6icAhT0PJ9HzJxyo9h1Llhq0fUgEN6zsbKvm9LT8XuDe/K5zCRhDSRQxnbb/z9FD7ttBNzH7yTCWSvP6IeNrg3AOx18scSpctXgpemwIxNkYzrLT720OFSdObXD/1go3xv/ZesJKwPKDvG3tywqGUkhbUi3wOQyV1gRYTXcHXYbOVxMry652OKZOVyxj6PxEJqlrHFLSTf3J/3N06JAMUPCgyHai919tMAkQTy7hkWLGZE6oeGv6ropPSwN9OOSHpco5mK1+FYIlWGO2GniYNnljcHbNJ0VSuAmKKvAuBPZGYKqWm+fjOXM7y7rjjWIiQ1y8comDTLiyvTpzCzhez7FT1w43tw6+VXEGB9W2VJKeuqOMSCeMkQH5DulcqBC1ELSLCW2ReA6qMt4dpFF9UUZjgBXtxlstUhGH1LF2c8Z4g0fQKXE8XlgqH0R/URgF595bfUO+G9Bh0V/Q64UcilFopmqtgyw3s4HyUyBs3gcGJ7IyasULblXYdRwzeg+zLNt6sN/+mank5O/i5m2Ga3Os6A0ZowmzkFk+rAzwHLMEm+/bpGcX+xFXP/DbYZ0VvZnra4c4JFxdGmKIM2iInL4PdQxQOqXeidm2g9rIjWpG0XDxXdbAU4HEDxdyooTSjXhwZXlSk2HXOUGJtu1eK5m7wRvgGiS57bvlo6bup+KU+VE4niJ+iW3zPJGnSyv7SEILKOdHMjni+RBXX4enr8dRlBwVNf495QAaZx6qAv4ZbKJjEo+DEExOvhsGiGxfBETkDWVq6kc+mvxNGFKZ8j3GMTkgR+XMqdP2IV+ESjzVD9LC4uAgJp9vrlLpVVxLnYH99kaycOcPNnvDIipEPuSAozhKZyGToAAxPpqcIDdrD7ifnA2c3HXGhQYghHsWeNg4vWIgX4mrMFYYKlFJw9nrgOZV3MH+b9h2TRb1ipcbnDhxhVShdGiCKVXPhGy2GPabB39143Ka3du4nZ3zV4yxyncbboPRlWiMkZXRT8cvElHklQH8dayQOh1YKJ4VZjJof27H6FJBYfmtDjGRz2O8DniuzpzPIaLFLd37NeAKDAVp74TYf7alcGfK8DUWv+hsSj5R/+6hbXvKuGexytwrtw7zw+0iWP8L3YBMPJIL1+s3ufZfO8sVVOEvyg66sdYcimQtjekgpSdCCG51PSk7nb6CHyAY1BXeENXIXTQcXEjykAqDZ5tGVba7z96eET1lnjeuFRjoxrmE1zx1kwJfDoPMJbfTcOcl+4Vbl9766rfZxUUxKc9nK/ZmwIpp5kI/YW/p36rLfqwqrQFyViu8ZZQ08CxaC4DM4TUXSxtpeozJVFx0H8sQSngRvM49aSPBrhPt+B9+xPCj3EvPNbZMDxqYcypTg2TLlSGJwoqthCjBYiDQ0OXRhLIdQ/fDj/beGGoIa+1tN3KOsTUZbrkyIBtS8Pw4wnD637TiozTZcU0zeakQ5tCDZZG4fvBhsRB9BAFpTNPaN6OPGkaz48fxFzGqKBh6q44MU/z8ta4NIrIem/7iZiaF9TDcqRSUqivGREPuvu9syzNZt+kHxQu505eotg3PO/LwCJwEnTYgY3pkQjlDrvR2+Kxf8GFbqjTzQurxAibj8i9/9sy2FyAge2JZvwFOJfn6dfpb0ZSQxOd7temSmevCCdKOv/mu4GUBOiQCiEn8cs/mzpGLX1Zi2wxMbZYoL5AKrl/0TTBZTmp9QOLHTVWHJCQYr3NvAk5rXrvxdtuBcYnRZccBzxV8PFZe85O0esfYg2dL4nmaTchGPz8RwLCbkhZio987TE6IkYTwxMSbYhxonc+CpmNTBY9FKvSv98yV6YyNyPDh4eTfE4VqBkMCP+mlAt83ctYULk+6wNxUD5K1wpCYsBIVDo/iXIrAqxNTOVBWjTEiDe63depvjTk50Z8havU0w6OhpmAY3/p/UtxEjX+1cCAlALokgPpEn54tQdiAa/x8g9kich0H/J/GP4NkoBK/xltVVXS8WlfJGppM5trG++nSAOSqIg7iWBQJNgdF91d46Aie+Buyq9woR3HyAGB0gaxgPJ/ztZ3unRPjQWnjQvSXblQ7U7F1oLKyHGHquiHnURJwdNVJxjKGYs5FMSjMxjETReiGaL0iuZ+XoVMw==";
        }

        @Override
        public String getApiKey(int environment) {
            return "bab69de25e8049efb272069e0298697f";
        }

        @Override
        public String getProduct() {
            return "25MMToyota";
        }
    }

    private static class GtmcSign extends ProjectSign implements Sign {

        @NonNull
        @Override
        public String getX_Channel() {
            return "25MMT";
        }

        @Nullable
        @Override
        public String getX_Tenant() {
            return "GTMC";
        }

        @Override
        public String getSignAlias() {
            return "SIGN_GTMC";
        }

        @Override
        public String getSecretKey() {
            return "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";
        }

        @Override
        public String getApiKey(int environment) {
            return "1073c2a4d23342be89e61f07c727f130";
        }

        @Override
        public String getProduct() {
            return "25MMToyota";
        }
    }

    private static class DefaultSign extends ProjectSign implements Sign {

        @NonNull
        @Override
        public String getX_Channel() {
            return "";
        }

        @Nullable
        @Override
        public String getX_Tenant() {
            return "";
        }

        @Override
        public String getSignAlias() {
            return "";
        }

        @Override
        public String getSecretKey() {
            return "";
        }

        @Override
        public String getApiKey(int environment) {
            return "";
        }

        @Override
        public String getProduct() {
            return "";
        }
    }
}
