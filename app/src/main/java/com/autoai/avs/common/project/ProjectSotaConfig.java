package com.autoai.avs.common.project;

import androidx.annotation.NonNull;
import com.autoai.avs.common.base.project.SotaConfig;

/**
 * @ClassName ProjectSotaConfig
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/5/16 17:52
 * @Version 1.0
 */
public class ProjectSotaConfig implements SotaConfig {

//    /**
//     * 设置-应用更新-sota租户参数
//     */
    //private static final String SETTING_UPDATE_SOTA_TENANT = "wecockpit-dou8";

    /**
     * 设置-应用更新-sota车型参数
     */
    private static final String SETTING_UPDATE_SOTA_MODEL = "800-800-800";

    /**
     * 设置-应用更新-sota租户参数
     */
    private static final String SETTING_UPDATE_SOTA_LANGUAGE = "zh_cn";

    /**
     * url，必须以/结束
     */
    private static final String SETTING_BASE_URL = "https://test-wecockpit.autoai.com/";

    /**
     * 城市
     */
    private static final String SETTING_CITY = "10003";

    private static final String SETTING_DEVICE_ID = "TEST0000001";

    @NonNull
    @Override
    public String getUserId() {
        return "";
    }

    @NonNull
    @Override
    public String getUserName() {
        return "";
    }

//    @NonNull
//    @Override
//    public String getTenant() {
//        return SETTING_UPDATE_SOTA_TENANT;
//    }

    @NonNull
    @Override
    public String getModel() {
        return SETTING_UPDATE_SOTA_MODEL;
    }

    @NonNull
    @Override
    public String getCity() {
        return SETTING_CITY;
    }

    @Override
    public int getKernelVersion() {
        return 32;
    }

    @NonNull
    @Override
    public String getKernelVersionName() {
        return "L24-001";
    }

    @NonNull
    @Override
    public String getLanguage() {
        return SETTING_UPDATE_SOTA_LANGUAGE;
    }

    @Override
    public Boolean showLog() {
        return true;
    }

    @NonNull
    @Override
    public String getBaseUrl() {
        return SETTING_BASE_URL;
    }

    @NonNull
    @Override
    public String getDeviceId() {
        return SETTING_DEVICE_ID;
    }
}
