package com.autoai;

public class Constant {
    /**
     * ios 清晰度问题 高为1920 宽为1080,android 宽为1920 高为 1080 (ios 无横屏情况全为竖屏 车机端做的本地旋转) |||| 小窗系数
     * datong 长条屏 竖屏 IOS 窗口系数
     */
    public static final float SCREEN_TEXTURE_VIEW_INITIAL_IOS_SCALE = 0.32f;
    public static final float SCREEN_TEXTURE_VIEW_INITIAL_ANDROID_SCALE = 0.4f;

    /**
     * Android 端 窗口系数
     */
    public static final float TEXTURE_VIEW_INITIAL_SCALE = 0.75f;

    /**
     * 横屏系数
     */
    public static final float TEXTURE_VIEW_INITIAL_LANDSCAPE_SCALE = 0.48f;
    /**
     * 窗口缩放，最小缩放比例
     */
    public static final float MINI_SCALE_PORTRAIT = 0.87f;
    public static final float MINI_SCALE_LANDSCAPE = 0.62f;

}
