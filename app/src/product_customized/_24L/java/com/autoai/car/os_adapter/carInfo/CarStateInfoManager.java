package com.autoai.car.os_adapter.carInfo;

import android.car.Car;
import android.car.drivingstate.CarDrivingStateListener;
import android.car.drivingstate.CarDrivingStateManager;
import android.car.drivingstate.Gear;
import android.content.Context;
import android.os.RemoteException;
import android.util.Log;

import com.autoai.car.os_adapter.carInfo.callbacks.VehicleGearChangeListener;
import com.autoai.wdlink.hu.BuildConfig;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 负责app与车机相关状态的交互
 */
public class CarStateInfoManager {


    private static final String TAG = "CarStateInfoManager";
    private static CarStateInfoManager instance;

    /**
     * 当前档位
     */
    private int curVehicleGear = -1;

    /**
     * 避免重复初始化
     */
    private boolean hasInitedFlag = false;

    private List<VehicleGearChangeListener> vehicleGearChangeListenerList = new ArrayList<>();

    /**
     * 单例
     *
     * @return
     */
    public static CarStateInfoManager getInstance() {
        if (instance == null) {
            synchronized (CarStateInfoManager.class) {
                instance = new CarStateInfoManager();
            }

        }
        return instance;
    }

    /**
     * 初始化，注册client端到Car Automative service端，并获取 carDrivingStateManager
     *
     * @param context
     */
    public void init(Context context) {

        if(BuildConfig.FLAVOR.contains("_24L")){
            return;
        }
        if (!hasInitedFlag) {
            hasInitedFlag = true;
            Log.i(TAG, "CarStateInfoManager 初始化");
            Car car = Car.createCar(context);
            CarDrivingStateManager carDrivingStateManager = (CarDrivingStateManager) car.getCarManager(Car.CAR_DRIVING_STATE_SERVICE);
            try {
                carDrivingStateManager.registerListener(carDrivingStateListener);
                Integer vehicleGear = carDrivingStateManager.getVehicleGear();
                if(vehicleGear == null){
                    vehicleGear = Gear.P;
                }
                curVehicleGear = vehicleGear;
                Log.i(TAG, "收到档位回调 gear = " + curVehicleGear);
            } catch (RemoteException e) {
                Log.e(TAG, "carDrivingStateManager.registerListener注册监听异常");
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * 系统档位监听
     */
    private final CarDrivingStateListener carDrivingStateListener = new CarDrivingStateListener() {
        @Override
        public void onVehicleGearChange(int gear) {
            CarDrivingStateListener.super.onVehicleGearChange(gear);
            Log.i(TAG, "收到档位回调 gear = " + gear);
            curVehicleGear = gear;
            dispatchGearChanged(gear);
        }
    };


    public int getCurVehicleGear() {
        return curVehicleGear;
    }


    public void dispatchGearChanged(int curVehicleGear){
        for(VehicleGearChangeListener vehicleGearChangeListener:this.vehicleGearChangeListenerList){
            vehicleGearChangeListener.onVehicleGearChange(curVehicleGear);
        }
    }
    /**
     * 添加监听回调
     *
     * @param vehicleGearChangeListener
     */
    public void addVehicleGearChangeListener(VehicleGearChangeListener vehicleGearChangeListener) {
        if(!vehicleGearChangeListenerList.contains(vehicleGearChangeListener)){
            vehicleGearChangeListenerList.add(vehicleGearChangeListener);
        }
    }


    public void removeVehicleGearChangeListener(VehicleGearChangeListener vehicleGearChangeListener) {
        vehicleGearChangeListenerList.remove(vehicleGearChangeListener);
    }
}
