package com.autoai.car.os_adapter.imp;

import android.car.drivingstate.Gear;
import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;

import com.autoai.car.os_adapter.carInfo.CarStateInfoManager;
import com.autoai.car.os_adapter.carInfo.callbacks.VehicleGearChangeListener;
import com.autoai.car.os_adapter.interfaces.CarInfoInterface;
import com.autoai.wdlink.hu.model.DevelopModel;
import com.autoai.wdlink.hu.sdk.LinkUtil;
import com.autoai.welinkapp.LinkHost;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * <p>
 * 管理车机走行相关业务
 */
public class CarInfoImp implements CarInfoInterface {

    private static final String TAG = "CarDriveSafeRuleControl";
    private static CarInfoImp instance;


    /**
     * 当前行车走行状态
     */
    private int curDriveState = -1;

    /**
     * 当前真实档位
     */
    private int curVehicleGear = -1;
    /**
     * 避免重复初始化
     */
    private boolean initedFlag = false;

    private Context context = null;

    private List<CarInfoInterface.DriveStateChangedListener> driveStateChangedListeners = new ArrayList<>();


    /**
     * 系统档位变化监听
     */
    private VehicleGearChangeListener vehicleGearChangeListener = new VehicleGearChangeListener() {
        @Override
        public void onVehicleGearChange(int gear) {
            curVehicleGear = gear;
            //--->档位发生变化
            boolean connected = LinkUtil.INSTANCE.isConnected();
            Log.i(TAG, "CarDriveSafeRuleControl::onVehicleGearChange 收到档位回调 connected = " + connected);
            if (gear == Gear.P) {
                curDriveState = DRIVE_STATE_OFF;
                dispatchDriveStateChanged(curDriveState);
            } else {
                Log.i(TAG, "CarDriveSafeRuleControl::onVehicleGearChange 收到档位回调 gear = " + gear);
                curDriveState = DRIVE_STATE_ON;
                if (DevelopModel.getInstance().getDriveSafetyVersion()) {
                    dispatchDriveStateChanged(curDriveState);
                }
            }
            if (connected && !DevelopModel.getInstance().getDriveSafetyVersion()) {
                //触发一个很长的流程：链路说明
                // https://navinfo.feishu.cn/docx/Gt5Td0ESOoJ5K2xohJqcvXI7nZf
                //结果会触发onCheckIsPlayVideoResult
                LinkHost.checkIsPlayVideo(curDriveState);
            }
        }
    };


    /**
     * she tips: LinkHost.checkIsPlayVideo();的结果会触发onCheckIsPlayVideoResult
     *
     * @param isPlay
     */
    public void onCheckIsPlayVideoResult(int isPlay) {
        Log.i(TAG, "CarDriveSafeRuleControl::onCheckIsPlayVideoResult isPlay = " + isPlay);
        curVehicleGear = CarStateInfoManager.getInstance().getCurVehicleGear();
        if (isPlay == DRIVE_STATE_VIDEO) {
            if (curVehicleGear != Gear.P) {
                curDriveState = DRIVE_STATE_ON;
            } else {
                curDriveState = DRIVE_STATE_OFF;
            }
        } else {
            curDriveState = DRIVE_STATE_OFF;
        }
        dispatchDriveStateChanged(curDriveState);
    }

    /**
     * 单例
     *
     * @return
     */
    public static CarInfoImp getInstance() {
        if (instance == null) {
            synchronized (CarInfoImp.class) {
                instance = new CarInfoImp();
            }
        }
        return instance;
    }


    /**
     * 初始化，注册client端到Car Automative service端，并获取 carDrivingStateManager
     *
     * @param context
     */
    public void init(Context context) {
        if (!initedFlag) {
            initedFlag = true;
            this.context = context;
            CarStateInfoManager.getInstance().init(context);
            CarStateInfoManager.getInstance().addVehicleGearChangeListener(vehicleGearChangeListener);

            curVehicleGear = CarStateInfoManager.getInstance().getCurVehicleGear();
            Log.i(TAG, "CarDriveSafeRuleControl::curVehicleGear =  " + curVehicleGear);

            if (curVehicleGear == Gear.P) {
                curDriveState = DRIVE_STATE_OFF;
            } else {
                curDriveState = DRIVE_STATE_ON;
            }


        }
    }

    public int getCurDriveState() {
        return curDriveState;
    }

    private void dispatchDriveStateChanged(int curDriveState) {
        for (CarInfoInterface.DriveStateChangedListener driveStateChangedListener : this.driveStateChangedListeners) {
            driveStateChangedListener.onDriveStateChanged(curDriveState);
        }
    }

    /**
     * 添加监听回调
     *
     * @param driveStateChangedListener
     */
    public void addVehicleGearChangeListener(@NonNull CarInfoInterface.DriveStateChangedListener driveStateChangedListener) {
        if (!driveStateChangedListeners.contains(driveStateChangedListener)) {
            driveStateChangedListeners.add(driveStateChangedListener);
        }
        //---> 第一次添加的时候，触发回调，将当前的状态返回给调用者
        driveStateChangedListener.onDriveStateChanged(curDriveState);
    }


    public void removeVehicleGearChangeListener(@NonNull CarInfoInterface.DriveStateChangedListener driveStateChangedListener) {
        driveStateChangedListeners.remove(driveStateChangedListener);
    }

    @Override
    public int getCurGearValue() {
        return curVehicleGear;
    }
}
