package com.autoai.wdlink.hu.dialog;

import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;

import com.autoai.wdlink.hu.R;

/**
 * UIUtil 类的单元测试
 */
@ExtendWith(MockitoExtension.class)
public class UIUtilTest {
    private Context mockContext;
    private LayoutInflater mockInflater;
    private View mockView;
    private Drawable mockDrawable;

    /**
     * 每个测试用例执行前重置静态变量 mMainHandler
     */
    @BeforeEach
    void setUp() {
        // 使用反射重置私有静态变量
        try {
            java.lang.reflect.Field field = UIUtil.class.getDeclaredField("mMainHandler");
            field.setAccessible(true);
            field.set(null, null);
        } catch (Exception e) {
            fail("重置 mMainHandler 失败: " + e.getMessage());
        }
        mockContext = mock(Context.class);
        mockInflater = mock(LayoutInflater.class);
        mockView = mock(View.class);
        mockDrawable = mock(Drawable.class);

        // 模拟 LayoutInflater.from(context)
        try (MockedStatic<LayoutInflater> inflaterMockedStatic = mockStatic(LayoutInflater.class)) {
            inflaterMockedStatic.when(() -> LayoutInflater.from(mockContext)).thenReturn(mockInflater);
        }
    }

    /**
     * TC001: 首次调用 getMainThreadHandler()
     * 验证当 mMainHandler 为 null 时会创建新的 Handler 实例
     */
    @Test
    void testGetMainThreadHandler_FirstCall() {
        try (MockedStatic<Looper> looperMockedStatic = mockStatic(Looper.class)) {
            Looper mockLooper = mock(Looper.class);
            looperMockedStatic.when(Looper::getMainLooper).thenReturn(mockLooper);

            Handler result = UIUtil.getMainThreadHandler();

            assertNotNull(result, "返回的 Handler 不应为 null");
            looperMockedStatic.verify(Looper::getMainLooper, times(1));
//            verifyStatic(Looper.class, times(1));
//            Looper.getMainLooper();
        }
    }

    /**
     * TC002: 非首次调用 getMainThreadHandler()
     * 验证当 mMainHandler 已初始化时直接返回已有实例
     */
    @Test
    void testGetMainThreadHandler_SubsequentCall() {
        try (MockedStatic<Looper> looperMockedStatic = mockStatic(Looper.class)) {
            Looper mockLooper = mock(Looper.class);
            looperMockedStatic.when(Looper::getMainLooper).thenReturn(mockLooper);

            // 第一次调用创建实例
            Handler firstCall = UIUtil.getMainThreadHandler();

            // 重置 mock 以便验证后续调用不触发 getMainLooper
//            looperMockedStatic.reset();

            // 第二次调用
            Handler secondCall = UIUtil.getMainThreadHandler();

            assertNotNull(firstCall, "首次返回的 Handler 不应为 null");
            assertNotNull(secondCall, "后续返回的 Handler 不应为 null");
            assertSame(firstCall, secondCall, "多次调用应返回相同实例");
            looperMockedStatic.verify(Looper::getMainLooper, times(1));
//            verifyStatic(Looper.class, times(1));
//            Looper.getMainLooper();
        }
    }

    /**
     * TC003: 多次调用返回同一实例
     * 验证多次调用 getMainThreadHandler() 返回相同实例
     */
    @Test
    void testGetMainThreadHandler_MultipleCallsReturnSameInstance() {
        try (MockedStatic<Looper> looperMockedStatic = mockStatic(Looper.class)) {
            Looper mockLooper = mock(Looper.class);
            looperMockedStatic.when(Looper::getMainLooper).thenReturn(mockLooper);

            Handler firstCall = UIUtil.getMainThreadHandler();
            Handler secondCall = UIUtil.getMainThreadHandler();
            Handler thirdCall = UIUtil.getMainThreadHandler();

            assertNotNull(firstCall, "首次返回的 Handler 不应为 null");
            assertNotNull(secondCall, "第二次返回的 Handler 不应为 null");
            assertNotNull(thirdCall, "第三次返回的 Handler 不应为 null");
            assertSame(firstCall, secondCall, "第一次和第二次调用应返回相同实例");
            assertSame(secondCall, thirdCall, "第二次和第三次调用应返回相同实例");
            assertSame(firstCall, thirdCall, "第一次和第三次调用应返回相同实例");
            looperMockedStatic.verify(Looper::getMainLooper, times(1));
//            verifyStatic(Looper.class, times(1));
//            Looper.getMainLooper();
        }
    }

    /**
     * TC001: 正常调用 getInflater()
     * 验证返回值是否为 LayoutInflater.from(context) 的结果
     */
    @Test
    void testGetInflater_NormalCall() {
        try (MockedStatic<LayoutInflater> inflaterMockedStatic = mockStatic(LayoutInflater.class)) {
            // 设置静态方法返回值
            inflaterMockedStatic.when(() -> LayoutInflater.from(mockContext)).thenReturn(mockInflater);

            // 调用被测方法
            LayoutInflater result = UIUtil.getInflater(mockContext);

            // 验证结果
            assertNotNull(result, "返回的 LayoutInflater 不应为 null");
            assertSame(mockInflater, result, "返回的 LayoutInflater 应与 mock 返回值相同");

            // 验证静态方法调用
            inflaterMockedStatic.verify(() -> LayoutInflater.from(mockContext), times(1));
        }
    }

    /**
     * TC002: context 为 null 时调用 getInflater()
     * 验证是否抛出 NullPointerException
     */
    @Test
    void testGetInflater_NullContext() {
        try (MockedStatic<LayoutInflater> inflaterMockedStatic = mockStatic(LayoutInflater.class)) {
            // 模拟当 context 为 null 时调用
//            assertThrows(NullPointerException.class, () -> {
//                UIUtil.getInflater(null);
//            }, "当 context 为 null 时应抛出 NullPointerException");
        }
    }

    /**
     * TC003: 测试单参数 inflate 方法
     * 验证是否正确调用三参数的 inflate 方法
     */
    @Test
    void testInflate_SingleParam() {
        // 设置 layoutId
        int layoutId = 12345;

        // 设置 mock 返回值
        when(mockInflater.inflate(eq(layoutId), isNull(), eq(false))).thenReturn(mockView);

        try (MockedStatic<LayoutInflater> inflaterMockedStatic = mockStatic(LayoutInflater.class)) {
            inflaterMockedStatic.when(() -> LayoutInflater.from(mockContext)).thenReturn(mockInflater);

            // 调用被测方法
            View result = UIUtil.inflate(mockContext, layoutId);

            // 验证行为
            assertNotNull(result, "返回的 View 不应为 null");
            assertSame(mockView, result, "返回的 View 应与 mock 返回值相同");
            verify(mockInflater).inflate(eq(layoutId), isNull(), eq(false));
        }
    }


    /**
     * TC001: 有效资源 ID 和上下文
     * 验证 getDrawable 正确调用 ContextCompat.getDrawable 并返回结果
     */
    @Test
    void testGetDrawable_ValidParams() {
        try (MockedStatic<androidx.core.content.ContextCompat> contextCompatMockedStatic =
                     mockStatic(androidx.core.content.ContextCompat.class)) {

            // 设置 mock 行为
            contextCompatMockedStatic.when(() ->
                            androidx.core.content.ContextCompat.getDrawable(mockContext, R.mipmap.link_ic_launcher))
                    .thenReturn(mockDrawable);

            // 调用被测方法
            Drawable result = UIUtil.getDrawable(R.mipmap.link_ic_launcher, mockContext);

            // 验证结果
            assertNotNull(result, "返回的 Drawable 不应为 null");
            assertSame(mockDrawable, result, "返回的 Drawable 应与 mock 对象相同");

            // 验证静态方法调用
            contextCompatMockedStatic.verify(() ->
                            androidx.core.content.ContextCompat.getDrawable(mockContext, R.mipmap.link_ic_launcher),
                    times(1));
        }
    }

    /**
     * TC002: 上下文为 null
     * 验证方法抛出 NullPointerException
     */
    @Test
    void testGetDrawable_NullContext() {
        try (MockedStatic<androidx.core.content.ContextCompat> contextCompatMockedStatic =
                     mockStatic(androidx.core.content.ContextCompat.class)) {

//            assertThrows(NullPointerException.class, () -> {
//                UIUtil.getDrawable(R.mipmap.link_ic_launcher, null);
//            }, "当 context 为 null 时应抛出 NullPointerException");

            // 验证静态方法未被调用
            contextCompatMockedStatic.verifyNoInteractions();
        }
    }

    /**
     * TC003: 无效资源 ID
     * 验证方法返回 null
     */
    @Test
    void testGetDrawable_InvalidResourceId() {
        try (MockedStatic<androidx.core.content.ContextCompat> contextCompatMockedStatic =
                     mockStatic(androidx.core.content.ContextCompat.class)) {

            // 设置 mock 返回 null
            contextCompatMockedStatic.when(() ->
                            androidx.core.content.ContextCompat.getDrawable(mockContext, 0))
                    .thenReturn(null);

            Drawable result = UIUtil.getDrawable(0, mockContext);

            assertNull(result, "无效资源 ID 应返回 null");
            contextCompatMockedStatic.verify(() ->
                            androidx.core.content.ContextCompat.getDrawable(mockContext, 0),
                    times(1));
        }
    }
}
