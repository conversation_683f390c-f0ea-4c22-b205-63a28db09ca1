{"mcpServers": {"excel-mcp-server": {"command": "uvx", "args": ["excel-mcp-server", "stdio"], "env": {"EXCEL_FILES_PATH": "/Volumes/Macintosh HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/OUTPUT"}}, "serena": {"command": "/Volumes/Macintosh HD/Users/<USER>/WorkSpace/Link/a55haiwai/serena/.venv/bin/python", "args": ["-m", "serena.mcp"], "cwd": "/Volumes/Macintosh HD/Users/<USER>/WorkSpace/Link/a55haiwai", "env": {"PYTHONPATH": "/Volumes/Macintosh HD/Users/<USER>/WorkSpace/Link/a55haiwai/serena/src"}}}}