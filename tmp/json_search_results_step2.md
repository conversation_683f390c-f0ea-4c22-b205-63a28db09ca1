# JSON搜索结果 - 第2步：JSONArray/JsonArray

## 搜索模式：JSONArray|JsonArray

### 文件列表和行号：

#### 1. app/src/main/java/com/autoai/wdlink/hu/jswebview/browse/Message.java
- 第3行：import org.json.JSONArray;
- 第103行：JSONArray jsonArray = new JSONArray(jsonStr); (2次)

#### 2. avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/client/Client.java
- 第20行：import org.json.JSONArray;
- 第244行：JSONArray tasksArray = null;
- 第247行：tasksArray = tasks.getJSONArray("data");

#### 3. avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/client/view/ClientView.java
- 第22行：import org.json.JSONArray;
- 第104行：JSONArray jsonArray = new JSONArray(displayInfo); (2次)
- 第120行：JSONArray jsonArray = new JSONArray(displayInfo); (2次)

#### 4. avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkJson.java
- 第12行：import org.json.JSONArray;
- 第732行：JSONArray pointsArray = extData.getJSONArray("points"); (2次)
- 第736行：JSONArray point = pointsArray.getJSONArray(i); (2次)