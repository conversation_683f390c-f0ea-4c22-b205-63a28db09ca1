# JSON搜索结果 - 第1步：JSONObject/JsonObject

## 搜索模式：JSONObject|JsonObject

### 文件列表和行号：

#### 1. app/src/main/java/com/autoai/wdlink/hu/jswebview/browse/Message.java
- 第5行：import org.json.JSONObject;
- 第70行：JSONObject jsonObject = new JSONObject(); (2次)
- 第87行：JSONObject jsonObject = new JSONObject(jsonStr); (2次)
- 第106行：JSONObject jsonObject = jsonArray.getJSONObject(i); (2次)

#### 2. avslink/linkHost/src/main/java/com/autoai/welinkapp/model/PlatformAdaptor.java
- 第15行：import org.json.JSONObject;
- 第231行：JSONObject jsonObjectroad = new JSONObject(); (2次)
- 第232行：JSONObject jsonObjecttotal = new JSONObject(); (2次)

#### 3. avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/client/Client.java
- 第22行：import org.json.JSONObject;
- 第246行：JSONObject tasks = new JSONObject(Adb.getStringResponseFromServer(device, "getRecentTasks")); (2次)
- 第249行：int taskId = tasksArray.getJSONObject(i).getInt("taskId");
- 第250行：String topPackage = tasksArray.getJSONObject(i).getString("topPackage");
- 第275行：if (tasksArray.getJSONObject(i).getString("topPackage").equals(device.specified_app)) {
- 第277行：appTaskId = tasksArray.getJSONObject(i).getInt("taskId");
- 第295行：String output = adb.runAdbCmd("am display move-stack " + tasksArray.getJSONObject(0).getInt("taskId") + " " + displayId);

#### 4. avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/client/view/ClientView.java
- 第106行：if (jsonArray.getJSONObject(i).getInt("id") == 0) {
- 第107行：int width = jsonArray.getJSONObject(i).getInt("width");
- 第108行：int height = jsonArray.getJSONObject(i).getInt("height");
- 第109行：int rotation = jsonArray.getJSONObject(i).getInt("rotation");
- 第122行：if (jsonArray.getJSONObject(i).getInt("id") == displayId) {
- 第123行：int width = jsonArray.getJSONObject(i).getInt("width");
- 第124行：int height = jsonArray.getJSONObject(i).getInt("height");

#### 5. avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkPctl.java
- 第22行：import org.json.JSONObject;
- 第57行：JSONObject authData = mSdkJson.getCustomExtData(jstr);
- 第61行：JSONObject extData = mSdkJson.getCustomExtData(jstr);

#### 6. avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkJson.java
- 第14行：import org.json.JSONObject;
- 第40行：private JSONObject getJson(JSONObject obj); (2次)
- 第41行：JSONObject json = new JSONObject(); (2次)
- 第55行：public JSONObject getOnExitAudio() {
- 第56行：JSONObject json = new JSONObject(); (2次)
- 第67行：public JSONObject getOnResumeAudio() {
- 第68行：JSONObject json = new JSONObject(); (2次)
- 第79行：public JSONObject getOnMusicStop() {
- 第80行：JSONObject json = new JSONObject(); (2次)
- 第81行：JSONObject extData = new JSONObject(); (2次)
- 第93行：public JSONObject getOnMusicPlay() {
- 第94行：JSONObject json = new JSONObject(); (2次)
- 第95行：JSONObject extData = new JSONObject(); (2次)
- 第107-109行：注释的JSONObject代码
- 第122行：public JSONObject getOnControlEnAndCh(int type)
- 第124行：JSONObject json = new JSONObject(); (2次)
- 第125行：JSONObject extData = new JSONObject(); (2次)
- 第137-140行：注释的JSONObject代码
- 第152行：public JSONObject getOnHuKeyState(int keyState)
- 第154行：JSONObject json = new JSONObject(); (2次)
- 第155行：JSONObject extData = new JSONObject(); (2次)
- 第167行：public JSONObject getOnAutoBTConnection(String btMacInfo) {
- 第168行：JSONObject json = new JSONObject(); (2次)
- 第169行：JSONObject extData = new JSONObject(); (2次)
- 第180行：public JSONObject getOnHuChannel(int key) {
- 第181行：JSONObject json = new JSONObject(); (2次)
- 第182行：JSONObject extData = new JSONObject(); (2次)
- 第194行：public JSONObject getOnWeChatResponse(int type) {
- 第195行：JSONObject json = new JSONObject(); (2次)
- 第196行：JSONObject extData = new JSONObject(); (2次)
- 第207行：public JSONObject getOnKeyStateInfo(int hardKey) {
- 第208行：JSONObject json = new JSONObject(); (2次)
- 第209行：JSONObject extData = new JSONObject(); (2次)
- 第220行：public JSONObject getOnHuBTPhone(int btPhoneState) {
- 第221行：JSONObject json = new JSONObject(); (2次)
- 第222行：JSONObject extData = new JSONObject(); (2次)
- 第233行：public JSONObject getOnHuLocalVrState(int localVrState) {
- 第234行：JSONObject json = new JSONObject(); (2次)
- 第235行：JSONObject extData = new JSONObject(); (2次)
- 第246行：public JSONObject getOnHuReqAppData(int type) {
- 第247行：JSONObject json = new JSONObject(); (2次)
- 第248行：JSONObject extData = new JSONObject(); (2次)
- 第259行：public JSONObject getOnHuReqPhoneIsPayingVideo(int type) {
- 第260行：JSONObject json = new JSONObject(); (2次)
- 第261行：JSONObject extData = new JSONObject(); (2次)
- 第272行：public JSONObject getOnControlMobileAduio(int hardKey) {
- 第273行：JSONObject json = new JSONObject(); (2次)
- 第274行：JSONObject extData = new JSONObject(); (2次)
- 第285-288行：注释的JSONObject代码
- 第299-302行：注释的JSONObject代码
- 第314行：public JSONObject getOnHuRvcState(int rvcState) {
- 第315行：JSONObject json = new JSONObject(); (2次)
- 第316行：JSONObject extData = new JSONObject(); (2次)
- 第327行：public JSONObject getOnHuMicState(boolean state) {
- 第328行：JSONObject json = new JSONObject(); (2次)
- 第329行：JSONObject extData = new JSONObject(); (2次)
- 第340行：public JSONObject getOnHuAppFront(boolean status) {
- 第341行：JSONObject json = new JSONObject(); (2次)
- 第342行：JSONObject extData = new JSONObject(); (2次)
- 第360行：public JSONObject getOnHuReqKeyFrame(int frame,int time) {
- 第361行：JSONObject json = new JSONObject(); (2次)
- 第362行：JSONObject extData = new JSONObject(); (2次)
- 第446行：JSONObject extData = getExtData(json, "extData");
- 第457行：JSONObject extData = getExtData(json, "extData");
- 第468行：JSONObject extData = getExtData(json, "extData");
- 第480行：JSONObject extData = getExtData(json, "extData");
- 第496行：JSONObject extData = getExtData(json, "extData");
- 第517行：JSONObject extData = getExtData(json, "extData");
- 第540行：JSONObject extData = getExtData(json, "extData");
- 第541行：JSONObject jsonHome = getExtData(json, "phoneHome");
- 第542行：JSONObject jsonBack = getExtData(json, "phoneBack");
- 第543行：JSONObject jsonHuHome = getExtData(json, "huHome");
- 第544行：JSONObject jsonPhoneSize = getExtData(json, "phoneSize");
- 第546行：JSONObject jsonObject = new JSONObject(json); (2次)
- 第556行：hidTouch.videoMode = new JSONObject(json).optJSONObject("command").optInt("videoMode"); (2次)
- 第593行：JSONObject extData = getExtData(json, "extData");
- 第604行：JSONObject extData = getExtData(json, "extData");
- 第615行：JSONObject extData = getExtData(json, "extData");
- 第628行：JSONObject extData = getExtData(json, "extData");
- 第639行：JSONObject extData = getExtData(json, "extData");
- 第650行：JSONObject extData = getExtData(json, "extData");
- 第661行：JSONObject extData = getExtData(json, "extData");
- 第672行：JSONObject extData = getExtData(json, "extData");
- 第683行：JSONObject extData = getExtData(json, "extData");
- 第696行：JSONObject json = new JSONObject(str); (2次)
- 第697行：JSONObject command = json.getJSONObject("command"); (2次)
- 第707行：private JSONObject getExtData(String str, String key) {
- 第708行：JSONObject extData = new JSONObject(); (2次)
- 第710行：JSONObject json = new JSONObject(str); (2次)
- 第711行：JSONObject command = json.getJSONObject("command"); (2次)
- 第712行：extData = command.getJSONObject(key);
- 第720行：public JSONObject getCustomExtData(String jstr) {
- 第721行：JSONObject extData = getExtData(jstr, "extData");
- 第729行：JSONObject extData = getExtData(json, "extData");
- 第758行：public JSONObject getIosHidTouchPoint(int type, int enableMoveEvents){
- 第759行：JSONObject json = new JSONObject(); (2次)
- 第760行：JSONObject extData = new JSONObject(); (2次)
- 第778行：JSONObject extData = getExtData(json, "extData");
- 第799行：JSONObject extData = getExtData(json, "extData");
- 第821行：JSONObject extData = getExtData(json, "extData");
- 第855行：public JSONObject getOnCarBTConnected(String connectedState) {
- 第856行：JSONObject json = new JSONObject(); (2次)
- 第857行：JSONObject extData = new JSONObject(); (2次)
- 第874行：public JSONObject getOnPhoneBTMacAddress(String macAddress) {
- 第877行：JSONObject json = new JSONObject(); (2次)
- 第878行：JSONObject extData = new JSONObject(); (2次)
- 第888行：JSONObject result = getJson(json);

#### 7. avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkStatus.java
- 第19行：import org.json.JSONObject;
- 第120行：private void sendJsonMsg(JSONObject obj) {