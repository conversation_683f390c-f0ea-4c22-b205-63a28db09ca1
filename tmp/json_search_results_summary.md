# JSON处理代码搜索结果汇总

## 项目中所有JSON相关代码文件和行号

### 1. 主要JSON处理文件

#### 1.1 app/src/main/java/com/autoai/wdlink/hu/jswebview/browse/Message.java
**功能：WebView桥接消息处理**
- 第3行：import org.json.JSONArray;
- 第4行：import org.json.JSONException;
- 第5行：import org.json.JSONObject;
- 第69行：public String toJson() { // 将Message对象转换为JSON字符串
- 第70行：JSONObject jsonObject = new JSONObject();
- 第84行：public static Message toObject(String jsonStr) { // 从JSON字符串创建Message对象
- 第87行：JSONObject jsonObject = new JSONObject(jsonStr);
- 第100行：public static List<Message> toArrayList(String jsonStr) { // 从JSON数组创建Message列表
- 第103行：JSONArray jsonArray = new JSONArray(jsonStr);
- 第106行：JSONObject jsonObject = jsonArray.getJSONObject(i);

#### 1.2 app/src/main/java/com/autoai/wdlink/hu/jswebview/browse/BridgeWebView.java
**功能：WebView桥接核心处理**
- 第122行：String messageJson = m.toJson(); // 调用Message的toJson方法
- 第124-125行：JSON字符串转义处理

#### 1.3 app/src/main/java/com/autoai/wdlink/hu/model/DevelopModel.java
**功能：开发模式数据管理**
- 第5行：import com.blankj.utilcode.util.GsonUtils;
- 第127行：switchData = GsonUtils.fromJson(json, SwitchData.class); // 使用Gson解析JSON
- 第147行：SharePreferenceUtil.Companion.saveString(MyApplication.application, SHARED_KEY_SWITCHES, GsonUtils.toJson(switchData)); // 使用Gson序列化

### 2. AVS Link模块JSON处理

#### 2.1 avslink/linkHost/src/main/java/com/autoai/welinkapp/model/PlatformAdaptor.java
**功能：平台适配器，处理导航和媒体信息**
- 第14行：import org.json.JSONException;
- 第15行：import org.json.JSONObject;
- 第231行：JSONObject jsonObjectroad = new JSONObject(); // 创建道路信息JSON
- 第232行：JSONObject jsonObjecttotal = new JSONObject(); // 创建总距离信息JSON
- 第235-246行：构建导航TBT（Turn By Turn）信息的JSON对象

#### 2.2 avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/client/Client.java
**功能：客户端连接管理，处理设备任务信息**
- 第20行：import org.json.JSONArray;
- 第21行：import org.json.JSONException;
- 第22行：import org.json.JSONObject;
- 第244行：JSONArray tasksArray = null; // 任务数组
- 第246行：JSONObject tasks = new JSONObject(Adb.getStringResponseFromServer(device, "getRecentTasks")); // 解析服务器响应
- 第247行：tasksArray = tasks.getJSONArray("data"); // 获取任务数据数组
- 第249-295行：遍历任务数组，获取任务ID和包名等信息

#### 2.3 avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/client/view/ClientView.java
**功能：客户端视图，处理显示信息**
- 第22行：import org.json.JSONArray;
- 第104行：JSONArray jsonArray = new JSONArray(displayInfo); // 解析显示信息
- 第106-124行：遍历显示数组，获取显示器宽高和旋转信息

### 3. Link SDK模块JSON处理

#### 3.1 avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkJson.java
**功能：SDK JSON处理核心类，负责各种消息的JSON序列化和反序列化**
- 第12行：import org.json.JSONArray;
- 第13行：import org.json.JSONException;
- 第14行：import org.json.JSONObject;

**主要方法和行号：**
- 第40行：private JSONObject getJson(JSONObject obj) // 通用JSON处理方法
- 第55行：public JSONObject getOnExitAudio() // 音频退出事件
- 第67行：public JSONObject getOnResumeAudio() // 音频恢复事件
- 第79行：public JSONObject getOnMusicStop() // 音乐停止事件
- 第93行：public JSONObject getOnMusicPlay() // 音乐播放事件
- 第122行：public JSONObject getOnControlEnAndCh(int type) // 语言控制事件
- 第152行：public JSONObject getOnHuKeyState(int keyState) // HU按键状态事件
- 第167行：public JSONObject getOnAutoBTConnection(String btMacInfo) // 蓝牙连接事件
- 第180行：public JSONObject getOnHuChannel(int key) // HU通道事件
- 第194行：public JSONObject getOnWeChatResponse(int type) // 微信响应事件
- 第207行：public JSONObject getOnKeyStateInfo(int hardKey) // 按键状态信息事件
- 第220行：public JSONObject getOnHuBTPhone(int btPhoneState) // HU蓝牙电话事件
- 第233行：public JSONObject getOnHuLocalVrState(int localVrState) // HU本地VR状态事件
- 第246行：public JSONObject getOnHuReqAppData(int type) // HU请求应用数据事件
- 第259行：public JSONObject getOnHuReqPhoneIsPayingVideo(int type) // HU请求手机播放视频事件
- 第272行：public JSONObject getOnControlMobileAduio(int hardKey) // 控制移动音频事件
- 第314行：public JSONObject getOnHuRvcState(int rvcState) // HU倒车状态事件
- 第327行：public JSONObject getOnHuMicState(boolean state) // HU麦克风状态事件
- 第340行：public JSONObject getOnHuAppFront(boolean status) // HU应用前台状态事件
- 第360行：public JSONObject getOnHuReqKeyFrame(int frame,int time) // HU请求关键帧事件
- 第446-888行：大量JSON数据解析方法，包括：
  - 导航信息解析
  - 触摸事件解析
  - 蓝牙连接信息解析
  - 各种状态信息解析
- 第707行：private JSONObject getExtData(String str, String key) // 提取扩展数据的通用方法
- 第720行：public JSONObject getCustomExtData(String jstr) // 获取自定义扩展数据
- 第758行：public JSONObject getIosHidTouchPoint(int type, int enableMoveEvents) // iOS HID触控事件
- 第855行：public JSONObject getOnCarBTConnected(String connectedState) // 车机蓝牙连接事件
- 第874行：public JSONObject getOnPhoneBTMacAddress(String macAddress) // 手机蓝牙MAC地址事件

#### 3.2 avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkPctl.java
**功能：SDK协议控制类**
- 第22行：import org.json.JSONObject;
- 第57行：JSONObject authData = mSdkJson.getCustomExtData(jstr); // 获取认证数据
- 第61行：JSONObject extData = mSdkJson.getCustomExtData(jstr); // 获取扩展数据

#### 3.3 avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkStatus.java
**功能：SDK状态管理类**
- 第19行：import org.json.JSONObject;
- 第120行：private void sendJsonMsg(JSONObject obj) // 发送JSON消息的私有方法
- 第247-328行：多个发送JSON消息的方法，调用SdkJson类的各种get方法

### 4. JSON处理库使用统计

#### 4.1 org.json (Android原生JSON库)
**使用文件：7个**
- Message.java
- BridgeWebView.java  
- PlatformAdaptor.java
- Client.java
- ClientView.java
- SdkJson.java
- SdkStatus.java

#### 4.2 Gson (Google JSON库)
**使用文件：1个**
- DevelopModel.java (通过GsonUtils工具类使用)

#### 4.3 其他JSON库
- 未发现 Jackson、FastJSON、Moshi、Retrofit 等其他JSON库的使用

### 5. JSON处理模式分析

#### 5.1 主要处理模式
1. **消息序列化/反序列化**：WebView桥接通信
2. **配置数据持久化**：开发模式设置的保存和读取
3. **协议数据处理**：车机与手机之间的各种事件和状态信息交换
4. **设备信息解析**：显示器信息、任务信息等系统数据解析
5. **导航数据处理**：TBT导航信息的JSON格式化

#### 5.2 关键JSON处理类
1. **SdkJson.java**：最重要的JSON处理类，包含80+个JSON相关方法
2. **Message.java**：WebView通信的JSON消息封装
3. **PlatformAdaptor.java**：导航信息的JSON格式化

### 6. 总结
- **总计发现JSON相关代码文件：8个**
- **主要使用org.json库进行JSON处理**
- **JSON处理主要集中在车机通信协议、WebView桥接、配置管理等模块**
- **SdkJson.java是最核心的JSON处理文件，包含项目中大部分JSON序列化和反序列化逻辑**