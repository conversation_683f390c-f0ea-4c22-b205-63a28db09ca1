# JSON搜索结果 - 第7步：JSON字符串和方法

## 搜索模式：JSON字符串处理相关

### 主要发现的JSON处理方法：

#### 1. app/src/main/java/com/autoai/wdlink/hu/jswebview/browse/BridgeWebView.java
- 第122行：String messageJson = m.toJson();

#### 2. app/src/main/java/com/autoai/wdlink/hu/model/DevelopModel.java  
- 第127行：switchData = GsonUtils.fromJson(json, SwitchData.class);
- 第147行：SharePreferenceUtil.Companion.saveString(MyApplication.application, SHARED_KEY_SWITCHES, GsonUtils.toJson(switchData));

#### 3. app/src/main/java/com/autoai/wdlink/hu/jswebview/browse/Message.java
- 第69行：public String toJson() {
- 第84行：public static Message toObject(String jsonStr) {
- 第100行：public static List<Message> toArrayList(String jsonStr) {

### 注意：
- 搜索结果中包含大量注释和文档内容，已过滤掉非代码部分
- 主要的JSON处理集中在WebView桥接、开发模式设置和消息处理等模块