# JSON搜索结果 - 第6步：JSON方法调用

## 搜索模式：\.toJson\(|\.fromJson\(|\.parseJson|\.asJson|\.toJsonString|\.parseObject|\.parseArray

### 文件列表和行号：

#### 1. app/src/main/java/com/autoai/wdlink/hu/jswebview/browse/BridgeWebView.java
- 第122行：String messageJson = m.toJson();

#### 2. app/src/main/java/com/autoai/wdlink/hu/model/DevelopModel.java
- 第127行：switchData = GsonUtils.fromJson(json, SwitchData.class);
- 第147行：SharePreferenceUtil.Companion.saveString(MyApplication.application, SHARED_KEY_SWITCHES, GsonUtils.toJson(switchData));