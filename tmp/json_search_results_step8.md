# JSON搜索结果 - 第8步：JSON库导入

## 搜索模式：org\.json|fastjson|alibaba\.fastjson|moshi|retrofit

### 发现的JSON库导入：

#### org.json 库使用：

1. **app/src/main/java/com/autoai/wdlink/hu/jswebview/browse/Message.java**
   - 第3行：import org.json.JSONArray;
   - 第4行：import org.json.JSONException;
   - 第5行：import org.json.JSONObject;

2. **avslink/linkHost/src/main/java/com/autoai/welinkapp/model/PlatformAdaptor.java**
   - 第14行：import org.json.JSONException;
   - 第15行：import org.json.JSONObject;

3. **avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/client/Client.java**
   - 第20行：import org.json.JSONArray;
   - 第21行：import org.json.JSONException;
   - 第22行：import org.json.JSONObject;

4. **avslink/app_scrcpy/src/main/java/top/eiyooooo/easycontrol/app/client/view/ClientView.java**
   - 第22行：import org.json.JSONArray;

5. **avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkPctl.java**
   - 第22行：import org.json.JSONObject;

6. **avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkJson.java**
   - 第12行：import org.json.JSONArray;
   - 第13行：import org.json.JSONException;
   - 第14行：import org.json.JSONObject;

7. **avslink/linkSdk/src/main/java/com/autoai/welink/sdk/SdkStatus.java**
   - 第19行：import org.json.JSONObject;

### 注意：
- 项目主要使用 org.json 库进行JSON处理
- 未发现 fastjson、moshi、retrofit 等其他JSON库的使用