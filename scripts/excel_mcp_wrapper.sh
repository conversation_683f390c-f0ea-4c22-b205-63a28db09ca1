#!/bin/bash

# Excel MCP Server Wrapper for Claude Code CLI
# Usage: ./excel_mcp_wrapper.sh <command> [args...]

EXCEL_FILES_PATH="/Volumes/Macintosh HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/OUTPUT"
MCP_SERVER="uvx excel-mcp-server stdio"

# Function to send MCP request
send_mcp_request() {
    local method="$1"
    local params="$2"
    local id=$((RANDOM % 1000))
    
    # Initialize first if needed
    if [ "$method" != "initialize" ]; then
        echo '{"jsonrpc": "2.0", "id": 1, "method": "initialize", "params": {"protocolVersion": "2024-11-05", "capabilities": {"roots": {"listChanged": true}}, "clientInfo": {"name": "claude-code", "version": "1.0"}}}'
    fi
    
    # Send actual request  
    if [ -z "$params" ]; then
        echo "{\"jsonrpc\": \"2.0\", \"id\": $id, \"method\": \"$method\", \"params\": {}}"
    else
        echo "{\"jsonrpc\": \"2.0\", \"id\": $id, \"method\": \"$method\", \"params\": $params}"
    fi
}

# Function to call MCP server
call_mcp_server() {
    local method="$1"
    local params="$2"
    
    send_mcp_request "$method" "$params" | \
    EXCEL_FILES_PATH="$EXCEL_FILES_PATH" $MCP_SERVER 2>/dev/null | \
    grep -v "Service stopped" | \
    tail -1 | \
    jq . 2>/dev/null || echo "MCP call failed"
}

# Main command dispatcher
case "$1" in
    "list-tools")
        echo "📋 Available Excel MCP Tools:"
        call_mcp_server "tools/list" ""
        ;;
    "list-resources")
        echo "📁 Available Excel Resources:"
        call_mcp_server "resources/list" "{}"
        ;;
    "read-excel")
        if [ -z "$2" ]; then
            echo "Usage: $0 read-excel <filename>"
            exit 1
        fi
        echo "📖 Reading Excel file: $2"
        call_mcp_server "tools/call" "{\"name\": \"read_excel\", \"arguments\": {\"filename\": \"$2\"}}"
        ;;
    "list-files")
        echo "📂 Excel files in directory:"
        ls -la "$EXCEL_FILES_PATH"/*.xlsx 2>/dev/null || echo "No Excel files found"
        ;;
    *)
        echo "Excel MCP Wrapper Commands:"
        echo "  list-tools     - List available MCP tools"
        echo "  list-resources - List available MCP resources"
        echo "  read-excel <file> - Read Excel file content"
        echo "  list-files     - List available Excel files"
        ;;
esac