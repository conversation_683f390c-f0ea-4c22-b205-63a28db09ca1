# 消费者控制事件重构测试文档

## 重构内容总结

### 问题解决
1. **主线程阻塞问题**：原来的 `sendConsumerControlKeyEvent()` 中使用 `Thread.sleep(50)` 阻塞主线程
2. **内存分配问题**：原来的 `sendConsumerControlEvent()` 中每次都 `new byte[2]`

### 解决方案
1. **扩展 HidReport 类**：
   - 添加 `ConsumerControl` 设备类型
   - 添加 `obtainConsumerControl()` 方法，支持对象池复用

2. **在 HidReportSender 中添加后台执行方法**：
   - 添加 `sendConsumerControlEvent()` 方法
   - 使用 `HidReport.obtainConsumerControl()` 避免重复创建对象
   - 通过 `addInputReport()` 在后台线程池中执行

3. **重构 HidScreenTouchManager**：
   - 修改 `sendConsumerControlKeyEvent()` 使用 `ThreadUtils.postOnBackgroundSingleThread()`
   - 调用 `hidReportSender.sendConsumerControlEvent()` 而不是直接发送
   - 移除原来的 `sendConsumerControlEvent()` 方法

## 代码变更详情

### HidReport.java 变更
- 添加 `ConsumerControl` 枚举值
- 添加 `obtainConsumerControl()` 静态方法，支持消费者控制类型的对象池复用

### HidReportSender.java 变更  
- 添加 `sendConsumerControlEvent()` 公共方法
- 使用对象池避免重复创建 byte 数组
- 后台线程执行，不阻塞主线程

### HidScreenTouchManager.java 变更
- 重构 `sendConsumerControlKeyEvent()` 方法
- 使用 `ThreadUtils.postOnBackgroundSingleThread()` 后台执行
- 删除原来的 `sendConsumerControlEvent()` 私有方法
- 添加方向性日志标识（>>> 和 <<<）

## 性能优化效果

1. **主线程性能**：消费者控制事件发送不再阻塞主线程
2. **内存性能**：使用对象池复用 HidReport 对象，减少 GC 压力
3. **代码一致性**：与 `sendTouchDownMoveEvent` 使用相同的后台执行模式

## 测试建议

1. **功能测试**：验证消费者控制按键事件是否正常发送
2. **性能测试**：确认主线程不再被阻塞
3. **内存测试**：验证对象池是否正常工作，减少内存分配
4. **日志测试**：确认日志输出包含方向性标识

## 兼容性说明

- 保持了原有的 API 接口不变
- 保持了原有的消费者控制协议编码（0x02B0）
- 保持了原有的 Report ID（2）和数据格式
