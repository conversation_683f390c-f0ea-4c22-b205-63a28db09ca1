互联link反控ios、Android
2025.04.11 ********（车展） 修改 iOS 互联后操作手机电话拨打号盘的场景下，比较容易双击的问题。
2025.04.14 ******** 
1、修改 更换 iPhone 设备后，反控位置不准确的问题。
2025-05-09 ********
1、将 校准点移动到屏幕右侧避免误触icon，但是可能会点击到辅助按钮。
2、互联完成后不自动连接蓝牙。
3、锁屏休眠 改成移动鼠标来实现。
2025-05-29 ********
重构iOS反控校准。
2025-06-06 ********
修改反控点击出现偏差问题。 优化拖动效果。遗留偶现的点击变成滑动、滑动不上的问题。
2025-06-06 ********
修改开关蓝牙后，iOS没有重新校准导致不能点击的问题。
2025-07-08 ********
重构蓝牙到linkSDk模块中。修改安卓锁屏方案为发送消费者协议按键。
2025-07-08 ********
修改USB过滤设备&USB支持多指。