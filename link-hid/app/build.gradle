plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
}

android {
    namespace 'com.autoai.avslinkhid'
    compileSdk 33

    defaultConfig {
        applicationId "com.autoai.avslinkhid"
        minSdk 24
        targetSdk 33
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary true
        }

        buildConfigField "String", "LIND_HID_VERSION", "\"${project.properties['LIND_HID_VERSION']}\""
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    buildFeatures {
        compose true
    }
    composeOptions {
        kotlinCompilerExtensionVersion '1.4.3'
    }
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib:1.8.10"
    implementation 'androidx.activity:activity-ktx:1.9.3'


    //implementation 'androidx.core:core-ktx:1.9.0'
//    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.8.7'
//    implementation 'androidx.activity:activity-compose:1.7.0'
//    implementation platform('androidx.compose:compose-bom:2023.03.00')
//    implementation 'androidx.compose.ui:ui'
//    implementation 'androidx.compose.ui:ui-graphics'
//    implementation 'androidx.compose.ui:ui-tooling-preview'
//    implementation 'androidx.compose.material3:material3'
//    testImplementation 'junit:junit:4.13.2'
//    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
//    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'
//    androidTestImplementation platform('androidx.compose:compose-bom:2023.03.00')
//    androidTestImplementation 'androidx.compose.ui:ui-test-junit4'
//    debugImplementation 'androidx.compose.ui:ui-tooling'
//    debugImplementation 'androidx.compose.ui:ui-test-manifest'
}