pluginManagement {
    repositories {
        gradlePluginPortal()
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/central' }
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        maven { url 'https://wdnexus.autoai.com/content/repositories/autoai-AVS/' }
        maven { url "https://wdnexus.autoai.com/content/repositories/releases/" }
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/central' }
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        maven { url 'https://wdnexus.autoai.com/content/repositories/autoai-AVS/' }
        maven { url "https://wdnexus.autoai.com/content/repositories/releases/" }
        mavenCentral()
    }
}
rootProject.name = "avslinkhid"
//include ':app'
include ':avslinkhid'
