apply plugin: 'maven-publish'

//声明变量记录上传Maven库地址
def repositoryUrl
//判断发到正式库还是snapshot库
if (isReleaseBuild()) {
    //上传Release私有仓库
    repositoryUrl = "https://wdnexus.autoai.com/content/repositories/autoai-AVS/"
} else {
    println 'SNAPSHOT Versions'
    //上传snapshot私有仓库
    repositoryUrl = "https://wdnexus.autoai.com/content/repositories/snapshots/autoai-AVS/"
}

//从项目gradle.properties中读取Nexus服务器登录用户名
def getRepositoryUserName() {
//    return hasProperty('USERNAME') ? USERNAME : ""
    return "autoai-AVS"
}
//读取Nexus服务器登录密码
def getRepositoryPassword() {
//    return hasProperty('PASSWORD') ? PASSWORD : ""
    return "@pIbl9kE"
}
def isReleaseBuild() {
    return !VERSION.contains("SNAPSHOT")
}

afterEvaluate {
    task androidSourcesJar(type: Jar) {
        archiveClassifier.set('sources')
        from android.sourceSets.main.java.source
    }

    publishing {
        publications {
            release(MavenPublication) {
                from(components["release"])
                pom {
                    groupId = GROUP_ID
                    version = VERSION
                    artifactId = ARTIFACT_ID
                }
                artifact(androidSourcesJar)
            }
        }
        repositories {
            maven {
                credentials {
                    username getRepositoryUserName()
                    password getRepositoryPassword()
                }
                url repositoryUrl
            }
        }
    }
}

