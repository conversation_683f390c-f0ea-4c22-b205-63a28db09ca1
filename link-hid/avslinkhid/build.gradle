plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

ext {
    VERSION = LIND_HID_VERSION
    GROUP_ID = "com.autoai.avs.link"
    ARTIFACT_ID = "linkhid"
}


apply from: file('./maven_push.gradle')

android {
    namespace 'com.autoai.avslinkhid'
    compileSdk 33

    defaultConfig {
        minSdk 24

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }


        debug {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    compileOnly "com.autoai.avs.link:linksdk:$LINK_SDK_VERSION"
    implementation 'androidx.appcompat:appcompat:1.2.0'
    implementation 'androidx.core:core:1.7.0'

    implementation 'androidx.appcompat:appcompat:1.4.0'
    implementation 'androidx.core:core-ktx:+'
//    implementation 'androidx.appcompat:appcompat:1.7.0'
//    implementation 'com.google.android.material:material:1.12.0'
//    testImplementation 'junit:junit:4.13.2'
//    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
//    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'
}