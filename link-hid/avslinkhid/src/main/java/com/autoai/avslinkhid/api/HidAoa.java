package com.autoai.avslinkhid.api;

import static com.autoai.avslinkhid.util.TouchUtil.HID_KEY_HOME;

import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.hardware.usb.UsbDeviceConnection;
import android.os.SystemClock;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.NonNull;

import com.autoai.avslinkhid.datatransfer.AutoAgreeManager;
import com.autoai.avslinkhid.hidtouchscreen.HidScreenConstantsKt;
import com.autoai.avslinkhid.keepalive.AndroidKeepAliveManager;
import com.autoai.avslinkhid.keepalive.AoaKeepAliveEventSender;
import com.autoai.avslinkhid.model.AutoAgreeConfig;
import com.autoai.avslinkhid.util.TouchUtil;
import com.autoai.common.util.LogUtil;
import com.autoai.common.util.ThreadUtils;
import com.autoai.welink.param.HidTouch;

import java.util.concurrent.atomic.AtomicBoolean;

public class HidAoa implements HidApi{
    private static final String TAG = HidScreenConstantsKt.HidScreen + "HidAOA";
    private static HidAoa mInstance;
    private AtomicBoolean isRegisterHid = new AtomicBoolean(false);
    private static Context mContext;
    private String name;
    private static int w;
    private static int h;

    // AOA防锁屏心跳管理器
    private AndroidKeepAliveManager keepAliveManager;

    private HidAoa() {
        // 初始化AOA防锁屏心跳管理器
        keepAliveManager = new AndroidKeepAliveManager(
            new AoaKeepAliveEventSender(),
            "HidAoa"
        );
    }

    public static HidApi getInstance(int width, int height) {
        if(mInstance == null) {
            synchronized (HidAoa.class) {
                if(mInstance == null) {
                    mInstance = new HidAoa();
                    w = width;
                    h = height;
                }
            }
        }
        return mInstance;
    }

    @Override
    public boolean registerHID(@NonNull Context context) {
        mContext = context;
        if(!isRegisterHid.get()){
            isRegisterHid.set(true);
            boolean result = TouchUtil.getInstance().registerHID(w,h) == 0;

            // AOA注册成功后启动防锁屏心跳
            if (result) {
                keepAliveManager.startAndroidHeartBeat();
            }

            return result;
        }

        return true;
    }

    @Override
    public boolean unregisterHID() {
        if(isRegisterHid.get()){
            isRegisterHid.set(false);

            // AOA注销时停止防锁屏心跳
            keepAliveManager.stopAndroidHeartBeat();

           return TouchUtil.getInstance().unregisterHID() == 0;
        }

        return true;
    }

    @Override
    public boolean isRegisterHID() {
        return isRegisterHid.get();
    }

    @Override
    public void connectBluetoothDevice(BluetoothDevice device) {
        // AOA 不需要手动连接蓝牙HID设备
    }


    @Override
    public boolean onTouch(View view, MotionEvent motionEvent) {
        int touchViewWidth = view.getWidth();
        int touchViewHeight = view.getHeight();
        TouchUtil.getInstance().setScreenSize(touchViewWidth, touchViewHeight);
        LogUtil.d(TAG,"onTouch--> w = " + touchViewWidth + " h = " + touchViewHeight + ", scale = " + TouchUtil.getInstance().getScaleScreen() + ", x = " + motionEvent.getX() + ", y = " + motionEvent.getY());

        // 用户操作时重新启动心跳定时器
        keepAliveManager.restartHeartBeatTimer();

        TouchUtil.getInstance().MulitTouch(motionEvent);
         return true;
    }

    @Override
    public void backHome() {
        TouchUtil.getInstance().sendHidKey(HID_KEY_HOME);
    }

    @Override
    public void setH264Coordinates(HidTouch rotation) {
        TouchUtil.getInstance().setH264Coordinates(rotation);
    }

    @Override
    public boolean setUsbDeviceConnection(UsbDeviceConnection connection) {
        TouchUtil.getInstance().setUsbDeviceConnection(connection);
        return true;
    }

    @Override
    public void setName(String name, String deviceAddress) {
        this.name = name;
    }

    @Override
    public void autoAgree(int delay) {
        LogUtil.d(TAG, "autoAgree: begin");
        ThreadUtils.postOnMainThread(() -> {
            View view = new View(mContext);
            view.setEnabled(true);

            if(name == null){
                LogUtil.e(TAG, "autoAgree: bluetoothName is null");
                return ;
            }

            AutoAgreeConfig config = AutoAgreeManager.getConfig(name);
            if(config == null){
                LogUtil.e(TAG, "autoAgree: config is null");
                return ;
            }

            TouchUtil.getInstance().setScreenSize(config.width, config.height);
            LogUtil.d(TAG, "autoAgree: x = " + config.pointX + " y = " + config.pointY);

            MotionEvent eventDown = MotionEvent.obtain(SystemClock.uptimeMillis(), SystemClock.uptimeMillis(), MotionEvent.ACTION_DOWN, config.pointX, config.pointY, 0);
            TouchUtil.getInstance().MulitTouch(eventDown);
            MotionEvent eventUp = MotionEvent.obtain(SystemClock.uptimeMillis(), SystemClock.uptimeMillis(), MotionEvent.ACTION_UP, config.pointX, config.pointY, 0);
            TouchUtil.getInstance().MulitTouch(eventUp);
            LogUtil.d(TAG,"autoAgree: -------------------------------");
        }, delay);
    }

    @Override
    public void startKeepAliveHeartBeat() {
        if (isRegisterHid.get()) {
            keepAliveManager.startAndroidHeartBeat();
        }
    }

    @Override
    public void stopKeepAliveHeartBeat() {
        keepAliveManager.stopAndroidHeartBeat();
    }

    @Override
    public void restartKeepAliveHeartBeatTimer() {
        keepAliveManager.restartHeartBeatTimer();
    }

    @Override
    public void setScreenTimeout(int timeoutMs) {
        LogUtil.d(TAG, "setScreenTimeout: " + timeoutMs + "ms");

        // 计算心跳间隔
        int heartBeatDelay = com.autoai.avslinkhid.util.HeartBeatCalculator.calculateHeartBeatDelay(timeoutMs);

        // 设置到AOA心跳管理器
        if (keepAliveManager != null) {
            keepAliveManager.setHeartBeatDelay(heartBeatDelay);
        } else {
            LogUtil.e(TAG, "setScreenTimeout: keepAliveManager is null");
        }
    }
}
