package com.autoai.avslinkhid.datatransfer;

import static com.autoai.avslinkhid.hidtouchscreen.HidScreenConstantsKt.HidScreenLogEnable;

import android.annotation.SuppressLint;
import android.content.Context;
import android.hardware.usb.UsbConstants;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbDeviceConnection;
import android.hardware.usb.UsbManager;

import com.autoai.avslinkhid.hidtouchscreen.HidReport;
import com.autoai.avslinkhid.hidtouchscreen.HidScreenConstantsKt;
import com.autoai.common.util.LogUtil;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.Arrays;
import java.util.HashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * AOA device data transfer, include send and receive thread.
 */
@SuppressWarnings("WeakerAccess")
public class TouchManager {
    private static final String TAG = HidScreenConstantsKt.HidScreen + "TouchManager";

    private static final boolean CHECK_AOA_MANAGER = LogUtil.isFullLog();

    @SuppressLint("StaticFieldLeak")
    private static TouchManager mInstance = null;
    private UsbDeviceConnection mUsbDeviceConnection = null;
    private static int iWidth = 0;
    private static int iHeight = 0;
    private static final int HID_ORIENTATION_0 = 0;
    private static final int HID_ORIENTATION_90 = 1;
    private static final int HID_ORIENTATION_180 = 2;
    private static final int HID_ORIENTATION_270 = 3;

    private static final int HID_KEY_HOME = 0;
    private static final int HID_KEY_BACK = 1;
    private static final boolean CHECK_DEVICE_MANAGER = LogUtil.isFullLog();
    private static final int AOA_REGISTER_HID = 54;
    private static final int AOA_UNREGISTER_HID = 55;
    private static final int AOA_SET_HID_REPORT_DESC = 56;
    private static final int AOA_SEND_HID_EVENT = 57;
    private static final int POINT_BUFFER_LEN = 7;
    private static final int HID_POINT_VALUE = 0;
    private static final int HID_KEY_VALUE = 1;

    // USB控制传输超时时间常量 (毫秒)
    private static final int USB_CONTROL_TRANSFER_TIMEOUT_LONG = 5000;   // HID注册/注销等长时间操作
    private static final int USB_CONTROL_TRANSFER_TIMEOUT_SHORT = 1000;  // HID事件发送等短时间操作

    public TouchManager() {
    }

    public static TouchManager getInstance() {
        if (null == mInstance) {
            synchronized (TouchManager.class) {
                if (null == mInstance) {
                    mInstance = new TouchManager();
                }
            }
        }
        return mInstance;
    }

    public void setUsbDeviceConnection(UsbDeviceConnection usbDeviceConnection) {
        mUsbDeviceConnection = usbDeviceConnection;
    }
    public int registerHID(int w, int h) {
        iWidth = w;
        iHeight = h;
        int ret = hidSendRegisterEvent(iWidth, iHeight);
        LogUtil.d(TAG, "register HID end, ret: " + ret);
        return ret;
    }


    public int hidSendRegisterEvent(int w, int h) {
        if (mUsbDeviceConnection == null) {
            return -1;
        }

        byte x_l = (byte) (w & 0xFF);
        byte x_h = (byte) (byte) ((w >> 8) & 0xFF);
        byte y_l = (byte) (h & 0xFF);
        byte y_h = (byte) (byte) ((h >> 8) & 0xFF);
        byte[] reportBuf = {
                0x05, 0x0D, 0x09, 0x04,         // USAGE_PAGE (Digitizers) // USAGE (Touch Screen)
                (byte) 0xA1, 0x01,               // COLLECTION (Application)
                (byte) 0x85, 0x01, 0x09, 0x22,   // REPORT_ID (Touch)   // USAGE (Finger)

                (byte) 0xA1, 0x02,               // COLLECTION (Logical)
                0x09, 0x55,                     // USAGE ( Contact Identifier)
                0x15, 0x00, 0x25, 0x05,         // LOGICAL_MINIMUM (0)  // LOGICAL_MAXIMUM (5) - 支持5指
                0x75, 0x08, (byte) 0x95, 0x01,   // REPORT_SIZE (8)  // REPORT_COUNT (1)
                (byte) 0xB1, 0x05,               // FEATURE (Data,Var,Abs)
                0x09, 0x54,                     // USAGE ( Contact Identifier)
                (byte) 0x81, 0x02,               // INPUT (Data,Var,Abs)

                0x05, 0x0D,                     // USAGE_PAGE (Digitizers)
                0x09, 0x22,                     // USAGE (Finger)
                (byte) 0xA1, 0x02,               // COLLECTION (Logical)
                0x09, 0x42,                     // USAGE (Tip Switch)
                0x15, 0x00,                     // LOGICAL_MINIMUM (0)
                0x25, 0x01,                     // LOGICAL_MAXIMUM (1)
                0x75, 0x01, (byte) 0x81, 0x02, 0x09, 0x32,   //REPORT_SIZE (1)// INPUT (Data,Var,Abs) // USAGE (In Range)
                0x75, 0x01, (byte) 0x81, 0x02, 0x09, 0x51,   //REPORT_SIZE (1) // INPUT (Data,Var,Abs)// USAGE ( Contact Identifier)
                0x25, 0x05,                     //LOGICAL_MAXIMUM (5) - 支持5个触控点ID
                0x75, 0x06, (byte) 0x81, 0x02,   //REPORT_SIZE (6)// INPUT (Data,Var,Abs)

                0x05, 0x01, 0x09, 0x30,         //USAGE_PAGE (Generic Desk)  USAGE (X)
                0x46, x_l, x_h, 0x26, x_l, x_h, // PHYSICAL_MAXIMUM (1024)   // LOGICAL_MAXIMUM
                0x75, 0x10, (byte) 0x81, 0x02,   //REPORT_SIZE (10)// INPUT (Data,Var,Abs)

                0x09, 0x31,                     // USAGE (Y)
                0x46, y_l, y_h, 0x26, y_l, y_h,    // PHYSICAL_MAXIMUM (1028)   // LOGICAL_MAXIMUM
                (byte) 0x81, 0x02,               // INPUT (Data,Var,Abs)
                (byte) 0xC0,                     // END_COLLECTION

                (byte) 0xC0,
                (byte) 0xC0
        };
        int result = mUsbDeviceConnection.controlTransfer(UsbConstants.USB_DIR_OUT | UsbConstants.USB_TYPE_VENDOR,
                AOA_REGISTER_HID,
                HID_POINT_VALUE,
                reportBuf.length,
                null,
                0,
                USB_CONTROL_TRANSFER_TIMEOUT_LONG);
        if (result < 0) {
            LogUtil.e(TAG, "register hid event failed, result=" + result);
            return -2;
        }

        byte[] reportKeyBuf = {0x05, 0x0C, 0x09, 0x01, (byte) 0xA1, 0x01, (byte) 0x85, 0x01, 0x05, 0x0C,
                0x15, 0x00, 0x25, 0x01, 0x75, 0x01, (byte) 0x95, 0x1C, 0x09, 0x40,
                0x09, 0x42, 0x09, 0x43, 0x09, 0x44, 0x09, 0x45, 0x09, (byte) 0x8C,
                0x09, (byte) 0xE2, 0x09, (byte) 0xB0, 0x09, (byte) 0xB5, 0x09, (byte) 0xB6,
                0x09, (byte) 0xB7, 0x09, (byte) 0xCD, 0x09, (byte) 0xEA, 0x09, (byte) 0xE9,
                0x0A, 0x23, 0x02, 0x0A, 0x24, 0x02, (byte) 0x81, 0x02, (byte) 0xC0};
        result = mUsbDeviceConnection.controlTransfer(UsbConstants.USB_DIR_OUT | UsbConstants.USB_TYPE_VENDOR,
                AOA_REGISTER_HID,
                HID_KEY_VALUE,
                reportKeyBuf.length,
                null,
                0,
                USB_CONTROL_TRANSFER_TIMEOUT_LONG);
        if (result < 0) {
            LogUtil.e(TAG, "register hid key event failed, result=" + result);
            return -3;
        }

        result = mUsbDeviceConnection.controlTransfer(UsbConstants.USB_DIR_OUT | UsbConstants.USB_TYPE_VENDOR,
                AOA_SET_HID_REPORT_DESC,
                HID_POINT_VALUE,
                0,
                reportBuf,
                reportBuf.length,
                USB_CONTROL_TRANSFER_TIMEOUT_LONG);
        if (result < 0) {
            LogUtil.e(TAG, "cannot set the report hid desc, result=" + result);
            return -4;
        }

        result = mUsbDeviceConnection.controlTransfer(UsbConstants.USB_DIR_OUT | UsbConstants.USB_TYPE_VENDOR,
                AOA_SET_HID_REPORT_DESC,
                HID_KEY_VALUE,
                0,
                reportKeyBuf,
                reportKeyBuf.length,
                USB_CONTROL_TRANSFER_TIMEOUT_LONG);
        if (result < 0) {
            LogUtil.e(TAG, "cannot set the report hid key desc, result=" + result);
            return -5;
        }

        LogUtil.i(TAG, "register hid success");
        return 0;
    }



    public int unregisterHID(int id) {
        iWidth = 0;
        iHeight = 0;
        int ret = hidSendUnregisterEvent(id);
        LogUtil.d(TAG, "unregister HID end, ret: " + ret);
        return ret;
    }

    public int hidSendUnregisterEvent(int id) {
        if (mUsbDeviceConnection == null) {
            return -1;
        }

        int result = mUsbDeviceConnection.controlTransfer(UsbConstants.USB_DIR_OUT | UsbConstants.USB_TYPE_VENDOR,
                AOA_UNREGISTER_HID,
                HID_POINT_VALUE,
                0,
                null,
                0,
                USB_CONTROL_TRANSFER_TIMEOUT_LONG);
        if (result < 0) {
            LogUtil.e(TAG, "unregister hid event failed, result=" + result);
            return -2;
        }

        result = mUsbDeviceConnection.controlTransfer(UsbConstants.USB_DIR_OUT | UsbConstants.USB_TYPE_VENDOR,
                AOA_UNREGISTER_HID,
                HID_KEY_VALUE,
                0,
                null,
                0,
                USB_CONTROL_TRANSFER_TIMEOUT_LONG);
        if (result < 0) {
            LogUtil.e(TAG, "unregister hid key event failed, result=" + result);
            return -3;
        }

        LogUtil.i(TAG, "unregister hid end");
        return 0;
    }


    public int sendPointEvent(int id, int x, int y, int type, int orientation) {
        if (CHECK_AOA_MANAGER) {
            LogUtil.e(TAG, "zhangyf sendPointEvent id:" + id + ", x:" + x + ", y:" + y + ", iWidth:" + iWidth + ", iHeight:" + iHeight + ", orientation:" + orientation);
        }
        int ret;
        switch (orientation) {
            case HID_ORIENTATION_0:
                ret = hidSendPointEvent(id, x, y, type);
                break;
            case HID_ORIENTATION_90:
                ret = hidSendPointEvent(id, (iHeight - y) * iWidth / iHeight, x * iHeight / iWidth, type);
                break;
            case HID_ORIENTATION_180:
                ret = hidSendPointEvent(id, iWidth - x, iHeight - y, type);
                break;
            case HID_ORIENTATION_270:
                ret = hidSendPointEvent(id, iWidth - (iHeight - y) * iWidth / iHeight, iHeight - x * iHeight / iWidth, type);
                break;
            default:
                ret = -1;
                LogUtil.e(TAG, "unknown orientation");
                break;
        }
        return ret;
    }



    public int hidSendPointEvent(int id, int x, int y, int type) {
        if (mUsbDeviceConnection == null) {
            return -1;
        }
        if (CHECK_DEVICE_MANAGER) {
            LogUtil.e(TAG, "zhangyf hidSendPointEvent id:" + id + ", x:" + x + ", y:" + y + ", type:" + type);
        }
        // fixme: 跟蓝牙 hid 共用一个对象池
        byte[] pointBuf = new byte[POINT_BUFFER_LEN];
        pointBuf[0] = 0x01;
        byte temp = (byte) ((id << 2) & 0xFC);
        if (0 == type) {
            pointBuf[1] = 0x01;
            pointBuf[2] = (byte) (0x03 | temp);
        } else {
            pointBuf[2] = temp; //(byte)(0x0|temp);
        }
        pointBuf[3] = (byte) (x & 0xFF);
        pointBuf[4] = (byte) ((x >> 8) & 0xFF);
        pointBuf[5] = (byte) (y & 0xFF);
        pointBuf[6] = (byte) ((y >> 8) & 0xFF);

        int result = mUsbDeviceConnection.controlTransfer(UsbConstants.USB_DIR_OUT | UsbConstants.USB_TYPE_VENDOR,
                AOA_SEND_HID_EVENT,
                HID_POINT_VALUE,
                0,
                pointBuf,
                pointBuf.length,
                USB_CONTROL_TRANSFER_TIMEOUT_SHORT);
        if (result < 0) {
            LogUtil.e(TAG, "sendPointEvent failed, result=" + result);
            return -1;
        }
        return 0;
    }

    public int hidSendHomeKeyEvent() {
        if (mUsbDeviceConnection == null) {
            return -1;
        }
        byte[] keyBuf = new byte[3];
        keyBuf[0] = 0x01;
        //keyBuf[1] = 0x00;
        keyBuf[2] = 0x40; //home
        int result = mUsbDeviceConnection.controlTransfer(UsbConstants.USB_DIR_OUT | UsbConstants.USB_TYPE_VENDOR,
                AOA_SEND_HID_EVENT,
                HID_KEY_VALUE,
                0,
                keyBuf,
                keyBuf.length,
                USB_CONTROL_TRANSFER_TIMEOUT_SHORT);
        if (result < 0) {
            LogUtil.e(TAG, "send home Key event  failed, result=" + result);
            return -1;
        }
        return hidSendKeyEvent();
    }

    public int hidSendBackKeyEvent() {
        if (mUsbDeviceConnection == null) {
            return -1;
        }
        byte[] keyBuf = new byte[3];
        keyBuf[0] = 0x01;
        //keyBuf[1] = 0x00;
        keyBuf[2] = (byte) 0x80; //back
        int result = mUsbDeviceConnection.controlTransfer(UsbConstants.USB_DIR_OUT | UsbConstants.USB_TYPE_VENDOR,
                AOA_SEND_HID_EVENT,
                HID_KEY_VALUE,
                0,
                keyBuf,
                keyBuf.length,
                USB_CONTROL_TRANSFER_TIMEOUT_SHORT);
        if (result < 0) {
            LogUtil.e(TAG, "send home Key event  failed, result=" + result);
            return -1;
        }
        return hidSendKeyEvent();
    }

    private int hidSendKeyEvent() {
        byte[] keyBuf = new byte[3];
        keyBuf[0] = 0x01;
        //keyBuf[1] = 0x00;
        //keyBuf[2] = 0x00;
        int result = mUsbDeviceConnection.controlTransfer(UsbConstants.USB_DIR_OUT | UsbConstants.USB_TYPE_VENDOR,
                AOA_SEND_HID_EVENT,
                HID_KEY_VALUE,
                0,
                keyBuf,
                keyBuf.length,
                USB_CONTROL_TRANSFER_TIMEOUT_SHORT);
        if (result < 0) {
            LogUtil.e(TAG, "send Key event failed, result=" + result);
            return -1;
        }
        return 0;
    }

    public int sendKeyEvent(int key) {
        int ret;
        switch (key) {
            case HID_KEY_HOME:
                ret = hidSendHomeKeyEvent();
                break;
            case HID_KEY_BACK:
                ret = hidSendBackKeyEvent();
                break;
            default:
                ret = -1;
                LogUtil.e(TAG, "unknown key value");
        }
        return ret;
    }

    /**
     * 发送AOA防锁屏保活按键事件
     * 使用消费者控制协议中的唤醒按键，防止Android手机锁屏
     * 唤醒按键(0x43)适合作为保活按键，不会干扰用户正常操作
     * fixme: Intent.ACTION_SCREEN_OFF 使用这个给车机发送通知呢。
     */
    public int sendKeepAliveKeyEvent() {
        if (mUsbDeviceConnection == null) {
            LogUtil.e(TAG, "sendKeepAliveKeyEvent: USB connection is null");
            return -1;
        }

        try {
            // 发送唤醒按键按下事件
            // 消费者控制协议报告格式：[Report ID, 按键位图字节1, 按键位图字节2, 按键位图字节3, 按键位图字节4]
            // 唤醒按键(0x43)在描述符中的位置是第3个按键，对应第1个字节的第3位
            byte[] keyDownBuf = new byte[4];
            keyDownBuf[0] = 0x01;  // Report ID
            keyDownBuf[1] = 0x04;  // 第3位置1 (0x43 Wake Up按键)
            keyDownBuf[2] = 0x00;  // 其他按键位图
            keyDownBuf[3] = 0x00;  // 其他按键位图

            int result = mUsbDeviceConnection.controlTransfer(UsbConstants.USB_DIR_OUT | UsbConstants.USB_TYPE_VENDOR,
                    AOA_SEND_HID_EVENT,
                    HID_KEY_VALUE,
                    0,
                    keyDownBuf,
                    keyDownBuf.length,
                    USB_CONTROL_TRANSFER_TIMEOUT_SHORT);
            if (result < 0) {
                LogUtil.e(TAG, "sendKeepAliveKeyEvent: send wake up key down event failed, result=" + result);
                return -1;
            }

            // 短暂延迟确保按键被识别
            Thread.sleep(50);

            // 发送唤醒按键释放事件（所有位都为0）
            byte[] keyUpBuf = new byte[4];
            keyUpBuf[0] = 0x01;  // Report ID
            keyUpBuf[1] = 0x00;  // 释放所有按键
            keyUpBuf[2] = 0x00;  // 释放所有按键
            keyUpBuf[3] = 0x00;  // 释放所有按键

            result = mUsbDeviceConnection.controlTransfer(UsbConstants.USB_DIR_OUT | UsbConstants.USB_TYPE_VENDOR,
                    AOA_SEND_HID_EVENT,
                    HID_KEY_VALUE,
                    0,
                    keyUpBuf,
                    keyUpBuf.length,
                    USB_CONTROL_TRANSFER_TIMEOUT_SHORT);
            if (result < 0) {
                LogUtil.e(TAG, "sendKeepAliveKeyEvent: send wake up key up event failed, result=" + result);
                return -1;
            }

            LogUtil.d(TAG, "sendKeepAliveKeyEvent: AOA wake up keep alive key event sent successfully");
            return 0;

        } catch (InterruptedException e) {
            LogUtil.e(TAG, "sendKeepAliveKeyEvent: interrupted", e);
            Thread.currentThread().interrupt();
            return -1;
        } catch (Exception e) {
            LogUtil.e(TAG, "sendKeepAliveKeyEvent: failed", e);
            return -1;
        }
    }

//    public boolean isAOANotReady() {
//        return !isAOADeviceStart;
//    }

//    private boolean checkDevices() {
//        if (mContext == null || mUsbManager == null) {
//            LogUtil.e(TAG, "AOAManager checkDevices fail");
//            return false;
//        }
//
//        HashMap<String, UsbDevice> deviceList = mUsbManager.getDeviceList();
//        LogUtil.d(TAG, "AOAManager device count = " + deviceList.size());
//        for (UsbDevice device : deviceList.values()) {
//            if (device == null) {
//                continue;
//            }
//
//            if (!isAOADevice(device)) {
//                continue;
//            }
//
//            if (mDeviceManager.init(mUsbManager, device)) {
//                LogUtil.d(TAG, "AOAManager init device success");
//                return true;
//            }
//        }
//        return false;
//    }

//    private boolean isAOADevice(UsbDevice device) {
//        if (device == null) {
//            LogUtil.e(TAG, "device is null in isAOADevice");
//            return false;
//        }
//        int vid = device.getVendorId();
//        int pid = device.getProductId();
//        return (vid == VID_ACCESSORY && (pid >> 8 & 0x000000ff) == PID_ACCESSORY && (pid & 0x000f) <= 5);
//    }

//    private class AOAServerThread extends Thread {
//        private boolean isRunning = false;
//        private ServerSocket mServerSocket = null;
//        private Socket mThreadSocket = null;
//        private BufferedInputStream mInputStream = null;
//        private BufferedOutputStream mOutputStream = null;
//
//        public AOAServerThread(int port) {
//            LogUtil.d(TAG, "AOAServerThread Created, port: " + port);
//            setName(AOA_SERVER_THREAD_NAME);
//
//            try {
//                mServerSocket = new ServerSocket();
//                mServerSocket.setReuseAddress(true);
//                mServerSocket.bind(new InetSocketAddress(port));
//                isRunning = true;
//            } catch (Exception e) {
//                LogUtil.e(TAG, "Create Socket Fail");
//            }
//        }
//
//        public void cancel() {
//            if (null != mServerSocket) {
//                try {
//                    mServerSocket.close();
//                } catch (IOException e) {
//                    LogUtil.e(TAG, "close mServerSocket get IOException");
//                }
//                mServerSocket = null;
//            }
//            if (null != mThreadSocket) {
//                try {
//                    mThreadSocket.close();
//                } catch (IOException e) {
//                    LogUtil.e(TAG, "close mThreadSocket get IOException");
//                }
//                mThreadSocket = null;
//            }
//            if (null != mInputStream) {
//                try {
//                    mInputStream.close();
//                } catch (IOException e) {
//                    LogUtil.e(TAG, "close mInputStream get IOException");
//                }
//                mInputStream = null;
//            }
//            if (null != mOutputStream) {
//                try {
//                    mOutputStream.close();
//                } catch (IOException e) {
//                    LogUtil.e(TAG, "close mOutputStream get IOException");
//                }
//                mOutputStream = null;
//            }
//
//            isRunning = false;
//        }
//
//        public void closeSocket() {
//            if (null != mInputStream) {
//                try {
//                    mInputStream.close();
//                } catch (IOException e) {
//                    LogUtil.e(TAG, "close mInputStream get IOException");
//                }
//                mInputStream = null;
//            }
//            if (null != mOutputStream) {
//                try {
//                    mOutputStream.close();
//                } catch (IOException e) {
//                    LogUtil.e(TAG, "close mOutputStream get IOException");
//                }
//                mOutputStream = null;
//            }
//            if (mSocket != null) {
//                try {
//                    mSocket.close();
//                } catch (IOException e) {
//                    LogUtil.e(TAG, "close mSocket get IOException");
//                }
//                mSocket = null;
//            }
//        }
//
//        @Override
//        public void run() {
//            LogUtil.d(TAG, "Begin to listen in AOAServerThread");
//            try {
//                while (isRunning) {
//                    mThreadSocket = mServerSocket.accept();
//                    if (null == mThreadSocket) {
//                        LogUtil.e(TAG, "client connected fail");
//                        continue;
//                    }
//                    if (!isAOADeviceStart) {
//                        LogUtil.e(TAG, "AOA is not start!");
//                        mThreadSocket.close();
//                        mThreadSocket = null;
//                        continue;
//                    }
//                    if ((null == mInputStream) && (null == mOutputStream)) {
//                        LogUtil.d(TAG, "client connected in AOAServerThread");
//                        mSocket = mThreadSocket;
//                        mSocket.setTcpNoDelay(true);
//                        mInputStream = new BufferedInputStream(mSocket.getInputStream());
//                        mOutputStream = new BufferedOutputStream(mSocket.getOutputStream());
//
//                        mAOASendThread = new AOASendThread();
//                        mAOASendThread.start();
//                        mAOARecvThread = new AOARecvThread();
//                        mAOARecvThread.start();
//                    } else {
//                        LogUtil.e(TAG, "thread is already start");
//                        mThreadSocket.close();
//                        mThreadSocket = null;
//                    }
//                    sleep(100);
//                }
//                LogUtil.e(TAG, "AOAServerThread loop stop !!!");
//            } catch (Exception e) {
//                LogUtil.e(TAG, "Get Exception in AOAServerThread");
//            }
//
//            LogUtil.e(TAG, "AOAServerThread exit !!!");
//        }
//    }

//    private class AOASendThread extends Thread {
//        private int errorCount;
//        private boolean isRunning;
//        private final byte[] buffer = new byte[AOA_BUF_SIZE];
//
//        public AOASendThread() {
//            LogUtil.d(TAG, "AOASendThread Created");
//            setName(AOA_SEND_THREAD_NAME);
//            isRunning = true;
//        }
//
//        public void cancel() {
//            isRunning = false;
//        }
//
//        public int readData(byte[] data, int offset, int len) {
//            if (mAOAServerThread.mInputStream == null) {
//                LogUtil.e(TAG, "mInputStream is null");
//                return -1;
//            }
//
//            int ret;
//            try {
//                ret = mAOAServerThread.mInputStream.read(data, offset, len);
//            } catch (IOException e) {
//                LogUtil.e(TAG, "readData IOException");
//                return -2;
//            }
//
//            return ret;
//        }
//
//        public int writeData(byte[] data, int offset, int len) {
//            if (mAOAServerThread.mOutputStream == null) {
//                LogUtil.e(TAG, "mOutputStream is null");
//                return -1;
//            }
//
//            try {
//                mAOAServerThread.mOutputStream.write(data, offset, len);
//                mAOAServerThread.mOutputStream.flush();
//            } catch (IOException e) {
//                LogUtil.e(TAG, "writeData IOException");
//                return -2;
//            }
//
//            return len;
//        }
//
//        @Override
//        public void run() {
//            while (mSocket != null && isRunning) {
//                if (!mSocket.isConnected()) {
//                    LogUtil.e(TAG, "socket is disconnected when read data");
//                    break;
//                }
//
//                Arrays.fill(buffer, (byte) 0x00);
//                int len = readData(buffer, 0, AOA_BUF_SIZE);
//                if (len < 0) {
//                    LogUtil.e(TAG, "read from socket fail, ret: " + len);
//                    break;
//                }
//                if (CHECK_AOA_MANAGER)
//                    LogUtil.v(TAG, "read from socket end, send to usb, len: " + len);
//
//                int ret = mDeviceManager.bulkTransferOut(buffer, len);
//                if (ret < 0) {
//                    LogUtil.e(TAG, "bulkTransferOut fail, ret: " + ret);
//                    if (errorCount > MAX_ERROR_COUNT) {
//                        errorCount = 0;
//                        break;
//                    }
//                    try {
//                        Thread.sleep(10L);
//                    } catch (InterruptedException e) {
//                        e.printStackTrace();
//                    }
//                    errorCount++;
//                }else {
//                    errorCount = 0;
//                }
//            }
//
//            if (mAOAServerThread != null) {
//                mAOAServerThread.closeSocket();
//            }
//            LogUtil.d(TAG, "AOASendThread exit !!!");
//        }
//    }

//    private class AOARecvThread extends Thread {
//        private int errorCount;
//        private boolean isRunning;
//        private final byte[] buffer = new byte[AOA_BUF_SIZE];
//
//        public AOARecvThread() {
//            LogUtil.d(TAG, "AOARecvThread Created");
//            setName(AOA_RECV_THREAD_NAME);
//            isRunning = true;
//        }
//
//        public void cancel() {
//            isRunning = false;
//        }
//
//        @Override
//        public void run() {
//            while (mSocket != null && isRunning) {
//                if (!mSocket.isConnected()) {
//                    LogUtil.e(TAG, "AOARecvThread read data cancled");
//                    break;
//                }
//
//                Arrays.fill(buffer, (byte) 0x00);
//                int len = mDeviceManager.bulkTransferIn(buffer, AOA_BUF_SIZE);
//                if (len < 0) {
//                    LogUtil.e(TAG, "bulkTransferIn fail");
//                    if (errorCount > MAX_ERROR_COUNT) {
//                        errorCount = 0;
//                        break;
//                    }
//                    errorCount++;
//                    try {
//                        Thread.sleep(10L);
//                    } catch (InterruptedException e) {
//                        e.printStackTrace();
//                    }
//                    continue;
//                } else if (len == 0) {
//                    errorCount = 0;
//                    continue;
//                }else {
//                    errorCount = 0;
//                }
//                if (CHECK_AOA_MANAGER)
//                    LogUtil.v(TAG, "read from usb end, send to socket, len: " + len);
//                try {
//                    int ret = mAOASendThread.writeData(buffer, 0, len);
//                    if (ret < 0) {
//                        LogUtil.e(TAG, "write to socket fail, ret: " + ret);
//                        break;
//                    }
//                } catch (NullPointerException e) {
//                    LogUtil.e(TAG, "AOARecvThread null");
//                }
//            }
//
//            if (mAOAServerThread != null) {
//                mAOAServerThread.closeSocket();
//            }
//            LogUtil.d(TAG, "AOARecvThread exit !!!");
//        }
//    }
}
