package com.autoai.avslinkhid.hidtouchscreen;

import static com.autoai.avslinkhid.hidtouchscreen.HidScreenConstantsKt.HidScreenLogEnable;

import android.Manifest;
import android.annotation.SuppressLint;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothHidDevice;
import android.bluetooth.BluetoothHidDeviceAppQosSettings;
import android.bluetooth.BluetoothHidDeviceAppSdpSettings;
import android.bluetooth.BluetoothProfile;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Build;
import android.util.Log;
import android.util.Size;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.autoai.avslinkhid.api.HidApi;
import com.autoai.avslinkhid.api.HidUtil;
import com.autoai.avslinkhid.api.HidWlanAndroid;
import com.autoai.avslinkhid.datatransfer.HidConfig;
import com.autoai.common.util.LogUtil;
import com.autoai.common.util.ThreadUtils;
import com.autoai.welink.sdk.BT.BtConnectionManager;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Executors;

/**
 * 蓝牙hid连接状态管理
 */
@SuppressLint({"NewApi", "MissingPermission"})
public class HidScreenConnectManager {

    /**
     * 日志tag
     */
    private final String TAG = HidScreenConstantsKt.HidScreen + "Screen";
    private static final String TAG_IOSHID = "IOSHID";

    /**
     * 静态单例
     */
    //=============================== start ===============================================
    private static class Holder {
        private static final HidScreenConnectManager HID_SCREEN_CONNECT_MANAGER = new HidScreenConnectManager();
    }

    private HidScreenConnectManager() {
    }

    public static HidScreenConnectManager getInstance() {
        return Holder.HID_SCREEN_CONNECT_MANAGER;
    }
    //=============================== end ===============================================

    private Context mContext = null;

    /**
     * 初始化flag，避免重复
     */
    boolean isInit = false;

    /**
     * 连接反控hid类型 flag
     * true --> hid 触控板
     * false --> hid 蓝牙鼠标
     */
    private boolean isAndroid = false;

    /**
     * 连接状态flag
     */
    private boolean isConnected = false;

    /**
     * 判重flag
     */
    private volatile boolean isRegistered = false;


    /**
     * 蓝牙是否与互联设备匹配
     */
    private boolean isBTPairedConnected = false;

    /**
     * 标记是否有待处理的注册请求（因btHidProfileDevice为null而延迟）
     */
    private volatile boolean pendingRegistration = false;

    /**
     * 只有 iOS 侧才会使用该监听
     */
    private HidCommonInterface.ConnectionStateChangeListener iOSConnectionStateChangeListener;
    private Size hidScreenSize = new Size(1080, 1920);
    private int hidScreenAngle = 0;

    /**
     * 当前hid连接的蓝牙设备
     */
    private BluetoothDevice btHidConnectedDevice;

    /**
     * 蓝牙hid相关-系统在客户端api proxy， 在{@link #mProfileServiceListener } onServiceConnected回调中初始化
     */
    private BluetoothHidDevice btHidProfileProxy;
    private BluetoothHidDeviceAppSdpSettings sdp = null;

    /**
     * 绑定系统蓝牙{@link BluetoothAdapter#getProfileProxy}回调
     * 在回调onServiceConnected中，获取 蓝牙hid - profile
     */
    private final BluetoothProfile.ServiceListener mProfileServiceListener = new BluetoothProfile.ServiceListener() {
        @Override
        public void onServiceConnected(int profile, BluetoothProfile proxy) {
            LogUtil.i(TAG + TAG_IOSHID, "HidScreenConnectManager::onServiceConnected profile=" + profile + ", proxy=" + proxy);
            if (profile == BluetoothProfile.HID_DEVICE) {
                //--> hid 相关的客户端 proxy
                btHidProfileProxy = (BluetoothHidDevice) proxy;
                //--> log调试
                List<BluetoothDevice> connectedDevices = btHidProfileProxy.getConnectedDevices();
                LogUtil.i(TAG + TAG_IOSHID, "HidScreenConnectManager::onServiceConnected connectedDevices=" + connectedDevices + " " + BluetoothProfile.HID_DEVICE);

                // 添加一个标记，记录是否因为btHidProfileDevice为null而未能注册
                if (isRegistered && sdp != null && pendingRegistration) {
                    LogUtil.d(TAG + TAG_IOSHID, "HidScreenConnectManager::onServiceConnected HID profile now connected,");
                    pendingRegistration = false;
                    registerTouchScreenApp(0);
                }
            }
        }

        @Override
        public void onServiceDisconnected(int profile) {
            LogUtil.d(TAG + TAG_IOSHID, "HidScreenConnectManager::onServiceDisconnected profile=" + profile + ", btHidDevice=" + btHidProfileProxy + ", btDevice=" + btHidConnectedDevice);
            if (profile == BluetoothProfile.HID_DEVICE) {
                pendingRegistration = false;
                unregisterTouchScreenApp(true);
            }
        }
    };


    /**
     * 初始化
     *
     * @param context
     */
    public void init(@NonNull Context context) {
        if (!isInit) {
            isInit = true;

            LogUtil.d(TAG + TAG_IOSHID, "HidScreenConnectManager::init Link SDK Version LIND_HID_VERSION=********");
            mContext = context.getApplicationContext();
            //--> 获取蓝牙-HID BluetoothProfile.HID_DEVICE
            BluetoothAdapter.getDefaultAdapter().getProfileProxy(mContext, mProfileServiceListener, BluetoothProfile.HID_DEVICE);
            //-->  初始化系统经典蓝牙状态 Receiver
            BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
            if (bluetoothAdapter != null) {
                String macAddress;
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    if (ContextCompat.checkSelfPermission(mContext, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
                        macAddress = bluetoothAdapter.getAddress();
                        Log.d("Bluetooth MAC Address", macAddress);
                    }
                } else {
                    macAddress = bluetoothAdapter.getAddress();
                    Log.d("Bluetooth MAC Address", macAddress);
                }
            } else {
                Log.d("Bluetooth Error", "Bluetooth not supported on this device.");
            }

        }
    }

    /**
     * 注册iOS设备的蓝牙连接状态
     * @param listener
     */
    public void registeriOSConnectionStateChangeListener(HidCommonInterface.ConnectionStateChangeListener listener) {
        iOSConnectionStateChangeListener = listener;
    }

    /**
     * 蓝牙断开后，反向注册iOS的蓝牙连接，否则 {@link HidScreenConnectManager.mHidDeviceCallback} 连接状态可能不准
     */
    public void unregisteriOSConnectionStateChangeListener() {
        iOSConnectionStateChangeListener = null;
    }


    private boolean isBluetoothDeviceConnected(BluetoothDevice device) {
        if (device == null) {
            return false;
        }

        try {
            Method isConnectedMethod = device.getClass().getMethod("isConnected");
            isConnectedMethod.setAccessible(true);
            return (boolean) isConnectedMethod.invoke(device);
        } catch (Exception e) {
            LogUtil.e(TAG, "Failed to call isConnected() via reflection: " + e.getMessage());
            return false;
        }
    }


    /**
     * 注册hid情况回调
     */
    private final BluetoothHidDevice.Callback mHidDeviceCallback = new BluetoothHidDevice.Callback() {
        private int retryCount = 0;

        /**
         * 通知 app 有变化通知回调 系统  <---> app 的通知
         */
        @Override
        public void onAppStatusChanged(BluetoothDevice pluggedDevice, boolean registered) {
            LogUtil.d(TAG + TAG_IOSHID, "HidScreenConnectManager::onAppStatusChanged <<< pluggedDevice=" + pluggedDevice
                    + ", pluggedDevice.getName()=" + (pluggedDevice != null ? pluggedDevice.getName() : "null")
                    + ", Address=" + (pluggedDevice != null ? pluggedDevice.getAddress() : "null")
                    + ", Type=" + (pluggedDevice != null ? pluggedDevice.getType() : "null")
                    + ", registered=" + registered + ", HID isConnected=" + isHIDDeviceConnected(pluggedDevice)
                    + ", BondState=" + (pluggedDevice != null ? pluggedDevice.getBondState() : "null")
                    + ", ConnectedProfiles=" + (pluggedDevice != null ? isBluetoothDeviceConnected(pluggedDevice) : "null"));


            // pluggedDevice 返回的是最近一次连接HID的设备，而不是当前配对连接的设备。即使手机侧已经关闭蓝牙，但是仍然会回调pluggedDevice 。

            // 一般拿不到设备信息
            List<BluetoothDevice> connectedDevices = btHidProfileProxy.getConnectedDevices();
            LogUtil.d(TAG, "----------------->>>>>>");
            // 遍历打印连接设备信息
            for (BluetoothDevice device : connectedDevices) {
                LogUtil.i(TAG, "Connected hide profile device - Name: " + device.getName()
                        + ", Address: " + device.getAddress()
                        + ", BondState: " + device.getBondState()
                        + ", Type: " + device.getType());
            }

            // 已经连接的设备
            Set<BluetoothDevice> deviceSet = BtConnectionManager.getInstance().getConnectedBluetoothDevices();
            LogUtil.d(TAG, "----------------->>>>>>");
            LogUtil.d(TAG, "getBondedClassicDevice: deviceSet = " + deviceSet);

            for (BluetoothDevice device : deviceSet) {
                LogUtil.i(TAG, "Connected getDeviceSet - Name: " + device.getName()
                        + ", Address: " + device.getAddress()
                        + ", BondState: " + device.getBondState()
                        + ", Type: " + device.getType());
            }

            LogUtil.d(TAG, "----------------->>>>>>");
            // 如果是先连接蓝牙，然后互联注册 SDP， 则会走这里， 需要补充 HID 连接
            // 但是这里，如果切换连接手机，可能会再次连上原来的手机 hid。
            // && isBluetoothDeviceConnected(pluggedDevice) && !isHIDDeviceConnected(pluggedDevice)
           if (registered) {
               LogUtil.d(TAG + TAG_IOSHID, "HidScreenConnectManager::onAppStatusChanged SDP registered successfully, connecting to device");
               retryCount = 0;
               connectDevice(pluggedDevice);
           }
        }

        /**
         * 系统蓝牙  <---> 远端设备 发起连接 回调
         * 触发 by {@link HidScreenConnectManager#connectDevice }
         */
        @Override
        public void onConnectionStateChanged(final BluetoothDevice device, final int state) {
            LogUtil.d(TAG + TAG_IOSHID, "HidScreenConnectManager::onConnectionStateChanged <<< device=" + device + ", device.name=" + (device != null ? device.getName() : null)
                    + ", BtDevice=" + btHidConnectedDevice + ", state=" + state + "(" + getConnectionStateString(state) + ")");
            LogUtil.d(TAG + TAG_IOSHID, "HidScreenConnectManager::onConnectionStateChanged deviceType=" + HidUtil.deviceTypeToString(HidUtil.deviceType) + " isRegisterHID=" + HidUtil.isRegisterHID());
            // 一般拿不到设备信息
            List<BluetoothDevice> connectedDevices = btHidProfileProxy.getConnectedDevices();
            LogUtil.d(TAG, "----------------->>>>>>");
            // 遍历打印连接设备信息
            for (BluetoothDevice deviceItem : connectedDevices) {
                LogUtil.d(TAG, " Connected hid profile device - Name: " + deviceItem.getName()
                        + ", Address: " + deviceItem.getAddress()
                        + ", BondState: " + deviceItem.getBondState()
                        + ", Type: " + deviceItem.getType());
            }

            switch (state) {
                case BluetoothProfile.STATE_DISCONNECTED:
                    isConnected = false;
                    if (iOSConnectionStateChangeListener != null) {
                        iOSConnectionStateChangeListener.onDisConnected();
                    }
                    break;
                case BluetoothProfile.STATE_CONNECTING:
                    if (iOSConnectionStateChangeListener != null) {
                        iOSConnectionStateChangeListener.onConnecting();
                    }
                    break;
                case BluetoothProfile.STATE_CONNECTED:
                    LogUtil.d(TAG + TAG_IOSHID, "HidScreenConnectManager::onConnectionStateChanged STATE_CONNECTED");
                    if (!Objects.equals(btHidConnectedDevice, device)) {
                        LogUtil.e(TAG + TAG_IOSHID, "HidScreenConnectManager::onConnectionStateChanged btDevice != device");
                    }
                    btHidConnectedDevice = device;
                    isConnected = true;
                    retryCount = 0;
                    if (iOSConnectionStateChangeListener != null) {
                        iOSConnectionStateChangeListener.onConnected();
                    }

//                    // 安卓设备连接成功后启动防锁屏心跳
//                    if (isAndroid && HidUtil.isRegisterHID()) {
//                        LogUtil.d(TAG, "Android HID connected, starting keep alive heartbeat");
//                        try {
//                            // 获取当前HID实例并启动心跳
//                            HidApi currentHidApi = HidUtil.getCurrentHidApi();
//                            if (currentHidApi != null) {
//                                currentHidApi.startKeepAliveHeartBeat();
//                            }
//                        } catch (Exception e) {
//                            LogUtil.e(TAG, "Failed to start Android keep alive heartbeat", e);
//                        }
//                    }
                    break;
                case BluetoothProfile.STATE_DISCONNECTING:
                    isConnected = false;
                    if (iOSConnectionStateChangeListener != null) {
                        iOSConnectionStateChangeListener.onDisconnecting();
                    }
                    break;
            }
        }

        private void retryLink() {
            LogUtil.d(TAG, "retryLink: btHidDevice = " + btHidProfileProxy + ", isConnected = " + isConnected + ", retryCount = " + retryCount);
            int MAX_RETRY_COUNT = 3;
            if (btHidProfileProxy != null && !isConnected && retryCount++ < MAX_RETRY_COUNT) {
                if (!connectDevice(btHidConnectedDevice)) {
                    ThreadUtils.postOnMainThread(this::retryLink, 5 * 1000);
                }
            } else {
                LogUtil.d(TAG, "retryLink：retry over");
            }
        }
    };

    /**
     * Sdp：Service Discovery Protocol
     *
     * @param isAndroid
     * @return
     */
    private BluetoothHidDeviceAppSdpSettings getSdp(boolean isAndroid) {
        if (isAndroid) {
            LogUtil.d(TAG, "Android BluetoothHidDeviceAppSdpSettings");
            return new BluetoothHidDeviceAppSdpSettings(
                    "link",
                    "link touch screen digitizer",
                    "link",
                    BluetoothHidDevice.SUBCLASS2_DIGITIZER_TABLET,
                    HidDescriptorsKt.getDESCRIPTOR_FORMAT());//关键：蓝牙键盘 注册info
        }

        LogUtil.d(TAG, "IOS BluetoothHidDeviceAppSdpSettings");
        return new BluetoothHidDeviceAppSdpSettings(
                HidConfig.MOUSE_NAME,
                HidConfig.DESCRIPTION,
                HidConfig.PROVIDER,
                BluetoothHidDevice.SUBCLASS1_MOUSE,
                HidConfig.MOUSE_COMBO);//关键：蓝牙鼠标 注册info
    }

    private void registerTouchScreenApp(int retryCount) {
        LogUtil.d(TAG + TAG_IOSHID, "HidScreenConnectManager::registerTouchScreenApp btHidDevice=" + btHidProfileProxy + ", retryCount=" + retryCount);
        if (btHidProfileProxy == null) {
            // 标记有待处理的注册请求，等待HID profile连接后再注册
            pendingRegistration = true;
            LogUtil.d(TAG + TAG_IOSHID, "HidScreenConnectManager::registerTouchScreenApp btHidProfileDevice is null, registration pending");
            return;
        }
        //--> 真正的将蓝牙hid，通过btHidDevice 注册到远端手机侧
        BluetoothHidDeviceAppQosSettings inQos = new BluetoothHidDeviceAppQosSettings(
                BluetoothHidDeviceAppQosSettings.SERVICE_GUARANTEED, 200, 2, 200,
                10000 /* 10 ms */, 2000 /* 2 ms */);
        BluetoothHidDeviceAppQosSettings outQos = new BluetoothHidDeviceAppQosSettings(
                BluetoothHidDeviceAppQosSettings.SERVICE_GUARANTEED, 900, 9, 900,
                10000 /* 10 ms */, 2000 /* 2 ms */);
        //--> 真正的将蓝牙hid，通过btHidDevice 注册到远端手机侧
        boolean registerApp = btHidProfileProxy.registerApp(sdp, inQos, outQos, Executors.newCachedThreadPool(), mHidDeviceCallback);
        LogUtil.i(TAG + TAG_IOSHID, "HidScreenConnectManager::registerTouchScreenApp registerApp result=" + registerApp);
    }

    private void unregisterTouchScreenApp(boolean closeHidProfileDevice) {
        LogUtil.i(TAG, "unregisterTouchScreenApp: btHidDevice = " + btHidProfileProxy + ", btDevice = " + btHidConnectedDevice);
        // 切换手机蓝牙连接，会自动断开
//        disconnectDevice();

        pendingRegistration = false;
        if (btHidProfileProxy != null) {
            boolean unregisterResult = btHidProfileProxy.unregisterApp();
            LogUtil.d(TAG, "unregisterTouchScreenApp：unregisterResult = " + unregisterResult);
        }
        if (closeHidProfileDevice) {
            btHidProfileProxy = null;
        }
    }

    /**
     * 连接远程手机侧手机 HID（蓝牙）
     * @param pluggedDevice
     */
    public void connectBluetoothHIDDevice(@Nullable BluetoothDevice pluggedDevice) {
        connectDevice(pluggedDevice);
    }

    /**
     * 系统蓝牙  <---> 远端设备 发起连接
     *
     * @param pluggedDevice
     * @return
     */
    private boolean connectDevice(@Nullable BluetoothDevice pluggedDevice) {
        pluggedDevice = getBondedClassicDevice(pluggedDevice);
        LogUtil.w(TAG, "connectDevice：pluggedDevice = " + pluggedDevice);
        //-->没有设备连接
        if (btHidProfileProxy == null || pluggedDevice == null) {
            LogUtil.w(TAG, "connectDevice：no connect");
            // 互联后自动注册了 HID 服务，蓝牙没有连接成功，自动断开 hid 注册。 等待蓝牙再次连接后注册 hid。
//            HidUtil.unregisterHIDAfterConnect();
            return false;
        }

        //--> 未连接发起连接
        boolean result = false;
        if (!isHIDDeviceConnected(pluggedDevice)) {
            LogUtil.d(TAG, "connectDevice: connecting");
            result = btHidProfileProxy.connect(pluggedDevice);
        }

        //-->全局变量持有
        if (result) {
            btHidConnectedDevice = pluggedDevice;
        }
        LogUtil.d(TAG, "connectDevice: btHidConnectedDevice = " + btHidConnectedDevice + ", result = " + result + " status:" + btHidProfileProxy.getConnectionState(pluggedDevice));
        return result;
    }

    private void disconnectDevice() {
        LogUtil.d(TAG, "disconnectDevice：btHidDevice = " + btHidProfileProxy + ", btDevice = " + btHidConnectedDevice);
        if (btHidProfileProxy != null && btHidConnectedDevice != null) {
            boolean disconnectResult = btHidProfileProxy.disconnect(btHidConnectedDevice);
            LogUtil.d(TAG, "disconnectDevice：disconnectResult = " + disconnectResult);
        }
        btHidConnectedDevice = null;
        isConnected = false;
    }

    /**
     * 获取当前连接的设备
     * 连接原则： 1、设备已经连接的优先，2、然后是上一次配对连接的设备，3、最后是所有配对的设备中的一个。
     * @param pluggedDevice
     * @return
     */
    @Nullable
    private BluetoothDevice getBondedClassicDevice(@Nullable BluetoothDevice pluggedDevice) {
        //fixme:确定不是这个api吗，
        // answer: getBondedDevices 是返回的已经配对的设备，HidBtStateReceiver.INSTANCE.getDeviceSet(); 是返回的已经连接的设备。 后者更合适。
//        BluetoothAdapter.getDefaultAdapter().getBondedDevices()
        LogUtil.d(TAG, "getBondedClassicDevice: pluggedDevice = " + pluggedDevice);
        if (btHidProfileProxy == null) {
            return null;
        }
        Set<BluetoothDevice> deviceSet = BtConnectionManager.getInstance().getConnectedBluetoothDevices();
        LogUtil.d(TAG, "getBondedClassicDevice: deviceSet = " + deviceSet);

        // 通过蓝牙地址判断 pluggedDevice 是否在已连接设备集合中
        if (pluggedDevice != null) {
            for (BluetoothDevice device : deviceSet) {
                if (pluggedDevice.getAddress().equals(device.getAddress())) {
                    LogUtil.d(TAG, "getBondedClassicDevice: found matching device by address");
                    return pluggedDevice;
                }
            }
        }

        for (BluetoothDevice device : deviceSet) {
            // 如果系统没有给上一次配对连接设备，则直接返回默认第一个配对连接设备。
            // todo: 这里最好加上互联设备的校验，譬如已经无线互联了 iPhone，则优先返回 iPhone 设备蓝牙。
            // 如果 pluggeddevice 已经不在已经配对的蓝牙设备中，则断开连接。
            boolean result = btHidProfileProxy.disconnect(pluggedDevice);
            LogUtil.d(TAG, "getBondedClassicDevice：result = " + result + ", pluggedDevice = " + pluggedDevice);
            pluggedDevice = device;
            return pluggedDevice;
        }

        return null;
        // 默认不自动连接配对设备和 pluggeddevice，否则会导致用户刚断开蓝牙，开启互联之后，又自动连上了。
//        if (pluggedDevice == null) {
//            // 如果 pluggedDevice 和已经连接设备都是空，则从配对设备中挑选
//            Set<BluetoothDevice>  bondedDevices = BluetoothAdapter.getDefaultAdapter().getBondedDevices();
//            for (BluetoothDevice device : bondedDevices) {
//                pluggedDevice = device;
//                LogUtil.d(TAG, "getBondedClassicDevice：getBondedDevices = " +  pluggedDevice );
//                break;
//            }
//        }
//        return pluggedDevice;
    }

    @NonNull
    public Size getHidScreenSize() {
        return hidScreenSize;
    }

    public int getHidScreenAngle() {
        return hidScreenAngle;
    }

    public void setHidScreenConfig(int w, int h, int angle) {
        LogUtil.d(TAG, "setHidScreenConfig：w = " + w + ", h = " + h + ", angle = " + angle);
        hidScreenSize = new Size(w, h);
        hidScreenAngle = angle;
    }

    public boolean isConnected() {
        return isConnected;
    }

    /**
     * 检查指定的蓝牙设备是否已连接
     * @param device 要检查的蓝牙设备
     * @return 如果设备已连接返回true，否则返回false
     */
    public boolean isHIDDeviceConnected(BluetoothDevice device) {
        if (device == null || btHidProfileProxy == null) {
            return false;
        }
        return btHidProfileProxy.getConnectionState(device) == BluetoothProfile.STATE_CONNECTED;
    }

    /**
     * 监听获取 HID 设备并注册触摸屏外接设备
     *
     * @param isAndroidDevice {@link Context}
     */
    public void startGetHidProfileAndRegisterScreenDigitizer(boolean isAndroidDevice) {
        isAndroid = isAndroidDevice;
        LogUtil.i(TAG + TAG_IOSHID, "HidScreenConnectManager::startGetHidProfileAndRegisterScreenDigitizer isRegistered=" + isRegistered + ", isAndroid=" + isAndroid);
        if (isRegistered) {
            return;
        }
        isRegistered = true;
        sdp = getSdp(isAndroid);
        registerTouchScreenApp(0);
    }

    public void closeHidProfileProxyIfNeed() {
        LogUtil.i(TAG, "closeHidProfileProxyIfNeed: btHidDevice = " + btHidProfileProxy + ", btDevice = " + btHidConnectedDevice + ", isRegistered = " + isRegistered);
        if (!isRegistered) {
            return;
        }
        unregisterTouchScreenApp(false);
        isRegistered = false;
    }

    /**
     * 将hid事件发送出去的最直接的方法
     * @param reportId
     * @param reportData
     * @return
     */
    public boolean sendReport(int reportId, byte[] reportData) {
        if (btHidProfileProxy == null || btHidConnectedDevice == null) {
            return false;
        }
        return btHidProfileProxy.sendReport(btHidConnectedDevice, reportId, reportData);
    }

    public void hidSendHomeKeyEvent() {
        if (HidScreenLogEnable) {
            LogUtil.i(TAG, "hidSendHomeKeyEvent: isConnected = " + isConnected());
        }
        if (!isConnected()) {
            return;
        }

        byte[] reportData = new byte[8];
        reportData[0] = (byte) 0x08;
        reportData[2] = (byte) 0x2A;
        boolean ret = btHidProfileProxy.sendReport(btHidConnectedDevice, 0X02, reportData);
        if (HidScreenLogEnable) {
            LogUtil.i(TAG, "hidSendHomeKeyEvent: sendReport = " + reportData[2] + "," + reportData[3] + ", ret = " + ret + " Thread: " + Thread.currentThread().toString());
        }
        hidSendHomeKeyBackEvent();
    }

    public void hidSendHomeKeyEvent(byte data) {
        if (HidScreenLogEnable) {
            LogUtil.i(TAG, "hidSendHomeKeyEvent: isConnected = " + isConnected());
        }
        if (!isConnected()) {
            return;
        }

        byte[] reportData = new byte[8];
        reportData[0] = (byte) 0x08;
        reportData[2] = data;
        String hexString = String.format("%02x,%02x,%02x", reportData[0], reportData[2], reportData[3]);
        boolean ret = btHidProfileProxy.sendReport(btHidConnectedDevice, 0X02, reportData);
        if (HidScreenLogEnable) {
            LogUtil.i(TAG, "hidSendHomeKeyEvent: sendReport = " + hexString + ", ret = " + ret);
        }
        hidSendHomeKeyBackEvent();
    }

    private void hidSendHomeKeyBackEvent() {
        byte[] reportData = new byte[8];
        reportData[0] = (byte) 0X00;
        reportData[2] = (byte) 0x00;
        btHidProfileProxy.sendReport(btHidConnectedDevice, 0X02, reportData);
    }

    /**
     * 获取当前连接的蓝牙设备名称, 没有连接的情况下，返回"" 字符串
     */
    public String getBluetoothDeviceName() {
        if (!isConnected) {
            return "";
        }

        if (btHidConnectedDevice == null) {
            return "";
        }

        return btHidConnectedDevice.getName();
    }

    /**
     * 当前连接注册的是否是安卓设备
     * @return true: Android
     */
    public Boolean isConnectedAndroidDevice() {
        return isAndroid;
    }

    /**
     * 更新蓝牙与互联设备的匹配状态
     * @param paired 是否匹配
     */
    public void updateBTPairedConnected(boolean paired) {
        LogUtil.d(TAG, "updateBTPairedConnected: " + paired);
        isBTPairedConnected = paired;
    }

    /**
     * 获取蓝牙是否与互联设备匹配
     * @return 是否匹配
     */
    public boolean isBTPairedConnected() {
        return isBTPairedConnected;
    }

    /**
     * 将连接状态值转换为可读的字符串
     * @param state 连接状态值
     * @return 状态对应的字符串描述
     */
    private String getConnectionStateString(int state) {
        switch (state) {
            case BluetoothProfile.STATE_DISCONNECTED:
                return "STATE_DISCONNECTED";
            case BluetoothProfile.STATE_CONNECTING:
                return "STATE_CONNECTING";
            case BluetoothProfile.STATE_CONNECTED:
                return "STATE_CONNECTED";
            case BluetoothProfile.STATE_DISCONNECTING:
                return "STATE_DISCONNECTING";
            default:
                return "UNKNOWN_STATE(" + state + ")";
        }
    }
}
