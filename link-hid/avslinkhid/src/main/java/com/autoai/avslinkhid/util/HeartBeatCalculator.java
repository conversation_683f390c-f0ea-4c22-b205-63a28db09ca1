package com.autoai.avslinkhid.util;

import com.autoai.common.util.LogUtil;

/**
 * 心跳间隔计算工具类
 * 根据手机锁屏超时时间计算合适的防锁屏心跳间隔
 * 
 * <AUTHOR>
 */
public class HeartBeatCalculator {
    private static final String TAG = "HeartBeatCalculator";
    
    private static final int MIN_HEART_BEAT_DELAY = 5000;   // 最小5秒
    private static final int MAX_HEART_BEAT_DELAY = 60000;  // 最大60秒
    private static final int DEFAULT_HEART_BEAT_DELAY = 12000; // 默认12秒
    private static final double SAFETY_FACTOR = 0.8; // 安全系数80%
    
    /**
     * 根据手机锁屏超时时间计算心跳间隔
     * @param screenTimeoutMs 手机锁屏超时时间（毫秒）
     * @return 计算后的心跳间隔（毫秒）
     */
    public static int calculateHeartBeatDelay(int screenTimeoutMs) {
        // 异常值处理
        if (screenTimeoutMs <= 0) {
            LogUtil.w(TAG, "Invalid screenTimeout: " + screenTimeoutMs + ", using default");
            return DEFAULT_HEART_BEAT_DELAY;
        }
        
        // 计算心跳间隔（保留80%安全余量）
        int calculatedDelay = (int) (screenTimeoutMs * SAFETY_FACTOR);
        
        // 边界值限制
        if (calculatedDelay < MIN_HEART_BEAT_DELAY) {
            LogUtil.w(TAG, "Calculated delay too small: " + calculatedDelay + ", using minimum: " + MIN_HEART_BEAT_DELAY);
            return MIN_HEART_BEAT_DELAY;
        }
        
        if (calculatedDelay > MAX_HEART_BEAT_DELAY) {
            LogUtil.w(TAG, "Calculated delay too large: " + calculatedDelay + ", using maximum: " + MAX_HEART_BEAT_DELAY);
            return MAX_HEART_BEAT_DELAY;
        }
        
        LogUtil.d(TAG, "Calculated heartbeat delay: " + calculatedDelay + "ms for screenTimeout: " + screenTimeoutMs + "ms");
        return calculatedDelay;
    }
    
    /**
     * 获取默认心跳间隔
     * @return 默认心跳间隔（毫秒）
     */
    public static int getDefaultHeartBeatDelay() {
        return DEFAULT_HEART_BEAT_DELAY;
    }
    
    /**
     * 验证心跳间隔是否在有效范围内
     * @param delayMs 心跳间隔（毫秒）
     * @return true如果在有效范围内
     */
    public static boolean isValidHeartBeatDelay(int delayMs) {
        return delayMs >= MIN_HEART_BEAT_DELAY && delayMs <= MAX_HEART_BEAT_DELAY;
    }
}