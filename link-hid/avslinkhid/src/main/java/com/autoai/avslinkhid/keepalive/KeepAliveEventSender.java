package com.autoai.avslinkhid.keepalive;

/**
 * Android防锁屏保活事件发送策略接口
 * 不同的连接方式（蓝牙HID、AOA）实现不同的保活事件发送策略
 */
public interface KeepAliveEventSender {
    
    /**
     * 发送防锁屏保活事件
     * @return true: 发送成功, false: 发送失败
     */
    boolean sendKeepAliveEvent();
    
    /**
     * 检查连接状态，确定是否可以发送保活事件
     * @return true: 连接正常可以发送, false: 连接异常不能发送
     */
    boolean isConnectionReady();
    
    /**
     * 获取发送策略的描述信息（用于日志）
     * @return 策略描述
     */
    String getStrategyDescription();
}
