package com.autoai.avslinkhid.calibration;

/**
 * 发送校准点的规则， 包括发送的坐标和延迟时间，并且移动之后是否需要点击。
 */
public class CalibrationPoint {
    private final int x;      // 发送的坐标 x
    private final int y;      // 发送的坐标 y
    private final int delayAfterMove;  // 移动后延迟时间（ms）
    private final int delayAfterClick; // 点击后延迟时间（ms）

    private final int delayAfterClickDown; // 点击按下后延迟抬起
    // 本次只需要点击，不需要移动
    private final boolean isNoNeedMovement;
    // 复位
    private boolean isResetMouse;
    
    // 点击行为的枚举类型
    public enum ClickAction {
        NONE(0),          // 不点击
        CLICK_FULL(1),    // 完整点击(按下+抬起)
        CLICK_DOWN_ONLY(2), // 只按下
        CLICK_UP_ONLY(3);   // 只抬起
        
        private final int value;
        
        ClickAction(int value) {
            this.value = value;
        }
        
        public int getValue() {
            return value;
        }
        
        public static ClickAction fromValue(int value) {
            for (ClickAction action : values()) {
                if (action.value == value) {
                    return action;
                }
            }
            return CLICK_FULL; // 默认返回完整点击
        }
    }
    
    private ClickAction clickAction = ClickAction.CLICK_FULL; // 默认完整点击
    
    // 构造函数
    public CalibrationPoint(int x, int y, int delayAfterMove, int delayAfterClick,  boolean isNoNeedMovement) {
        this(x, y, delayAfterMove, delayAfterClick, 0, isNoNeedMovement, ClickAction.CLICK_FULL );
    }
    
    // 构造函数
    public CalibrationPoint(int x, int y, int delayAfterMove, int delayAfterClick, 
                           int delayAfterClickDown, boolean isNoNeedMovement) {
        this(x, y, delayAfterMove, delayAfterClick, delayAfterClickDown, isNoNeedMovement, ClickAction.CLICK_FULL);
    }
    
    // 构造函数，使用ClickAction替代shouldClickAfterMove
    public CalibrationPoint(int x, int y, int delayAfterMove, int delayAfterClick, 
                           int delayAfterClickDown, boolean isNoNeedMovement, ClickAction clickAction) {
        this.x = x;
        this.y = y;
        this.delayAfterMove = delayAfterMove;
        this.delayAfterClick = delayAfterClick;
        this.delayAfterClickDown = delayAfterClickDown;
        this.isNoNeedMovement = isNoNeedMovement;
        this.isResetMouse = false;
        this.clickAction = clickAction;
    }

    public int getX() {
        return x;
    }

    public int getY() {
        return y;
    }

    /**
     * 获取移动后延迟时间
     */
    public int getDelayAfterMove() {
        return delayAfterMove;
    }

    /**
     * 获取点击后延迟时间
     */
    public int getDelayAfterClick() {
        return delayAfterClick;
    }

    /**
     * 判断是否需要在移动后点击
     * @return 如果点击行为不是NONE，则返回true
     */
    public boolean isShouldClickAfterMove() {
        return clickAction != ClickAction.NONE;
    }

    /**
     * 获取按下后多久抬起
     * @return
     */
    public int getDelayAfterClickDown() {
        return delayAfterClickDown;
    }

    public boolean isResetMouse() {
        return isResetMouse;
    }

    public void setResetMouse(boolean isResetMouse) {
        this.isResetMouse = isResetMouse;
    }

    // 获取点击行为
    public ClickAction getClickAction() {
        return clickAction;
    }
    
    // 判断是否只按下
    public boolean isClickDownOnly() {
        return clickAction == ClickAction.CLICK_DOWN_ONLY;
    }
    
    // 判断是否只抬起
    public boolean isClickUpOnly() {
        return clickAction == ClickAction.CLICK_UP_ONLY;
    }

    // toString
    @Override
    public String toString() {
        return "CalibrationPoint{" +
                "x=" + x +
                ", y=" + y +
                ", delayAfterMove=" + delayAfterMove +
                ", delayAfterClick=" + delayAfterClick +
                ", delayAfterClickDown=" + delayAfterClickDown +
                ", clickAction=" + clickAction +
                ", isNoNeedMovement=" + isNoNeedMovement +
                '}';
    }
}
