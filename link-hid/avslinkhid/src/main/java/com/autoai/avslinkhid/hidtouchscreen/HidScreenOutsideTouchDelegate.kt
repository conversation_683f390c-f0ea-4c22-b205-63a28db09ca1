package com.autoai.avslinkhid.hidtouchscreen

import android.graphics.Rect
import android.view.MotionEvent
import android.view.TouchDelegate
import android.view.View
import com.autoai.common.util.LogUtil

/**
 * 触摸投屏区域外的部分，处理边缘滑动返回，上滑回桌面
 */
class HidScreenOutsideTouchDelegate(val mBounds: Rect, val original: Rect, val mDelegateView: View, val callback: (()-><PERSON><PERSON>an)?) : TouchDelegate(mBounds, mDelegateView) {
    companion object{
        private const val TAG = HidScreen + "TouchDelegate"
        @JvmStatic
        fun setTouchDelegateWhenReady(delegateView: View, parentView: View, insets: Rect, callback: (()->Boolean)?) {
            LogUtil.i(TAG, "setTouchDelegateWhenReady: post: invoke")
            delegateView.post {
                val bound = Rect()
                delegateView.getHitRect(bound)
                val delegate = Rect(bound)
                bound.apply {
                    left += insets.left
                    top += insets.top
                    right -= insets.right
                    bottom -= insets.bottom
                }
                LogUtil.i(TAG, "setTouchDelegateWhenReady: touchDelegate: delegate = $delegate, bound = $bound")
                parentView.touchDelegate = HidScreenOutsideTouchDelegate(bound, delegate, delegateView, callback);
            }
        }
    }


    private var mDelegateTargeted = false
    private var isInDelegate = false
    private var isInOriginalView = false
    override fun onTouchEvent(event: MotionEvent): Boolean {
        val enable = callback?.invoke()
        if(HidScreenLogEnable){
            LogUtil.i(TAG, "onTouchEvent: enable = $enable")
        }
        if(enable != true){
            return false
        }
        val x = event.x.toInt()
        val y = event.y.toInt()
        var sendToDelegate = false
        var handled = false
        when (event.actionMasked) {
            MotionEvent.ACTION_DOWN -> {
                mDelegateTargeted = mBounds.contains(x, y)
                LogUtil.i(TAG, "onTouchEvent: ACTION_DOWN: x = $x, y = $y")
                sendToDelegate = mDelegateTargeted
                isInDelegate = false
                isInOriginalView = false
            }

            MotionEvent.ACTION_POINTER_DOWN,
            MotionEvent.ACTION_POINTER_UP,
            MotionEvent.ACTION_UP,
            MotionEvent.ACTION_MOVE -> {
                sendToDelegate = mDelegateTargeted
            }

            MotionEvent.ACTION_CANCEL -> {
                sendToDelegate = mDelegateTargeted
                mDelegateTargeted = false
            }
        }
        if (sendToDelegate) {
            if(original.contains(x, y)){
                //派发一次边缘 ACTION_DOWN 事件
                isInOriginalView = true
                if(isInDelegate){
                    isInDelegate = false
                    val downEvent = MotionEvent.obtain(event)
                    downEvent.action = MotionEvent.ACTION_DOWN
                    downEvent.setLocation((x - original.left).toFloat(), (y - original.top).toFloat())
                    mDelegateView.dispatchTouchEvent(downEvent)
                    LogUtil.w(TAG, "onTouchEvent: sendToDelegate down to original view")
                }
                LogUtil.i(TAG, "onTouchEvent: sendToDelegate move to original view")
                event.setLocation((x - original.left).toFloat(), (y - original.top).toFloat())
                mDelegateView.dispatchTouchEvent(event)
            } else {
                isInDelegate = true
                if(isInOriginalView){
                    isInOriginalView = false
                    //从范围内划出边界，发送取消事件
                    val upEvent = MotionEvent.obtain(event)
                    upEvent.action = MotionEvent.ACTION_UP
                    mDelegateView.dispatchTouchEvent(upEvent)
                    //不再响应后续的划入事件了
                    mDelegateTargeted = false
                }
                LogUtil.i(TAG, "onTouchEvent: sendToDelegate event in delegate")
            }
            handled = true
        }
        LogUtil.i(TAG, "onTouchEvent: handled = $handled")

        return handled
    }
}