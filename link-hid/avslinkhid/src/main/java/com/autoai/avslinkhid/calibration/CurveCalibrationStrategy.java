package com.autoai.avslinkhid.calibration;


import com.autoai.avslinkhid.calibration.CalibrationPoint;
import com.autoai.avslinkhid.calibration.CalibrationStrategy;
import com.autoai.avslinkhid.hidtouchscreen.HidScreenConstantsKt;
import com.autoai.avslinkhid.model.MyPoint;
import com.autoai.avslinkhid.util.SpUtil;
import com.autoai.common.util.LogUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import kotlin.Triple;

/**
 * 曲线校准策略, 用于低版本系统
 */
public final class CurveCalibrationStrategy extends BaseCalibrationStrategy {

    // 构造函数
    public CurveCalibrationStrategy(SpUtil sp){
        this.sp = sp;
        clearCalibrationParams();
    }

    /**
     * 与 LinearCalibrationStrategy 保持一致，
     * @return
     */
    @Override
    public List<CalibrationPoint> getCalibrationPoints() {
        return super.getCalibrationPoints();
    }

    @Override
    public int parseCalibrationPoints(List<MyPoint> points) {
        clearCalibrationParams();
        checkMyPoint.addAll(points);
        return checkPointV2() ? 1 : 0;
    }

    /**
     * 通过手机侧期望移动的位移像素，查询计算 hid 发送数据
     * @param expectMovePixel 期望移动的像素值
     * @param bTouchMoving 移动过程中，不需要太精确，减少一些微调移动
     * @return Pair<发送的hid数据, 实际移动的像素值, 是否移动到位>
     */
    @Override
    public Triple<Integer, Float, Boolean> queryHidSendData(float expectMovePixel, boolean bTouchMoving) {
        return super.queryHidSendData(expectMovePixel, bTouchMoving);
    }

    @Override
    public int getMaxStepsHidUnit() {
        return MAX_STEP;
    }

    @Override
    public void clearCalibrationParams() {
        checkMyPoint.clear();
        indexToYArrayForHid = null;
    }

    @Override
    public boolean isCalibrationReady() {
        return indexToYArrayForHid != null && indexToYArrayForHid.length > 0;
    }

    /**
     * 单次移动手机侧最大移动像素
     * 返回数组中的最大值，如果数组为空则返回默认值
     */
    @Override
    public float getMaxMovePixel() {
        if (indexToYArrayForHid != null && indexToYArrayForHid.length > 0) {
            float maxValue = 0;
            for (float value : indexToYArrayForHid) {
                if (value > maxValue) {
                    maxValue = value;
                }
            }
            return maxValue;
        }
        // 如果数组为空，返回默认值
        return 157.14f; // 默认最大步长值
    }


    /**
     * 二次回归系数
     */
    private static class QuadraticCoefficients {
        final double a, b, c;
        final boolean isValid;

        QuadraticCoefficients(double a, double b, double c, boolean isValid) {
            this.a = a;
            this.b = b;
            this.c = c;
            this.isValid = isValid;
        }
    }


    /**
     * 计算二次回归系数
     * @param dataPointsX 数据点X
     * @param dataPointsY 数据点Y
     * @return 二次回归系数
     */
    private QuadraticCoefficients calculateCoefficients(List<Double> dataPointsX, List<Double> dataPointsY) {
        int n = dataPointsX.size();
        if (n < 3) {
            printLog("calculateCoefficients: 二次回归数据点不足 (" + n + ")。至少需要3个点。");
            return new QuadraticCoefficients(0, 0, 0, false);
        }

        double sumX = 0, sumY = 0, sumX2 = 0, sumX3 = 0, sumX4 = 0, sumXY = 0, sumX2Y = 0;

        for (int i = 0; i < n; i++) {
            double x = dataPointsX.get(i);
            double y = dataPointsY.get(i);
            double x2 = x * x;
            sumX += x;
            sumY += y;
            sumX2 += x2;
            sumX3 += x2 * x;
            sumX4 += x2 * x2;
            sumXY += x * y;
            sumX2Y += x2 * y;
        }

        double detM = sumX4 * (sumX2 * n - sumX * sumX)
                    - sumX3 * (sumX3 * n - sumX2 * sumX)
                    + sumX2 * (sumX3 * sumX - sumX2 * sumX2);

        double epsilon = 1e-9;
        if (Math.abs(detM) < epsilon) {
            printLog("calculateCoefficients: 矩阵行列式接近于零 (detM = " + detM + ")。无法进行二次回归。");
            return new QuadraticCoefficients(0, 0, 0, false);
        }

        double num_a = sumX2Y * (sumX2 * n - sumX * sumX)
                     - sumXY  * (sumX3 * n - sumX2 * sumX)
                     + sumY   * (sumX3 * sumX - sumX2 * sumX2);

        double num_b = sumX4  * (sumXY * n - sumY * sumX)
                     - sumX3  * (sumX2Y * n - sumY * sumX2)
                     + sumX2  * (sumX2Y * sumX - sumXY * sumX2);

        double num_c = sumX4  * (sumX2 * sumY - sumX * sumXY)
                     - sumX3  * (sumX3 * sumY - sumX2 * sumXY)
                     + sumX2Y * (sumX3 * sumX - sumX2 * sumX2);

        double a = num_a / detM;
        double b = num_b / detM;
        double c = num_c / detM;

        printLog("calculateCoefficients: 二次回归系数: a=" + a + ", b=" + b + ", c=" + c);
        return new QuadraticCoefficients(a, b, c, true);
    }


    /**
     * 根据输入的 x，y，计算二次项方程参数
     * @param yList
     * @param yStepsList
     * @param regressionBaseXValues
     * @return 二次项方程参数
     */
    private QuadraticCoefficients prepareRegressionDataAndCoefficients(List<Float> yList, List<Integer> yStepsList, List<Integer> regressionBaseXValues) {
        List<Double> dataPointsX = new ArrayList<>();
        List<Double> dataPointsY = new ArrayList<>();

        printLog("准备用于回归的数据点 for base X values: " + regressionBaseXValues);
        for (int baseX : regressionBaseXValues) {
            int indexInYSteps = -1;
            for (int i = 0; i < yStepsList.size(); i++) {
                if (yStepsList.get(i).equals(baseX)) {
                    indexInYSteps = i;
                    break;
                }
            }

            if (indexInYSteps != -1) {
                dataPointsX.add((double) baseX);
                dataPointsY.add((double) yList.get(indexInYSteps));
                printLog("  Adding data point: (" + baseX + ", " + yList.get(indexInYSteps) + ")");
            } else {
                printLog("  Warning: " + baseX + " not found in yStepsList. skip.");
            }
        }
        return calculateCoefficients(dataPointsX, dataPointsY);
    }

    /**
     * 新的反控校准逻辑
     * 需要检查如下要素：
     * 1、点的数目，理论不会丢失。
     * @return
     */
    public boolean checkPointV2() {
        // 准备工作，将 checkMyPoint 中的点（x，y），倒序。
        List<Float> yList = prepareYList();

        // 检查所有点都是递增的
        // 控制台打印输出 yList
        printLog("yList = " + yList.size() + "  " + yList);

        if (yList.isEmpty()) {
            printLog("checkPointV2: yList null or empty.");
            return false;
        }

        if (yStepsList.size() != yList.size()) {
            printLog("checkPointV2: yStepsList size  (" + yStepsList.size() + ") and yList size: (" + yList.size() + ") not match.");
            return false;
        }

        // 分两段计算曲线参数
        int midPoint = yStepsList.size() / 2;
        List<Integer> baseXVals_le50 = yStepsList.subList(0, midPoint);
        List<Integer> baseXVals_gt50 = yStepsList.subList(midPoint, yStepsList.size());

        QuadraticCoefficients coeffs_le50 = prepareRegressionDataAndCoefficients(yList, yStepsList, baseXVals_le50);
        QuadraticCoefficients coeffs_gt50 = prepareRegressionDataAndCoefficients(yList, yStepsList, baseXVals_gt50);

        // 计算从0到最大索引值的所有 Y 值
        // 先检查高值区域(60-100)的斜率是否接近线性
        LinearEstimationResult linearEstimation = calculateLinearEstimationForHighValues(yList, yStepsList);
//        // 测试验证代码。 查看精准度如何
//        for (int idx = 0; idx < 100; idx += 5) {
//            QuadraticCoefficients currentCoeffs = (idx <= 50) ? coeffs_le50 : coeffs_gt50;
//            calculateYByQuadraticFunction(yList, yStepsList, idx, currentCoeffs);
//        }

        // 计算从0到最大索引值的所有 Y 值
        float[] indexToYArray = calculateAllYValues(yList, yStepsList, coeffs_le50, coeffs_gt50, linearEstimation);

        // 检查数组中的值是否严格递增（确保校准曲线单调性）
        List<Float> indexToYList = new ArrayList<>();
        for (float value : indexToYArray) {
            indexToYList.add(value);
        }
        if (!isStrictlyIncreasing(indexToYList)) {
            printLog("checkPointV2: indexToYArrayForHid not increasing");
            indexToYArrayForHid = null;
            return false;
        }

        indexToYArrayForHid = indexToYArray;


        printLog("indexToYArray " + Arrays.toString(indexToYArray));

        return true;
    }


    /**
     * 获取最大步长
     * @return 最大步长
     */
    private int getMaxStepV2() {
        if (indexToYArrayForHid == null) {
            return 0;
        }

        return (int) indexToYArrayForHid[indexToYArrayForHid.length - 1];
    }

    /**
     * 计算所有索引值对应的 Y 值，并创建一个用于反查的映射
     * @param yList 手机侧屏幕移动量列表
     * @param yStepsList 预定义的X轴步长列表
     * @param coeffs_le50 小于等于50的二次回归系数
     * @param coeffs_gt50 大于50的二次回归系数
     * @param linearParam 对高值区域使用线性估算
     * @return 索引到 Y 值的数组，索引对应数组下标，值为对应的Y值
     */
    private float[] calculateAllYValues(List<Float> yList, List<Integer> yStepsList,
                                           QuadraticCoefficients coeffs_le50,
                                           QuadraticCoefficients coeffs_gt50,
                                        LinearEstimationResult linearParam) {
        // 确保 yStepsList 非空
        if (yStepsList == null || yStepsList.isEmpty()) {
            printLog("calculateAllYValues: yStepsList 为空或 null");
            return new float[0];
        }

        // 找出 yStepsList 中的最大值
        int maxIndex = Collections.max(yStepsList);
        printLog("calculateAllYValues: 计算从 0 到 " + maxIndex + " 的所有索引值");

        // 创建一个数组来存储所有索引对应的Y值
        float[] indexToYArray = new float[maxIndex + 1];

        // 计算从 0 到 maxIndex 的所有 Y 值
        for (int idx = 0; idx <= maxIndex; idx++) {
            float yValue;
            // 对于高值区域(>60)且启用线性估算时，使用线性函数
            if (linearParam != null && linearParam.useLinearEstimation && idx >= 60) {
                yValue = linearParam.slope * idx + linearParam.intercept;
                // printLog("索引 " + idx + " 使用线性估算，Y 值: " + yValue);
            } else {
                // 否则使用二次函数
                QuadraticCoefficients currentCoeffs = (idx <= 50) ? coeffs_le50 : coeffs_gt50;
                yValue = calculateYByQuadraticFunction(yList, yStepsList, idx, currentCoeffs);
            }
            indexToYArray[idx] = yValue;
           // printLog("索引 " + idx + " 对应的 Y 值: " + yValue);
        }

        return indexToYArray;
    }

    /**
     * 新的反控校准逻辑, 根据输入索引点 index，计算对应手机侧屏幕移动量的 y 值。
     * @param yList 手机侧屏幕移动量列表
     * @param yStepsList 预定义的X轴步长列表
     * @param inputIndex 索引点
     * @param coeffs 预计算的二次回归系数
     * @return 手机侧屏幕移动量
     */
    private float calculateYByQuadraticFunction(List<Float> yList, List<Integer> yStepsList, int inputIndex, QuadraticCoefficients coeffs){

        // 异常处理：如果索引超出范围 (yStepsList is guaranteed non-empty by checkPointV2)
        if (inputIndex > yStepsList.get(yStepsList.size() - 1)) {
            printLog("calculateYByQuadraticFunction: Invalid inputIndex: " + inputIndex + ". 必须在 " + yStepsList.get(0) + " 和 " + yStepsList.get(yStepsList.size() - 1) + " 之间。");
            return 0;
        }

        float yValue;

        if (coeffs != null && coeffs.isValid) {
            //printLog("calculateYByQuadraticFunction: For inputIndex " + inputIndex + ", using calculated coeffs: a=" + coeffs.a + ", b=" + coeffs.b + ", c=" + coeffs.c);
            double predictedY = coeffs.a * inputIndex * inputIndex + coeffs.b * inputIndex + coeffs.c;
            yValue = (float) predictedY;
            //printLog("calculateYByQuadraticFunction: 根据 inputIndex " + inputIndex + " 预测的 Y 值: " + yValue);
        } else {
            printLog("calculateYByQuadraticFunction: Coefficients invalid or null for inputIndex " + inputIndex + ". Using fallback.");
            int originalValueIndex = yStepsList.indexOf(inputIndex);
            if (originalValueIndex != -1) {
                yValue = yList.get(originalValueIndex);
                printLog("calculateYByQuadraticFunction fallback: Found direct value for " + inputIndex + ": " + yValue);
            } else {
                printLog("calculateYByQuadraticFunction fallback: inputIndex " + inputIndex + " not in yStepsList. Returning 0.");
                yValue = 0;
            }
        }

//        // 查找并打印inputIndex对应的yList值， 用于精度测试
//        int indexInYSteps = yStepsList.indexOf(inputIndex);
//        if (indexInYSteps != -1) {
//            float originalValue = yList.get(indexInYSteps);
//            float diff = yValue - originalValue;
//            printLog("index: " + inputIndex + " 原始值: " + originalValue + " 计算值: " + yValue + " 差值: " + diff);
//        } else {
//            printLog("index: " + inputIndex + " 计算值: " + yValue + " (原始值在yStepsList中未找到进行比较)");
//        }
        return yValue;
    }

    /**
     * 线性估算结果类，包含是否使用线性估算、斜率和截距
     */
    private static class LinearEstimationResult {
        final boolean useLinearEstimation;
        final float slope;
        final float intercept;

        LinearEstimationResult(boolean useLinearEstimation, float slope, float intercept) {
            this.useLinearEstimation = useLinearEstimation;
            this.slope = slope;
            this.intercept = intercept;
        }
    }

    /**
     * 计算高值区域(60-100)的线性估算参数
     * @param yList 手机侧屏幕移动量列表
     * @param yStepsList 预定义的X轴步长列表
     * @return 线性估算结果，包含是否使用线性估算、斜率和截距
     */
    private LinearEstimationResult calculateLinearEstimationForHighValues(List<Float> yList, List<Integer> yStepsList) {
        try {
            // 默认不使用线性估算
            boolean useLinearForHighValues = false;
            float linearSlope = 0;
            float linearIntercept = 0;

            // 获取60,80,100这几个点，计算斜率
            List<MyPoint> highValuePoints = new ArrayList<>();
            List<Integer> highXValues = Arrays.asList(60, 80, 100);
            
            // 检查yStepsList是否包含所需的高值点
            for (int x : highXValues) {
                int idx = yStepsList.indexOf(x);
                if (idx != -1 && idx < yList.size()) {
                    highValuePoints.add(new MyPoint(x, yList.get(idx)));
                } else {
                    printLog("calculateLinearEstimationForHighValues: 未找到X值 " + x + " 或索引超出范围");
                }
            }

            // 计算60-80和80-100之间的斜率
            float slope1 = 0, slope2 = 0;

            if (highValuePoints.size() >= 3) {
                // 确保点的X坐标不相同，避免除以零
                float denominator1 = highValuePoints.get(1).getX() - highValuePoints.get(0).getX();
                float denominator2 = highValuePoints.get(2).getX() - highValuePoints.get(1).getX();

                if (Math.abs(denominator1) > 1e-6 && Math.abs(denominator2) > 1e-6) {
                    slope1 = (highValuePoints.get(1).getY() - highValuePoints.get(0).getY()) / denominator1;
                    slope2 = (highValuePoints.get(2).getY() - highValuePoints.get(1).getY()) / denominator2;

                    // 如果斜率误差小于0.1，使用线性估算
                    if (Math.abs(slope1 - slope2) < 0.1f) {
                        useLinearForHighValues = true;

                        // 使用整体斜率
                        float denominator = highValuePoints.get(2).getX() - highValuePoints.get(0).getX();
                        if (Math.abs(denominator) > 1e-6) {
                            linearSlope = (highValuePoints.get(2).getY() - highValuePoints.get(0).getY()) / denominator;
                            // 计算截距 b = y - kx
                            linearIntercept = highValuePoints.get(0).getY() - linearSlope * highValuePoints.get(0).getX();
                            printLog("calculateLinearEstimationForHighValues slope:" + linearSlope + "，linearIntercept=" + linearIntercept);
                        } else {
                            useLinearForHighValues = false;
                            printLog("calculateLinearEstimationForHighValues: ");
                        }
                    } else {
                        printLog("calculateLinearEstimationForHighValues: 斜率误差过大 |" + slope1 + " - " + slope2 + "| = "
                                + Math.abs(slope1 - slope2) + " >= 0.1，不使用线性估算");
                    }
                } else {
                    printLog("calculateLinearEstimationForHighValues: 计算斜率时分母接近零，不使用线性估算");
                }
            } else {
                printLog("calculateLinearEstimationForHighValues: same point =  " + highValuePoints.size());
            }

            return new LinearEstimationResult(useLinearForHighValues, linearSlope, linearIntercept);
        } catch (Exception e) {
            printLog("calculateLinearEstimationForHighValues exception: " + e.getMessage());
            // 发生异常时返回默认值，不使用线性估算
            return new LinearEstimationResult(false, 0, 0);
        }
    }

}
