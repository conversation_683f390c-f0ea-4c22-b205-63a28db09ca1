package com.autoai.avslinkhid.util;

import android.os.Handler;
import android.os.Looper;

import com.autoai.common.util.LogUtil;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
@SuppressWarnings("unused")
public class ThreadUtils {

    private static volatile Thread sMainThread;
    private static volatile Handler sMainThreadHandler;
    private static volatile ExecutorService backgroundThreadExecutor;

    private static volatile ExecutorService SingleThreadExecutor;

    private static volatile Set<String> runningTasks = Collections.synchronizedSet(new HashSet<>());

    /**
     * Returns true if the current thread is the UI thread.
     */
    public static boolean isMainThread() {
        if (sMainThread == null) {
            sMainThread = Looper.getMainLooper().getThread();
        }
        return Thread.currentThread() == sMainThread;
    }

    /**
     * Returns a shared UI thread handler.
     */
    public static Handler getUiThreadHandler() {
        if (sMainThreadHandler == null) {
            sMainThreadHandler = new Handler(Looper.getMainLooper());
        }

        return sMainThreadHandler;
    }

    /**
     * Checks that the current thread is the UI thread. Otherwise throws an exception.
     */
    public static void ensureMainThread() {
        if (!isMainThread()) {
            throw new RuntimeException("Must be called on the UI thread");
        }
    }

    /**
     * Posts runnable in background using shared background thread pool.
     */
    public static void postOnBackgroundThread(Runnable runnable) {
        if (backgroundThreadExecutor == null) {
            backgroundThreadExecutor = new ThreadPoolExecutor(8, 20, 1000, TimeUnit.MILLISECONDS, new SynchronousQueue<>(), new ThreadFactory() {
                private int threadCount;

                @Override
                public Thread newThread(Runnable r) {
                    return new Thread(r, "BackgroundThread" + threadCount++);
                }
            }, new ThreadPoolExecutor.DiscardPolicy());
        }
        backgroundThreadExecutor.execute(runnable);
    }

    /**
     * Posts the runnable on the main thread.
     */
    public static void postOnMainThread(Runnable runnable) {
        getUiThreadHandler().post(runnable);
    }

    /**
     * Posts the runnable on the main thread daily.
     */
    public static void postOnMainThread(Runnable runnable, long delayMillis) {
        getUiThreadHandler().postDelayed(runnable, delayMillis);
    }

    private static void createBackgroundThread() {
        if(SingleThreadExecutor == null){
            SingleThreadExecutor = Executors.newSingleThreadExecutor();
        }
    }
    public static void postOnBackgroundSingleThread(Runnable runnable){
        createBackgroundThread();
        SingleThreadExecutor.execute(runnable);
    }

    /**
     * 在单线程中执行任务，如果相同标识的任务已在执行则直接返回
     * @param taskId 任务唯一标识
     * @param runnable 要执行的任务
     * @return 是否成功提交任务执行
     */
    public static boolean postOnBackgroundSingleThread(String taskId, Runnable runnable) {
        if (taskId == null || runnable == null) {
            return false;
        }
        
        // 如果任务已经在执行，直接返回
        if (!runningTasks.add(taskId)) {
            LogUtil.d("ThreadUtils", " taskId: " + taskId + "  running.");
            return false;
        }

        createBackgroundThread();
        
        SingleThreadExecutor.execute(() -> {
            try {
                runnable.run();
            } finally {
                // 任务执行完成后，从集合中移除
                runningTasks.remove(taskId);
            }
        });
        return true;
    }
}
