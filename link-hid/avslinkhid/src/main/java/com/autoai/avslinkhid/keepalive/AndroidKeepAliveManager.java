package com.autoai.avslinkhid.keepalive;

import android.os.Handler;
import android.os.Looper;

import com.autoai.avslinkhid.hidtouchscreen.HidScreenConstantsKt;
import com.autoai.common.util.LogUtil;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Android防锁屏心跳管理器
 * 统一管理蓝牙HID和AOA连接的防锁屏心跳机制
 */
public class AndroidKeepAliveManager {
    private static final String TAG_ANDROID_KEEP_ALIVE = HidScreenConstantsKt.HidScreen + "AndroidKeepAlive";
    
    // 安卓防锁屏心跳间隔：12秒。(安卓设置最短 15秒）
    private static final int DEFAULT_ANDROID_HEART_BEAT_DELAY = 12000;
    
    // 动态心跳间隔（可运行时修改）
    private volatile int androidHeartBeatDelay = DEFAULT_ANDROID_HEART_BEAT_DELAY;
    
    // 心跳相关
    private final Handler heartBeatHandler = new Handler(Looper.getMainLooper());
    private Runnable androidHeartBeatRunnable;
    private final AtomicBoolean isHeartBeatRunning = new AtomicBoolean(false);
    
    // 保活事件发送策略
    private final KeepAliveEventSender keepAliveEventSender;
    private final String managerTag;
    
    /**
     * 构造函数
     * @param keepAliveEventSender 保活事件发送策略
     * @param managerTag 管理器标识（用于日志区分）
     */
    public AndroidKeepAliveManager(KeepAliveEventSender keepAliveEventSender, String managerTag) {
        this.keepAliveEventSender = keepAliveEventSender;
        this.managerTag = managerTag;
        initAndroidHeartBeat();
    }
    
    /**
     * 初始化安卓防锁屏心跳机制
     */
    private void initAndroidHeartBeat() {
        androidHeartBeatRunnable = () -> {
            // 检查心跳是否应该继续运行
            if (!isHeartBeatRunning.get()) {
                LogUtil.d(TAG_ANDROID_KEEP_ALIVE, managerTag + "::heartbeat stopped, exit runnable");
                return;
            }

            // 检查连接状态
            if (!keepAliveEventSender.isConnectionReady()) {
                LogUtil.w(TAG_ANDROID_KEEP_ALIVE, managerTag + "::connection not ready, stop heartbeat");
                stopAndroidHeartBeat();
                return;
            }

            LogUtil.d(TAG_ANDROID_KEEP_ALIVE, managerTag + "::Android keep alive heartbeat triggered: " + System.currentTimeMillis() + " ThreadId:" + Thread.currentThread().getId());

            try {
                sendAndroidKeepAliveEvent();
            } catch (Exception e) {
                LogUtil.e(TAG_ANDROID_KEEP_ALIVE, managerTag + "::Error sending keep alive event", e);
            }

            // 继续下一次心跳（如果没有被用户操作重新启动的话）
            if (isHeartBeatRunning.get()) {
                heartBeatHandler.postDelayed(androidHeartBeatRunnable, androidHeartBeatDelay);
            }
        };
    }
    
    /**
     * 启动安卓防锁屏心跳机制
     */
    public void startAndroidHeartBeat() {
        // 检查是否已经在运行
        if (isHeartBeatRunning.get()) {
            LogUtil.d(TAG_ANDROID_KEEP_ALIVE, managerTag + "::Android heartbeat already running");
            return;
        }

        if (!keepAliveEventSender.isConnectionReady()) {
            LogUtil.w(TAG_ANDROID_KEEP_ALIVE, managerTag + "::connection not ready, delay heartbeat start");
            // 延迟重试启动心跳
            heartBeatHandler.postDelayed(() -> startAndroidHeartBeat(), 5000);
            return;
        }

        LogUtil.d(TAG_ANDROID_KEEP_ALIVE, managerTag + "::Start Android keep alive heartbeat with " + androidHeartBeatDelay + "ms interval");
        isHeartBeatRunning.set(true);
        heartBeatHandler.postDelayed(androidHeartBeatRunnable, androidHeartBeatDelay);
    }
    
    /**
     * 停止安卓防锁屏心跳机制
     */
    public void stopAndroidHeartBeat() {
        if (isHeartBeatRunning.get()) {
            LogUtil.d(TAG_ANDROID_KEEP_ALIVE, managerTag + "::Stop Android keep alive heartbeat");
            isHeartBeatRunning.set(false);
            heartBeatHandler.removeCallbacks(androidHeartBeatRunnable);
        }
    }
    
    /**
     * 发送安卓防锁屏事件
     * 此方法只在用户停止操作指定时间后被调用
     */
    private void sendAndroidKeepAliveEvent() {
        if (!keepAliveEventSender.isConnectionReady()) {
            LogUtil.w(TAG_ANDROID_KEEP_ALIVE, managerTag + "::connection not ready, skip keep alive event");
            return;
        }

        try {
            boolean success = keepAliveEventSender.sendKeepAliveEvent();
            if (success) {
                LogUtil.d(TAG_ANDROID_KEEP_ALIVE, managerTag + "::Android keep alive event sent successfully (user idle for " + androidHeartBeatDelay + "ms) via " + keepAliveEventSender.getStrategyDescription());
            } else {
                LogUtil.w(TAG_ANDROID_KEEP_ALIVE, managerTag + "::Failed to send Android keep alive event via " + keepAliveEventSender.getStrategyDescription());
            }
        } catch (Exception e) {
            LogUtil.e(TAG_ANDROID_KEEP_ALIVE, managerTag + "::Failed to send Android keep alive event", e);
        }
    }
    
    /**
     * 手动启动心跳机制（供外部调用）
     * 当连接建立后可以调用此方法确保心跳启动
     */
    public void ensureHeartBeatStarted() {
        LogUtil.d(TAG_ANDROID_KEEP_ALIVE, managerTag + "::Ensure Android heartbeat started");
        startAndroidHeartBeat();
    }
    
    /**
     * 重新启动心跳定时器
     * 当检测到用户操作时调用，取消当前定时器并重新开始计时
     * 这样确保只在用户停止操作指定时间后才发送保活事件
     */
    public void restartHeartBeatTimer() {
        if (!isHeartBeatRunning.get()) {
            return; // 心跳未运行，无需重启
        }

        LogUtil.v(TAG_ANDROID_KEEP_ALIVE, managerTag + "::User operation detected, restarting heartbeat timer");

        // 取消当前的心跳定时器
        heartBeatHandler.removeCallbacks(androidHeartBeatRunnable);

        // 重新启动心跳定时器
        heartBeatHandler.postDelayed(androidHeartBeatRunnable, androidHeartBeatDelay);
    }
    
    /**
     * 检查心跳是否正在运行
     * @return true: 心跳正在运行, false: 心跳已停止
     */
    public boolean isHeartBeatRunning() {
        return isHeartBeatRunning.get();
    }
    
    /**
     * 设置心跳间隔
     * @param delayMs 心跳间隔（毫秒）
     */
    public void setHeartBeatDelay(int delayMs) {
        // 参数验证
        if (delayMs <= 0) {
            LogUtil.w(TAG_ANDROID_KEEP_ALIVE, managerTag + "::Invalid delay: " + delayMs + ", ignored");
            return;
        }
        
        int oldDelay = this.androidHeartBeatDelay;
        this.androidHeartBeatDelay = delayMs;
        
        LogUtil.d(TAG_ANDROID_KEEP_ALIVE, managerTag + "::HeartBeat delay changed from " + oldDelay + "ms to " + delayMs + "ms");
        
        // 如果心跳正在运行，重启以应用新间隔
        if (isHeartBeatRunning.get()) {
            LogUtil.d(TAG_ANDROID_KEEP_ALIVE, managerTag + "::Restarting heartbeat with new delay");
            restartHeartBeatTimer();
        }
    }
    
    /**
     * 获取当前心跳间隔
     * @return 当前心跳间隔（毫秒）
     */
    public int getHeartBeatDelay() {
        return androidHeartBeatDelay;
    }
}
