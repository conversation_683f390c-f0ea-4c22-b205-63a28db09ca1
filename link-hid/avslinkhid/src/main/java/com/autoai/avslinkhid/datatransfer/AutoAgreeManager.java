package com.autoai.avslinkhid.datatransfer;

import com.autoai.avslinkhid.hidtouchscreen.HidScreenConstantsKt;
import com.autoai.avslinkhid.model.AutoAgreeConfig;
import com.autoai.common.util.LogUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class AutoAgreeManager {
    private static final String TAG =  HidScreenConstantsKt.HidScreen + "AutoAgreeManager";
    private static Map<String, AutoAgreeConfig> configMap = new HashMap<>();

    static {
        initializeConfigs();
    }

    private static void initializeConfigs() {
        configMap.put("MI 9_cepheus_Xiaomi", new AutoAgreeConfig("MI 9_cepheus_Xiaomi", 378, 816, 281, 762));
        configMap.put("LE2120_OnePlus9Pro_OnePlus", new AutoAgreeConfig("LE2120_OnePlus9Pro_OnePlus", 366, 814, 299, 501));
        configMap.put("2201122C_zeus_Xiaomi",new AutoAgreeConfig("2201122C_zeus_Xiaomi", 366, 814, 260, 764));
    }

    public static Map<String, AutoAgreeConfig> getConfigMap() {
        return configMap;
    }

    public static AutoAgreeConfig getConfig(String key) {
        LogUtil.d(TAG, "configMap = " + configMap);
        return configMap.get(key);
    }
}
