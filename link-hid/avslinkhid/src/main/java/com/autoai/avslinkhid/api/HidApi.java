package com.autoai.avslinkhid.api;

import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.hardware.usb.UsbDeviceConnection;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.NonNull;

import com.autoai.welink.param.HidTouch;

/**
 * <AUTHOR>
 * Hid 通用抽象接口
 */
public interface HidApi {
    /**
     * 与蓝牙设备（手机）发起实际的连接
     * @param context
     * @return
     */
    boolean registerHID(@NonNull Context context);

    /**
     * 注销
     * @return
     */
    boolean unregisterHID();

    /**
     * 触摸事件
     * @param view
     * @param motionEvent
     * @return
     */
    boolean onTouch(View view, MotionEvent motionEvent);
    void backHome();
    void setH264Coordinates(HidTouch rotation);
    default void setName(String name, String deviceAddress){}
    default void autoAgree(int delay){}
    default boolean setUsbDeviceConnection(UsbDeviceConnection connection){
        return true;
    }

    /**
     * 启动Android防锁屏心跳机制（仅Android设备需要实现）
     */
    default void startKeepAliveHeartBeat(){}

    /**
     * 停止Android防锁屏心跳机制（仅Android设备需要实现）
     */
    default void stopKeepAliveHeartBeat(){}

    /**
     * 重新启动心跳定时器（用户操作时调用，仅Android设备需要实现）
     */
    default void restartKeepAliveHeartBeatTimer(){}

    /**
     * 是否已经注册 HID 服务
     * @return true: 已经注册
     */
    boolean isRegisterHID();


    /**
     * HID 连接某一个设备( 用户手动从车机侧连接手机蓝牙， 需要代码手动调用连接 HID)
     * @param device
     */
    default void connectBluetoothDevice(BluetoothDevice device){

    }

    /**
     * 设置手机锁屏超时时间，用于动态调整防锁屏心跳间隔
     * @param timeoutMs 手机锁屏超时时间（毫秒）
     */
    default void setScreenTimeout(int timeoutMs) {
        // 默认空实现，子类可以根据需要重写
    }
}
