package com.autoai.avslinkhid.api;

import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.os.SystemClock;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.NonNull;

import com.autoai.avslinkhid.datatransfer.AutoAgreeManager;
import com.autoai.avslinkhid.hidtouchscreen.HidScreenConnectManager;
import com.autoai.avslinkhid.hidtouchscreen.HidScreenConstantsKt;
import com.autoai.avslinkhid.hidtouchscreen.HidScreenTouchManager;
import com.autoai.avslinkhid.keepalive.AndroidKeepAliveManager;
import com.autoai.avslinkhid.keepalive.BluetoothKeepAliveEventSender;
import com.autoai.avslinkhid.model.AutoAgreeConfig;
import com.autoai.common.util.LogUtil;
import com.autoai.common.util.ThreadUtils;
import com.autoai.welink.param.HidTouch;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 */
public class HidWlanAndroid implements HidApi{
    private static final String TAG =  HidScreenConstantsKt.HidScreen + "AndroidWifiHidImpl";

    private static HidWlanAndroid mInstance;
    private AtomicBoolean isRegisterHid = new AtomicBoolean(false);
    private Context mContext;

    /**
     * 连接的手机侧设备名称
     */
    private String connectedDeviceName;
    /**
     * 已经互联设备的蓝牙名称，用于检查当前连接的蓝牙是否匹配
     */
    private String connectedDeviceBluetoothName;

    // 安卓防锁屏心跳管理器
    private AndroidKeepAliveManager keepAliveManager;

    private HidWlanAndroid(){
        // 初始化安卓防锁屏心跳管理器
        keepAliveManager = new AndroidKeepAliveManager(
            new BluetoothKeepAliveEventSender(),
            "HidWlanAndroid"
        );
    }

    public static HidApi getInstance(){
        if(null == mInstance){
            synchronized (HidWlanAndroid.class) {
                if(null == mInstance){
                    mInstance = new HidWlanAndroid();
                }
            }
        }
        return mInstance;
    }



    @Override
    public boolean registerHID(@NonNull Context context) {
        mContext = context;
        LogUtil.d(TAG,"Android registerHID:" + isRegisterHid.get()  +  "stack: " + HidUtil.getStackTraces(false));
        if(!isRegisterHid.get()) {
            isRegisterHid.set(true);
            HidScreenConnectManager.getInstance().startGetHidProfileAndRegisterScreenDigitizer( true);

            // 启动安卓防锁屏心跳
            keepAliveManager.startAndroidHeartBeat();
        }
        return true;
    }

    @Override
    public boolean unregisterHID() {
        LogUtil.d(TAG,"Android unregisterHID:" + isRegisterHid.get()  +  "stack: " + HidUtil.getStackTraces(false));
        if(isRegisterHid.get()){
            isRegisterHid.set(false);
            HidScreenConnectManager.getInstance().closeHidProfileProxyIfNeed();

            // 停止安卓防锁屏心跳
            keepAliveManager.stopAndroidHeartBeat();
        }
        return true;
    }

    @Override
    public boolean isRegisterHID() {
        return isRegisterHid.get();
    }

    @Override
    public void connectBluetoothDevice(BluetoothDevice device) {
        HidScreenConnectManager.getInstance().connectBluetoothHIDDevice(device);
    }

    @Override
    public boolean onTouch(View view, MotionEvent motionEvent) {
        // 用户操作时重新启动心跳定时器
        keepAliveManager.restartHeartBeatTimer();
        return HidScreenTouchManager.getInstance().onTouch(view, motionEvent);
    }

    @Override
    public void backHome() {
        //HidScreenConnectManager.getInstance().hidSendHomeKeyEvent((byte)0X2A);
    }

    @Override
    public void setH264Coordinates(HidTouch rotation) {
        HidScreenConnectManager.getInstance().setHidScreenConfig(rotation.phoneSize.w, rotation.phoneSize.h, rotation.angle);
    }

    @Override
    public void setName(String name, String deviceAddress) {
        connectedDeviceName = name;
        connectedDeviceBluetoothName = deviceAddress;
        HidScreenTouchManager.getInstance().setName(name, deviceAddress);

        // 设备连接后启动心跳机制
        if (isRegisterHid.get()) {
            keepAliveManager.startAndroidHeartBeat();
        }
    }

    @Override
    public void autoAgree(int delay){
        LogUtil.d(TAG, "autoAgree: begin");
        ThreadUtils.postOnMainThread(() -> {
            View view = new View(mContext);
            view.setEnabled(true);

            if(connectedDeviceName == null){
                LogUtil.e(TAG, "autoAgree: connectedDeviceName is null");
                return ;
            }

            AutoAgreeConfig config = AutoAgreeManager.getConfig(connectedDeviceName);
            if(config == null){
                LogUtil.e(TAG, "autoAgree: config is null");
                return ;
            }

            LogUtil.d(TAG, "autoAgree: x = " + config.pointX + " y = " + config.pointY);

            MotionEvent eventDown = MotionEvent.obtain(SystemClock.uptimeMillis(), SystemClock.uptimeMillis(), MotionEvent.ACTION_DOWN, config.pointX, config.pointY, 0);
            HidScreenTouchManager.getInstance().onTouch(view, eventDown);
            MotionEvent eventUp = MotionEvent.obtain(SystemClock.uptimeMillis(), SystemClock.uptimeMillis(), MotionEvent.ACTION_UP, config.pointX, config.pointY, 0);
            HidScreenTouchManager.getInstance().onTouch(view, eventUp);
            LogUtil.d(TAG,"autoAgree: -------------------------------");
        },delay);
    }

    @Override
    public void startKeepAliveHeartBeat() {
//        if (isRegisterHid.get()) {
//            keepAliveManager.startAndroidHeartBeat();
//        }
    }

    @Override
    public void stopKeepAliveHeartBeat() {
        keepAliveManager.stopAndroidHeartBeat();
    }

    @Override
    public void restartKeepAliveHeartBeatTimer() {
        keepAliveManager.restartHeartBeatTimer();
    }

    /**
     * 手动启动心跳机制（供外部调用）
     * 当HID连接建立后可以调用此方法确保心跳启动
     */
    public void ensureHeartBeatStarted() {
        if (isRegisterHid.get()) {
            LogUtil.d(TAG, "Ensure Android heartbeat started");
            keepAliveManager.ensureHeartBeatStarted();
        }
    }

    /**
     * 重新启动心跳定时器（兼容旧接口）
     * 当检测到用户操作时调用，取消当前定时器并重新开始计时
     * 这样确保只在用户停止操作指定时间后才发送保活事件
     */
    public void restartHeartBeatTimer() {
        keepAliveManager.restartHeartBeatTimer();
    }

    @Override
    public void setScreenTimeout(int timeoutMs) {
        LogUtil.d(TAG, "setScreenTimeout: " + timeoutMs + "ms");

        // 计算心跳间隔
        int heartBeatDelay = com.autoai.avslinkhid.util.HeartBeatCalculator.calculateHeartBeatDelay(timeoutMs);

        // 设置到心跳管理器
        if (keepAliveManager != null) {
            keepAliveManager.setHeartBeatDelay(heartBeatDelay);
        } else {
            LogUtil.e(TAG, "setScreenTimeout: keepAliveManager is null");
        }
    }
}
