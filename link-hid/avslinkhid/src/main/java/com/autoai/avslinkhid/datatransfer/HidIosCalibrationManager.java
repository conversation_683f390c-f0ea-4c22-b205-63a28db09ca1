package com.autoai.avslinkhid.datatransfer;

import static com.autoai.avslinkhid.model.HidVerificationType.*;
import static com.autoai.welink.param.HidConstants.TAG_IOS_CALIB;
import static com.autoai.avslinkhid.model.HidVerificationType.HID_VERIFY_TYPE_NONE;


import android.content.Context;
import android.util.Pair;

import com.autoai.avslinkhid.api.CalibrationCallBack;
import com.autoai.avslinkhid.api.HidIosCalibrationUtil;
import com.autoai.avslinkhid.calibration.CalibrationStrategy;
import com.autoai.avslinkhid.calibration.CurveCalibrationStrategy;
import com.autoai.avslinkhid.calibration.LinearCalibrationStrategy;
import com.autoai.avslinkhid.calibration.RegularCalibrationStrategy;
import com.autoai.avslinkhid.hidtouchscreen.HidScreenConstantsKt;
import com.autoai.avslinkhid.model.MyPoint;
import com.autoai.avslinkhid.util.HidIosUtil;
import com.autoai.avslinkhid.util.SpUtil;
import com.autoai.common.util.LogUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * she review tips：
 * 1.这个类主要负责维护将点复归到屏幕右下角相关逻辑
 */
public class HidIosCalibrationManager {
    private static final String TAG = HidScreenConstantsKt.HidScreen + "HidIosCalibration";
    private static final int DEFAULT_RESET_STEP = 10;

    private static boolean checkPointFlag = false;
    private static boolean touchBlocked = false;
    private static AtomicInteger hidVerificationState = new AtomicInteger(HID_VERIFY_TYPE_NONE);
    private static CalibrationCallBack back;
    private static SpUtil sp = null;
    /**
     * 反控可用flag
     */
    private static boolean controlAvailable = false;

    /**
     * 反控是否被打断，true：表示打断，不再发送hid坐标
     */
    private static boolean breakDown = false;
    /**
     * 记录IOS 反控校准，回传实际生效的点 举例：通过hid mouse发送给手机（0，40），手机实际（0，60）
     */
    private static List<MyPoint> checkMyPoint = new ArrayList<MyPoint>(10);

    /**
     * 校准策略，用于处理不同的校准算法
     */
    private static CalibrationStrategy calibrationStrategy = null;

    public static void setContext(Context context){
        sp = SpUtil.getInstance(context);
        // 默认使用线性校准策略, 因为线性和三段式校准的校准点是完整的。
        calibrationStrategy = new LinearCalibrationStrategy(sp);
    }

    /**
     * 获取当前的校准策略
     */
    public static CalibrationStrategy getCalibrationStrategy() {
        return calibrationStrategy;
    }


    /**
     * 反控是否被打断，true：表示打断，不再发送hid坐标
     */
    public static void setBreakDown(boolean breakDown){
        LogUtil.d(TAG + TAG_IOS_CALIB, "HidIosCalibrationManager:setBreakDown setting breakdown state=" + breakDown);
        HidIosCalibrationManager.breakDown = breakDown;
    }

    /**
     * 反控是否被打断，true：表示打断，不再发送hid坐标
     */
    public static boolean getBreakDown(){
        return breakDown;
    }

    /**
     * ios-校准逻辑，会通过{@link HidIosCalibrationUtil#startVerification()}方法，传点，此处会将IOS手机端实际生效的，屏幕上的x，y回传回来
     */
    public static List<MyPoint>  setCheckPoint(List<Double> points){
        List<MyPoint> pointLists = new ArrayList<>();
        for (int i = 0; i < points.size(); ) {
            pointLists.add(new MyPoint(points.get(i++).floatValue(), points.get(i++).floatValue()));
        }

        LogUtil.d(TAG + TAG_IOS_CALIB, "HidIosCalibrationManager:setCheckPoint converted points size=" + pointLists.size() + ", points=" + pointLists);
        return pointLists;
    }


    /**
     * 快速校验上一次的校准点
     * @return
     */
    public static boolean checkPrepareCalibPoints(List<Double> points) {
        try {
            LogUtil.d(TAG + TAG_IOS_CALIB, "HidIosCalibrationManager:checkPrepareCalibPoints validating prepare calibration points");
            // 如果已经设置了校准策略，则使用策略进行校准
            if (calibrationStrategy != null) {
                LogUtil.d(TAG, "calibrationStrategy using: " + calibrationStrategy.getClass().getSimpleName());

                // 根据校准结果设置控制可用标志
                controlAvailable = calibrationStrategy.checkPrepareCalibPoints(setCheckPoint(points));
                checkPointFlag = calibrationStrategy.isCalibrationReady();


                LogUtil.d(TAG + TAG_IOS_CALIB, "HidIosCalibrationManager:checkPrepareCalibPoints validation result=" + (controlAvailable ? "SUCCESS" : "FAILED") + ", checkPointFlag: " + checkPointFlag);

                return controlAvailable;
            }
            else {
                LogUtil.d(TAG + TAG_IOS_CALIB, "HidIosCalibrationManager:checkPrepareCalibPoints no calibration strategy available");
                return false;
            }

        }catch (Exception e){
            LogUtil.d(TAG + TAG_IOS_CALIB, "HidIosCalibrationManager:checkPrepareCalibPoints validation FAILED with exception");
            return false;
        }
    }
    /**
     * 反控校准逻辑
     * @return
     */
    public static boolean checkPoint(List<Double> points){
        try {
            LogUtil.d(TAG + TAG_IOS_CALIB, "HidIosCalibrationManager:checkPoint validating formal calibration points");
            checkMyPoint = setCheckPoint(points);

            // 如果已经设置了校准策略，则使用策略进行校准
            if (calibrationStrategy != null) {
                LogUtil.d(TAG + TAG_IOS_CALIB, "HidIosCalibrationManager:checkPoint using strategy: " + calibrationStrategy.getClass().getSimpleName());
                List<MyPoint> pointsCopy = new ArrayList<>(checkMyPoint);
                int result = calibrationStrategy.parseCalibrationPoints(pointsCopy);

                // 如果当前是LinearCalibrationStrategy校准， 不通过，则切换到曲线校准算法。
                if (calibrationStrategy instanceof LinearCalibrationStrategy && result == 0) {
                    LogUtil.d(TAG + TAG_IOS_CALIB, "HidIosCalibrationManager:checkPoint linear failed, switching to curve strategy");
                    calibrationStrategy = new CurveCalibrationStrategy(sp);
                    result = calibrationStrategy.parseCalibrationPoints(pointsCopy);
                }

                // 根据校准结果设置控制可用标志
                controlAvailable = (result > 0);
                checkPointFlag = calibrationStrategy.isCalibrationReady();


                LogUtil.d(TAG + TAG_IOS_CALIB, "HidIosCalibrationManager:checkPoint formal calibration result=" + (controlAvailable ? "SUCCESS" : "FAILED") + ", checkPointFlag: " + checkPointFlag);

                return controlAvailable;
            }
            else {
                LogUtil.d(TAG + TAG_IOS_CALIB, "HidIosCalibrationManager:checkPoint no calibration strategy available");
                return false;
            }

        }catch (Exception e){
            LogUtil.d(TAG + TAG_IOS_CALIB, "HidIosCalibrationManager:checkPoint formal calibration FAILED with exception");
            return false;
        }
    }


    /**
     * 获取复归到右下角，最大的次数
     * @return
     */
    public static int getMaxResetStep(){
        //--> 实际屏幕的最长边
        float viewPixel = Math.max(HidIosUtil.getTargetPhonePixelX(), HidIosUtil.getTargetPhonePixelY());
        if(viewPixel <= 0){
            LogUtil.d(TAG,"getTargetPhonePixelX = " + HidIosUtil.getTargetPhonePixelX() + ", getTargetPhonePixelY = " + HidIosUtil.getTargetPhonePixelY());
            viewPixel = Math.max(HidIosUtil.getViewPixelx(), HidIosUtil.getViewPixelY());
            if (viewPixel <= 0) {
                return DEFAULT_RESET_STEP;
            }
        }
        if (calibrationStrategy == null) {
            return DEFAULT_RESET_STEP;
        }
        //--> 校准点最大 步长
        float maxDistance = Math.max(50, calibrationStrategy.getMaxMovePixel());
        LogUtil.d(TAG,"viewPixel = " + viewPixel + ", maxDistance = " + maxDistance);

        //-->（实际屏幕的最长边/ 校准点最大 步长） + 1 保证每次能够复归到右下角
        return (int) (viewPixel / maxDistance + 1);
    }

    /**
     * 开始预备校准
     */
    public static void startPrepareVerification() {
        // 只有一种可能性，就是没有回传上次的点
        if (calibrationStrategy == null) {
            LogUtil.e(TAG, "startPrepareVerification calibrationStrategy null");
            calibrationStrategy = new LinearCalibrationStrategy(sp);
        }

        LogUtil.d(TAG + TAG_IOS_CALIB, "HidIosCalibrationManager:startPrepareVerification starting prepare calibration");
        BluetoothHidManager.getInstance().startPrepareVerificationThread();
    }
    /**
     * 开始正式校准
     */
    public static void startVerification(){
        LogUtil.d(TAG + TAG_IOS_CALIB, "HidIosCalibrationManager:startVerification starting formal calibration with LinearCalibrationStrategy");
        cleanCache();
        // （查看是否需要强制使用原来的校准方案）
        calibrationStrategy = new LinearCalibrationStrategy(sp);
//        calibrationStrategy = new RegularCalibrationStrategy(sp);

        BluetoothHidManager.getInstance().startVerificationThread();
    }


    public static int getHidVerificationState() {
        return hidVerificationState.get();
    }

    public static void setHidVerificationState(int hidVerificationState) {
        LogUtil.d(TAG_IOS_CALIB, "HidIosCalibrationManager::setHidVerificationState state=" + getHidVerificationStateString(hidVerificationState));
        HidIosCalibrationManager.hidVerificationState.set(hidVerificationState);
    }

    public static void registerCallback(CalibrationCallBack callBack){
        back = callBack;
    }

    public static CalibrationCallBack getBack() {
        return back;
    }

    public static boolean isCheckPointFlag() {
        return checkPointFlag;
    }

    public static void setCheckPointFlag(boolean checkPointFlag) {
        HidIosCalibrationManager.checkPointFlag = checkPointFlag;
    }

    public static boolean isTouchBlocked() {
        return touchBlocked;
    }

    public static void setTouchBlocked(boolean touchBlocked) {
        HidIosCalibrationManager.touchBlocked = touchBlocked;
    }

    /**
     * 清除一些校准参数，便于第二次重新校准
     */
    public static void cleanCache(){
        checkMyPoint.clear();

        // 如果已经设置了校准策略，则清除策略缓存
        if (calibrationStrategy != null) {
            calibrationStrategy.clearCalibrationParams();
            LogUtil.d(TAG + TAG_IOS_CALIB, "HidIosCalibrationManager:cleanCache cleared strategy cache: " + calibrationStrategy.getClass().getSimpleName());
        }
    }

    /**
     * 设置反控是否可用（只是经过校准），在{@link #breakDown } 校准过程中，使用breakDown来阻止发送hid数据。
     * @param controlAvailable true:可用
     */
    public static void setControlAvailable(boolean controlAvailable){
        LogUtil.d(TAG + TAG_IOS_CALIB, "HidIosCalibrationManager:setControlAvailable setting control available=" + controlAvailable);
        HidIosCalibrationManager.controlAvailable = controlAvailable;
    }


    /*
        获取反控是否可用
    */
    public static boolean getControlAvailable(){
        return controlAvailable;
    }
}
