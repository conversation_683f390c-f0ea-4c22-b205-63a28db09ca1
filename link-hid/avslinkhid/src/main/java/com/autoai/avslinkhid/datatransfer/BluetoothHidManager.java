package com.autoai.avslinkhid.datatransfer;

import static com.autoai.avslinkhid.model.HidVerificationType.*;
import static com.autoai.avslinkhid.model.HidVerificationType.getActionMessageName;
import static com.autoai.welink.param.HidConstants.ON_TOUCH_POINT_REQ_FAIL;
import static com.autoai.welink.param.HidConstants.ON_TOUCH_POINT_REQ_SUCCESS;
import static com.autoai.welink.param.HidConstants.TAG_IOS_CALIB;


import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.SystemClock;
import android.view.GestureDetector;
import android.view.MotionEvent;

import com.autoai.avslinkhid.calibration.CalibrationPoint;
import com.autoai.avslinkhid.calibration.CalibrationStrategy;
import com.autoai.avslinkhid.hidtouchscreen.HidCommonInterface;
import com.autoai.avslinkhid.hidtouchscreen.HidScreenConnectManager;
import com.autoai.avslinkhid.hidtouchscreen.HidScreenConstantsKt;
import com.autoai.avslinkhid.util.HidIosUtil;
import com.autoai.avslinkhid.util.ThreadUtils;
import com.autoai.common.util.LogUtil;

import java.util.List;

import kotlin.Triple;

/**
 * she review tips:目前看该类主要是IOS 反控 将 touch 事件-->hid-mouse 核心逻辑
 *
 *  校准流程文档
 * https://navinfo.feishu.cn/wiki/AMXXwColHi3TiIku22XcSZrPnwc
 * 触摸点校准流程时序图：
 *
 *                     车机端                                  手机端
 *                       ┃                                      ┃
 * 1.请求校准             ┃                                      ┃
 * onTouchPointRequest   ┃                                      ┃
 * state=1               ┃──────────────────────────────────────>┃
 * (ON_TOUCH_POINT_REQ_SUCCESS)                                 ┃
 *                       ┃                                      ┃
 * 2.准备校准             ┃                                      ┃
 * onTouchPointBegin sendMsg=1                                  ┃
 * (ON_TOUCH_POINT_READY)┃──────────────────────────────────────>┃
 *                       ┃                                      ┃
 *                       ┃                                      ┃
 *                       ┃<──────────────────────────────────────┃ 3.响应准备就绪
 *                       ┃                                      ┃   onTouchPointBegin recMsg
 *                       ┃                                      ┃   HidMessage{type=1}
 *                       ┃                                      ┃   (手机侧准备好了)
 *                       ┃                                      ┃
 * 4.处理上次校准点数据    ┠──────────┐                           ┃
 * onTouchPointReady     ┃          │                           ┃
 * (上一次的点)           ┃<─────────┘                           ┃
 *                       ┃                                      ┃
 * 5.开始校准             ┃                                      ┃
 * onTouchPointBegin sendMsg=3                                  ┃
 * (ON_TOUCH_POINT_BEGIN)┃──────────────────────────────────────>┃
 *                       ┃                                      ┃
 * 6.结束校准             ┃                                      ┃
 * onTouchPointBegin sendMsg=4                                  ┃
 * (ON_TOUCH_POINT_END)  ┃──────────────────────────────────────>┃
 *                       ┃                                      ┃
 *                       ┃<──────────────────────────────────────┃ 7.发送校准点数据
 *                       ┃                                      ┃   onTouchPointBegin recMsg
 *                       ┃                                      ┃   (收到手机侧的校准点)
 *                       ┃                                      ┃
 * 8.校准结果反馈         ┃                                      ┃
 * onTouchPointBegin sendMsg=5                                  ┃
 * (ON_TOUCH_POINT_END_SUCCESS)                                 ┃
 * 或 sendMsg=6          ┃──────────────────────────────────────>┃
 * (ON_TOUCH_POINT_END_FAIL)                                    ┃
 *                       ┃                                      ┃
 */
@SuppressLint({"NewApi", "MissingPermission"})
public class BluetoothHidManager {
    private static final String TAG =  HidScreenConstantsKt.HidScreen;

    private static final String TAG_IOSHID = "IOSHID";
    private static BluetoothHidManager instance;
    private int maxResetNum = 10;
    private int scrolling = 0;
    private float mPreX = 0;
    private float mPreY = 0;
    private static float SCALE_X = 0f;
    private static float SCALE_Y = 0f;
    private boolean mLongPress = false;
    private boolean startFlag = false;
    private boolean isBTHIDConnected = false;

    // 校准请求相关状态管理
    private boolean isCalibrationRequested = false;  // 是否有待处理的校准请求
    private Runnable calibrationTimeoutRunnable;     // 5秒超时任务
    private static final int CALIBRATION_TIMEOUT_DELAY = 3000; // 超时时间常量（5秒）

    // 本次是否需要左键点击
    private boolean mLeftClick = false;

    // 上一次左键点击, 用于屏蔽是否重复发送 hid 数据
    private boolean mLastLeftClick = false;
    private Runnable heartBeatRunnable;
    private Runnable mouseResetRunnable;
    private Runnable distanceRunnable;

    /**
     * 手势监听，用于识别各种手势，转化成特殊事件
     */
    private GestureDetector mGestureDetector;
    /**
     * {@link #mGestureDetector} 手势event监听到，事件处理callback
     */
    BluetoothGestureHandler bluetoothGestureHandler = new BluetoothGestureHandler();
    private boolean distanceRunFlag = false;

    // 上次发送时间戳
    private long lastSendTime = 0L;

    private  Handler handler = new Handler(Looper.getMainLooper()){
        @Override
        public void handleMessage(android.os.Message msg) {
            super.handleMessage(msg);
            LogUtil.d(TAG, "rev handleMessage: " + msg.what + " (" + getActionMessageName(msg.what) + ")");
            switch (msg.what) {
                case ACTION_CANCEL_ALL:
                    cancelTimer(mouseResetRunnable);
                    cancelTimer(heartBeatRunnable);
                    cancelTimer(distanceRunnable);
                    distanceRunFlag = false;
                    break;
                case ACTION_MOUSE_RESET:
                    int mouseResetDelay = (int) msg.obj;
                    startTimer(mouseResetRunnable, mouseResetDelay);
                    break;
                case ACTION_HEART_BEAT:
                    startTimer(heartBeatRunnable, HEART_BEAT_DELAY);
                    break;
                case ACTION_DISTANCE:
                    startTimer(distanceRunnable, DISTANCE_DELAY);
                    break;
                default:
                    break;
            }
        }
    };

    private HidCommonInterface.ConnectionStateChangeListener connectionStateChangeListener = new HidCommonInterface.ConnectionStateChangeListener() {
        @Override
        public void onConnecting() {

        }

        @Override
        public void onConnected() {
            isBTHIDConnected = true;
            LogUtil.d(TAG + TAG_IOSHID, "BluetoothHidManager:onConnected BT HID connected");

            // 检查是否有待处理的校准请求
            if (isCalibrationRequested) {
                LogUtil.d(TAG + TAG_IOSHID, "BluetoothHidManager:onConnected pending calibration request found, processing");
                isCalibrationRequested = false;
                cancelCalibrationTimeout();

                // 蓝牙连接成功，调用校准成功回调
                requestCalibration();
            }
        }

        @Override
        public void onDisconnecting() {

        }

        @Override
        public void onDisConnected() {
            isBTHIDConnected = false;
            LogUtil.d(TAG + TAG_IOSHID, "BluetoothHidManager:onDisConnected BT HID disconnected");

            // 蓝牙HID断开时，如果有待处理的校准请求，直接失败
            if (isCalibrationRequested) {
                LogUtil.d(TAG + TAG_IOSHID, "BluetoothHidManager:onDisConnected pending calibration request found, sending FAIL");
                isCalibrationRequested = false;
                cancelCalibrationTimeout();
                HidIosCalibrationManager.setHidVerificationState(HID_VERIFY_TYPE_UN_CONNECT);
                if (HidIosCalibrationManager.getBack() != null) {
                    HidIosCalibrationManager.getBack().onTouchPointRequest(ON_TOUCH_POINT_REQ_FAIL);
                } else {
                    LogUtil.e(TAG + TAG_IOS_CALIB, "BluetoothHidManager:onDisConnected callback is null, cannot send FAIL");
                }
            }
        }
    };


    public static BluetoothHidManager getInstance() {
        if (null == instance) {
            instance = new BluetoothHidManager();
        }
        return instance;
    }

    public BluetoothHidManager() {
    }

    public boolean startBluetooth(Context context) {
        LogUtil.d(TAG + TAG_IOSHID, "BluetoothHidManager::startBluetooth startFlag=" + startFlag);

        if (startFlag) {
            return false;
        }
        startFlag = true;
        HidIosCalibrationManager.setContext(context);

        //--> 手势监听
        new Handler(Looper.getMainLooper()).post(()-> {
                mGestureDetector = new GestureDetector(context, bluetoothGestureHandler);
                mGestureDetector.setOnDoubleTapListener(bluetoothGestureHandler);
            });

        //-->监听蓝牙hid实际的连接状态
        HidScreenConnectManager.getInstance().registeriOSConnectionStateChangeListener(connectionStateChangeListener);
        //-->发起蓝牙设备（将蓝牙hid，连接到手机）
        HidScreenConnectManager.getInstance().startGetHidProfileAndRegisterScreenDigitizer( false);

        //--> ios 定时给一个事件 唤醒常亮
        heartBeatRunnable = () -> {
            LogUtil.d(TAG, "senMouse moveMouseToPreventPhoneScreenLock: " + System.currentTimeMillis() + " ThreadId:" + Thread.currentThread().getId());
            moveMouseToPreventPhoneScreenLock();

            handler.postDelayed(heartBeatRunnable, HEART_BEAT_DELAY);
        };

        //-->复归逻辑
        mouseResetRunnable = () -> {
            if(!isBTHIDConnected){
                return;
            }
            ThreadUtils.postOnBackgroundSingleThread(() ->  {
                reset();
                sleepTime(100);
            });
        };

        LogUtil.d(TAG + TAG_IOSHID, "BluetoothHidManager::startBluetooth initialization completed");
        return true;
    }


    /**
     * 停止蓝牙连接
     */
    public void stopBluetooth() {
        LogUtil.d(TAG, "TouchScreen stopBluetooth startFlag = " + startFlag);

        // 重置校准请求相关状态
        resetCalibrationRequestState();

        controlTimer(ACTION_CANCEL_ALL,null);
        if(startFlag) {
            HidScreenConnectManager.getInstance().closeHidProfileProxyIfNeed();
        }

        HidScreenConnectManager.getInstance().unregisteriOSConnectionStateChangeListener();
        isBTHIDConnected = false;

        startFlag = false;
        HidIosUtil.clean();
        HidIosCalibrationManager.setHidVerificationState(HID_VERIFY_TYPE_UN_CONNECT);

        // fixme: 用户配对连接过，然后此时关闭蓝牙总开关，然后开启，是否需要重新校准呢？ 这里变成 false 后，需要重新校准才能 true.
        //HidIosCalibrationManager.setControlAvailable(false);
    }

    /**
     * 请求校准(表示车机侧准备好了，询问 iOS 侧是否就绪）
     * 优化逻辑：如果蓝牙未连接，等待5秒连接或超时
     */
    public void requestCalibration() {
        LogUtil.d(TAG + TAG_IOS_CALIB, "BluetoothHidManager:requestCalibration checking BT HID connection status=" + isBTHIDConnected);
        if(isBTHIDConnected){
            if (HidScreenConnectManager.getInstance().isBTPairedConnected()) {
                // 蓝牙HID已连接
                HidIosCalibrationManager.setBreakDown(false);
                HidIosCalibrationManager.setHidVerificationState(HID_VERIFY_TYPE_CONNECT);
                HidIosCalibrationManager.getBack().onTouchPointRequest(ON_TOUCH_POINT_REQ_SUCCESS);
                LogUtil.d(TAG + TAG_IOS_CALIB, "BluetoothHidManager:requestCalibration BT HID connected, sending SUCCESS to callback");
            }
            else {
                HidIosCalibrationManager.setHidVerificationState(HID_VERIFY_TYPE_UN_CONNECT);
                HidIosCalibrationManager.getBack().onTouchPointRequest(ON_TOUCH_POINT_REQ_FAIL);
                LogUtil.d(TAG + TAG_IOS_CALIB, "BluetoothHidManager:requestCalibration BT HID connected but not paired, sending FAIL to callback");
            }

        }else{
            // 蓝牙HID未连接，设置等待标志并启动超时机制
            isCalibrationRequested = true;
            startCalibrationTimeout();
            LogUtil.d(TAG + TAG_IOS_CALIB, "BluetoothHidManager:requestCalibration BT HID not connected, waiting for connection");
        }
    }

    /**
     * 鼠标校准
     * 模拟一次鼠标移动，点击
     */
    public boolean mouseCalibration(int num, int stepX, int stepY){
        if(HidIosCalibrationManager.getBreakDown()){
            // 如果是中断校准，则将鼠标归位。
            resetMouse();
            return false;
        }
        for (int i = 0; i < num; i++){
            senMouse((byte) -stepX,(byte) -stepY);
        }
        sendLeftClick(true);
        sendLeftClick(false);
        sleepTime(400);
        return true;
    }

    /**
     * 鼠标校准，根据校准策略，发送校准点
     */
    public boolean mouseCalibrationByStrategy(CalibrationPoint point) {
        if (point == null) {
            LogUtil.e(TAG + TAG_IOS_CALIB, "BluetoothHidManager:mouseCalibrationByStrategy calibration point is null");
            return false;
        }

        if (HidIosCalibrationManager.getBreakDown()) {
            // 如果是中断校准，则将鼠标归位。
            resetMouse();
            return false;
        }

        // 发送校准点
        if (point.isResetMouse()) {
            resetMouse();
        } else {
            senMouse((byte) -point.getX(), (byte) -point.getY());
        }

        // 移动后延迟
        sleepTime(point.getDelayAfterMove());

        // 点击
        if (point.isShouldClickAfterMove()) {
            // 根据点击行为执行不同操作
            if (point.isClickDownOnly()) {
                // 只按下
                sendLeftClick(true);
            } else if (point.isClickUpOnly()) {
                // 只抬起
                sendLeftClick(false);
            } else {
                // 完整点击(按下+抬起)
                sendLeftClick(true);
                if (point.getDelayAfterClickDown() > 0) {
                    sleepTime(point.getDelayAfterClickDown());
                }
                sendLeftClick(false);
            }
            // 点击后延迟
            sleepTime(point.getDelayAfterClick());
        }

        // 执行完成，打印一下执行的内容
        LogUtil.d(TAG, "mouseCalibrationByStrategy: " + point);
        return true;
    }

    /**
     * 将鼠标复归到右下角
     */
    private void resetMouse() {
        maxResetNum = HidIosCalibrationManager.getMaxResetStep();
        LogUtil.d(TAG,"maxResetNum = " + maxResetNum);
        if(HidIosUtil.getVerticalScreen()) {
            //--> 竖屏
            for (int i = 0; i < maxResetNum; i++) {
                senMouse((byte) 127, (byte) 127);
                sleepTime(10);
            }
        }else{
            //--> 横屏
            for (int i = 0; i < maxResetNum; i++) {
                senMouse((byte) -127, (byte) 127);
                sleepTime(10);
            }
        }
    }

    public void calibration(int times) {
        if(!isBTHIDConnected){
            return;
        }
        handler.postDelayed(() -> {reset();}, times);
    }

    private void reset(){
        resetMouse();
        LogUtil.d(TAG, "senMouse reset viewPixelX = " + HidIosUtil.getViewPixelx() + ", viewPixelY = " + HidIosUtil.getViewPixelY() + " ThreadId: " + Thread.currentThread().getId());
        mPreX = (HidIosUtil.getViewPixelx() - 15);
        mPreY = (HidIosUtil.getViewPixelY() - 15);
        if(HidIosUtil.getVerticalScreen()){
            if(mPreX > mPreY){
                float tmp = mPreX;
                mPreX = mPreY;
                mPreY = tmp;
            }
        }else{
            if(mPreY > mPreX){
                float tmp = mPreX;
                mPreX = mPreY;
                mPreY = tmp;
            }
        }
        LogUtil.d(TAG, "mPreX = " + mPreX + ", mPreY = " + mPreY + "  (" + (mPreX * SCALE_X) + ", " + (mPreY * SCALE_Y) + ")");
    }

    /**=-
     * 校准流程的公共部分
     * @param isPrepare 是否是预备校准流程
     */
    private void startCalibrationThread(boolean isPrepare) {

        boolean bRet = ThreadUtils.postOnBackgroundSingleThread("iosCalibration", () -> {
            // 取消 heartbeat
            cancelTimer(heartBeatRunnable);
            if(HidIosCalibrationManager.getBack() != null) {
                if(HidIosCalibrationManager.getBreakDown()){
                    LogUtil.e(TAG + TAG_IOS_CALIB,"mouseCalibration break down");
                    return;
                }
                //--> 通知应用测，开始传点
                if (isPrepare) {
                    LogUtil.d(TAG + TAG_IOS_CALIB, "BluetoothHidManager:startCalibrationThread onTouchPrepareBeginPoint");
                    HidIosCalibrationManager.getBack().onTouchPrepareBeginPoint();
                } else {
                    LogUtil.d(TAG + TAG_IOS_CALIB, "BluetoothHidManager:startCalibrationThread onTouchBeginPoint");
                    HidIosCalibrationManager.getBack().onTouchBeginPoint();
                }
            }

            // 获取校准策略
            CalibrationStrategy calibrationStrategy = HidIosCalibrationManager.getCalibrationStrategy();

            if (calibrationStrategy == null) {
                LogUtil.e(TAG,"mouseCalibration calibrationStrategy null");
                // 通知应用层传点结束
                if (isPrepare) {
                    HidIosCalibrationManager.getBack().onTouchPrepareEndPoint();
                }
                else {
                    HidIosCalibrationManager.getBack().onTouchEndPoint();
                }
                return;
            }
            LogUtil.d(TAG+ TAG_IOS_CALIB , "calibration class: " + calibrationStrategy.getClass().getSimpleName());

            // 获取校准点列表
            List<CalibrationPoint> calibrationPoints = isPrepare ? 
                calibrationStrategy.getPrepareCalibrationPoints() : 
                calibrationStrategy.getCalibrationPoints();

            if (calibrationPoints != null && !calibrationPoints.isEmpty()) {
                LogUtil.d(TAG, "BluetoothHidManager::startCalibrationThread getCalibrationPoints size: " + calibrationPoints.size());

                // 依次执行校准点
                for (CalibrationPoint point : calibrationPoints) {
                    if (!mouseCalibrationByStrategy(point)) {
                        LogUtil.e(TAG + TAG_IOS_CALIB, "BluetoothHidManager:startCalibrationThread calibration interrupted during strategy execution");
                        return;
                    }
                }
            } else if (!isPrepare) {
                // 如果没有校准点且不是预备校准，则使用旧的校准方法
                resetMouse();
                sleepTime(400);
                LogUtil.d(TAG + TAG_IOS_CALIB, "BluetoothHidManager:startCalibrationThread using legacy calibration method");
                if(!mouseCalibration(1,0,40) || !mouseCalibration(1,40,0) || !mouseCalibration(1,0,MAX_STEP)){
                    LogUtil.e(TAG + TAG_IOS_CALIB, "BluetoothHidManager:startCalibrationThread legacy calibration interrupted");
                    return;
                }
                if(!mouseCalibration(1,0,MID_STEP)){
                    LogUtil.e(TAG,"mouseCalibration break down");
                    return;
                }
                if(!mouseCalibration(1,0,MIN_STEP)){
                    LogUtil.e(TAG,"mouseCalibration break down");
                    return;
                }
            }
            
            //--> 将鼠标复位
            reset();

            if(HidIosCalibrationManager.getBack() != null) {
                sleepTime(200);
                if(HidIosCalibrationManager.getBreakDown()){
                    LogUtil.e(TAG + TAG_IOS_CALIB, "BluetoothHidManager:startCalibrationThread calibration interrupted at completion");
                    return;
                }
                //--> 通知应用层，传点结束
                if (isPrepare) {
                    LogUtil.d(TAG + TAG_IOS_CALIB, "BluetoothHidManager:startCalibrationThread prepare calibration completed");
                    HidIosCalibrationManager.getBack().onTouchPrepareEndPoint();
                } else {
                    LogUtil.d(TAG + TAG_IOS_CALIB, "BluetoothHidManager:startCalibrationThread formal calibration completed");
                    HidIosCalibrationManager.getBack().onTouchEndPoint();
                    controlTimer(ACTION_HEART_BEAT,null);
                }
            }
        });
    }

    /**
     * iOS 预备校准流程
     */
    public void startPrepareVerificationThread() {
        startCalibrationThread(true);
    }

    /**
     * IOS 校准流程
     * 移动了 5个位置，但是 Y 轴方向只移动了 4 次，计算出长，中，小，三个间距。
     */
    public void startVerificationThread() {
        startCalibrationThread(false);
    }
    private void cancelTimer(Runnable runnable) {
        handler.removeCallbacks(runnable);
        //LogUtil.d(TAG,"cancelTimer " +  System.identityHashCode(runnable));
    }

    private void startTimer(Runnable runnable, long delay) {
        boolean ret = handler.postDelayed(runnable, delay);
        //LogUtil.d(TAG,"startTimer " +   System.identityHashCode(runnable) + ",ret = " + ret);
    }

    /**
     * 根据投屏view 尺寸
     * 用于ios 复归逻辑使用的右下角坐标
     */
    public void upCalibrationPoint() {
        mPreX = (HidIosUtil.getViewPixelx() - 15);
        mPreY = (HidIosUtil.getViewPixelY() - 15);
        if(HidIosUtil.getVerticalScreen()){
            if(mPreX > mPreY){
                float tmp = mPreX;
                mPreX = mPreY;
                mPreY = tmp;
            }
        }else{
            if(mPreY > mPreX){
                float tmp = mPreX;
                mPreX = mPreY;
                mPreY = tmp;
            }
        }
        LogUtil.d(TAG,"mPreX = " + mPreX + ", mPreY = " + mPreY + "  (" + (mPreX * SCALE_X) + ", " + (mPreY * SCALE_Y) + ")");
    }

    /**
     * 鼠标点击
     * @param click ：true -->按下  false --> 抬起
     */
    public void sendLeftClick(boolean click) {
        LogUtil.e(TAG, "sendLeftClick：" + click);
        mLeftClick = click;
        senMouse((byte) 0x00, (byte) 0x00);
    }

    /**
     * 模拟单击
     */
    public void hidSingleClickEvent() {
        LogUtil.d(TAG,"senMouse onSingleTapUp begin" );
        sleepTime(50);
        sendLeftClick(true);
        sleepTime(50);
        sendLeftClick(false);
        LogUtil.d(TAG,"senMouse onSingleTapUp end  " + " ThreadId:" + Thread.currentThread().getId());
    }

    /**
     * 接收实际要通过hid原始的投屏view上的点
     * @param event
     * @return
     */
    public boolean hidSendPointEvent(MotionEvent event){
        if(!isBTHIDConnected){
            return false;
        }
        //-->交给手势处理
        if (mGestureDetector.onTouchEvent(event)) {
            cancelTimer(distanceRunnable);
            distanceRunFlag = false;
            // up 事件仍然传递，否则可能会只有按下，没有抬起动作导致一直按住。
            if (event.getActionMasked() != MotionEvent.ACTION_CANCEL &&
                    event.getActionMasked() != MotionEvent.ACTION_UP &&
                    event.getActionMasked() != MotionEvent.ACTION_OUTSIDE) {
                LogUtil.d(TAG, "senMouse hidSendPointEvent ignore = " + event.getActionMasked());
                return true;
            }
        }

        int pointCount = event.getPointerCount();
        float x = event.getX();
        float y = event.getY();
        int actionMask = event.getActionMasked();

        ThreadUtils.postOnBackgroundSingleThread(() -> hidThrEvent(pointCount,x,y,actionMask));
        return true;
    }

    public void cancelHeartbeat() {
        controlTimer(ACTION_CANCEL_ALL,null);
    }

    private void controlTimer(int action, Object obj) {
        Message message = handler.obtainMessage(action);
        message.obj = obj;
        handler.sendMessage(message);
    }

    /**
     * 启动校准请求超时机制
     * 5秒后如果蓝牙仍未连接，则调用失败回调
     */
    private void startCalibrationTimeout() {
        cancelCalibrationTimeout(); // 先取消之前的超时任务，避免重复

        calibrationTimeoutRunnable = () -> {
            if (isCalibrationRequested) {
                // 超时处理：重置状态并调用失败回调
                isCalibrationRequested = false;
                HidIosCalibrationManager.setHidVerificationState(HID_VERIFY_TYPE_UN_CONNECT);
                if (HidIosCalibrationManager.getBack() != null) {
                    HidIosCalibrationManager.getBack().onTouchPointRequest(ON_TOUCH_POINT_REQ_FAIL);
                    LogUtil.d(TAG + TAG_IOS_CALIB, "BluetoothHidManager:startCalibrationTimeout timeout after 5 seconds, sending FAIL");
                } else {
                    LogUtil.e(TAG + TAG_IOS_CALIB, "BluetoothHidManager:startCalibrationTimeout timeout but callback is null");
                }
            }
        };

        handler.postDelayed(calibrationTimeoutRunnable, CALIBRATION_TIMEOUT_DELAY);
        LogUtil.d(TAG + TAG_IOS_CALIB, "BluetoothHidManager:startCalibrationTimeout started 5 second timeout timer");
    }

    /**
     * 取消校准请求超时任务
     */
    private void cancelCalibrationTimeout() {
        if (calibrationTimeoutRunnable != null) {
            handler.removeCallbacks(calibrationTimeoutRunnable);
            calibrationTimeoutRunnable = null;
            LogUtil.d(TAG + TAG_IOS_CALIB, "BluetoothHidManager:cancelCalibrationTimeout cancelled timeout timer");
        }
    }

    /**
     * 重置校准请求相关状态
     * 在互联断开或其他需要清理状态的场景调用
     */
    private void resetCalibrationRequestState() {
        if (isCalibrationRequested) {
            isCalibrationRequested = false;
            cancelCalibrationTimeout();
            LogUtil.d(TAG + TAG_IOS_CALIB, "BluetoothHidManager:resetCalibrationRequestState reset due to link disconnect or cleanup");
        }
    }

    /**
     * IOS 互联 hid-mouse协议
     * 该方法主要将Android touch事件 --> mouse 鼠标协议事件
     * @param pointCount
     * @param x
     * @param y
     * @param actionMask
     * @return
     */
    private boolean hidThrEvent(int pointCount,float x, float y, int actionMask) {

        boolean upPosition = true;
        //--> IOS 鼠标协议，不支持多指
        if (pointCount > 1) {
            LogUtil.d(TAG, "senMouse hidThrEvent pointCount:" + pointCount);

            return true;
        }

        //--> 新的touch 事件过来之后，取消前边的动作
        controlTimer(ACTION_CANCEL_ALL,null);

        //--> 实际手机分辨率/投屏view的分辨率
        SCALE_X = HidIosUtil.getProportionX();
        SCALE_Y = HidIosUtil.getProportionY();

        //--> 异常值处理
        if(SCALE_X < 0 || SCALE_Y < 0){
            LogUtil.e(TAG, "SCALE_X or SCALE_Y is -1");
            return true;
        }

        if(x < 0){
            x = 0;
        }
        if(y < 0){
            y = 0;
        }

        LogUtil.d(TAG, "senMouse event.getPointerCount() = " + pointCount + ", getAction = " + actionMask + " ( " + x + ", " + y + " y)" + " ThreadId: " + Thread.currentThread().getId());
        //--> 正式的转化逻辑
        switch (actionMask) {
            case MotionEvent.ACTION_DOWN:
                printBase(x,y);
                moveToTarget(x, y);
                //--> 如果在IOS 底部居中的home键区域，模拟home
                if(HidIosUtil.isClickBtn(x, y)){
                    hidSendHomeKey();
                }
                // 可能出现没有移动到位置的时候，就按下的效果。 因此移动后，停留一下再按下。
                sleepTime(100);
                break;
            case MotionEvent.ACTION_POINTER_DOWN:
            case MotionEvent.ACTION_MOVE:
                if(!motionActionMove(false, pointCount, x, y)) {
                    upPosition = false;
                }
                break;
            case MotionEvent.ACTION_CANCEL:
                //Log.e( "IIIIIIII",   "!!!!! sendLeftClick true ACTION_CANCEL " + " Thread: " + Thread.currentThread());
                // fixme:是为了模拟一次点击？ guch: 答复是收到 cancel 的话，肯定是有一次 action_down。
                sendLeftClick(true);
            case MotionEvent.ACTION_UP:
                motionActionUp(x, y);
                break;
            case MotionEvent.ACTION_POINTER_UP:
            default:
                break;
        }
        LogUtil.d(TAG, "onTouch over-------------");
        if(upPosition) {
            printBase(x, y);
        }
        return true;
    }


    private void printBase(float x, float y){
        LogUtil.d(TAG, "---------------------------- ");
        LogUtil.d(TAG, "senMouse 原始px: getX() = " + x + ", getY() = " +y + "  (" + (x * SCALE_X) + ", " + (y * SCALE_Y) + ")");
        LogUtil.d(TAG, "senMouse 上一次px: mPreX = " + mPreX + ", mPreY = " + mPreY  + "  (" + (mPreX * SCALE_X) + ", " + (mPreY * SCALE_Y) + ")");
        LogUtil.d(TAG, "senMouse SCALE_X = " + SCALE_X + ", SCALE_Y = " + SCALE_Y);
        LogUtil.d(TAG, "----------------------------\n\n");
    }

    private void sleepTime(long time){
        try {
            Thread.sleep(time);
        } catch (Exception e) {
            LogUtil.d(TAG, "sleep ERR");
        }
    }

    private boolean motionActionMove(boolean forceMove, int pointerCount,float x, float y){
        LogUtil.d(TAG,"motionActionMove after x = " + x + ", y = " + y + ", mPreX = " + mPreX + ", mPreY = " + mPreY);
        scrolling++;
        long currentTime = SystemClock.elapsedRealtime();
        if(!forceMove) {
            // 检查距离上一次发送的时间间隔
            if (currentTime - lastSendTime > DISTANCE_DELAY) {
                // 间隔超过DISTANCE_DELAY，立即发送
                LogUtil.d(TAG, "motionActionMove interval exceeded, sending immediately");
                return motionActionMove(true, pointerCount, x, y);
            }

            // 否则延迟发送
            LogUtil.d(TAG, "motionActionMove no need move, x = " + x + ", y = " + y + ", mPreX = " + mPreX + ", mPreY = " + mPreY);
            distanceRunFlag = true;
            distanceRunnable = ()->{
                if(distanceRunFlag) {
                    LogUtil.d(TAG, "distanceRunnable x =" + x + ", y = " + y + ", mPreX = " + mPreX + ", mPreY = " + mPreY);
                    motionActionMove(true, pointerCount, x, y);
                }
                distanceRunFlag = false;
            };
            controlTimer(ACTION_DISTANCE,null);
            return false;
        }

        lastSendTime = currentTime;
        LogUtil.d(TAG, "motionActionMove ACTION_MOVE x = " + x + ", y = " + y + ", mPreX = " + mPreX + ", mPreY = " + mPreY);
        //单指代表移动鼠标
        if (pointerCount == 1) {
            sendLeftClick(true);
            scollMinStep(x, y, true);
        } else {
            senMouse((byte) 0, (byte) 0);
        }
        return true;
    }

    /**
     * 是否有滑动屏幕，如果滑动后，快速复归。 但是可能会有复归过程中，再次点击导致冲突问题。
     * @return
     */
    private boolean isTouchMoving(){
        if(scrolling > 2){
            return true;
        }
        return false;
    }

    private void motionActionUp(float x, float y){
        if (mLongPress) {
            mLongPress = false;
        }

        motionActionMove(true, 1, x, y);
        distanceRunFlag = false;
        sleepTime(20);
        sendLeftClick(false);
        if (isTouchMoving()) {
            controlTimer(ACTION_MOUSE_RESET, MOUSE_RESET_DELAY_200);
        }else {
            controlTimer(ACTION_MOUSE_RESET, MOUSE_RESET_DELAY);
        }

        controlTimer(ACTION_HEART_BEAT,null);
        LogUtil.d(TAG, "heartBeatRunnable start");

        scrolling = 0;
        LogUtil.d(TAG, "ACTION_UP ========");
    }

    public boolean areFloatsEqual(float a, float b) {
        final float EPSILON = 0.0001f; // 设定一个小的阈值
        return Math.abs(a - b) < EPSILON;
    }

    /**
     * 发送 hid-鼠标数据
     * @param dx
     * @param dy
     */
    public void senMouse(byte dx, byte dy) {
        if(!isBTHIDConnected) {
            return;
        }

        //Log.d("POINT_TAG", "senMouse click: " + mLeftClick + "(" + dx + "," + dy + ")");

        byte[] bytes = new byte[5];
        //bytes[0]字节：bit0: 1表示左键按下 0表示左键抬起 | bit1: 1表示右键按下 0表示右键抬起 | bit2: 1表示中键按下 | bit7～3：补充的常数，无意义，这里为0即可
        bytes[0] = (byte) (bytes[0] | (mLeftClick ? 1 : 0));
    //        bytes[0] = (byte) (bytes[0] | (mRightClick ? 1 : 0) << 1);
        bytes[1] = dx;
        bytes[2] = dy;
        bytes[3] = 0; //垂直滚轮
        bytes[4] = 0; //水平滚轮

        boolean ret = HidScreenConnectManager.getInstance().sendReport(4,bytes);

         LogUtil.d(TAG,"senMouse  t:" + Thread.currentThread().getId() + " L: " + mLastLeftClick + "  Left:" + mLeftClick + ", dx:" + dx + ", dy:" + dy + ", ret = " + ret +  " vertical:" + HidIosUtil.getVerticalScreen());
        if (ret) {
            mLastLeftClick = mLeftClick;
        }
    }


    /**
     *
     * @return
     */
    public boolean hidSendHomeKey() {
        if(!isBTHIDConnected){
            return false;
        }
        boolean ret = false;
        //返回command + H
        byte[] keyBuf = new byte[10];
        keyBuf[0] = 0X08;
        keyBuf[3] = 0x0B;

        ret = HidScreenConnectManager.getInstance().sendReport(1,keyBuf);
        LogUtil.d(TAG, "HID = " + byteArrayToHex(keyBuf) + " hidSendHomeKey ret = " + ret);

        resetHidSendHomeKey();
        return ret;
    }

    private boolean resetHidSendHomeKey(){
        byte[] keyBuf = new byte[10];
        boolean ret = HidScreenConnectManager.getInstance().sendReport(1,keyBuf);
        LogUtil.d(TAG,"resetHidSendHomeKey ret = " + ret);
        return ret;
    }

    public static String byteArrayToHex(byte[] byteArray) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : byteArray) {
            hexString.append(String.format("%02X,", b)); // 使用String.format格式化输出
        }
        return hexString.toString();
    }

    /**
     * 控制滚动到指定位置
     * @param targetX
     * @param targetY
     */
    public void scrollToPosition(float targetX, float targetY) {
        scollMinStep(targetX, targetY);
    }

    /**
     * 移动光标到指定位置
     * @param targetX
     * @param targetY
     */
    private void scollMinStep(float targetX, float targetY){
        scollMinStep(targetX, targetY, true);
    }
    /**
     * 移动光标到指定位置
     * @param targetX
     * @param targetY
     * @param bTouchMoving 是否触摸屏幕 move 中， 如果是的话，则不需要太精确，往移动方向滑动就行。
     */
    private void scollMinStep(float targetX, float targetY, boolean bTouchMoving){
        // 使用校准策略查询HID发送数据
        CalibrationStrategy calibrationStrategy = HidIosCalibrationManager.getCalibrationStrategy();
        if (calibrationStrategy == null) {
            LogUtil.e(TAG, "scollMinStep calibrationStrategy == null");
            return;
        }
        float currentX = mPreX;
        float currentY = mPreY;

        int scrollMinStepCountX = 0;
        int scrollMinStepCountY = 0;
        int stepLengthX = 0;
        int stepLengthY = 0;

        // 定义坐标轴处理的通用方法
        // 循环处理X和Y轴的移动，直到两个轴都达到目标位置
        boolean xReachedMinPrecision = false;
        boolean yReachedMinPrecision = false;
        while((!areFloatsEqual(currentX, targetX) && !xReachedMinPrecision) ||
                (!areFloatsEqual(currentY, targetY) && !yReachedMinPrecision)) {
            // 处理X轴移动

            byte moveXValue = 0;
            byte moveYValue = 0;
            if(!areFloatsEqual(currentX, targetX) && !xReachedMinPrecision) {
                // 计算期望移动的像素值(手机侧坐标系）
                float expectMovePixelX = (targetX - currentX) * SCALE_X;

                // 使用校准策略查询HID发送数据
                Triple<Integer, Float, Boolean> resultX = calibrationStrategy.queryHidSendData(expectMovePixelX, bTouchMoving);

                // 如果手指移动过程中，一些细微的移动就先不挪动。
                if (bTouchMoving && Math.abs(resultX.getFirst()) <= 2) {
                    xReachedMinPrecision = true;
                }
                else {
                    // 更新当前位置（车机侧坐标系）
                    currentX += (resultX.getSecond() / SCALE_X);

                    // 发送HID数据
                    if(!HidIosUtil.getVerticalScreen()) {
                        moveYValue = resultX.getFirst().byteValue();
                    } else {
                        moveXValue = resultX.getFirst().byteValue();
                    }

                    stepLengthX = resultX.getFirst();
                    scrollMinStepCountX++;

                    // 已经达到最低精度
                    xReachedMinPrecision = resultX.getThird();
                }
            }

            // 处理Y轴移动
            if(!areFloatsEqual(currentY, targetY) && !yReachedMinPrecision) {
                // 计算期望移动的像素值
                float expectMovePixelY = (targetY - currentY) * SCALE_Y;

                // 使用校准策略查询HID发送数据
                Triple<Integer, Float, Boolean> resultY = calibrationStrategy.queryHidSendData(expectMovePixelY, bTouchMoving);

                if (bTouchMoving && Math.abs(resultY.getFirst()) <= 2) {
                    yReachedMinPrecision = true;
                }
                else {


                    // 更新当前位置
                    currentY += (resultY.getSecond() / SCALE_Y);


                    // 发送HID数据
                    if(!HidIosUtil.getVerticalScreen()) {
                        moveXValue = (byte) -resultY.getFirst().byteValue();
                    } else {
                        moveYValue = resultY.getFirst().byteValue();
                    }

                    stepLengthY = resultY.getFirst();
                    scrollMinStepCountY++;

                    // 已经达到最低精度
                    yReachedMinPrecision = resultY.getThird();
                }
            }

            senMouse(moveXValue, moveYValue);
        }

        mPreX = currentX;
        mPreY = currentY;

    }


    /**
     * 移动光标到指定位置
     * @param current 当前鼠标位置
     * @param target  要移动到的目标位置
     * @param scale   手机实际分辨率/投屏屏幕的缩放比
     * @param isMoveYValue  true:移动Y方向值，false:移动X方向值
     * @return 最新的 currentValue
     */
    private float moveStep(float current, float target, float scale, boolean isMoveYValue){
        // 获取校准策略
        CalibrationStrategy calibrationStrategy = HidIosCalibrationManager.getCalibrationStrategy();
        if (calibrationStrategy == null) {
            LogUtil.e(TAG, "moveStep calibrationStrategy == null");
            return current;
        }

        // 记录移动步数和步长，用于调试
        int stepCount = 0;
        int stepLength = 0;

        // 循环处理移动，直到达到目标位置
        boolean reachedMinPrecision = false;
        while(!areFloatsEqual(current, target) && !reachedMinPrecision) {
            // 计算期望移动的像素值
            float expectMovePixel = (target - current) * scale;

            // 使用校准策略查询HID发送数据
            Triple<Integer, Float, Boolean> result = calibrationStrategy.queryHidSendData(expectMovePixel, false);

            // 更新当前位置
            current += (result.getSecond() / scale);

            // 发送HID数据
            if(isMoveYValue) {
                // 移动Y轴
                if(!HidIosUtil.getVerticalScreen()) {
                    // 横屏模式
                    senMouse((byte) -result.getFirst().byteValue(), (byte)0);
                } else {
                    // 竖屏模式
                    senMouse((byte)0, result.getFirst().byteValue());
                }
            } else {
                // 移动X轴
                if(!HidIosUtil.getVerticalScreen()) {
                    // 横屏模式
                    senMouse((byte)0, result.getFirst().byteValue());
                } else {
                    // 竖屏模式
                    senMouse(result.getFirst().byteValue(), (byte)0);
                }
            }

            stepLength = result.getFirst();
            stepCount++;

            // 已经达到最低精度
            reachedMinPrecision = result.getThird();

            LogUtil.d(TAG, "moveStep: direction=" + (isMoveYValue ? "Y" : "X") +
                    ", expectPixel=" + expectMovePixel +
                    ", actualPixel=" + result.getSecond() +
                    ", current=" + current);
        }

        // 如果移动了至少一步，记录日志
        if (stepCount > 0) {
            LogUtil.d(TAG, "moveStep完成: direction=" + (isMoveYValue ? "Y" : "X") +
                    ", steps=" + stepCount +
                    ", lastStepLength=" + stepLength);
        }

        return current;
    }

    /**
     * 移动光标到指定位置
     * @param targetX （车机侧坐标系）
     * @param targetY
     */
    private void moveToTarget(float targetX, float targetY){
        //-->事件起始点 mPreX ,mPreY
        float currentX = mPreX;
        float currentY = mPreY;
        if(HidIosUtil.getArea(targetX)) {
            LogUtil.d(TAG,"area 1，3");
            mPreY = moveStep(currentY, targetY, SCALE_Y, true);
            mPreX = moveStep(currentX, targetX, SCALE_X, false);
        }else{
            LogUtil.d(TAG,"area 2");
            mPreX = moveStep(currentX, targetX, SCALE_X, false);
            mPreY = moveStep(currentY, targetY, SCALE_Y, true);
        }
    }

    public void authorize(int delay){
        LogUtil.d(TAG,"authorize begin");
        handler.postDelayed(()->{
            float x = HidIosUtil.getViewPixelx();
            float y = HidIosUtil.getViewPixelY();
            x = x * 0.5f;
            y = y * 0.58f;
            MotionEvent event = MotionEvent.obtain(SystemClock.uptimeMillis(), SystemClock.uptimeMillis(), MotionEvent.ACTION_DOWN, x, y, 0);
            hidSendPointEvent(event);
            sleepTime(300);
            event = MotionEvent.obtain(SystemClock.uptimeMillis(), SystemClock.uptimeMillis(), MotionEvent.ACTION_UP, x, y, 0);
            hidSendPointEvent(event);
            LogUtil.d(TAG,"-------------------------------");
            //reset();
        },delay);
    }

    /**
     * 移动鼠标，以免手机自动锁屏，不采用按下的方案，会误触
     */
    private void moveMouseToPreventPhoneScreenLock() {
        sendLeftClick(false);
        senMouse((byte)1, (byte) 0);
        senMouse((byte) -1, (byte) 0);
    }

}
