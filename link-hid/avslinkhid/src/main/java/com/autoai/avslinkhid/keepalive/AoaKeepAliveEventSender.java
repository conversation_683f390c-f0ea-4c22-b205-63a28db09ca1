package com.autoai.avslinkhid.keepalive;

import com.autoai.avslinkhid.util.TouchUtil;
import com.autoai.common.util.LogUtil;

/**
 * AOA有线连接的防锁屏保活事件发送策略实现
 */
public class AoaKeepAliveEventSender implements KeepAliveEventSender {
    private static final String TAG = "AoaKeepAliveEventSender";
    
    @Override
    public boolean sendKeepAliveEvent() {
        try {
            TouchUtil.getInstance().sendKeepAliveEvent();
            return true;
        } catch (Exception e) {
            LogUtil.e(TAG, "Failed to send AOA keep alive event", e);
            return false;
        }
    }
    
    @Override
    public boolean isConnectionReady() {
        // AOA连接状态检查：检查TouchUtil是否已注册HID
        // 对于AOA连接，如果TouchUtil已经注册HID，说明连接正常
        try {
            return TouchUtil.getInstance().isSupportHid();
        } catch (Exception e) {
            LogUtil.w(TAG, "Failed to check AOA connection status", e);
            return false;
        }
    }
    
    @Override
    public String getStrategyDescription() {
        return "AOA USB Consumer Control Wake Up";
    }
}
