package com.autoai.avslinkhid.keepalive;

import com.autoai.avslinkhid.hidtouchscreen.HidScreenConnectManager;
import com.autoai.avslinkhid.hidtouchscreen.HidScreenTouchManager;
import com.autoai.common.util.LogUtil;

/**
 * 蓝牙HID连接的防锁屏保活事件发送策略实现
 */
public class BluetoothKeepAliveEventSender implements KeepAliveEventSender {
    private static final String TAG = "BluetoothKeepAliveEventSender";
    
    @Override
    public boolean sendKeepAliveEvent() {
        try {
            HidScreenTouchManager.getInstance().sendKeepAliveEvent();
            return true;
        } catch (Exception e) {
            LogUtil.e(TAG, "Failed to send Bluetooth keep alive event", e);
            return false;
        }
    }
    
    @Override
    public boolean isConnectionReady() {
        HidScreenConnectManager connectManager = HidScreenConnectManager.getInstance();
        return connectManager.isConnected() && connectManager.isConnectedAndroidDevice();
    }
    
    @Override
    public String getStrategyDescription() {
        return "Bluetooth HID Consumer Control";
    }
}
