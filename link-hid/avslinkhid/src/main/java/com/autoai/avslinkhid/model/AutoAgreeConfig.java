package com.autoai.avslinkhid.model;

/**
 * <AUTHOR>
 */
public class AutoAgreeConfig {
    public String bluetoothName;
    public int width;
    public int height;
    public float pointX;
    public float pointY;

    public AutoAgreeConfig(String name, int width, int height, float pointX, float pointY) {
        this.bluetoothName = name;
        this.width = width;
        this.height = height;
        this.pointX = pointX;
        this.pointY = pointY;
    }

    @Override
    public String toString() {
        return "AutoAgreeConfig{" +
                "bluetoothName='" + bluetoothName + '\'' +
                ", width=" + width +
                ", height=" + height +
                ", pointX=" + pointX +
                ", pointY=" + pointY +
                '}';
    }
}
