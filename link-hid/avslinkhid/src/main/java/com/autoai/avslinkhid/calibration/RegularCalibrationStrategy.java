package com.autoai.avslinkhid.calibration;

import static com.autoai.avslinkhid.util.HidIosUtil.areFloatsEqual;
import static com.autoai.avslinkhid.util.SpUtil.SP_MAX_STEP;
import static com.autoai.avslinkhid.util.SpUtil.SP_MID_STEP;
import static com.autoai.avslinkhid.util.SpUtil.SP_MIN_STEP;

import android.util.Pair;

import com.autoai.avslinkhid.calibration.CalibrationPoint;
import com.autoai.avslinkhid.calibration.CalibrationStrategy;
import com.autoai.avslinkhid.hidtouchscreen.HidScreenConstantsKt;
import com.autoai.avslinkhid.model.MyPoint;
import com.autoai.avslinkhid.util.SpUtil;
import com.autoai.common.util.LogUtil;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import kotlin.Triple;


/**
 * 常规校准策略
 * 用于处理HID设备的校准，将HID发送的坐标与实际屏幕坐标进行映射
 */
public final class RegularCalibrationStrategy implements CalibrationStrategy {

    private static final String TAG = HidScreenConstantsKt.HidScreen + "HidIosCalibration";

    // 校准点检查次数
    private static final int STEP_CHECK_NUM = 4;
    // 默认步长值
    private static final float DEFAULT_STEP_MAX = 157.14f;
    private static final float DEFAULT_STEP_MID = 77.61f;
    private static final float DEFAULT_STEP_MIN = 17.32f;
    // HID发送数据步长常量
    private static final int MAX_STEP = 100;
    private static final int MID_STEP = 50;
    private static final int MIN_STEP = 25;
    // 校准点移动后延迟时间（毫秒）
    private static final int CALIBRATION_DELAY_AFTER_MOVE = 1;
    // 校准点点击后延迟时间（毫秒）
    private static final int CALIBRATION_DELAY_AFTER_CLICK = 400;
    // 最小步长
    private float stepMin = 0;
    // 中间步长
    private float stepMid = 0;
    // 最大步长
    private float stepMax = 0;
    // 是否强制重新计算 stepMin, stepMid, stepMax
    private boolean forceRecalculateStep = false;
    // 是否已经检查过point
    private boolean checkPointFlag = false;

    // 保存参数
    private SpUtil sp = null;
       /**
     * 记录IOS 反控校准，回传实际生效的点 举例：通过hid mouse发送给手机（0，40），手机实际（0，60）
     */
     private final List<MyPoint> checkMyPoint = new ArrayList<>(10);


     // 构造函数
     public RegularCalibrationStrategy(SpUtil sp){
        if (sp == null) {
            printLogE("RegularCalibrationStrategy: sp is null");
        }
        this.sp = sp;
        clearCalibrationParams();

     }


     /**
     * 获取发送校准点
     * @return 校准点列表
     */
    @Override
    public List<CalibrationPoint> getCalibrationPoints() {
        List<CalibrationPoint> points = new ArrayList<>();

        // 第一个点：重置鼠标位置
        CalibrationPoint resetPoint = new CalibrationPoint(0, 0, CALIBRATION_DELAY_AFTER_MOVE, CALIBRATION_DELAY_AFTER_CLICK, false);
        resetPoint.setResetMouse(true);
        points.add(resetPoint);

        // 绕过辅助触控按钮
        points.add(new CalibrationPoint(40, 40, CALIBRATION_DELAY_AFTER_MOVE, CALIBRATION_DELAY_AFTER_CLICK, false));

        // 校准步长点
        points.add(new CalibrationPoint(0, MAX_STEP, CALIBRATION_DELAY_AFTER_MOVE, CALIBRATION_DELAY_AFTER_CLICK, false));
        points.add(new CalibrationPoint(0, MID_STEP, CALIBRATION_DELAY_AFTER_MOVE, CALIBRATION_DELAY_AFTER_CLICK, false));
        points.add(new CalibrationPoint(0, MIN_STEP, CALIBRATION_DELAY_AFTER_MOVE, CALIBRATION_DELAY_AFTER_CLICK, false));

        return points;
    }

    @Override
    public List<CalibrationPoint> getPrepareCalibrationPoints() {
        return Collections.emptyList();
    }


    /**
     * 快速校验校准点是否符合预期
     * 如果解析成功，则返回 1
     */
    @Override
    public boolean checkPrepareCalibPoints(List<MyPoint> points) {
        // 不支持
        return false;
    }

    /**
     * 解析校准点
     * 将收集到的校准点进行处理和验证，确定是否可以用于校准
     * @param points 校准点列表
     * @return 返回是否解析成功
     * 如果解析成功，则返回 0， 否则返回 1
     */
    @Override
    public int parseCalibrationPoints(List<MyPoint> points) {
        if (points == null || points.isEmpty()) {
            printLogE("parseCalibrationPoints: points is null or empty");
            return 0;
        }
        printLog("parseCalibrationPoints: points size: = " + points.size());
        checkMyPoint.clear();
        checkMyPoint.addAll(points);
         // 执行校准点检查
        boolean checkResult = checkPoint();

        // 设置校准完成标志
        checkPointFlag = checkResult;

        return checkResult ? 1 : 0;
    }

    /**
     * 通过手机侧期望移动的位移像素，查询计算 hid 发送数据
     * @param expectMovePixel 期望移动的像素值
     * @return Pair<发送的hid数据, 实际移动的像素值, 是否移动到位>
     */
    @Override
    public Triple<Integer, Float, Boolean> queryHidSendData(float expectMovePixel, boolean bTouchMoving) {
        // 如果期望移动像素为0，直接返回0
        if (areFloatsEqual(expectMovePixel, 0)) {
            return new Triple<>(0, 0.0f, true);
        }

        float actualMovePixel = expectMovePixel;

        // 根据期望移动像素，选择合适的步长
        if (Math.abs(expectMovePixel) >= getStepMax()) {
            actualMovePixel = expectMovePixel > 0 ? getStepMax() : -getStepMax();
        } else if (Math.abs(expectMovePixel) >= getStepMid()) {
            actualMovePixel = expectMovePixel > 0 ? getStepMid() : -getStepMid();
        } else {
            actualMovePixel = expectMovePixel > 0 ?
                              Math.min(getStepMin(), expectMovePixel) :
                              Math.max(-getStepMin(), expectMovePixel);
        }

        // 计算HID发送数据，转换为整数
        int hidData = getStepByPx(actualMovePixel);

        printLog("queryHidSendData: expectMovePixel=" + expectMovePixel +
                 ", actualMovePixel=" + actualMovePixel +
                 ", hidData=" + hidData);

        return new Triple<>(hidData, actualMovePixel, Math.abs(expectMovePixel - actualMovePixel) < 0.1f);
    }


    /**
     * 根据像素值计算HID发送数据
     * @param px 像素值
     * @return HID发送数据
     */
    private int getStepByPx(float px){
        if (areFloatsEqual(px, getStepMax())) {
            return MAX_STEP;
        } else if(areFloatsEqual(px, getStepMid())){
            return MID_STEP;
        } else if (areFloatsEqual(px, getStepMin())){
            return MIN_STEP;
        } else if(areFloatsEqual(px, -getStepMax())){
            return -MAX_STEP;
        } else if(areFloatsEqual(px, -getStepMid())){
            return -MID_STEP;
        } else if(areFloatsEqual(px, -getStepMin())){
            return -MIN_STEP;
        } else {
            // 线性插值计算中间值
            float a = MIN_STEP * px / getStepMin();
            int ret = Math.round(a);
            printLog("getStepByPx: MIN_STEP=" + MIN_STEP + ", px=" + px +
                   ", stepMin=" + getStepMin() + ", ret=" + ret + ", a=" + a);
            return ret;
        }
    }

    /**
     * 清空校准参数
     */
    @Override
    public void clearCalibrationParams() {
        checkMyPoint.clear();
        stepMin = DEFAULT_STEP_MIN;
        stepMid = DEFAULT_STEP_MID;
        stepMax = DEFAULT_STEP_MAX;
        forceRecalculateStep = false;
        checkPointFlag = false;
        printLog("clearCalibrationParams ");
    }

    /**
     * 当前是否已经解析校准完成
     */
    @Override
    public boolean isCalibrationReady() {
        return checkPointFlag;
    }

    /**
     * 单次移动最大像素对应的发送单位
     */
    @Override
    public int getMaxStepsHidUnit() {
        return MAX_STEP;
    }

    /**
     * 单次移动手机侧最大移动像素
     */
    @Override
    public float getMaxMovePixel() {
        return getStepMax();
    }

    /**
     * 过滤掉 X 不一致的点，只使用 Y 值数据变化来校准
     * @param checkMyPoint 校准点列表
     */
    private void deleteExpPoint(List<MyPoint> checkMyPoint){
        Map<Float, Integer> countMap = new HashMap<>(10);

        for (MyPoint point : checkMyPoint) {
            countMap.put(point.getX(), countMap.getOrDefault(point.getX(), 0) + 1);
        }

        printLog("checkPoint countMap = " + countMap.size()  + ", pointSize: "  + checkMyPoint.size());

        for (Float key : countMap.keySet()){
            printLog("key = " + key + ", value = " + countMap.get(key));
        }

        Iterator<MyPoint> iterator = checkMyPoint.iterator();
        while (iterator.hasNext()){
            MyPoint point = iterator.next();
            if(countMap.get(point.getX()) != STEP_CHECK_NUM){
                iterator.remove();
            }
        }
    }

    /**
     * 反控校准逻辑
     * @return 是否校准成功
     */
    private  boolean checkPoint() {
        try {
            // 只使用 Y 值来校准。
            deleteExpPoint(checkMyPoint);
            printLog("checkPoint size = " + checkMyPoint.size());

            //--> 异常：点的个数不对
            if (checkMyPoint.size() != STEP_CHECK_NUM) {
                checkMyPoint.clear();
                printLog("checkPoint err");
                return false;
            }

            //--> 异常：X异常值
            if (!areFloatsEqual(checkMyPoint.get(0).getX(), checkMyPoint.get(1).getX())
                    || !areFloatsEqual(checkMyPoint.get(1).getX(), checkMyPoint.get(2).getX())
                    || !areFloatsEqual(checkMyPoint.get(2).getX(), checkMyPoint.get(3).getX())) {
                checkMyPoint.clear();
                printLog("checkPoint x is not equal");
                return false;
            }
            //--> 异常：Y异常值
            if (areFloatsEqual(checkMyPoint.get(0).getY(), checkMyPoint.get(1).getY())
                    || areFloatsEqual(checkMyPoint.get(1).getY(), checkMyPoint.get(2).getY())
                    || areFloatsEqual(checkMyPoint.get(2).getY(), checkMyPoint.get(3).getY())) {
                checkMyPoint.clear();
                printLogE("checkPoint y is not equal");
                return false;
            }

            // 下面强制计算反控校准数据
            withForceRecalculateStep(() -> {
                //--> 根据反控校准点大，中，小步长
                float stepMax = getStepMax();
                float stepMid = getStepMid();
                float stepMin = getStepMin();
                printLog("checkPoint success: max = " + stepMax + ", mid = " + stepMid + ", min = " + stepMin);
            });

        }catch (Exception e){
            printLogE("checkPoint err" + e);
            return false;
        }
        return true;
    }

    /**
     * 强制重新计算 iOS 步长校准
     * @param runnable 要执行的操作
     */
    private void withForceRecalculateStep(Runnable runnable) {
        try {
            forceRecalculateStep = true;
            runnable.run();
        } finally {
            forceRecalculateStep = false;
        }
    }

    /**
     * 使用校准点计算 大步长
     * @return 大步长值
     */
    private float getStepMax() {
        if(checkMyPoint.size() < STEP_CHECK_NUM && sp != null){
            float defaultValue = sp.getFloat(SP_MAX_STEP, DEFAULT_STEP_MAX);
            printLog("getStepMax with default value size: " + checkMyPoint.size() + " value: " + defaultValue);
            if (defaultValue < 1) {
                defaultValue = DEFAULT_STEP_MAX;
            }
            return defaultValue;
        }
        if(areFloatsEqual(stepMax,0) || forceRecalculateStep){
            stepMax = checkMyPoint.get(0).getY() - checkMyPoint.get(1).getY();
            if (stepMax > 0) {
                sp.put(SP_MAX_STEP, stepMax);
            }

        }
        return stepMax;
    }

    /**
     * 使用校准点计算 中步长
     * @return 中步长值
     */
    private float getStepMid() {
        if(checkMyPoint.size() < STEP_CHECK_NUM && sp != null){
            float defaultValue = sp.getFloat(SP_MID_STEP, DEFAULT_STEP_MID);
            printLog("getStepMid with default value size: " + checkMyPoint.size() + " value: " + defaultValue);
            if (defaultValue < 1) {
                defaultValue = DEFAULT_STEP_MID;
            }
            return defaultValue;
        }
        if(areFloatsEqual(stepMid,0) || forceRecalculateStep){
            stepMid = checkMyPoint.get(1).getY() - checkMyPoint.get(2).getY();
            if (stepMid > 0) {
                sp.put(SP_MID_STEP,stepMid);
            }
        }
        return stepMid;
    }

    /**
     * 使用校准点计算 小步长
     * @return 小步长值
     */
    private float getStepMin() {
        if (checkMyPoint.size() < STEP_CHECK_NUM && sp != null) {
            float defaultValue = sp.getFloat(SP_MIN_STEP, DEFAULT_STEP_MIN);
            printLog("getStepMin with default value size: " + checkMyPoint.size() + " value: " + defaultValue);
            if (defaultValue < 1) {
                defaultValue = DEFAULT_STEP_MIN;
            }
            return defaultValue;
        }
        if (areFloatsEqual(stepMin, 0) || forceRecalculateStep) {
            stepMin = checkMyPoint.get(2).getY() - checkMyPoint.get(3).getY();
            if (stepMin > 0) {
                sp.put(SP_MIN_STEP, stepMin);
            }
        }
        return stepMin;
    }


    /**
     * 打印Error日志
     * @param msg 日志信息
     */
    private void printLogE(String msg){
        LogUtil.e(TAG, msg);
    }

    /**
     * 打印调试日志
     * @param msg 日志信息
     */
    private void printLog(String msg){
        LogUtil.d(TAG, msg);
    }
}
