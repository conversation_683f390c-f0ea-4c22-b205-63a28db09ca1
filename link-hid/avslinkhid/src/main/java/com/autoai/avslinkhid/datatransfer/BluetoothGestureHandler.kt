package com.autoai.avslinkhid.datatransfer


import android.view.GestureDetector
import android.view.MotionEvent
import com.autoai.avslinkhid.hidtouchscreen.HidScreen
import com.autoai.common.util.LogUtil
import java.lang.Thread.sleep

/**
 * 手势给蓝牙发送数据
 */
class BluetoothGestureHandler(): GestureDetector.OnGestureListener, GestureDetector.OnDoubleTapListener{
    private val TAG = HidScreen + "BluetoothHidManager"

    override fun onDown(e: MotionEvent): Boolean {
        return false
    }

    override fun onShowPress(e: MotionEvent) {}

    override fun onSingleTapUp(e: MotionEvent): Boolean {
//        com.autoai.avslinkhid.util.ThreadUtils.postOnBackgroundSingleThread( {
//            BluetoothHidManager.getInstance().hidSingleClickEvent();
//        });
//        return true
        return false;
    }

    override fun onScroll(
        e1: MotionEvent?,
        e2: MotionEvent,
        distanceX: Float,
        distanceY: Float
    ): <PERSON><PERSON><PERSON> {
        return false
    }



    override fun onLongPress(e: MotionEvent) {
        //单键长按效果
        // fixme: 这里长按之后，是哪里释放的呢。
        BluetoothHidManager.getInstance().sendLeftClick(true)
    }

    override fun onFling(
        e1: MotionEvent?,
        e2: MotionEvent,
        velocityX: Float,
        velocityY: Float
    ): Boolean {
        return false
    }

    override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
        return false
    }

    override fun onDoubleTap(e: MotionEvent): Boolean {
        LogUtil.d(TAG,"senMouse onDoubleTap  action: ${e.action} (${e.getX()}, ${e.getY()})")
        return false
    }

    override fun onDoubleTapEvent(e: MotionEvent): Boolean {
        LogUtil.d(TAG,"senMouse onDoubleTapEvent  action: ${e.action} (${e.getX()}, ${e.getY()})")
//        if (e.action == MotionEvent.ACTION_DOWN) {
//            BluetoothHidManager.getInstance().scrollToPosition(e.getX(), e.getY())
//            sleep(100)
//            BluetoothHidManager.getInstance().sendLeftClick(true)
//
//        } else if (e.action == MotionEvent.ACTION_UP) {
//            BluetoothHidManager.getInstance().sendLeftClick(false)
//        }
        return false
    }

}