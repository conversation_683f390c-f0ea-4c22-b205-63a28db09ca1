package com.autoai.avslinkhid.api;

import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.NonNull;

import com.autoai.avslinkhid.datatransfer.BluetoothHidManager;
import com.autoai.avslinkhid.datatransfer.HidIosCalibrationManager;
import com.autoai.avslinkhid.hidtouchscreen.HidScreenConnectManager;
import com.autoai.avslinkhid.hidtouchscreen.HidScreenConstantsKt;
import com.autoai.avslinkhid.util.HidIosUtil;
import com.autoai.common.util.LogUtil;
import com.autoai.welink.param.HidTouch;

import java.util.concurrent.atomic.AtomicBoolean;

public class HidWlanIos implements HidApi{
    private static final String TAG = HidScreenConstantsKt.HidScreen + "IosWifiHid";
    private static final String TAG_IOSHID = "IOSHID";
    private static HidWlanIos mInstance;
    private static AtomicBoolean isRegisterHid = new AtomicBoolean(false);
    private HidWlanIos(){

    }

    public static HidApi getInstance() {
        if(mInstance == null){
            synchronized (HidWlanIos.class){
                if(mInstance == null){
                    mInstance = new HidWlanIos();
                }
            }
        }
        return mInstance;
    }

    @Override
    public boolean registerHID(@NonNull Context context) {
        LogUtil.d(TAG + TAG_IOSHID, "HidWlanIos::registerHID isRegisterHid=" + isRegisterHid.get());
        if(!isRegisterHid.get()){
            isRegisterHid.set(true);
            boolean result = BluetoothHidManager.getInstance().startBluetooth(context);
            LogUtil.d(TAG + TAG_IOSHID, "HidWlanIos::registerHID startBluetooth result=" + result);
            return result;
        }
        LogUtil.d(TAG,"IOS registerHID:" + isRegisterHid.get());
        return true;
    }

    @Override
    public boolean unregisterHID() {
        LogUtil.d(TAG,"IOS unregisterHID Before:" + isRegisterHid.get()   +  "stack: " + HidUtil.getStackTraces(false));
        if(isRegisterHid.get()) {
            BluetoothHidManager.getInstance().stopBluetooth();
        }
        isRegisterHid.set(false);

        LogUtil.d(TAG,"IOS unregisterHID After:" + isRegisterHid.get());
        return true;
    }

    @Override
    public boolean isRegisterHID() {
        return isRegisterHid.get();
    }

    @Override
    public void connectBluetoothDevice(BluetoothDevice device) {
        HidScreenConnectManager.getInstance().connectBluetoothHIDDevice(device);
    }

    @Override
    public boolean onTouch(View view, MotionEvent motionEvent) {
        if(!HidIosCalibrationManager.getControlAvailable()){
            LogUtil.e(TAG,"controlAvailable: false");
            return true;
        }
        int touchViewWidth = view.getWidth();
        int touchViewHeight = view.getHeight();
        LogUtil.d(TAG,"senMouse onTouch--> w = " + touchViewWidth + " h = " + touchViewHeight + " motionEvent: " + motionEvent.getActionMasked());
        if(HidIosUtil.getViewPixelx() != touchViewWidth || HidIosUtil.getViewPixelY() != touchViewHeight){
            //-->投屏区域发生变化，重新更新ios mouse 复归参数
            HidIosUtil.setViewPixel(touchViewWidth,touchViewHeight);
            BluetoothHidManager.getInstance().upCalibrationPoint();
        }
        return BluetoothHidManager.getInstance().hidSendPointEvent(motionEvent);
    }

    @Override
    public void backHome() {
        BluetoothHidManager.getInstance().hidSendHomeKey();
        BluetoothHidManager.getInstance().hidSendHomeKey();
    }

    @Override
    public void setH264Coordinates(HidTouch rotation) {
        HidIosUtil.setTargetPhonePixel(rotation.phoneSize.w,rotation.phoneSize.h);
        if(HidIosUtil.getViewPixelx() == 0 || HidIosUtil.getViewPixelY() == 0) {
            HidIosUtil.setViewPixel((int) (rotation.h264W * 0.94f / 2), (int) (rotation.h264H * 0.94f / 2));
            LogUtil.d(TAG,"setViewPixel w = " + HidIosUtil.getViewPixelx() + ", h = " + HidIosUtil.getViewPixelY());
        }
        HidIosUtil.setVerticalScreen(rotation.angle == 0 || rotation.angle == 180);
        LogUtil.d(TAG,"setTargetPhonePixel w = " + rotation.phoneSize.w + ", h = " + rotation.phoneSize.h);
        LogUtil.d(TAG,"setVerticalScreen angle = " + rotation.angle);
    }

    @Override
    public void setName(String name, String deviceAddress) {

    }

    @Override
    public void autoAgree(int delay) {
        BluetoothHidManager.getInstance().authorize(delay);
    }

    @Override
    public void setScreenTimeout(int timeoutMs) {
        // iOS设备不需要防锁屏功能，空实现
        LogUtil.d(TAG, "setScreenTimeout: iOS device, ignored - " + timeoutMs + "ms");
    }
}
