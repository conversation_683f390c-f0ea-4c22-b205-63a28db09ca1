package com.autoai.avslinkhid.api;


import static com.autoai.welink.macro.WL_API_DEVICE_TYPE.AOA;
import static com.autoai.welink.macro.WL_API_DEVICE_TYPE.EAP;
import static com.autoai.welink.macro.WL_API_DEVICE_TYPE.WLAN_ANDROID;
import static com.autoai.welink.macro.WL_API_DEVICE_TYPE.WLAN_HARMONY;
import static com.autoai.welink.macro.WL_API_DEVICE_TYPE.WLAN_IPHONE;

import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.hardware.usb.UsbDeviceConnection;
import android.os.Looper;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.NonNull;

import com.autoai.welink.sdk.BT.BtConnectionManager;
import com.autoai.avslinkhid.hidtouchscreen.HidScreenConnectManager;
import com.autoai.avslinkhid.hidtouchscreen.HidScreenConstantsKt;
import com.autoai.common.util.LogUtil;
import com.autoai.welink.macro.WL_API_DEVICE_TYPE;
import com.autoai.welink.param.HidTouch;

import java.util.Set;

/**
 * <AUTHOR>
 * 整体hid功能入口
 */
public class HidUtil {
    public static final String TAG =  HidScreenConstantsKt.HidScreen + "HidUtil";
    private static final String TAG_IOSHID = "IOSHID";
    private static HidApi mHidApi;
    public static Context mcontext;
    /**
     * 连接类型{@link com.autoai.welink.macro.WL_API_DEVICE_TYPE}
     */
    public static int deviceType = 0;
    private static UsbDeviceConnection connection;

    /**
     * 初始化hid
     * @param context
     */
    public static void init(@NonNull Context context) {
        mcontext = context;
        HidScreenConnectManager.getInstance().init(context);
    }

    /**
     * 将设备类型转换为字符串描述
     * @param deviceType 设备类型的整数值
     * @return 设备类型的字符串描述
     */
    public static String deviceTypeToString(int deviceType) {
        switch (deviceType) {
            case WL_API_DEVICE_TYPE.OUT:
                return "OUT (Device plug out)";
            case WL_API_DEVICE_TYPE.AOA:
                return "AOA (Device android AOA)";
            case WL_API_DEVICE_TYPE.IDB:
                return "IDB (Device iphone IDB)";
            case WL_API_DEVICE_TYPE.WLAN_IPHONE:
                return "WLAN_IPHONE (Device wlan iphone)";
            case WL_API_DEVICE_TYPE.WLAN_ANDROID:
                return "WLAN_ANDROID (Device wlan android)";
            case WL_API_DEVICE_TYPE.EAP:
                return "EAP (Device iphone EAP)";
            case WL_API_DEVICE_TYPE.WLAN_HARMONY:
                return "WLAN_HARMONY (Device phone HARMONY)";
            default:
                return "UNKNOWN (" + deviceType + ")";
        }
    }

    /**
     * 注册hid
     * @param deviceType WL_API_DEVICE_TYPE 4 IOS无线, 5 安卓无线, 6 安卓有线
     * @param width 宽 仅安卓有线使用该参数
     * @param height 高 仅安卓有线使用该参数
     * @return true or false
     */
    public static boolean registerHID(int deviceType, int width, int height) {
        LogUtil.d(TAG + TAG_IOSHID, "HidUtil::registerHID deviceType=" + deviceTypeToString(deviceType) + ", width=" + width + ", height=" + height);

        // 标记是否需要延迟注册
        boolean needDelay = false;

        // 如果机型不一样，则 sdp 也不一致，所以先要反注册
        if (HidUtil.deviceType > 0 && HidUtil.deviceType != deviceType) {
            LogUtil.d(TAG + TAG_IOSHID, "HidUtil::registerHID device type changed, unregistering first");
            unregisterHID();
            needDelay = true; // 标记需要延迟注册
        }

        // 设置设备类型和初始化HidApi
        HidUtil.deviceType = deviceType;
        if(deviceType == WLAN_IPHONE || deviceType == EAP){
            LogUtil.d(TAG + TAG_IOSHID, "HidUtil::registerHID creating HidWlanIos for iOS device, deviceType=" + deviceTypeToString(deviceType));
            mHidApi = HidWlanIos.getInstance();
        }else if(deviceType == WLAN_ANDROID || deviceType == WLAN_HARMONY){
            mHidApi = HidWlanAndroid.getInstance();
        }else if(deviceType == AOA){
            mHidApi = HidAoa.getInstance(width,height);
            if(connection!= null) {
                mHidApi.setUsbDeviceConnection(connection);
            }
        }else{
            LogUtil.e(TAG + TAG_IOSHID, "HidUtil::registerHID ERR unsupported deviceType=" + deviceTypeToString(deviceType));
            return false;
        }

        if (mcontext == null) {
            LogUtil.e(TAG + TAG_IOSHID, "HidUtil::registerHID mcontext null ERR");
            return false;
        }

        // 设置HID操作回调
        BtConnectionManager.getInstance().setHidOperationCallback(new BtConnectionManager.HidOperationCallback() {
            @Override
            public void registerHIDAfterEnable() {
                HidUtil.registerHIDAfterEnable();
            }

            @Override
            public void unregisterHIDAfterDisabled() {
                HidUtil.unregisterHIDAfterDisabled();
            }

            @Override
            public void connectBluetoothHIDDevice(BluetoothDevice device) {
                HidUtil.connectBluetoothHIDDevice(device);
            }
        });

        // 根据是否需要延迟决定如何注册
        if (needDelay) {
            // 使用异步方式延迟500ms后再注册HID，避免阻塞主线程
            LogUtil.d(TAG + TAG_IOSHID, "HidUtil::registerHID delaying registration by 500ms due to device type change");
            final HidApi finalHidApi = mHidApi;
            final Context finalContext = mcontext;
            new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
                boolean result = finalHidApi != null && finalHidApi.registerHID(finalContext);
                LogUtil.d(TAG + TAG_IOSHID, "HidUtil::registerHID delayed registration result: " + result);
            }, 500);
            return true; // 异步注册，直接返回成功
        } else {
            // 无需延迟，直接注册
            boolean result = mHidApi != null && mHidApi.registerHID(mcontext);
            LogUtil.d(TAG + TAG_IOSHID, "HidUtil::registerHID registration result: " + result);
            return result;
        }
    }

    /**
     * 设置usb连接，仅针对安卓有线连接设置
     * @param connection UsbDeviceConnection
     * @return true or false
     */
    public static boolean setUsbDeviceConnection(UsbDeviceConnection connection){
        HidUtil.connection = connection;
        return mHidApi!=null && mHidApi.setUsbDeviceConnection(connection);
    }

    /**
     * 反控触摸事件处理
     * @param view view
     * @param motionEvent event
     * @return true or false
     */
    public static boolean onTouch(View view, MotionEvent motionEvent) {
        if(view == null || motionEvent == null){
            return false;
        }
        return mHidApi != null && mHidApi.onTouch(view, motionEvent);
    }

    /**
     * 返回键
     */
    public static void backHome() {
        if(mHidApi != null) {
            mHidApi.backHome();
        }else{
            LogUtil.e(TAG,"backHome ERR");
        }
    }

    /**
     * 反注册hid
     * @return true or false
     */
    public static boolean unregisterHID() {
        LogUtil.d(TAG, "HidUtil:unregisterHID deviceType: "  + deviceTypeToString(deviceType));
        // 设置HID操作回调
        BtConnectionManager.getInstance().setHidOperationCallback(null);
        deviceType = 0;
        return mHidApi != null && mHidApi.unregisterHID();
    }

    /**
     * 互联后反注册 HID， 不重置 deviceType (用于互联后，手动关闭蓝牙，需要重新注册 SDP）
     * 如果没有互联，则不需要注册 SDP
     * @return true
     */
    public static boolean unregisterHIDAfterDisabled() {
        LogUtil.d(TAG, "HidUtil:unregisterHIDAfterDisabled deviceType: "  + deviceTypeToString(deviceType));
        boolean bRet = false;
        if (HidUtil.deviceType == WL_API_DEVICE_TYPE.WLAN_IPHONE) {
            bRet = HidWlanIos.getInstance().unregisterHID();
        } else if (HidUtil.deviceType == WL_API_DEVICE_TYPE.WLAN_ANDROID || HidUtil.deviceType == WL_API_DEVICE_TYPE.WLAN_HARMONY) {
            bRet = HidWlanAndroid.getInstance().unregisterHID();
        }
        return bRet;
    }

    /**
     * 互联后重新注册 HID (开启蓝牙）
     * @return true
     */
    public static boolean registerHIDAfterEnable() {
        LogUtil.d(TAG, "HidUtil:registerHIDAfterEnable deviceType: "  + deviceTypeToString(deviceType));
        boolean bRet = false;
        if (HidUtil.mcontext != null) {
            if (HidUtil.deviceType == WL_API_DEVICE_TYPE.WLAN_IPHONE) {
                bRet = HidWlanIos.getInstance().registerHID(HidUtil.mcontext);
            } else if (HidUtil.deviceType == WL_API_DEVICE_TYPE.WLAN_ANDROID || HidUtil.deviceType == WL_API_DEVICE_TYPE.WLAN_HARMONY) {
                bRet = HidWlanAndroid.getInstance().registerHID(HidUtil.mcontext);
            }
        }
        else {
            LogUtil.e(TAG,"registerHIDAfterConnect HidUtil.mcontext == null");
        }
        return bRet;
    }
    



    /**
     * 当前是否已经注册 HID 服务
     * @return true:已经注册
     */
    public static boolean isRegisterHID() {
        return mHidApi != null && mHidApi.isRegisterHID();
    }

    /**
     * 获取当前的HidApi实例
     * @return 当前的HidApi实例，可能为null
     */
    public static HidApi getCurrentHidApi() {
        return mHidApi;
    }

    /**
     * HID 连接某一个设备( 用户手动从车机侧连接手机蓝牙， 需要代码手动调用连接 HID)
     */
    public static void connectBluetoothHIDDevice(BluetoothDevice device) {
        if(mHidApi != null) {
            mHidApi.connectBluetoothDevice(device);
        }else{
            LogUtil.e(TAG,"connectBluetoothDevice ERR");
        }
    }


    /**
     * 设置屏幕信息
     * @param rotation 屏幕信息
     */
    public static void setH264Coordinates(HidTouch rotation) {
        if(mHidApi != null) {
            mHidApi.setH264Coordinates(rotation);
        }else{
            LogUtil.e(TAG,"setH264Coordinates ERR");
        }
    }

    /**
     * 设置设备唯一标识，通过此标识获取设备自动同意录屏配置信息
     * @param name 设备标识 model+"_"+deviceName+"_"+deviceBrand 组合而成
     * @param deviceAddress 设备蓝牙名称，用于判断当前互联的设备是否是已经配对的设备
     */
    public static void setName(String name, String deviceAddress) {
        if(mHidApi != null) {
            mHidApi.setName(name, deviceAddress);
        }else{
            LogUtil.e(TAG,"setBluetoothName ERR");
        }
    }

    /**
     * 自动同意录屏幕
     * @param delay 延迟点击时间
     */
    public static void autoAgree(int delay) {
        if(mHidApi != null) {
            mHidApi.autoAgree(delay);
        }else{
            LogUtil.e(TAG,"autoAgree ERR");
        }
    }



    /**
     * 获取已连接的蓝牙设备集合
     * @return 已连接的蓝牙设备集合
     */
    public static Set<BluetoothDevice> getConnectedBluetoothDevices() {
        return BtConnectionManager.getInstance().getConnectedBluetoothDevices();
    }





    public static void updateBTPairedConnected(boolean connected) {
        HidScreenConnectManager.getInstance().updateBTPairedConnected(connected);
    }



    /**
     * 设置手机锁屏超时时间，用于动态调整防锁屏心跳间隔
     * @param timeoutMs 手机锁屏超时时间（毫秒）
     */
    public static void setScreenTimeout(int timeoutMs) {
        LogUtil.d(TAG, "setScreenTimeout: " + timeoutMs + "ms, deviceType: " + deviceTypeToString(deviceType));

        if (mHidApi != null) {
            mHidApi.setScreenTimeout(timeoutMs);
        } else {
            LogUtil.e(TAG, "setScreenTimeout: mHidApi is null");
        }
    }

    /**
     * 获取当前线程的堆栈跟踪信息
     * @param appendMainThreadStackTraces 是否附加主线程的堆栈跟踪信息
     * @return 堆栈跟踪信息字符串
     */
    public static String getStackTraces(boolean appendMainThreadStackTraces) {
        StringBuilder sb = new StringBuilder();
        Thread t = Thread.currentThread();
        sb.append(t.toString()).append('\n');
        StackTraceElement[] stacks = t.getStackTrace();
        for (int i = 4; i < stacks.length; i++) {
            sb.append("\tat ").append(stacks[i].toString()).append('\n');
        }
        sb.append('\n');

        if (appendMainThreadStackTraces && t != Looper.getMainLooper().getThread()) {
            t = Looper.getMainLooper().getThread();
            sb.append(t.toString()).append('\n');
            for (StackTraceElement ste : t.getStackTrace()) {
                sb.append("\tat ").append(ste.toString()).append('\n');
            }
            sb.append('\n');
        }
        return sb.toString();
    }

}
