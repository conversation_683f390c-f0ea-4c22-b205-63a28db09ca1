package com.autoai.avslinkhid.calibration;

import android.util.Pair;

import com.autoai.avslinkhid.model.MyPoint;
import com.autoai.avslinkhid.util.SpUtil;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.HashSet;

import kotlin.Triple;

/**
 * 三段线性校准策略
 */
public final class LinearCalibrationStrategy extends BaseCalibrationStrategy {


    /**
     * 构造函数
     * @param sp SharedPreferences工具类，用于保存和读取校准参数
     */
    public LinearCalibrationStrategy(SpUtil sp) {
        this.sp = sp;
        clearCalibrationParams();
    }

    @Override
    public List<CalibrationPoint> getCalibrationPoints() {
        return super.getCalibrationPoints();
    }

    @Override
    public int parseCalibrationPoints(List<MyPoint> points) {
        clearCalibrationParams();
        checkMyPoint.addAll(points);
        return checkPointV2() ? 1 : 0;
    }

    /**
     * 通过手机侧期望移动的位移像素，查询计算 hid 发送数据
     * @param expectMovePixel 期望移动的像素值
     * @return Pair<发送的hid数据, 实际移动的像素值, 是否移动到位>
     */
    @Override
    public Triple<Integer, Float, Boolean> queryHidSendData(float expectMovePixel, boolean bTouchMoving) {
        return super.queryHidSendData(expectMovePixel, bTouchMoving);
    }


    @Override
    public int getMaxStepsHidUnit() {
        return MAX_STEP;
    }

    @Override
    public void clearCalibrationParams() {
        checkMyPoint.clear();
        indexToYArrayForHid = null;
    }

    @Override
    public boolean isCalibrationReady() {
        return indexToYArrayForHid != null && indexToYArrayForHid.length > 0;
    }

    /**
     * 单次移动手机侧最大移动像素
     * 返回数组中的最大值，如果数组为空则返回默认值
     */
    @Override
    public float getMaxMovePixel() {
        if (indexToYArrayForHid != null && indexToYArrayForHid.length > 0) {
            float maxValue = 0;
            for (float value : indexToYArrayForHid) {
                if (value > maxValue) {
                    maxValue = value;
                }
            }
            return maxValue;
        }
        // 如果数组为空，返回默认值
        return 157.14f; // 默认最大步长值
    }

    /**
     * 新的反控校准逻辑
     * 需要检查如下要素：
     * 1、点的数目，理论不会丢失。
     * @return 是否校验通过
     */
    public boolean checkPointV2() {
        // 准备工作，将 checkMyPoint 中的点（x，y），倒序。 并且当前值-前一个值（第一个是 0） 取绝对值，整理成新的数组列表
        List<Float> yList = prepareYList();
        
        // 检查所有点都是递增的
        if (!isStrictlyIncreasing(yList)) {
            printLog("checkPointV2: yList 不是严格递增的");
            return false;
        }

//        // 用于测试的固定值
//        yList = new ArrayList<>(Arrays.asList(
//                5.0f, 11.660034f, 18.0f, 24.339966f, 30.669983f, 41.0f, 52.0f,
//                62.669983f, 73.339966f, 84.339966f, 95.339966f, 106.339966f,
//                117.339966f, 128.33997f, 136.33997f, 142.66998f, 148.66998f, 154.66998f,
//                161.0f, 167.33002f
//        ));

        // 控制台打印输出 yList
        printLog("yList = " + yList.size() + "  " + yList);

        if (yList.isEmpty()) {
            printLogE("checkPointV2: yList null or empty。");
            return false;
        }
        // iPhone 14 测试数据
        // [3.0, 8.0, 13.0, 18.0, 23.0, 34.0, 46.330017, 58.660034, 70.660034, 83.0, 95.339966, 107.66998, 120.0, 132.0, 139.33002, 144.33002, 149.0, 153.66998, 158.66998, 163.66998]
        // iPhone 15 测试数据
        //  [5.0, 11.660034, 18.0, 24.339966, 30.669983, 41.0, 52.0, 62.669983, 73.339966, 84.339966, 95.339966, 106.339966, 117.339966, 128.33997, 136.33997, 142.66998, 148.66998, 154.66998, 161.0, 167.33002]

        if (yStepsList.size() != yList.size()) {
            printLogE("checkPointV2: yStepsList size (" + yStepsList.size() + ")  yList size  (" + yList.size() + ") not match");
            return false;
        }

        // 获取目标点列表
        List<MyPoint> targetPoints = getTargetPoints(yList, yStepsList);
        if (targetPoints == null) {
            return false;
        }

        // 计算线段列表 - 只计算一次，后续重用
        List<EffectiveSegment> effectiveSegments = calculateEffectiveSegments(targetPoints);

        if (effectiveSegments.isEmpty()) {
            printLog("No EffectiveSegments calculated.");
            return false;
        }

        // 计算并存储相邻线段之间的交点
        calculateIntersectionPoints(effectiveSegments);

        // 打印更新后的线段信息（包含交点）
        printSegmentsInfo(effectiveSegments);

//        int inputIndex = 45;
//        float yValue = calculateYByLinearFunction(yList, yStepsList, inputIndex, effectiveSegments);
//        // 查找并打印inputIndex对应的yList值
//        int indexInYSteps = yStepsList.indexOf(inputIndex);
//        if (indexInYSteps != -1) {
//            float originalValue = yList.get(indexInYSteps);
//            float diff = yValue - originalValue;
//            printLog("index: " + inputIndex + " 原始值: " + originalValue + " 计算值: " + yValue + " 差值: " + diff);
//        } else {
//            printLog("index: " + inputIndex + " 计算值: " + yValue + " (原始值在yStepsList中未找到进行比较)");
//        }

        // 初始化索引到Y值的映射数组
        float[] indexToYArray = initializeIndexToYArrayForHid(yList, yStepsList, effectiveSegments);
        printLog("indexToYArrayForHid " + Arrays.toString(indexToYArray));

        // 检查数组中的值是否严格递增（确保校准曲线单调性）
        if (!isArrayStrictlyIncreasing(indexToYArray)) {
            printLog("checkPointV2: indexToYArrayForHid not increasing");
            indexToYArrayForHid = null;
            return false;
        }

        indexToYArrayForHid = indexToYArray;
        // 检验点规则。 使用算法挨个计算点规则，是否有偏差较远的点。
        return indexToYArrayForHid.length > yList.size();
    }

    /**
     * 获取目标点列表
     * @param yList Y值列表
     * @param yStepsList Y步长列表
     * @return 目标点列表，如果出错则返回null
     */
    private List<MyPoint> getTargetPoints(List<Float> yList, List<Integer> yStepsList) {
        // 定义目标点列表
        List<MyPoint> targetPoints = new ArrayList<>();

        // 确保两个列表长度相同
        if (yList.size() != yStepsList.size()) {
            printLogE("getTargetPoints: yList size (" + yList.size() + ") and yStepsList size (" + yStepsList.size() + ") not match");
            return null;
        }

        // 遍历 yStepsList，使用其值作为 x，对应索引的 yList 值作为 y
        for (int i = 0; i < yStepsList.size(); i++) {
            int x = yStepsList.get(i);
            float y = yList.get(i);
            targetPoints.add(new MyPoint(x, y));
        }

        if (targetPoints.isEmpty()) {
            printLogE("getTargetPoints: targetPoints is empty");
            return null;
        }
        
        return targetPoints;
    }
    
    /**
     * 打印线段信息
     * @param effectiveSegments 有效线段列表
     */
    private void printSegmentsInfo(List<EffectiveSegment> effectiveSegments) {
        printLog("更新后的线段信息（含交点）: ");
        for (int i = 0; i < effectiveSegments.size(); i++) {
            printLog("线段 " + i + ": " + effectiveSegments.get(i).toString());
        }
    }
    
    /**
     * 检查数组是否严格递增
     * @param array 要检查的数组
     * @return 如果数组严格递增则返回true，否则返回false
     */
    private boolean isArrayStrictlyIncreasing(float[] array) {
        List<Float> list = new ArrayList<>();
        for (float value : array) {
            list.add(value);
        }
        return isStrictlyIncreasing(list);
    }

    /**
     * 计算有效线段列表
     * @param targetPoints 目标点列表
     * @return 有效线段列表
     */
    private List<EffectiveSegment> calculateEffectiveSegments(List<MyPoint> targetPoints) {
        List<EffectiveSegment> effectiveSegments = new ArrayList<>();

        // 斜率误差不超过 0.2f
        float slopeTolerance = 0.2f;

        // 如果点数太少，直接返回一个线段
        if (targetPoints.size() < 3) {
            if (targetPoints.size() >= 2) {
                List<MyPoint> allPoints = new ArrayList<>(targetPoints);
                float slope = calculateSlope(allPoints.get(0), allPoints.get(allPoints.size() - 1));
                float intercept = calculateIntercept(allPoints.get(0), slope);
                effectiveSegments.add(new EffectiveSegment(allPoints, slope, intercept));
            }
            return effectiveSegments;
        }

        // 第一步：计算每个相邻点之间的斜率
        float[] slopes = new float[targetPoints.size() - 1];
        for (int i = 0; i < targetPoints.size() - 1; i++) {
            MyPoint p1 = targetPoints.get(i);
            MyPoint p2 = targetPoints.get(i + 1);
            slopes[i] = calculateSlope(p1, p2);
            printLog("segment : [" + i + " -> " + (i + 1) + "] slope: " + slopes[i] + " p1: " + p1 + " p2:" + p2);
        }

        // 第二步：计算相邻斜率之间的差异
        float[] slopeDifferences = new float[slopes.length - 1];
        for (int i = 0; i < slopes.length - 1; i++) {
            slopeDifferences[i] = Math.abs(slopes[i] - slopes[i + 1]);
            printLog("slope difference [" + i + " -> " + (i + 1) + "]: " + slopeDifferences[i]);
        }

        // 第三步：找出斜率差异最大的几个点作为分割点
        List<Integer> splitIndices = findSplitIndices(slopeDifferences, targetPoints.size());

        // 根据分割点创建线段
        List<List<MyPoint>> segments = splitPointsIntoSegments(targetPoints, splitIndices);

        // 为每个分段创建EffectiveSegment对象
        for (List<MyPoint> segment : segments) {
            if (segment.size() >= 2) {
                // 检查段内小段斜率差异是否都在容差范围内
                if (!checkIntraSegmentSlopeDifferences(segment, slopeTolerance)) {
                    printLog("段内小段斜率差异超过容差 " + slopeTolerance + "，不符合条件");
                    effectiveSegments.clear();
                    return effectiveSegments;
                }

                // 计算整个段的平均斜率
                float avgSlope = calculateAverageSlope(segment);
                float intercept = calculateIntercept(segment.get(0), avgSlope);
                    effectiveSegments.add(new EffectiveSegment(new ArrayList<>(segment), avgSlope, intercept));
                    printLog("Created segment with " + segment.size() + " points, slope: " + avgSlope);
            }
        }

        // 打印当前的线段信息
        printLog("当前的线段信息: ");
        for (EffectiveSegment seg : effectiveSegments) {
            printLog(seg.toString());
        }

        // 计算并存储相邻线段之间的交点
        calculateIntersectionPoints(effectiveSegments);
        return effectiveSegments;
    }

    /**
     * 计算一组点的平均斜率
     * @param points 点列表
     * @return 平均斜率
     */
    private float calculateAverageSlope(List<MyPoint> points) {
        if (points.size() < 2) {
            return 0;
        }

        // 使用首尾两点计算整体斜率
        return calculateSlope(points.get(0), points.get(points.size() - 1));
    }

    /**
     * 检查段内小段斜率差异是否都在容差范围内
     * @param segment 要检查的段
     * @param tolerance 容差值
     * @return 如果所有小段斜率差异都在容差范围内，返回true；否则返回false
     */
    private boolean checkIntraSegmentSlopeDifferences(List<MyPoint> segment, float tolerance) {
        if (segment.size() < 3) {
            // 少于3个点无法形成多个小段，直接返回true
            return true;
        }

        // 计算每个相邻点之间的斜率
        float[] slopes = new float[segment.size() - 1];
        for (int i = 0; i < segment.size() - 1; i++) {
            slopes[i] = calculateSlope(segment.get(i), segment.get(i + 1));
        }

        // 检查相邻小段之间的斜率差异是否都在容差范围内
        for (int i = 0; i < slopes.length - 1; i++) {
            float diff = Math.abs(slopes[i] - slopes[i + 1]);
            if (diff > tolerance) {
                printLog("相邻小段 [" + i + " -> " + (i + 1) + "] 和 [" + (i + 1) + " -> " + (i + 2) +
                         "] 斜率差异 " + diff + " 超过容差 " + tolerance);
                return false;
            }
        }

        printLog("段内所有小段斜率差异都在容差 " + tolerance + " 范围内");
        return true;
    }

    /**
     * 找出斜率差异最大的几个点作为分割点
     * @param slopeDifferences 斜率差异数组
     * @param totalPoints 总点数
     * @return 分割点索引列表
     */
    private List<Integer> findSplitIndices(float[] slopeDifferences, int totalPoints) {
        // 如果斜率差异数组为空，返回空列表
        if (slopeDifferences == null || slopeDifferences.length == 0) {
            return new ArrayList<>();
        }

        // 创建索引和差异值的对应关系
        List<Pair<Integer, Float>> indexDifferencePairs = new ArrayList<>();
        for (int i = 0; i < slopeDifferences.length; i++) {
            // 排除第一段和第二段之间的斜率差异（索引0）和倒数第二段与最后一段之间的斜率差异（最后一个索引）
            if (i == 0 || i == slopeDifferences.length - 1) {
                // 跳过第一个和最后一个斜率差异
                printLog("排除斜率差异点: " + (i) + " 值: " + slopeDifferences[i]);
                continue;
            }
            indexDifferencePairs.add(new Pair<>(i, slopeDifferences[i])); // i+1 因为分割点是在两个点之间
        }

        // 按斜率差异从大到小排序
        Collections.sort(indexDifferencePairs, (p1, p2) -> Float.compare(p2.second, p1.second));

        // 选择最多两个分割点（最多形成三段），并确保选择的点之间不相邻
        List<Integer> splitIndices = new ArrayList<>();
        int maxSplits = Math.min(2, indexDifferencePairs.size());
        
        // 已选择的索引集合，用于记录已选和需要排除的索引
        Set<Integer> selectedOrExcludedIndices = new HashSet<>();

        // 后交点=(25.720213,54.83412)]
        // 前交点=(71.645386,230.77344)]
        for (int i = 0; i < indexDifferencePairs.size() && splitIndices.size() < maxSplits; i++) {
            int currentIndex = indexDifferencePairs.get(i).first;
            // currentIndex 表示当前是第几段线段断开的
            // 由于一段折线通常会影响两段线段的斜率变化，所以当前位置折线选中后，周边的索引需要排除
            if (!selectedOrExcludedIndices.contains(currentIndex)) {
                double preSlopeChanged = Math.abs(slopeDifferences[currentIndex - 1]) + Math.abs(slopeDifferences[currentIndex]);
                double nextSlopeChanged = Math.abs(slopeDifferences[currentIndex + 1]) + Math.abs(slopeDifferences[currentIndex]);
                if (preSlopeChanged > nextSlopeChanged) {
                    splitIndices.add(currentIndex);
                }
                else {
                    splitIndices.add(currentIndex + 1);
                }

                // 将当前索引及其相邻索引添加到排除集合
                selectedOrExcludedIndices.add(currentIndex);
                selectedOrExcludedIndices.add(currentIndex - 1);
                selectedOrExcludedIndices.add(currentIndex + 1);
                
                printLog("选择分割点: " + currentIndex + "，排除相邻点: " + (currentIndex-1) + ", " + (currentIndex+1));
            }
        }

        // 按索引从小到大排序，确保分割点顺序正确
        Collections.sort(splitIndices);

        printLog("选择的分割点索引: " + splitIndices);
        return splitIndices;
    }


    /**
     * 根据分割点将点列表分割成多个段，分割点归到前一个线段
     * @param points 点列表
     * @param splitIndices 分割点索引列表
     * @return 分割后的段列表
     */
    private List<List<MyPoint>> splitPointsIntoSegments(List<MyPoint> points, List<Integer> splitIndices) {
        List<List<MyPoint>> segments = new ArrayList<>();

        if (splitIndices == null || splitIndices.isEmpty()) {
            // 如果没有分割点，整个点列表作为一个段
            segments.add(new ArrayList<>(points));
            printLog("没有分割点，添加整个点列表作为一个段: 0 到 " + (points.size() - 1));
            return segments;
        }

        // 使用一个for循环解决，将分割点归到前一个线段
        int startIdx = 0;
        for (int i = 0; i < splitIndices.size(); i++) {
            int endIdx = splitIndices.get(i);

            // 确保索引有效
            if (endIdx >= points.size()) {
                endIdx = points.size() - 1;
            }

            // 确保当前段有效（起始索引不大于结束索引）
            if (startIdx <= endIdx) {
                List<MyPoint> segment = new ArrayList<>();
                for (int j = startIdx; j <= endIdx; j++) {
                    segment.add(points.get(j));
                }

                segments.add(segment);
                printLog("添加第" + (i + 1) + "段: " + startIdx + " 到 " + endIdx);
            }

            // 更新下一段的起始索引为当前分割点之后的点
            startIdx = endIdx + 1;
        }

        // 处理最后一段（如果有）
        if (startIdx < points.size()) {
            List<MyPoint> lastSegment = new ArrayList<>();
            for (int j = startIdx; j < points.size(); j++) {
                lastSegment.add(points.get(j));
            }

            segments.add(lastSegment);
            printLog("添加最后一段: " + startIdx + " 到 " + (points.size() - 1));
        }

        return segments;
    }

    /**
     * 计算线段间的交点
     * @param effectiveSegments 有效线段列表
     */
    private void calculateIntersectionPoints(List<EffectiveSegment> effectiveSegments) {
        // 计算并存储相邻线段之间的交点
        for (int i = 0; i < effectiveSegments.size() - 1; i++) {
            EffectiveSegment currentSeg = effectiveSegments.get(i);
            EffectiveSegment nextSeg = effectiveSegments.get(i + 1);

            // 计算交点
            MyPoint intersection = calculateIntersection(currentSeg, nextSeg, 0.001f);
            if (intersection != null) {
                // 设置当前线段的后交点
                currentSeg.setNextIntersection(intersection);
                // 设置下一个线段的前交点
                nextSeg.setPrevIntersection(intersection);

                printLog("计算交点: 线段 " + i + " 和 线段 " + (i+1) + " 的交点: (" +
                        intersection.getX() + ", " + intersection.getY() + ")");
            } else {
                printLog("线段 " + i + " 和 线段 " + (i+1) + " 平行，没有交点");
            }
        }
    }

    /**
     * 三段式线性方程，根据输入索引点 index，计算对应手机侧屏幕移动量的 y 值。 （根据手机侧设置，可能两段，也可能一段）
     * @param yList 手机侧屏幕移动量列表
     * @param yStepsList 预定义的X轴步长列表
     * @param inputIndex 索引点
     * @param effectiveSegments 预计算的有效线段列表
     * @return 手机侧屏幕移动量
     */
    private float calculateYByLinearFunction(List<Float> yList, List<Integer> yStepsList, int inputIndex, List<EffectiveSegment> effectiveSegments) {
        //printLog("calculateYByLinearFunction: input Size =" + inputIndex  + " yListSize: " + yList.size() + " yStepsListSize:" + yList.size());

        if (yList == null || yStepsList == null || yList.size() != yStepsList.size() || yList.isEmpty()) {
            //printLog("calculateYByLinearFunction Error: " + (yList != null ? yList.size() : "null") + ", yStepsList Size:: " + (yStepsList != null ? yStepsList.size() : "null"));
            return -1.0f;
        }

        if (effectiveSegments == null || effectiveSegments.isEmpty()) {
            //printLog("calculateYByLinearFunction: effectiveSegments is null or empty");
            return -1.0f;
        }

        // 检查inputIndex是否与任何交点的X值匹配
        for (int i = 0; i < effectiveSegments.size(); i++) {
            EffectiveSegment seg = effectiveSegments.get(i);

            // 检查前交点
            if (seg.prevIntersection != null && Math.abs(inputIndex - seg.prevIntersection.getX()) < 1e-6f) {
                printLog("inputIndex: " + inputIndex + " range " + i + " prevIntersection: " + seg.prevIntersection.getY());
                return seg.prevIntersection.getY();
            }

            // 检查后交点
            if (seg.nextIntersection != null && Math.abs(inputIndex - seg.nextIntersection.getX()) < 1e-6f) {
                printLog("inputIndex: " + inputIndex + " range " + i + " nextIntersection: " + seg.nextIntersection.getY());
                return seg.nextIntersection.getY();
            }
        }

        // 内侧，输入点在第一个线段之前
        EffectiveSegment firstSegment = effectiveSegments.get(0);
        if (inputIndex < firstSegment.startPoint.getX()) {
            printLog("inputIndex: " + inputIndex + " 在第一个线段之前，使用第一个线段外推");
            return firstSegment.calculateY(inputIndex);
        }

        // 外侧，输入点在最后一个线段之后
        EffectiveSegment lastSegment = effectiveSegments.get(effectiveSegments.size() - 1);
        if (inputIndex > lastSegment.endPoint.getX()) {
            printLog("inputIndex: " + inputIndex + " 在最后一个线段之后，使用最后一个线段外推");
            return lastSegment.calculateY(inputIndex);
        }

        // 内插：查找包含输入点的有效线段
        for (int i = 0; i < effectiveSegments.size(); i++) {
            EffectiveSegment seg = effectiveSegments.get(i);

            // 检查输入点是否在线段的有效范围内（考虑交点）
            if (seg.coversEffectiveX(inputIndex)) {
                //printLog("inputIndex: " + inputIndex + " segIdx: " + i + " range ");
                return seg.calculateY(inputIndex);
            } else {
                //printLog("inputIndex: " + inputIndex + " segIdx: " + i + " not range");
            }
        }

        printLog("calculateYByLinearFunction: no result inputIndex: " + inputIndex + " return -1.0f");
        return -1.0f;
    }


    private static float calculateIntercept(MyPoint p, float slope) {
        if (Float.isInfinite(slope)) {
            return Float.NaN; // Intercept is not well-defined for vertical lines
        }
        return p.getY() - slope * p.getX();
    }

    // 用于存储有效线性段的辅助类
    public static class EffectiveSegment {
        MyPoint startPoint;  // 起始点
        MyPoint endPoint;    // 终点
        List<MyPoint> constituentPoints;  // 构成该线段的所有点
        float slope;         // 斜率
        float intercept;     // 截距
        boolean isVertical;  // 是否为垂直线段

        // 存储与前一个线段的交点
        MyPoint prevIntersection = null;
        // 存储与后一个线段的交点
        MyPoint nextIntersection = null;

        // 用于指示交点是否已计算
        boolean prevIntersectionCalculated = false;
        boolean nextIntersectionCalculated = false;

        EffectiveSegment(List<MyPoint> points, float slope, float intercept) {
            if (points == null || points.isEmpty()) {
                throw new IllegalArgumentException("构成点列表不能为空。");
            }
            this.constituentPoints = new ArrayList<>(points);
            this.startPoint = points.get(0);
            this.endPoint = points.get(points.size() - 1);
            this.slope = slope;
            this.intercept = intercept;
            this.isVertical = Float.isInfinite(slope);
        }

        // 设置与前一个线段的交点
        void setPrevIntersection(MyPoint point) {
            this.prevIntersection = point;
            this.prevIntersectionCalculated = true;
        }

        // 设置与后一个线段的交点
        void setNextIntersection(MyPoint point) {
            this.nextIntersection = point;
            this.nextIntersectionCalculated = true;
        }

        // 获取该线段的有效起始点X坐标（考虑交点）
        float getEffectiveStartX() {
            // 如果有与前一个线段的交点，则使用交点的X坐标
            // 否则使用线段自身的起始点X坐标
            return prevIntersection != null ? prevIntersection.getX() : startPoint.getX();
        }

        // 获取该线段的有效终止点X坐标（考虑交点）
        float getEffectiveEndX() {
            // 如果有与后一个线段的交点，则使用交点的X坐标
            // 否则使用线段自身的终止点X坐标
            return nextIntersection != null ? nextIntersection.getX() : endPoint.getX();
        }

        // 检查点是否在该线段的有效范围内（考虑交点）
        boolean coversEffectiveX(float x) {
            float effectiveStartX = getEffectiveStartX();
            float effectiveEndX = getEffectiveEndX();

            // 确保范围正确，交点可能会导致顺序颠倒
            float minX = Math.min(effectiveStartX, effectiveEndX);
            float maxX = Math.max(effectiveStartX, effectiveEndX);

            return x >= minX - 1e-6f && x <= maxX + 1e-6f;
        }

        float calculateY(float x) {
            if (isVertical) {
                return (Math.abs(x - startPoint.getX()) < 1e-6f) ? startPoint.getY() : Float.NaN;
            }
            return slope * x + intercept;
        }

        boolean coversX(float x) {
            // 检查x是否在[startPoint.x, endPoint.x]范围内（包含边界）
            // 确保startPoint.x <= endPoint.x用于比较，尽管它们应该从targetPoints中就已经排序
            float minX = Math.min(startPoint.getX(), endPoint.getX());
            float maxX = Math.max(startPoint.getX(), endPoint.getX());
            return x >= minX - 1e-6f && x <= maxX + 1e-6f;
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("线段 [(").append(startPoint.getX()).append(",").append(startPoint.getY()).append(") 到 (")
                    .append(endPoint.getX()).append(",").append(endPoint.getY()).append("), 斜率=").append(slope)
                    .append(", 截距=").append(intercept).append(", 点数=").append(constituentPoints.size());

            if (prevIntersection != null) {
                sb.append(", 前交点=(").append(prevIntersection.getX()).append(",").append(prevIntersection.getY()).append(")");
            }

            if (nextIntersection != null) {
                sb.append(", 后交点=(").append(nextIntersection.getX()).append(",").append(nextIntersection.getY()).append(")");
            }

            sb.append("]");
            return sb.toString();
        }
    }

    // 计算两点间斜率的辅助方法
    private float calculateSlope(MyPoint p1, MyPoint p2) {
        if (Math.abs(p2.getX() - p1.getX()) < 1e-9f) { // 接近垂直
            if (p2.getY() > p1.getY()) return Float.POSITIVE_INFINITY;
            if (p2.getY() < p1.getY()) return Float.NEGATIVE_INFINITY;
            return 0; // 点重合，斜率未定义但返回0表示无变化
        }
        return (p2.getY() - p1.getY()) / (p2.getX() - p1.getX());
    }

    // 检查两个斜率是否实际相等的辅助方法
    private boolean areSlopesEqual(float s1, float s2, float tolerance) {
        if (Float.isInfinite(s1) && Float.isInfinite(s2)) {
            return Math.signum(s1) == Math.signum(s2); // 都是+无穷或都是-无穷
        }
        if (Float.isInfinite(s1) || Float.isInfinite(s2)) {
            return false; // 一个是无穷，另一个不是
        }
        return Math.abs(s1 - s2) < tolerance;
    }

    /**
     * 计算两个线段的交点
     * @param seg1
     * @param seg2
     * @param tolerance
     * @return
     */
    private MyPoint calculateIntersection(EffectiveSegment seg1, EffectiveSegment seg2, float tolerance) {
        if (seg1.isVertical && seg2.isVertical) {
            printLog("calculateIntersection: Both segments are vertical and parallel.");
            return null;
        } else if (seg1.isVertical) {
            // Seg1 is vertical, Seg2 is not
            float xIntersect = seg1.startPoint.getX();
            float yIntersect = seg2.calculateY(xIntersect);
            printLog("calculateIntersection: Seg1 vertical. Intersection at (" + xIntersect + ", " + yIntersect + ")");
            return new MyPoint(xIntersect, yIntersect);
        } else if (seg2.isVertical) {
            // Seg2 is vertical, Seg1 is not
            float xIntersect = seg2.startPoint.getX();
            float yIntersect = seg1.calculateY(xIntersect);
            printLog("calculateIntersection: Seg2 vertical. Intersection at (" + xIntersect + ", " + yIntersect + ")");
            return new MyPoint(xIntersect, yIntersect);
        }

        // 平行
        if (areSlopesEqual(seg1.slope, seg2.slope, tolerance)) {
            // Slopes are equal, lines are parallel
            printLog("calculateIntersection: Segments are parallel (slopes: " + seg1.slope + ", " + seg2.slope + "), no intersection.");
            return null;
        }

        // Calculate intersection for non-parallel, non-vertical lines
        // x = (b2 - b1) / (k1 - k2)
        float xIntersect = (seg2.intercept - seg1.intercept) / (seg1.slope - seg2.slope);
        // y = k1 * x + b1
        float yIntersect = seg1.slope * xIntersect + seg1.intercept;
        printLog("calculateIntersection: Intersection at (" + xIntersect + ", " + yIntersect + ")");
        return new MyPoint(xIntersect, yIntersect);
    }




    /**
     * 初始化索引到Y值的映射数组
     * @param yList 手机侧屏幕移动量列表
     * @param yStepsList 预定义的X轴步长列表
     * @param effectiveSegments 预计算的有效线段列表
     * @return 索引到Y值的映射数组
     */
    private float[] initializeIndexToYArrayForHid(List<Float> yList, List<Integer> yStepsList, List<EffectiveSegment> effectiveSegments) {
        // 确保 yStepsList 非空
        if (yStepsList == null || yStepsList.isEmpty()) {
            printLog("initializeIndexToYArrayForHid: yStepsList 为空或 null");
            return new float[0];
        }

        // 找出 yStepsList 中的最大值
        int maxIndex = Collections.max(yStepsList);
        printLog("initializeIndexToYArrayForHid: 计算从 0 到 " + maxIndex + " 的所有索引值");

        // 创建一个数组来存储所有索引对应的Y值
        float[] indexToYArray = new float[maxIndex + 1];

        // 计算从 0 到 maxIndex 的所有 Y 值
        for (int idx = 0; idx <= maxIndex; idx++) {
            float yValue = calculateYByLinearFunction(yList, yStepsList, idx, effectiveSegments);
            indexToYArray[idx] = yValue;
        }

        return indexToYArray;
    }

}
