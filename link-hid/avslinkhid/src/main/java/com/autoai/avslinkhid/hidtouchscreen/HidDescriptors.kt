package com.autoai.avslinkhid.hidtouchscreen

val DESCRIPTOR_FORMAT = byteArrayOf(

    // Consumer Control Collection - 消费者控制协议，支持AC HOME等按键
    0x05.toByte(), 0x0C.toByte(),  // Usage Page (Consumer)
    0x09.toByte(), 0x01.toByte(),  // Usage (Consumer Control)
    0xA1.toByte(), 0x01.toByte(),  // Collection (Application)

    0x85.toByte(), 0x02.toByte(),  // Report ID (2) - 消费者控制专用报告ID

    0x05.toByte(), 0x0C.toByte(),  // Usage Page (Consumer)
    0x15.toByte(), 0x00.toByte(),  // Logical Minimum (0)
    0x26.toByte(), 0xFF.toByte(), 0x03.toByte(),  // Logical Maximum (1023)
    0x19.toByte(), 0x00.toByte(),  // Usage Minimum (0)
    0x2A.toByte(), 0xFF.toByte(), 0x03.toByte(),  // Usage Maximum (1023)
    0x75.toByte(), 0x10.toByte(),  // Report Size (16)
    0x95.toByte(), 0x01.toByte(),  // Report Count (1)
    0x81.toByte(), 0x00.toByte(),  // Input (Data, Array)

    0xC0.toByte(),  // End Collection (Application)


    0x05.toByte(), 0x0D.toByte(),  // Usage Page (Digitizer)
    0x09.toByte(), 0x04.toByte(),  // Usage (Touch Screen)
    0xA1.toByte(), 0x01.toByte(),  // Collection (Application)

    0x85.toByte(), 0x01.toByte(),  // Report ID (1)
    0x09.toByte(), 0x22.toByte(),  // Usage (Finger)
    0xA1.toByte(), 0x00.toByte(),  // Collection (Physical)

    0x09.toByte(), 0x42.toByte(),  // Usage (Tip Switch)
    0x15.toByte(), 0x00.toByte(),  // Logical Minimum (0)
    0x25.toByte(), 0x01.toByte(),  // Logical Maximum (1)
    0x75.toByte(), 0x01.toByte(),  // Report Size (1)
    0x95.toByte(), 0x01.toByte(),  // Report Count (1)
    0x81.toByte(), 0x02.toByte(),  // Input (Data, Var, Abs)

    0x95.toByte(), 0x07.toByte(),  // Report Count (7)
    0x81.toByte(), 0x03.toByte(),  // Input (Cnst, Var, Abs)

    0x25.toByte(), 0x0A.toByte(),  // Logical Maximum (10)
    0x75.toByte(), 0x08.toByte(),  // Report Size (8)
    0x09.toByte(), 0x51.toByte(),  // Usage (Contact Identifier)
    0x95.toByte(), 0x01.toByte(),  // Report Count (1)
    0x81.toByte(), 0x02.toByte(),  // Input (Data, Var, Abs)

    0x05.toByte(), 0x01.toByte(),  // Usage Page (Generic Desktop)
    0x26.toByte(), 0x00.toByte(), 0x0F,  // Logical Maximum (0x0F00)
    0x75.toByte(), 0x10.toByte(),  // Report Size (16)
    0x95.toByte(), 0x01.toByte(),  // Report Count (1)

    0x09.toByte(), 0x30.toByte(),  // Usage (X)
    0x35.toByte(), 0x00.toByte(),  // Physical Minimum (0)
    0x46.toByte(), 0x00.toByte(), 0x00,  // Physical Maximum (65535)
    0x81.toByte(), 0x02.toByte(),  // Input (Data, Var, Abs)

    0x09.toByte(), 0x31.toByte(),  // Usage (Y)
    0x46.toByte(), 0x00.toByte(), 0x00,  // Physical Maximum (65535)
    0x81.toByte(), 0x02.toByte(),  // Input (Data, Var, Abs)

    0xC0.toByte(),  // End Collection (Physical)
    0x05.toByte(), 0x0D.toByte(),  // Usage Page (Digitizer)
    0x09.toByte(), 0x54.toByte(),  // Usage (Contact Count)
    0x95.toByte(), 0x01.toByte(),  // Report Count (1)
    0x75.toByte(), 0x08.toByte(),  // Report Size (8)
    0x15.toByte(), 0x00.toByte(),  // Logical Minimum (0)
    0x25.toByte(), 0x08.toByte(),  // Logical Maximum (8)
    0x81.toByte(), 0x02.toByte(),  // Input (Data, Var, Abs)

    0xC0.toByte() // End Collection (Application)
)
//
//val DESCRIPTOR_FORMAT1 = byteArrayOf(
//    0x05.toByte(), 0x0C.toByte(), 0x09.toByte(), 0x01.toByte(), 0xA1.toByte(), 0x01.toByte(), 0x85.toByte(), 0x01.toByte(), 0x05.toByte(), 0x0C.toByte(),
//    0x15.toByte(), 0x00.toByte(), 0x25.toByte(), 0x01.toByte(), 0x75.toByte(), 0x01.toByte(), 0x95.toByte(), 0x1C.toByte(), 0x09.toByte(), 0x40.toByte(),
//    0x09.toByte(), 0x42.toByte(), 0x09.toByte(), 0x43.toByte(), 0x09.toByte(), 0x44.toByte(), 0x09.toByte(), 0x45.toByte(), 0x09.toByte(), 0x8C.toByte(),
//    0x09.toByte(), 0xE2.toByte(), 0x09.toByte(), 0xB0.toByte(), 0x09.toByte(), 0xB5.toByte(), 0x09.toByte(), 0xB6.toByte(),
//    0x09.toByte(), 0xB7.toByte(), 0x09.toByte(), 0xCD.toByte(), 0x09.toByte(), 0xEA.toByte(), 0x09.toByte(), 0xE9.toByte(),
//    0x0A.toByte(), 0x23.toByte(), 0x02.toByte(), 0x0A.toByte(), 0x24.toByte(), 0x02.toByte(), 0x81.toByte(), 0x02.toByte(), 0xC0.toByte()
//)
//
//
//val DESCRIPTOR_FORMAT3 = byteArrayOf(
//    0x05.toByte(), 0x01.toByte(),         // 使用页 (Usage Page): 键盘/键盘指示器
//    0x09.toByte(), 0x06.toByte(),         // 使用 (Usage): 键盘
//    0xA1.toByte(), 0x01.toByte(),         // 集合 (Collection): 应用程序
//
//    0x85.toByte(), 0x01.toByte(),         // 报告ID (Report ID): 8
//    0x05.toByte(), 0x07.toByte(),         // 使用页 (Usage Page): 键盘/键盘指示器
//    0x19.toByte(), 0xE0.toByte(),         // 使用最小值 (Usage Minimum): 0xE0 (左侧控制键)
//    0x29.toByte(), 0xE7.toByte(),         // 使用最大值 (Usage Maximum): 0xE7 (右侧 GUI 键)
//    0x15.toByte(), 0x00.toByte(),         // 逻辑最小值 (Logical Minimum): 0
//    0x25.toByte(), 0x01.toByte(),         // 逻辑最大值 (Logical Maximum): 1
//
//    0x75.toByte(), 0x01.toByte(),         // 字段大小 (Report Size): 1 位
//    0x95.toByte(), 0x08.toByte(),         // 报告计数 (Report Count): 8
//    0x81.toByte(), 0x02.toByte(),         // 输入 (Input): 数据, 变量, 绝对
//
//    0x95.toByte(), 0x01.toByte(),         // 报告计数 (Report Count): 1
//    0x75.toByte(), 0x08.toByte(),         // 字段大小 (Report Size): 8 位
//    0x81.toByte(), 0x03.toByte(),         // 输入 (Input): 常量
//
//    0x95.toByte(), 0x06.toByte(),         // 报告计数 (Report Count): 6
//    0x75.toByte(), 0x08.toByte(),         // 字段大小 (Report Size): 8 位
//    0x15.toByte(), 0x00.toByte(),         // 逻辑最小值 (Logical Minimum): 0
//    0x25.toByte(), 0x65.toByte(),         // 逻辑最大值 (Logical Maximum): 101 (用于LED)
//    0x05.toByte(), 0x07.toByte(),         // 使用页 (Usage Page): 键盘/键盘指示器
//    0x19.toByte(), 0x00.toByte(),         // 使用最小值 (Usage Minimum): 0x00
//    0x29.toByte(), 0x65.toByte(),         // 使用最大值 (Usage Maximum): 0x65 (0x65是字符的最大值)
//    0x81.toByte(), 0x00.toByte(),         // 输出 (Output): 数据, 不可变, 绝对
//
//    0x05.toByte(), 0x0C.toByte(),         // 使用页 (Usage Page): 消费者
//    0x09.toByte(), 0x01.toByte(),         // 使用 (Usage): 消费者控件
//    0xA1.toByte(), 0x01.toByte(),         // 集合 (Collection): 应用程序
//
//    0x85.toByte(), 0x09.toByte(),         // 报告ID (Report ID): 9
//    0x05.toByte(), 0x0C.toByte(),         // 使用页 (Usage Page): 消费者
//    0x19.toByte(), 0x00.toByte(),         // 使用最小值 (Usage Minimum): 0x00
//    0x29.toByte(), 0xFF.toByte(),         // 使用最大值 (Usage Maximum): 0xFF
//    0x15.toByte(), 0x00.toByte(),         // 逻辑最小值 (Logical Minimum): 0
//    0x26.toByte(), 0xFF.toByte(), 0x00,   // 逻辑最大值 (Logical Maximum): 255
//    0x75.toByte(), 0x10.toByte(),         // 字段大小 (Report Size): 16 位
//    0x95.toByte(), 0x01.toByte(),         // 报告计数 (Report Count): 1
//    0x81.toByte(), 0x02.toByte(),         // 输入 (Input): 数据, 变量, 绝对
//
//    0xC0.toByte()                          // 结束集合 (End Collection)
//)
//
//val DESCRIPTOR_FORMAT5 = byteArrayOf(
//    0x05.toByte(), 0x01.toByte(),
//    0x09.toByte(), 0x06.toByte(),
//    0xA1.toByte(), 0x01.toByte(),
//    0x85.toByte(), 0x01.toByte(),
//    0x05.toByte(), 0x07.toByte(),
//    0x19.toByte(), 0xE0.toByte(),
//    0x29.toByte(), 0xE7.toByte(),
//    0x15.toByte(), 0x00.toByte(),
//    0x25.toByte(), 0x01.toByte(),
//    0x75.toByte(), 0x01.toByte(),
//    0x95.toByte(), 0x08.toByte(),
//    0x81.toByte(), 0x02.toByte(),
//    0x75.toByte(), 0x08.toByte(),
//    0x95.toByte(), 0x01.toByte(),
//    0x81.toByte(), 0x01.toByte(),
//    0x75.toByte(), 0x08.toByte(),
//    0x95.toByte(), 0x06.toByte(),
//    0x15.toByte(), 0x00.toByte(),
//    0x25.toByte(), 0x65.toByte(),
//    0x05.toByte(), 0x07.toByte(),
//    0x19.toByte(), 0x00.toByte(),
//    0x29.toByte(), 0x65.toByte(),
//    0x81.toByte(), 0x00.toByte(),
//    0xC0.toByte()
//)
