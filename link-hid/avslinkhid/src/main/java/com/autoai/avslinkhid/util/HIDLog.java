package com.autoai.avslinkhid.util;

import com.autoai.common.util.LogUtil;

/**
 * LinkHIDLogUtil 封装 log，后续方便替换日志
 */
public class HIDLog {
    // 默认TAG前缀
    private static final String DEFAULT_TAG_PREFIX = "LinkHID";

    // 是否启用日志
    private static boolean sEnabled = true;
    
    /**
     * 设置是否启用日志
     * @param enabled true启用，false禁用
     */
    public static void setEnabled(boolean enabled) {
        sEnabled = enabled;
    }
    
    /**
     * 使用多个TAG组合输出VERBOSE级别日志
     * @param tags 多个TAG，会被拼接
     * @param msg 日志消息
     */
    public static void v(String[] tags, String msg) {
        if (sEnabled) {
            LogUtil.v(buildTag(tags), msg);
        }
    }
    
    /**
     * 使用单个TAG输出VERBOSE级别日志
     * @param tag 单个TAG
     * @param msg 日志消息
     */
    public static void v(String tag, String msg) {
        if (sEnabled) {
            LogUtil.v(buildTag(tag), msg);
        }
    }
    
    /**
     * 使用多个TAG组合输出VERBOSE级别日志，带异常信息
     * @param tags 多个TAG，会被拼接
     * @param msg 日志消息
     * @param tr 异常
     */
    public static void v(String[] tags, String msg, Throwable tr) {
        if (sEnabled) {
            LogUtil.v(buildTag(tags), msg, tr);
        }
    }
    
    /**
     * 使用单个TAG输出VERBOSE级别日志，带异常信息
     * @param tag 单个TAG
     * @param msg 日志消息
     * @param tr 异常
     */
    public static void v(String tag, String msg, Throwable tr) {
        if (sEnabled) {
            LogUtil.v(buildTag(tag), msg, tr);
        }
    }
    
    /**
     * 使用多个TAG组合输出DEBUG级别日志
     * @param tags 多个TAG，会被拼接
     * @param msg 日志消息
     */
    public static void d(String[] tags, String msg) {
        if (sEnabled) {
            LogUtil.d(buildTag(tags), msg);
        }
    }
    
    /**
     * 使用单个TAG输出DEBUG级别日志
     * @param tag 单个TAG
     * @param msg 日志消息
     */
    public static void d(String tag, String msg) {
        if (sEnabled) {
            LogUtil.d(buildTag(tag), msg);
        }
    }
    
    /**
     * 使用多个TAG组合输出DEBUG级别日志，带异常信息
     * @param tags 多个TAG，会被拼接
     * @param msg 日志消息
     * @param tr 异常
     */
    public static void d(String[] tags, String msg, Throwable tr) {
        if (sEnabled) {
            LogUtil.d(buildTag(tags), msg, tr);
        }
    }
    
    /**
     * 使用单个TAG输出DEBUG级别日志，带异常信息
     * @param tag 单个TAG
     * @param msg 日志消息
     * @param tr 异常
     */
    public static void d(String tag, String msg, Throwable tr) {
        if (sEnabled) {
            LogUtil.d(buildTag(tag), msg, tr);
        }
    }
    
    /**
     * 使用多个TAG组合输出INFO级别日志
     * @param tags 多个TAG，会被拼接
     * @param msg 日志消息
     */
    public static void i(String[] tags, String msg) {
        if (sEnabled) {
            LogUtil.i(buildTag(tags), msg);
        }
    }
    
    /**
     * 使用单个TAG输出INFO级别日志
     * @param tag 单个TAG
     * @param msg 日志消息
     */
    public static void i(String tag, String msg) {
        if (sEnabled) {
            LogUtil.i(buildTag(tag), msg);
        }
    }
    
    /**
     * 使用多个TAG组合输出INFO级别日志，带异常信息
     * @param tags 多个TAG，会被拼接
     * @param msg 日志消息
     * @param tr 异常
     */
    public static void i(String[] tags, String msg, Throwable tr) {
        if (sEnabled) {
            LogUtil.i(buildTag(tags), msg, tr);
        }
    }
    
    /**
     * 使用单个TAG输出INFO级别日志，带异常信息
     * @param tag 单个TAG
     * @param msg 日志消息
     * @param tr 异常
     */
    public static void i(String tag, String msg, Throwable tr) {
        if (sEnabled) {
            LogUtil.i(buildTag(tag), msg, tr);
        }
    }
    
    /**
     * 使用多个TAG组合输出WARN级别日志
     * @param tags 多个TAG，会被拼接
     * @param msg 日志消息
     */
    public static void w(String[] tags, String msg) {
        if (sEnabled) {
            LogUtil.w(buildTag(tags), msg);
        }
    }
    
    /**
     * 使用单个TAG输出WARN级别日志
     * @param tag 单个TAG
     * @param msg 日志消息
     */
    public static void w(String tag, String msg) {
        if (sEnabled) {
            LogUtil.w(buildTag(tag), msg);
        }
    }
    
    /**
     * 使用多个TAG组合输出WARN级别日志，带异常信息
     * @param tags 多个TAG，会被拼接
     * @param msg 日志消息
     * @param tr 异常
     */
    public static void w(String[] tags, String msg, Throwable tr) {
        if (sEnabled) {
            LogUtil.w(buildTag(tags), msg, tr);
        }
    }
    
    /**
     * 使用单个TAG输出WARN级别日志，带异常信息
     * @param tag 单个TAG
     * @param msg 日志消息
     * @param tr 异常
     */
    public static void w(String tag, String msg, Throwable tr) {
        if (sEnabled) {
            LogUtil.w(buildTag(tag), msg, tr);
        }
    }
    
    /**
     * 使用多个TAG组合输出ERROR级别日志
     * @param tags 多个TAG，会被拼接
     * @param msg 日志消息
     */
    public static void e(String[] tags, String msg) {
        if (sEnabled) {
            LogUtil.e(buildTag(tags), msg);
        }
    }
    
    /**
     * 使用单个TAG输出ERROR级别日志
     * @param tag 单个TAG
     * @param msg 日志消息
     */
    public static void e(String tag, String msg) {
        if (sEnabled) {
            LogUtil.e(buildTag(tag), msg);
        }
    }
    
    /**
     * 使用多个TAG组合输出ERROR级别日志，带异常信息
     * @param tags 多个TAG，会被拼接
     * @param msg 日志消息
     * @param tr 异常
     */
    public static void e(String[] tags, String msg, Throwable tr) {
        if (sEnabled) {
            LogUtil.e(buildTag(tags), msg, tr);
        }
    }
    
    /**
     * 使用单个TAG输出ERROR级别日志，带异常信息
     * @param tag 单个TAG
     * @param msg 日志消息
     * @param tr 异常
     */
    public static void e(String tag, String msg, Throwable tr) {
        if (sEnabled) {
            LogUtil.e(buildTag(tag), msg, tr);
        }
    }
    
    /**
     * 格式化输出DEBUG级别日志
     * @param tag 单个TAG
     * @param format 格式化字符串
     * @param args 参数
     */
    public static void d(String tag, String format, Object... args) {
        if (sEnabled) {
            LogUtil.d(buildTag(tag), String.format(format, args));
        }
    }
    
    /**
     * 格式化输出INFO级别日志
     * @param tag 单个TAG
     * @param format 格式化字符串
     * @param args 参数
     */
    public static void i(String tag, String format, Object... args) {
        if (sEnabled) {
            LogUtil.i(buildTag(tag), String.format(format, args));
        }
    }
    
    /**
     * 格式化输出WARN级别日志
     * @param tag 单个TAG
     * @param format 格式化字符串
     * @param args 参数
     */
    public static void w(String tag, String format, Object... args) {
        if (sEnabled) {
            LogUtil.w(buildTag(tag), String.format(format, args));
        }
    }
    
    /**
     * 格式化输出ERROR级别日志
     * @param tag 单个TAG
     * @param format 格式化字符串
     * @param args 参数
     */
    public static void e(String tag, String format, Object... args) {
        if (sEnabled) {
            LogUtil.e(buildTag(tag), String.format(format, args));
        }
    }
    
    /**
     * 初始化日志系统，透传到LogUtil
     * @param debug 是否输出日志
     * @param level 日志级别
     * @param funcInfo 是否输出函数信息
     * @param path 日志文件路径
     */
    public static void init(boolean debug, int level, boolean funcInfo, String path) {
        LogUtil.init(debug, level, funcInfo, path);
    }
    
    /**
     * 构建TAG字符串
     * @param tags 多个TAG
     * @return 拼接后的TAG
     */
    private static String buildTag(String[] tags) {
        if (tags == null || tags.length == 0) {
            return DEFAULT_TAG_PREFIX;
        }
        
        StringBuilder builder = new StringBuilder(DEFAULT_TAG_PREFIX);
        for (String tag : tags) {
            if (tag != null && !tag.isEmpty()) {
                builder.append("-").append(tag);
            }
        }
        return builder.toString();
    }
    
    /**
     * 构建单TAG字符串
     * @param tag 单个TAG
     * @return 拼接后的TAG
     */
    private static String buildTag(String tag) {
        if (tag == null || tag.isEmpty()) {
            return DEFAULT_TAG_PREFIX;
        }
        return DEFAULT_TAG_PREFIX + "-" + tag;
    }
}
