package com.autoai.avslinkhid.hidtouchscreen;

import static android.view.MotionEvent.ACTION_CANCEL;
import static android.view.MotionEvent.ACTION_DOWN;
import static android.view.MotionEvent.ACTION_MOVE;
import static android.view.MotionEvent.ACTION_POINTER_DOWN;
import static android.view.MotionEvent.ACTION_POINTER_UP;
import static android.view.MotionEvent.ACTION_UP;

import static com.autoai.avslinkhid.util.ThreadUtils.postOnBackgroundSingleThread;

import android.annotation.SuppressLint;
import android.graphics.Point;
import android.view.MotionEvent;
import android.view.View;


import com.autoai.avslinkhid.api.HidApi;
import com.autoai.avslinkhid.api.HidUtil;
import com.autoai.avslinkhid.api.HidWlanAndroid;
import com.autoai.avslinkhid.datatransfer.AutoAgreeManager;
import com.autoai.avslinkhid.model.AutoAgreeConfig;
import com.autoai.common.util.LogUtil;


import java.util.HashSet;
import java.util.Set;

/**
 * she review tips:目前看该类主要是Android 反控 将 touch 事件-->hid-触控板 核心逻辑
 */
public class HidScreenTouchManager{
    private static final String TAG = HidScreenConstantsKt.HidScreen + "TouchManager";

    /**
     * 触摸 View 的宽高
     */
    private int touchViewWidth = 0, touchViewHeight = 0;
    /**
     * 被控制屏幕和触摸 View 之间的缩放比例
     */
    private double screen2ViewRatio = 1;
    private final HidScreenConnectManager hidScreenConnectManager;
    private final HidReportSender hidReportSender;
    private static HidScreenTouchManager instance;
    private final Point outPoint = new Point();
    private final Set<Integer> pointerActionDownIds = new HashSet<>();
    /**
     * 当前互联的设备名称
     */
    private String connectedDeviceName;
    /**
     * 当前互联的蓝牙设备名称
     */
    private String connectedDeviceBluetoothName;

    private HidScreenTouchManager() {
        this.hidScreenConnectManager = HidScreenConnectManager.getInstance();
        this.hidReportSender = new HidReportSender(hidScreenConnectManager);
    }

    public static HidScreenTouchManager getInstance() {
        if (instance == null) {
            synchronized (HidScreenTouchManager.class) {
                if (instance == null) {
                    instance = new HidScreenTouchManager();
                }
            }
        }
        return instance;
    }

    @SuppressLint("ClickableViewAccessibility")
    public boolean onTouch(View view, MotionEvent motionEvent) {
        int pointerCount = motionEvent.getPointerCount();
        LogUtil.e("doMultiTouchHID", "pointerCount:" + pointerCount + " actionMask:" + motionEvent.getActionMasked() + " actionIndex:" + motionEvent.getActionIndex() + " pointerId: "+ motionEvent.getPointerId(motionEvent.getActionIndex()));
        LogUtil.d(TAG, "isEnabled: " + view.isEnabled());
        if(!view.isEnabled()){
            return false;
        }

        if(!hidScreenConnectManager.isConnected()){
            LogUtil.e(TAG, "isConnected = false");
            return false;
        }

        /**
         * 互联的设备蓝牙名称和已经连接的蓝牙名称不一致，不需要反控, 目前不支持 iOS
         */
        String pairedBluetoothName = HidScreenConnectManager.getInstance().getBluetoothDeviceName();

        boolean isAndroidDevice = hidScreenConnectManager.isConnectedAndroidDevice();

        // 首先检查蓝牙是否与互联设备匹配（通过updateBTPairedWithPhone回调设置）
        if (isAndroidDevice && !hidScreenConnectManager.isBTPairedConnected()) {
            LogUtil.e(TAG, "motionEvent BT not paired with phone");
            return false;
        }


        LogUtil.i(TAG, "motionEvent.getActionMasked() = "+ motionEvent.getActionMasked());
        switch (motionEvent.getActionMasked()) {
            case ACTION_DOWN:
            case ACTION_POINTER_DOWN:
                if (!ensureTouchEnable(view)){
                    LogUtil.e(TAG, "ensureTouchEnable failed");
                    return false;
                }
                sendTouchEvent(motionEvent, motionEvent.getActionIndex(), ACTION_DOWN);
                break;
            case ACTION_MOVE:
                //-->fixme:多指的 move事件要都send吗？
                for (int actionIndex = 0; actionIndex < motionEvent.getPointerCount(); actionIndex++) {
                    sendTouchEvent(motionEvent, actionIndex, ACTION_MOVE);
                }
                break;
            case ACTION_UP:
            case ACTION_POINTER_UP:
                sendTouchEvent(motionEvent, motionEvent.getActionIndex(), ACTION_UP);
                //LinkHost.checkIsPlayVideo();
                break;
            case ACTION_CANCEL:
                //LinkHost.checkIsPlayVideo();
                LogUtil.i(TAG, "onTouch: CANCEL getPointerCount() = " + motionEvent.getPointerCount());
                for (int actionIndex = 0; actionIndex < motionEvent.getPointerCount(); actionIndex++) {
                    sendTouchEvent(motionEvent, actionIndex, ACTION_CANCEL);
                }
                break;
            default:
                break;
        }
        return true;
    }

    /**
     * 当前互联成功的手机蓝牙名称，用于判断当前连接的蓝牙是否是已经互联的设备。
     * @param deviceName, 设备名称， bluetoothName 蓝牙名称
     */
    public void setName(String deviceName, String bluetoothName){
        this.connectedDeviceName = deviceName;
        this.connectedDeviceBluetoothName = bluetoothName;
    }

    /**
     * 发送安卓防锁屏保活事件
     * 使用消费者控制协议发送保活信号
     */
    public void sendKeepAliveEvent() {
        if (!hidScreenConnectManager.isConnected()) {
            LogUtil.w(TAG, "HID not connected, cannot send keep alive event");
            return;
        }

        // 检查是否为安卓设备
        if (!hidScreenConnectManager.isConnectedAndroidDevice()) {
            LogUtil.d(TAG, "Not Android device, skip keep alive event");
            return;
        }

        try {
            // 发送消费者控制按键保活事件
            sendConsumerControlKeyEvent();
        } catch (Exception e) {
            LogUtil.w(TAG, "Consumer control keep alive failed, fallback to touch", e);

        }
    }

    /**
     * 发送消费者控制按键事件（后台执行，无阻塞）
     * 使用消费者控制协议编码0x02B0，发送特定按键保活信号
     */
    private void sendConsumerControlKeyEvent() {
        android.util.Log.e(TAG, ">>> Sending consumer control key event for Android device");

        // 使用后台线程池执行
        postOnBackgroundSingleThread(() -> {
            try {
                // 按下消费者控制按键 - 发送0x02B0编码
                hidReportSender.sendConsumerControlEvent((byte) 0xB0, (byte) 0x02, true);

                // 短暂延迟确保按键被识别
                Thread.sleep(50);

                // 释放按键 - 发送空编码
                hidReportSender.sendConsumerControlEvent((byte) 0x00, (byte) 0x00, false);

                android.util.Log.e(TAG, "<<< Consumer control key event completed successfully");
            } catch (InterruptedException e) {
                android.util.Log.e(TAG, "Consumer control key event interrupted", e);
                Thread.currentThread().interrupt();
            } catch (Exception e) {
                android.util.Log.e(TAG, "Failed to send consumer control key event", e);
            }
        });
    }



    private boolean ensureTouchEnable(View view) {
        touchViewWidth = view.getWidth();
        touchViewHeight = view.getHeight();
        LogUtil.d(TAG, "onTouch: touchViewWidth = " + touchViewWidth + ", touchViewHeight = " + touchViewHeight);
        if(touchViewWidth == 0 || touchViewHeight == 0){
            AutoAgreeConfig config = AutoAgreeManager.getConfig(connectedDeviceName);
            if(config != null){
                touchViewWidth = config.width;
                touchViewHeight = config.height;
            }
        }
        if(touchViewWidth == 0 || touchViewHeight == 0){
            return false;
        }
        int hidScreenAngle = hidScreenConnectManager.getHidScreenAngle();
        int edge = hidScreenConnectManager.getHidScreenSize().getWidth();
        if(hidScreenAngle == 90 || hidScreenAngle == 270){
            edge = hidScreenConnectManager.getHidScreenSize().getHeight();
        }
        screen2ViewRatio = (double) edge / touchViewWidth;
        LogUtil.i(TAG, "onTouch: touchViewWidth = " + touchViewWidth + ", touchViewHeight = " + touchViewHeight
                + ", screen2ViewRatio = " + screen2ViewRatio + ", hidScreenAngle = " + hidScreenAngle);
        return true;
    }

    /**
     * 核心的send方法
     * @param motionEvent 触摸事件
     * @param actionIndex 多指index
     * @param action      触摸具体事项
     */
    private void sendTouchEvent(MotionEvent motionEvent, int actionIndex, int action) {
        int pointerId = motionEvent.getPointerId(actionIndex);
        if(action == ACTION_DOWN){
            pointerActionDownIds.add(pointerId);
        } else if(!pointerActionDownIds.contains(pointerId)){ //已经划出屏幕的事件滑动回来也不再响应
            return;
        } else if(action == ACTION_UP || action == ACTION_CANCEL){
            pointerActionDownIds.remove(pointerId);
        }

        float fx = motionEvent.getX(actionIndex);
        float fy = motionEvent.getY(actionIndex);

        LogUtil.i(TAG, "onTouch: actionIndex = " + actionIndex + ", PointerId = " + pointerId
                + " getActionMasked = " + motionEvent.getActionMasked()
                + ", down: fx = " + fx + ", fy = " + fy
                + ", padWidth = " + touchViewWidth + ", padHeight = " + touchViewHeight
        );
        if (fx >= 0 && fx <= touchViewWidth && fy >= 0 && fy <= touchViewHeight) {
            calculateSendPoint(fx, fy, outPoint);
            hidReportSender.sendTouchDownMoveEvent(pointerId, action == ACTION_DOWN || action == ACTION_MOVE, outPoint.x, outPoint.y);
        } else {
            calculateSendPoint(fx, fy, outPoint);
            hidReportSender.sendTouchDownMoveEvent(pointerId, false, outPoint.x, outPoint.y);
            //划出到屏幕外，直接调用 cancel
            pointerActionDownIds.remove(pointerId);
        }
    }

    private void calculateSendPoint(float x, float y, Point outPoint){
        LogUtil.i(TAG, "calculateSendPoint: x = " + x + ", y = " + y + ", screen2ViewRatio = " + screen2ViewRatio);
        if(x < 0) {
            x = 0;
        }
        if(y < 0) {
            y = 0;
        }
        switch (hidScreenConnectManager.getHidScreenAngle()){
            case 90:
                float temp = x;
                x = touchViewHeight - y;
                y = temp;
                break;
            case 180:
                x = touchViewWidth - x;
                y = touchViewHeight - y;
                break;
            case 270:
                float tempX = x;
                x =  y;
                y = touchViewWidth - tempX;
                break;
            default:
                break;
        }
        long rx = Math.round(x * screen2ViewRatio);
        long ry = Math.round(y * screen2ViewRatio);
        int deltaX = (int) Math.round(rx * 3840d / hidScreenConnectManager.getHidScreenSize().getWidth());
        int deltaY = (int) Math.round(ry * 3840d / hidScreenConnectManager.getHidScreenSize().getHeight());
        outPoint.x = deltaX;
        outPoint.y = deltaY;
        LogUtil.i(TAG, "calculateSendPoint: x = " + x + ", y = " + y + ", rx = " + rx + ", ry = " + ry + ", deltaX = " + deltaX + ", deltaY = " + deltaY);
    }

    /**
     * 重新启动心跳定时器，用于防锁屏机制的用户活跃状态检测
     */
    private void restartHeartBeatTimer() {
        try {
            // 通知当前HID实现重新启动心跳定时器
            if (hidScreenConnectManager.isConnectedAndroidDevice()) {
                HidApi currentHidApi = HidUtil.getCurrentHidApi();
                if (currentHidApi != null) {
                    currentHidApi.restartKeepAliveHeartBeatTimer();
                }
            }
        } catch (Exception e) {
            LogUtil.w(TAG, "Failed to restart heartbeat timer", e);
        }
    }
}
