package com.autoai.avslinkhid.util;
import static android.view.MotionEvent.ACTION_UP;

import android.hardware.usb.UsbDeviceConnection;
import android.view.MotionEvent;

import com.autoai.avslinkhid.datatransfer.TouchManager;
import com.autoai.avslinkhid.hidtouchscreen.HidScreenConnectManager;
import com.autoai.avslinkhid.hidtouchscreen.HidScreenConstantsKt;
import com.autoai.common.util.LogUtil;
import com.autoai.welink.macro.WL_API_TOUCH_TYPE;
import com.autoai.welink.param.HidTouch;


public class TouchUtil {
    private static final String TAG = HidScreenConstantsKt.HidScreen + "TouchUtil";
    private static final boolean CHECK_TOUCH_UTIL = LogUtil.isFullLog();
    private TouchManager touchManager = TouchManager.getInstance();
    private static TouchUtil instance;
    private static final int HID_ORIENTATION_0 = 0;
    private static final int HID_ORIENTATION_90 = 1;
    private static final int HID_ORIENTATION_180 = 2;
    private static final int HID_ORIENTATION_270 = 3;
    public static final int HID_KEY_HOME = 0;    //HOME按键
    public static final int HID_KEY_BACK = 1;      //BACK按键
    public static final int HID_KEY_HU_HOME = 2;  //车机home
    private int screenW = 1;
    private int screenH = 1;
    private int videoW = 1;
    private int videoH = 1;
    private int downFlag = 0;
    private HidTouch rotation = new HidTouch();
    private int clickVirtualBtn = -1;
    private int hidId = -1;
    private boolean isRegisterHid = false;
    //    private final int[] touchType = {WL_API_TOUCH_TYPE.UNKNOWN, WL_API_TOUCH_TYPE.UNKNOWN};
    private final static int INVALID_ID = -1;
    // 支持5指多点触控
    private final static int SUPPORT_POINT_MAX = 5;

    private float scaleScreen = 1f;
    public static class TouchEvent {
        public int type;
        public int pointerId;
        public boolean enableSend;
        public int pressure;
        public int xCoordinate;
        public int yCoordinate;

        private TouchEvent() {
//            int type = WL_API_TOUCH_TYPE.UNKNOWN;
//            int pointerId = INVALID_ID;
//            boolean enableSend = false;
//            int pressure = -1;
//            int xCoordinate = -1;
//            int yCoordinate = -1;
        }
    }

    public TouchUtil() {
    }

    public static TouchUtil getInstance() {
        if (null == instance) {
            instance = new TouchUtil();
        }
        return instance;
    }

//    //public void setDataTransfer(DataTransfer2 mDataTransfer) {
//        this.mDataTransfer = mDataTransfer;
//    }

    public void setH264Coordinates(HidTouch rotation) {
        this.rotation = rotation;
        LogUtil.d(TAG,"CustomMotionListener setH264Coordinates w = " +  rotation.phoneSize.w + ", h = " + rotation.phoneSize.h);
        if (rotation.isSupportHID != getInstance().isSupportHid() && !rotation.isSupportHID) {
            this.isRegisterHid = false;
        }
    }

    public void setUsbDeviceConnection(UsbDeviceConnection connection){
        touchManager.setUsbDeviceConnection(connection);
    }


    public void setVideoSize(int w, int h) {
        this.videoW = w;
        this.videoH = h;
    }

    public int registerHID(int w, int h) {
        LogUtil.v(TAG, "registerHID " + w + " " + h);
        this.videoW = w;
        this.videoH = h;
        this.isRegisterHid = true;
        this.hidId = touchManager.registerHID(w,h);
        return hidId;
    }

    public int unregisterHID() {
        this.isRegisterHid = false;
        return touchManager.unregisterHID(hidId);
    }

    public boolean isSupportHid() {
        return rotation.isSupportHID;
    }

    public boolean isRegisterHid() {
        return isRegisterHid;
    }

    public void setScreenSize(int w, int h) {
        this.screenW = w;
        this.screenH = h;
    }
    public void setScaleScreen(float scale){
        LogUtil.v(TAG,"setScaleScreen-->"+scale);
        this.scaleScreen = scale;
    }

    public float getScaleScreen(){
        return scaleScreen;
    }
    public void sendHidKey(int key) {
        if (key != HID_KEY_HU_HOME) {
            touchManager.sendKeyEvent(key);
        }
    }

    /**
     * 发送AOA防锁屏保活事件
     * 使用消费者控制按键事件防止手机锁屏
     */
    public void sendKeepAliveEvent() {
        LogUtil.d(TAG, ">>> Sending AOA keep alive event for Android device");

        // 使用后台线程池执行，避免阻塞主线程
        ThreadUtils.postOnBackgroundSingleThread(() -> {
            try {
                // 发送消费者控制按键保活事件
                touchManager.sendKeepAliveKeyEvent();
                LogUtil.d(TAG, "<<< AOA keep alive event completed successfully");
            } catch (Exception e) {
                LogUtil.e(TAG, "Failed to send AOA keep alive event", e);
            }
        });
    }

    public void MulitTouch(MotionEvent event) {
        MotionEvent copiedEvent = MotionEvent.obtain(event);
        LogUtil.v(TAG, "MulitTouch " + rotation.isSupportHID);
        ThreadUtils.postOnBackgroundSingleThread(() -> doMultiTouchHID(copiedEvent));
    }

//    /**
//     * @param event     event
//     * @param viewModel viewModel
//     */
//    @SuppressWarnings("unused")
//    private void doSingleTouch(MotionEvent event, SdkViewModel viewModel) {
//        switch (event.getAction()) {
//            case MotionEvent.ACTION_DOWN:
//                viewModel.getSdkApi().sendPoint(
//                        (int) event.getX(),
//                        (int) event.getY(),
//                        0,
//                        50,
//                        WL_API_TOUCH_TYPE.DOWN);
//                viewModel.getSdkApi().sendPointSync();
//                break;
//            case MotionEvent.ACTION_UP:
//                viewModel.getSdkApi().sendPoint(
//                        (int) event.getX(),
//                        (int) event.getY(),
//                        0,
//                        50,
//                        WL_API_TOUCH_TYPE.UP);
//                viewModel.getSdkApi().sendPointSync();
//                break;
//            case MotionEvent.ACTION_MOVE:
//                viewModel.getSdkApi().sendPoint(
//                        (int) event.getX(),
//                        (int) event.getY(),
//                        0,
//                        50,
//                        WL_API_TOUCH_TYPE.MOVE);
//                viewModel.getSdkApi().sendPointSync();
//                break;
//            default:
//                break;
//        }
//    }

    /**
     * @param event     event
     */
    private void doMultiTouchHID(MotionEvent event) {
        int pointerCount = event.getPointerCount();
//        if (pointerCount > SUPPORT_POINT_MAX) {
//            //return;
//        }
        //LogUtil.e("doMultiTouchHID", "pointerCount:" + pointerCount + " action:" + event.getAction() + " actionIndex:" + event.getActionIndex() + " pointerId: "+ event.getPointerId(event.getActionIndex()));
        try {
            setTouchEvent(event);
        } finally {
            // 确保回收
            event.recycle();
        }
    }
    /**
     * @param type touch type
     * @param id   point count
     * @param x    point x
     * @param y    point y
     */
    private void sendHid(int type, int id, int x, int y) {
        LogUtil.v(TAG, "sendHid " + type + " " + id + " " + x + " " + y);
        switch (type) {
            case WL_API_TOUCH_TYPE.DOWN:
//                if (!rotation.isSupportHID) {
//                    break;
//                }
                clickVirtualBtn = isClickVirtualBtn(x, y);
                if (clickVirtualBtn >= 0) {
                    break;
                }
                sendpointHID(id, x, y, 0, setAngle(rotation.angle));
                downFlag = 1;
                break;
            case WL_API_TOUCH_TYPE.MOVE:
//                if (!rotation.isSupportHID) {
//                    break;
//                }
                sendpointHID(id, x, y, 0, setAngle(rotation.angle));
                downFlag = 1;
                break;
            case WL_API_TOUCH_TYPE.UP:
//                if (!rotation.isSupportHID) {
//                    break;
//                }
                if (clickVirtualBtn >= 0) {
                    sendHidKey(clickVirtualBtn);
                    clickVirtualBtn = -1;
                    break;
                }
                sendpointHID(id, x, y, 1, setAngle(rotation.angle));
                downFlag = 0;
                break;
            default:
                break;
        }
    }

    /**
     * @param id    point count
     * @param x     point x
     * @param y     point y
     * @param type  touch type
     * @param angle angle
     */
    private void sendpointHID(int id, int x, int y, int type, int angle) {
        if (CHECK_TOUCH_UTIL) {
            LogUtil.d(TAG, "TouchUtil::sendpointHID >>> finger=" + id + " x=" + x + " y=" + y + " type=" + type);
        }

        try {
            // 检查视频尺寸有效性
            if (videoW <= 0 || videoH <= 0) {
                LogUtil.w(TAG, "Invalid video size: " + videoW + "x" + videoH);
                return;
            }

            // 坐标边界检查和修正
            if (x < 0) {
                x = 0;
            }
            if (y < 0) {
                y = 0;
            }

            // 坐标缩放转换：屏幕坐标 -> 视频坐标
            int scaledX = (int) (videoW * 1f / screenW * x);
            int scaledY = (int) (videoH * 1f / screenH * y);

            int validId = Math.max(0, Math.min(id, 10));
            if (validId != id) {
                LogUtil.w(TAG, "Touch ID clamped from " + id + " to " + validId);
            }

            // 发送触控事件到TouchManager
            touchManager.sendPointEvent(validId, scaledX, scaledY, type, angle);

            if (CHECK_TOUCH_UTIL) {
                LogUtil.d(TAG, "TouchUtil::sendpointHID <<< finger=" + validId + " scaled(" + scaledX + "," + scaledY + ")");
            }
        } catch (ArithmeticException e) {
            LogUtil.e(TAG, "sendpointHID arithmetic exception for finger " + id, e);
        } catch (Exception e) {
            LogUtil.e(TAG, "sendpointHID unexpected exception for finger " + id, e);
        }
    }

    /**
     * @param angle angle
     * @return angle set result.
     */
    private int setAngle(int angle) {
        if (CHECK_TOUCH_UTIL)
            LogUtil.v(TAG, "setAngle " + angle);
        int ret;
        switch (angle) {
            case 90:
                ret = HID_ORIENTATION_90;
                break;
            case 180:
                ret = HID_ORIENTATION_180;
                break;
            case 270:
                ret = HID_ORIENTATION_270;
                break;
            default:
                ret = HID_ORIENTATION_0;
                break;
        }
        return ret;
    }

    /**
     * @param x point x
     * @param y point y
     * @return virtual button type
     */
    private int isClickVirtualBtn(int x, int y) {
        if (x > rotation.home.x && x < (rotation.home.x + rotation.home.w)
                && y > rotation.home.y && y < (rotation.home.y + rotation.home.h)) {
            return HID_KEY_HOME;
        } else if (x > rotation.back.x && x < (rotation.back.x + rotation.back.w)
                && y > rotation.back.y && y < (rotation.back.y + rotation.back.h)) {
            return HID_KEY_BACK;
        } else if (x > rotation.huHome.x && x < (rotation.huHome.x + rotation.huHome.w)
                && y > rotation.huHome.y && y < (rotation.huHome.y + rotation.huHome.h)) {
            return HID_KEY_HU_HOME;
        }
        return -1;
    }


    private void setTouchEvent(MotionEvent event) {
        try {
            // 使用ACTION_MASK获取纯粹的action类型，支持多指操作
            final int actionMask = event.getActionMasked();
            final int actionIndex = event.getActionIndex();

            LogUtil.d(TAG, "TouchUtil::setTouchEvent >>> actionMask=" + actionMask + " actionIndex=" + actionIndex + " pointerCount=" + event.getPointerCount());

            switch (actionMask) {
                case MotionEvent.ACTION_DOWN:
                case MotionEvent.ACTION_POINTER_DOWN: {

                    int pointerId = event.getPointerId(actionIndex);
                    sendHid(WL_API_TOUCH_TYPE.DOWN,
                            pointerId,
                            (int) event.getX(actionIndex),
                            (int) event.getY(actionIndex));

                }
                break;
                case MotionEvent.ACTION_MOVE: {
                    // 处理多指移动事件，支持最多5指
                    LogUtil.d(TAG, "ACTION_MOVE: pointerCount=" + event.getPointerCount());

                    // 遍历所有已记录的触控点，更新其坐标
                    for (int pointCountIdx = 0; pointCountIdx < event.getPointerCount(); pointCountIdx++) {
                        // 根据pointerId查找当前事件中的索引位置
                        int pointerId = event.getPointerId(pointCountIdx);
                        sendHid(WL_API_TOUCH_TYPE.MOVE,
                                pointerId,
                                (int) event.getX(pointCountIdx),
                                (int) event.getY(pointCountIdx));
                    }
                }
                break;
                case MotionEvent.ACTION_CANCEL: {
                    // 所有触控点抬起，支持多指同时抬起
                    for (int pointCountIdx = 0; pointCountIdx < event.getPointerCount(); pointCountIdx++) {
                        // 根据pointerId查找当前事件中的索引位置
                        int pointerId = event.getPointerId(pointCountIdx);
                        sendHid(WL_API_TOUCH_TYPE.UP,
                                pointerId,
                                (int) event.getX(pointCountIdx),
                                (int) event.getY(pointCountIdx));
                    }
                }
                break;

                case MotionEvent.ACTION_UP:
                case MotionEvent.ACTION_POINTER_UP: {
                    int pointerId = event.getPointerId(actionIndex);
                    sendHid(WL_API_TOUCH_TYPE.UP,
                            pointerId,
                            (int) event.getX(actionIndex),
                            (int) event.getY(actionIndex));
                }
                break;
                default:
                    break;
            }
        } catch (IllegalArgumentException e) {
            //多点触控系统bug过滤
            LogUtil.w(TAG, "!!!!!!!!!!!!!!!!!!!!setTouchEvent: get system IllegalArgumentException!");
            //e.printStackTrace();
        }
    }
}
