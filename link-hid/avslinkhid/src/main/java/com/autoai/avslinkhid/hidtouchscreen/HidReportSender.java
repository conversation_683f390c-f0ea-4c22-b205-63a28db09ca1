package com.autoai.avslinkhid.hidtouchscreen;


import static com.autoai.avslinkhid.hidtouchscreen.HidScreenConstantsKt.HidScreenLogEnable;

import android.os.Handler;
import android.os.Looper;

import androidx.annotation.NonNull;
import androidx.core.util.Pools;

import com.autoai.common.util.LogUtil;

import java.util.Arrays;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * 发送触摸事件
 */
public class HidReportSender {
    private final String TAG = HidScreenConstantsKt.HidScreen + "HidReportSender";
    
    // 添加时间常量定义
    private static final long MIN_REPORT_EVENT_INTERVAL_MS = 16;  // 最小鼠标事件上报间隔
    private static final long DELAY_LAST_MOVE_EVENT_MS = 50;           // 最后一个移动事件的延迟时间

    private final HidScreenConnectManager hidScreenConnectManager;

    /**
     * 使用单线程池处理触摸事件发送，确保事件按顺序处理
     */
    private final ExecutorService sendHidScreenEventExecutor = Executors.newSingleThreadExecutor(
            runnable -> new Thread(runnable, "sendHidScreenTouchEventExecutor"));

    private final Pools.SynchronizedPool<SendReportRunnable> pool = new Pools.SynchronizedPool<>(20);

    // 延迟上报鼠标滚动事件队列
    private final LinkedBlockingQueue<HidReport> reportQueue = new LinkedBlockingQueue<>();
    private final Handler mainHandler = new Handler(Looper.getMainLooper());

    // 特殊处理 up 之前的 move 事件，延迟DELAY_LAST_MOVE_EVENT_MS 上报，以减少反勾问题。
    private boolean processEventActionUp = false;
    private final Runnable processQueueRunnable = new Runnable() {
        @Override
        public void run() {
            boolean bRet = processReportQueue();
            if (bRet && !reportQueue.isEmpty()) {
                mainHandler.postDelayed(this, MIN_REPORT_EVENT_INTERVAL_MS);
            }
        }
    };

    public HidReportSender(HidScreenConnectManager hidScreenConnectManager) {
        this.hidScreenConnectManager = hidScreenConnectManager;
    }

    private boolean processReportQueue() {
//        LogUtil.d(TAG, "processReportQueue " + reportQueue.size());
        if (!processEventActionUp && reportQueue.size() >= 2) {
            // 将要发送 up，则延迟 100ms 发送最后一个 move
            HidReport[] reports = reportQueue.toArray(new HidReport[0]);
            if ((reports.length >= 2 && reports[1].reportData[0] == 0) ||
                    (reports.length >= 3 && reports[2].reportData[0] == 0)) {
//                LogUtil.d(TAG, "delay processReportQueue " + reportQueue.size());
                processEventActionUp = true;
                mainHandler.postDelayed(processQueueRunnable, DELAY_LAST_MOVE_EVENT_MS);
                return false; // 不处理当前的report
            }
        }

        processEventActionUp = false;
        HidReport report = reportQueue.poll();
        if (report != null) {
            addInputReport(report);
        }

        return (report != null);
    }

    public void sendTouchDownMoveEvent(int touchId, boolean tipSwitch, int x, int y){
        LogUtil.i(TAG, "onTouch: touchId = " + touchId + ", x = " + x + ", y = " + y + ", isConnected = " + hidScreenConnectManager.isConnected());

        if(!hidScreenConnectManager.isConnected()){
            return;
        }
        HidReport hidReport = HidReport.obtainTouchScreenReport();
        hidReport.reportData[0] = (byte) (tipSwitch ? 1 : 0);
        hidReport.reportData[1] = (byte) touchId;
        hidReport.reportData[2] = (byte) (x & 0xFF);
        hidReport.reportData[3] = (byte) ((x >>> 8) & 0xFF);
        hidReport.reportData[4] = (byte) (y & 0xFF);
        hidReport.reportData[5] = (byte) ((y >>> 8) & 0xFF);

        long lastReportTS = System.currentTimeMillis();
        LogUtil.d(TAG, "add report to queue at " + lastReportTS + " ms:" + Arrays.toString(hidReport.reportData) + " size: " + reportQueue.size());
//        if (reportQueue.isEmpty()) {
//            mainHandler.postDelayed(processQueueRunnable, MIN_REPORT_MOUSE_EVENT_INTERVAL_MS);
//        }
//       //  将report添加到队列中
//        reportQueue.offer(hidReport);
        addInputReport(hidReport);
    }


    /**
     * 发送消费者控制事件（后台执行）
     * @param usageLow 使用码低字节 (当前使用0xB0)
     * @param usageHigh 使用码高字节 (当前使用0x02，组合为0x02B0)
     * @param isPressed 是否按下 (true=按下发送编码, false=释放发送0x0000)
     */
    public void sendConsumerControlEvent(byte usageLow, byte usageHigh, boolean isPressed) {
        android.util.Log.e(TAG, ">>> sendConsumerControlEvent usage: " + String.format("0x%02X%02X", usageHigh, usageLow) +
                ", isPressed: " + isPressed + ", isConnected: " + hidScreenConnectManager.isConnected());

        if (!hidScreenConnectManager.isConnected()) {
            return;
        }

        HidReport hidReport = HidReport.obtainConsumerControl();
        if (isPressed) {
            hidReport.reportData[0] = usageLow;   // 使用码低字节
            hidReport.reportData[1] = usageHigh;  // 使用码高字节
        } else {
            hidReport.reportData[0] = 0x00;       // 释放时发送0x0000
            hidReport.reportData[1] = 0x00;
        }

        addInputReport(hidReport);
    }

    private void addInputReport(final HidReport inputReport) {
        if(HidScreenLogEnable){
            LogUtil.d(TAG, "addInputReport: inputReport = " + inputReport);
        }
        SendReportRunnable reportRunnable = obtain();
        reportRunnable.report = inputReport;
        sendHidScreenEventExecutor.execute(reportRunnable);
    }

    private class SendReportRunnable implements Runnable {
        private HidReport report;

        @Override
        public void run() {
            if (hidScreenConnectManager.isConnected()) {
                report.sendState = HidReport.State.Sending;
                boolean ret = hidScreenConnectManager.sendReport(report.reportId, report.reportData);
                report.sendState = ret ? HidReport.State.Sent : HidReport.State.Failed;
                if(HidScreenLogEnable){
                    LogUtil.d(TAG, "sendReport: report = " + report);
                }
            }
            HidReport.recycle(report);
            recycle(this);
        }
    }

    private SendReportRunnable obtain(){
        SendReportRunnable instance = pool.acquire();
        return (instance != null) ? instance : new SendReportRunnable();
    }

    private void recycle(@NonNull SendReportRunnable event){
        try {
            pool.release(event);
        } catch (Exception e) {
            LogUtil.e(TAG, "recycle error: ", e);
        }
    }
}
