package com.autoai.avslinkhid.model;

/**
 * <AUTHOR>
 */
public class HidVerificationType {
    /**
     * 未校准
     */
    public static final int HID_VERIFY_TYPE_NONE = 0;
    /**
     * HID未连接，不可校准
     */
    public static final int HID_VERIFY_TYPE_UN_CONNECT = 1;
    /**
     * HID已连接，可校准
     */
    public static final int HID_VERIFY_TYPE_CONNECT = 2;
    /**
     * 校准中
     */
    public static final int HID_VERIFY_TYPE_VERIFICATION_ING = 3;
    /**
     * 校准完成
     */
    public static final int HID_VERIFY_TYPE_VERIFICATION_SUCCESS = 4;
    /**
     * 校准失败
     */
    public static final int HID_VERIFY_TYPE_VERIFICATION_FAIL = 5;

    /**
     * 校准失败,HID未连接
     */
    public static final int HID_VERIFY_TYPE_HID_CONNECT_VERIFICATION_FAIL = 6;


    public static final int MAX_STEP = 100;
    public static final int MID_STEP = 50;
    public static final int MIN_STEP = 25;
    public static final int SCROLL_STEP = 8;

    /**
     *  25 seconds in milliseconds
     */
    public static final int HEART_BEAT_DELAY = 25000;
    public static final int MOUSE_RESET_DELAY = 3000;
    public static final int MOUSE_RESET_DELAY_200 = 200;
    public static final int DISTANCE_DELAY = 20;

    public static final int ACTION_CANCEL_ALL = 1;
    public static final int ACTION_MOUSE_RESET = 2;
    public static final int ACTION_HEART_BEAT = 3;
    public static final int ACTION_DISTANCE = 4;


    /**
     * 获取消息类型的名称
     * @param what 消息类型
     * @return 消息类型的名称
     */
    public static String getActionMessageName(int what) {
        switch (what) {
            case ACTION_CANCEL_ALL:
                return "ACTION_CANCEL_ALL";
            case ACTION_MOUSE_RESET:
                return "ACTION_MOUSE_RESET";
            case ACTION_HEART_BEAT:
                return "ACTION_HEART_BEAT";
            case ACTION_DISTANCE:
                return "ACTION_DISTANCE";
            default:
                return "UNKNOWN_ACTION";
        }
    }

    /**
     * 获取HID校验状态的字符串描述
     * @param state 校验状态
     * @return 状态描述
     */
    public static String getHidVerificationStateString(int state) {
        switch (state) {
            case HID_VERIFY_TYPE_NONE:
                return "HID_VERIFY_TYPE_NONE";
            case HID_VERIFY_TYPE_UN_CONNECT:
                return "HID_VERIFY_TYPE_UN_CONNECT";
            case HID_VERIFY_TYPE_CONNECT:
                return "HID_VERIFY_TYPE_CONNECT";
            case HID_VERIFY_TYPE_VERIFICATION_ING:
                return "HID_VERIFY_TYPE_VERIFICATION_ING";
            case HID_VERIFY_TYPE_VERIFICATION_SUCCESS:
                return "HID_VERIFY_TYPE_VERIFICATION_SUCCESS";
            case HID_VERIFY_TYPE_VERIFICATION_FAIL:
                return "HID_VERIFY_TYPE_VERIFICATION_FAIL";
            case HID_VERIFY_TYPE_HID_CONNECT_VERIFICATION_FAIL:
                return "HID_VERIFY_TYPE_HID_CONNECT_VERIFICATION_FAIL";
            default:
                return "UNKNOWN_STATE_" + state;
        }
    }
}
