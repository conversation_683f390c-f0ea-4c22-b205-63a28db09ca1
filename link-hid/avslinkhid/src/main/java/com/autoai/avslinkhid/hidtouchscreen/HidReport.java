package com.autoai.avslinkhid.hidtouchscreen;

import android.util.Log;

import androidx.annotation.NonNull;
import androidx.core.util.Pools;


import java.util.Arrays;

public class HidReport {
    public final byte reportId;
    public final DeviceType deviceType;
    public final byte[] reportData;
    public State sendState = State.None;

    private HidReport() {
        this(DeviceType.TouchScreen, (byte) 0x01, new byte[]{0, 0, 0, 0, 0, 0});
    }

    private HidReport(DeviceType deviceType, byte reportId, byte[] data) {
        this.deviceType = deviceType;
        this.reportId = reportId;
        this.reportData = data;
        this.sendState = State.None;
    }

    public enum DeviceType {
        None, Mouse, Keyboard, TouchScreen, ConsumerControl
    }

    public enum State {
        None, Sending, Sent, Failed
    }

    @NonNull
    @Override
    public String toString() {
        return "HidReport{" +
                "ReportId=" + reportId +
                ", deviceType=" + deviceType +
                ", ReportData=" + Arrays.toString(reportData) +
                ", SendState=" + sendState +
                '}';
    }

    // 为不同类型的报告提供单独的对象池
    private static final Pools.SynchronizedPool<HidReport> sTouchScreenPool = new Pools.SynchronizedPool<>(20);
    private static final Pools.SynchronizedPool<HidReport> sConsumerControlPool = new Pools.SynchronizedPool<>(10);

    public static HidReport obtainTouchScreenReport(){
        HidReport instance = sTouchScreenPool.acquire();
        if (instance == null) {
            instance = new HidReport(DeviceType.TouchScreen, (byte) 0x01, new byte[]{0, 0, 0, 0, 0, 0});
        }
        instance.sendState = State.None;
        return instance;
    }

    /**
     * 获取消费者控制类型的HidReport实例
     * @return 消费者控制HidReport实例，Report ID为2，数据长度为2字节
     */
    public static HidReport obtainConsumerControl(){
        HidReport instance = sConsumerControlPool.acquire();
        if (instance == null) {
            instance = new HidReport(DeviceType.ConsumerControl, (byte) 0x02, new byte[]{0, 0});
        }
        instance.sendState = State.None;
        return instance;
    }

    public static void recycle(@NonNull HidReport event){
        // 重置共享状态
        event.sendState = State.None;

        try {
            switch (event.deviceType) {
                case TouchScreen:
                    // 清理 TouchScreen 特有的数据
                    Arrays.fill(event.reportData, (byte) 0);
                    sTouchScreenPool.release(event);
                    break;
                case ConsumerControl:
                    // 清理 ConsumerControl 特有的数据
                    Arrays.fill(event.reportData, (byte) 0);
                    sConsumerControlPool.release(event);
                    break;
                default:
                    // 对于其他类型，不进行池化
                    break;
            }
        } catch (Exception e) {
            Log.e(HidScreenConstantsKt.HidScreen + "HidReport", "recycle error: ", e);
        }
    }
}
