package com.autoai.avslinkhid.calibration;



import com.autoai.avslinkhid.hidtouchscreen.HidScreenConstantsKt;
import com.autoai.avslinkhid.model.MyPoint;
import com.autoai.avslinkhid.util.SpUtil;
import com.autoai.common.util.LogUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import kotlin.Pair;
import kotlin.Triple;

public class BaseCalibrationStrategy implements CalibrationStrategy {
    protected static final String TAG = HidScreenConstantsKt.HidScreen + "HidIosCalibration";

    // 用于存储hid发送数据对应的y值
    protected float[] indexToYArrayForHid = null;

    // HID发送数据步长常量, 后续调整成动态的
    protected final int MAX_STEP = 100;

    // 查找数组中大于0的最小值作为最小精度
    float minPrecision = Float.MAX_VALUE;
    int minPrecisionIndex =0;

    /**
     * 记录IOS 反控校准，回传实际生效的点 举例：通过hid mouse发送给手机（0，40），手机实际（0，60）
     */
    protected List<MyPoint> checkMyPoint = new ArrayList<>(10);

    // 保存参数
    protected SpUtil sp = null;

    // 分别为Y方向和X方向定义不同的步长数组
    int[] yStepsLen = {100, 90, 80, 70, 60};
    int[] xStepsLen = {50, 40, 30, 20, 5};

    // 校验使用的步长
    int[] xCheckStepsLen = {50,  5};

    protected List<Integer> yStepsList = Arrays.asList(5, 20, 30, 40, 50, 60, 70, 80, 90, 100);

    @Override
    public List<CalibrationPoint> getCalibrationPoints() {
        List<CalibrationPoint> points = new ArrayList<>();

        // 第一个点：重置鼠标位置
        CalibrationPoint resetPoint = new CalibrationPoint(0, 0, 1, 400, false);
        resetPoint.setResetMouse(true);
        points.add(resetPoint);

        addCalibrationSteps(points);
        return points;
    }

    @Override
    public List<CalibrationPoint> getPrepareCalibrationPoints()  {
        List<CalibrationPoint> points = new ArrayList<>();
        // 第一个点：重置鼠标位置
        CalibrationPoint resetPoint = new CalibrationPoint(0, 0, 1, 400, false);
        resetPoint.setResetMouse(true);
        points.add(resetPoint);

        addPrepareCalibrationSteps(points);

        return points;
    }

    @Override
    public int parseCalibrationPoints(List<MyPoint> points) {
        return 0;
    }

    /**
     * 快速校验校准点是否符合预期
     * 如果解析成功，则返回 1
     */
    @Override
    public boolean checkPrepareCalibPoints(List<MyPoint> inputPoints) {
        if (inputPoints.isEmpty() || inputPoints.size() < 2) {
            return false;
        }
        // 创建单独的X值列表
        List<Float> xList = new ArrayList<>();

        // 去除完全重复的点
        List<MyPoint> uniquePoints = new ArrayList<>();
        for (int i = 0; i < inputPoints.size(); i++) {
            MyPoint point = inputPoints.get(i);
            // 只与最后一个点比较是否重复
            if (!uniquePoints.isEmpty()) {
                MyPoint lastPoint = uniquePoints.get(uniquePoints.size() - 1);
                // 坐标有波动，y 由于有上下移动，忽略上下 5像素移动的
                if (Math.abs(point.getX() - lastPoint.getX()) < 1.0f &&
                        Math.abs(point.getY() - lastPoint.getY()) < 5.0f) {
                    continue;
                }
            }
            uniquePoints.add(point);
        }

        // 将 uniquePoints 倒序
        Collections.reverse(uniquePoints);
        // 如果去重后点数量不足，直接返回
        if (uniquePoints.size() < 2) {
            printLogE("prepareYList: point not enough");
            return false;
        }


        for (int i = 1; i < uniquePoints.size(); i++) {
            // 获取当前点和前一个点
            MyPoint currentPoint = uniquePoints.get(i);
            MyPoint prevPoint = uniquePoints.get(i - 1);

            // 计算X和Y方向的差值
            float xDiff = Math.abs(currentPoint.getX() - prevPoint.getX());
            float yDiff = Math.abs(currentPoint.getY() - prevPoint.getY());

            // 处理X方向移动
            if (xDiff > 0.5f && yDiff < 5.0f) {
                xList.add(xDiff);
            } else {
                printLogE("checkPrepareCalibPoints: invalid point for horizontal movement" + " prev = " + prevPoint + " current = " + currentPoint  +
                        " xSize:" + xList.size());
                xList.clear();
                return false;
            }
        }

        // 检查值是否一致
        printLog("xList: " + xList);

        // 验证校准点与实际移动值的匹配度
        boolean isCalibrationValid = validateCalibrationValues(xList, xCheckStepsLen);
        return isCalibrationValid;
    }

    /**
     * 验证校准值是否与预期相符
     * @param actualMoveList 实际移动的像素列表
     * @param expectedSteps 预期的步长数组
     * @return 如果校准值与预期相符返回true，否则返回false
     */
    private boolean validateCalibrationValues(List<Float> actualMoveList, int[] expectedSteps) {
        // 检查数据有效性
        if (actualMoveList == null || actualMoveList.isEmpty() || 
            expectedSteps == null || expectedSteps.length == 0 ||
            actualMoveList.size() < expectedSteps.length) {
            printLogE("validateCalibrationValues: invalid input.");
            return false;
        }

        // 允许的误差范围（像素）
        final float MAX_ALLOWED_ERROR = 3.0f;
        boolean allValuesValid = true;
        
        // 倒序遍历步长数组
        for (int i = expectedSteps.length - 1; i >= 0; i--) {
            int step = expectedSteps[i];
            // 查询该步长对应的实际移动像素
            float expectedPixel = queryExpectMovePixelByHid(step);
            // 获取实际测量的像素（从actualMoveList中）
            float actualPixel = actualMoveList.get(expectedSteps.length - 1 - i);
            
            // 计算误差
            float error = Math.abs(expectedPixel - actualPixel);
            
            printLog("步长 " + step + ": 预期像素 = " + expectedPixel + 
                    ", 实际像素 = " + actualPixel + ", 误差 = " + error  + "  ");
            
            // 检查误差是否在允许范围内
            if (error > MAX_ALLOWED_ERROR) {
                printLogE("validateCalibrationValues: 步长 " + step + " 的误差 " + 
                         error + " 超出允许范围 " + MAX_ALLOWED_ERROR);
                allValuesValid = false;
            }
        }
        
        return allValuesValid;
    }

    /**
     * 通过手机侧期望移动的位移像素，查询计算 hid 发送数据
     * @param expectMovePixel 期望移动的像素值
     * @param bTouchMoving 移动过程中，不需要太精确，减少一些微调移动
     * @return Pair<发送的hid数据, 实际移动的像素值, 是否移动到位>
     */
    @Override
    public Triple<Integer, Float, Boolean> queryHidSendData(float expectMovePixel, boolean bTouchMoving) {
        // 参数有效性检查
        if (!isValidInput(expectMovePixel)) {
            return new Triple<>(0, 0.0f, true);
        }

        // 查找最接近的索引
        int foundIndex = findClosestIndex(Math.abs(expectMovePixel));
        float actualMovePixel = indexToYArrayForHid[foundIndex];

        // 计算最低精度（如果尚未计算）
        calculateMinPrecisionIfNeeded();

        // 处理最小精度情况
        boolean isMinPrecision = foundIndex <= (minPrecisionIndex + (bTouchMoving ? 1 : 0));
        if (isMinPrecision) {
            actualMovePixel = handleMinPrecisionCase(expectMovePixel, actualMovePixel);
        }

        // 根据原始expectMovePixel的符号来返回正负值
        int hidValue = expectMovePixel >= 0 ? foundIndex : -foundIndex;
        float actualPixel = expectMovePixel >= 0 ? actualMovePixel : -actualMovePixel;
        
        return new Triple<>(hidValue, actualPixel, isMinPrecision);
    }

    /**
     * 根据HID发送数据查询期望移动的像素值
     * 这是queryHidSendData的逆操作
     * @param hidValue HID发送的数据值
     * @return 期望移动的像素值
     */
    
    private float queryExpectMovePixelByHid(int hidValue) {
        // 参数有效性检查
        if (indexToYArrayForHid == null || indexToYArrayForHid.length == 0) {
            printLog("queryExpectMovePixelByHid: indexToYArrayForHid null");
            return 0.0f;
        }
        
        // 获取索引的绝对值，确保在数组范围内
        int absIndex = Math.abs(hidValue);
        if (absIndex >= indexToYArrayForHid.length) {
            printLogE("queryExpectMovePixelByHid: outOfRange " + absIndex + " >= " + indexToYArrayForHid.length);
            // 如果索引超出范围，使用最大索引
            absIndex = indexToYArrayForHid.length - 1;
        }
        
        // 获取对应的像素值
        float pixelValue = indexToYArrayForHid[absIndex];
        
        // 根据原始hidValue的符号返回正负值
        return hidValue >= 0 ? pixelValue : -pixelValue;
    }

    /**
     * 检查输入参数是否有效
     */
    private boolean isValidInput(float expectMovePixel) {
        return indexToYArrayForHid != null && 
               indexToYArrayForHid.length > 0 && 
               !Float.isNaN(expectMovePixel) && 
               !Float.isInfinite(expectMovePixel);
    }

    /**
     * 查找最接近的索引
     */
    private int findClosestIndex(float targetValue) {
        int foundIndex = findClosestIndexByY(indexToYArrayForHid, targetValue);
        if (foundIndex < 0 || foundIndex >= indexToYArrayForHid.length) {
            foundIndex = 0;
        }
        return foundIndex;
    }

    /**
     * 计算最小精度（如果尚未计算）
     */
    private void calculateMinPrecisionIfNeeded() {
        if (minPrecision >= Float.MAX_VALUE) {
            for (int i = 0; i < indexToYArrayForHid.length; i++) {
                float value = indexToYArrayForHid[i];
                if (value > 0 && value < minPrecision) {
                    minPrecision = value;
                    minPrecisionIndex = i;
                }
            }
        }

    }

    /**
     * 处理最小精度情况
     */
    private float handleMinPrecisionCase(float expectMovePixel, float actualMovePixel) {
        printLog("handleMinPrecisionCase : expectMovePixel=" + expectMovePixel +
                " minPrecision : " + minPrecision + 
                " delta: " + (Math.abs(minPrecision) - Math.abs(actualMovePixel)));
        return Math.abs(minPrecision);
    }

    @Override
    public void clearCalibrationParams() {
        minPrecision = Float.MAX_VALUE;
        minPrecisionIndex = 0;
    }

    @Override
    public boolean isCalibrationReady() {
        return false;
    }

    @Override
    public int getMaxStepsHidUnit() {
        return 0;
    }

    @Override
    public float getMaxMovePixel() {
        return 0;
    }

    /**
     * 根据 Y 值查找最接近的索引值, 注意，只处理正数
     * @param indexToYArray 索引到 Y 值的数组
     * @param targetY 目标 Y 值
     * @return 与目标 Y 值最接近的索引
     */
    private int findClosestIndexByY(float[] indexToYArray, float targetY) {
        if (indexToYArray == null || indexToYArray.length == 0) {
            printLog("findClosestIndexByY: 数组为空或 null");
            return -1;
        }

        int closestIndex = -1;
        float minDifference = Float.MAX_VALUE;

        for (int i = 0; i < indexToYArray.length; i++) {
            float difference = Math.abs(indexToYArray[i] - targetY);
            if (difference < minDifference) {
                minDifference = difference;
                closestIndex = i;
            }
        }

        printLog("targetY:" + targetY + " nearest Index: " + closestIndex);
        return closestIndex;
    }



    /**
     * 检查列表中的值是否严格递增
     * @param list 要检查的浮点数列表
     * @return 如果列表严格递增返回true，否则返回false
     */
    protected boolean isStrictlyIncreasing(List<Float> list) {
        // 如果列表为空或只有一个元素，则认为是递增的
        if (list == null || list.size() <= 1) {
            return true;
        }

        // 遍历列表，检查每个元素是否大于前一个元素
        for (int i = 1; i < list.size(); i++) {
            // 获取当前元素和前一个元素
            float current = list.get(i);
            float previous = list.get(i - 1);

            // 如果当前元素小于或等于前一个元素，则不是严格递增的
            if (current <= previous) {
                // 记录不满足递增条件的位置和值
                printLog("非严格递增: 索引 " + (i-1) + " 的值 " + previous +
                        " 大于或等于索引 " + i + " 的值 " + current);
                return false;
            }
        }

        // 所有元素都满足严格递增条件
        return true;
    }

    /**
     * 打印调试日志
     * @param msg 日志信息
     */
    protected void printLog(String msg){
        LogUtil.d(TAG, msg);
    }


    /**
     * 打印Error日志
     * @param msg 日志信息
     */
    protected void printLogE(String msg){
        LogUtil.e(TAG, msg);
    }

    /**
     * 新的 move 形式的快速校准
     */
    protected void addCalibrationSteps(List<CalibrationPoint> points) {
        // 定义常量，参考CalibrationPoint类中的定义
        // iPhone 14 = 150ms， iPhone 12 = 200ms
        final int DELAY_AFTER_MOVE = 250;    // 移动后延迟时间（ms）
        final int DELAY_AFTER_CLICK = 250;   // 点击后延迟时间（ms）
        final int DELAY_AFTER_CLICK_DOWN = 30; // 点击按下后延迟抬起时间（ms）
        
        // 避开辅助控制按钮
        points.add(new CalibrationPoint(5, 5, DELAY_AFTER_MOVE, DELAY_AFTER_CLICK, DELAY_AFTER_CLICK_DOWN,  false));
        
        // 鼠标按下
        CalibrationPoint mouseDown = new CalibrationPoint(0, 0, DELAY_AFTER_MOVE, DELAY_AFTER_CLICK, DELAY_AFTER_CLICK_DOWN, true, CalibrationPoint.ClickAction.CLICK_DOWN_ONLY);
        points.add(mouseDown);
        

        // 确保两个数组长度一致
        int iterations = Math.min(yStepsLen.length, xStepsLen.length);
        
        for (int i = 0; i < iterations; i++) {
            int yStep = yStepsLen[i];
            int xStep = xStepsLen[i];
            
            // 螺旋式移动：上→左→下→右，每次循环使用对应索引的步长
            
            // 向上移动（使用yStep）
            points.add(new CalibrationPoint(0, yStep, DELAY_AFTER_MOVE, DELAY_AFTER_CLICK, DELAY_AFTER_CLICK_DOWN,  false,  CalibrationPoint.ClickAction.NONE));
            
            // 向左移动（使用xStep）
            points.add(new CalibrationPoint(xStep, 0, DELAY_AFTER_MOVE, DELAY_AFTER_CLICK, DELAY_AFTER_CLICK_DOWN, false,  CalibrationPoint.ClickAction.NONE));
            
            i+=1;
            if (i >= iterations) {
                break;
            }

            yStep = yStepsLen[i];
            xStep = xStepsLen[i];

            // 向下移动（使用yStep）
            points.add(new CalibrationPoint(0, -yStep, DELAY_AFTER_MOVE, DELAY_AFTER_CLICK, DELAY_AFTER_CLICK_DOWN, false,   CalibrationPoint.ClickAction.NONE));

            // 向右移动（使用xStep）
            points.add(new CalibrationPoint(-xStep, 0, DELAY_AFTER_MOVE, DELAY_AFTER_CLICK, DELAY_AFTER_CLICK_DOWN, false,   CalibrationPoint.ClickAction.NONE));
        }
        
        // 鼠标抬起
        CalibrationPoint mouseUp = new CalibrationPoint(0, 0, DELAY_AFTER_MOVE, DELAY_AFTER_CLICK, DELAY_AFTER_CLICK_DOWN,
              true, CalibrationPoint.ClickAction.CLICK_UP_ONLY);
        points.add(mouseUp);
    }

    /**
     * 预备校准点
     */
    protected void addPrepareCalibrationSteps(List<CalibrationPoint> points) {
        // 定义常量，参考CalibrationPoint类中的定义
        // iPhone 14 = 150ms， iPhone 12 = 200ms
        final int DELAY_AFTER_MOVE = 250;    // 移动后延迟时间（ms）
        final int DELAY_AFTER_CLICK = 250;   // 点击后延迟时间（ms）
        final int DELAY_AFTER_CLICK_DOWN = 30; // 点击按下后延迟抬起时间（ms）

        // 鼠标按下
        CalibrationPoint mouseDown = new CalibrationPoint(0, 1, DELAY_AFTER_MOVE, DELAY_AFTER_CLICK, DELAY_AFTER_CLICK_DOWN, false, CalibrationPoint.ClickAction.CLICK_DOWN_ONLY);
        points.add(mouseDown);



        boolean moveRight = true; // 标记当前移动方向

        for (int i = 0; i < xCheckStepsLen.length; i++) {
            int xStep = xCheckStepsLen[i];
            
            // 根据当前方向决定正负值
            int actualXStep = moveRight ? xStep : -xStep;
            
            // 左右往返移动
            points.add(new CalibrationPoint(actualXStep, 0, DELAY_AFTER_MOVE, DELAY_AFTER_CLICK, DELAY_AFTER_CLICK_DOWN, false, CalibrationPoint.ClickAction.NONE));
            
            // 每次移动后切换方向
            moveRight = !moveRight;
        }

        // 鼠标抬起
        CalibrationPoint mouseUp = new CalibrationPoint(0, 0, DELAY_AFTER_MOVE, DELAY_AFTER_CLICK, DELAY_AFTER_CLICK_DOWN,
                true, CalibrationPoint.ClickAction.CLICK_UP_ONLY);
        points.add(mouseUp);
    }


    /**
     * 添加标准校准测试步骤
     * 定义步长序列并按此序列进行往返移动
     * @param points 校准点列表
     */
    protected void addStandardCalibrationSteps(List<CalibrationPoint> points) {
        // 定义新的步长序列，并按此序列进行往返移动
//  int[] ySteps = {100, 75, 50, 45, 40, 35, 30, 25, 20, 15, 10, 5};
        int[] ySteps = {100, 95, 90, 85, 80, 75, 70, 65, 60, 55, 50, 45, 40, 35, 30, 25, 20, 15, 10, 5};
        boolean moveDown = true;

        for (int i = 0; i < ySteps.length; i++) {
            // 获取当前步长
            int currentYStep = ySteps[i];

            // 根据移动方向决定正负值
            int actualYStep = moveDown ? currentYStep : -currentYStep;

            // 绕过辅助触控按钮
            points.add(new CalibrationPoint(0, actualYStep, 250, 250, 30,  false));

            // 每次循环后切换方向， 避免越界
            moveDown = !moveDown;
        }
    }


    /**
     * 准备Y值列表(单调下降的方式校准)
     * @return Y值列表
     */
    protected List<Float> prepareBaseYList() {
        List<Float> yList = new ArrayList<>();
        for (int i = checkMyPoint.size() - 1; i > 0; i--) {
            MyPoint point = checkMyPoint.get(i);
            float diff = point.getY() - (checkMyPoint.get(i - 1).getY());
            yList.add(Math.abs(diff));
        }
        return yList;
    }

    /**
     * 准备Y值列表（螺旋下降）
     * @return Y值列表
     */
    protected List<Float> prepareYList() {
        // 创建单独的X, Y值列表
        List<Float> yList = new ArrayList<>();
        List<Float> xList = new ArrayList<>();


        // 去除完全重复的点
        List<MyPoint> uniquePoints = new ArrayList<>();
        for (int i = 0; i < checkMyPoint.size(); i++) {
            MyPoint point = checkMyPoint.get(i);
            // 只与最后一个点比较是否重复
            if (!uniquePoints.isEmpty()) {
                MyPoint lastPoint = uniquePoints.get(uniquePoints.size() - 1);
                if (arePointsEqual(point, lastPoint)) {
                    continue;
                }
            }
            uniquePoints.add(point);
        }
        
        // 将 uniquePoints 倒序
        Collections.reverse(uniquePoints);
        // 如果去重后点数量不足，直接返回
        if (uniquePoints.size() < 5) {
            printLogE("prepareYList: point not enough");
            return yList;
        }


        
        // 螺旋式移动模式下的数据处理，跳过第一个点是绕过辅助按钮
        boolean isXDirection = true; // 标记当前是否处理X方向
        for (int i = 1; i < uniquePoints.size(); i++) {
            // 获取当前点和前一个点
            MyPoint currentPoint = uniquePoints.get(i);
            MyPoint prevPoint = uniquePoints.get(i - 1);
            
            // 计算X和Y方向的差值
            float xDiff = Math.abs(currentPoint.getX() - prevPoint.getX());
            float yDiff = Math.abs(currentPoint.getY() - prevPoint.getY());
            
            if (isXDirection) {
                // 处理X方向移动
                if (xDiff > 0.5f && yDiff < 1.5f) {
                    xList.add(xDiff);
                } else {
                    printLogE("prepareYList: invalid point for horizontal movement" + " prev = " + prevPoint + " current = " + currentPoint  +
                            " xSize:" + xList.size() + "  yList: " + yList.size());
                    xList.clear();
                    yList.clear();
                    break;
                }
            } else {
                // 处理Y方向移动
                if (yDiff > 0.5f && xDiff < 1.5f) {
                    yList.add(yDiff);
                } else {
                    printLogE("prepareYList: invalid point for vertical movement" + " prev = " + prevPoint + " current = " + currentPoint +
                            " xSize:" + xList.size() + "  yList: " + yList.size());
                    xList.clear();
                    yList.clear();
                    break;
                }
            }
            
            // 切换方向
            isXDirection = !isXDirection;

            if (xList.size() == xStepsLen.length && yList.size() == yStepsLen.length) {
                break;
            }
        }
        
        // 将 xList 中的值添加到 yList 前面
        if (!xList.isEmpty()) {
            yList.addAll(0, xList);
        }

        return yList;
    }

    /**
     * 判断两个点是否相等（坐标有0.3的波动）
     * @param p1 第一个点
     * @param p2 第二个点
     * @return 如果两个点坐标完全一致返回true，否则返回false
     */
    private boolean arePointsEqual(MyPoint p1, MyPoint p2) {
        // 使用浮点数比较，考虑精度问题
        return Math.abs(p1.getX() - p2.getX()) < 1.0f &&
               Math.abs(p1.getY() - p2.getY()) < 1.0f;
    }

}
