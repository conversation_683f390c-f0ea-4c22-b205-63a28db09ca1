package com.autoai.avslinkhid.calibration;

import com.autoai.avslinkhid.model.MyPoint;

import java.util.List;

import kotlin.Triple;

/**
 *  提供校准策略类， 主要实现获取发送校准点和解析校准点过程。
 * 发送校准点可以定义为 发送的坐标以及延迟时间。
 */
public interface CalibrationStrategy {
    /**
     * 获取发送校准点
     * @return 校准点列表
     */
    List<CalibrationPoint> getCalibrationPoints();


    /**
     * 获取预备发送的校准点，需要根据上次的参数动态自动生成，非固定
     * @return 校准点列表
     */
    List<CalibrationPoint> getPrepareCalibrationPoints();

    /**
     * 解析校准点
     * @param points 校准点列表
     * @return 返回是否解析成功
     * 如果解析成功，则返回 1
     */
    int parseCalibrationPoints(List<MyPoint> points);


    /**
     * 快速校验校准点是否符合预期
     * 如果解析成功，则返回 1
     */
    boolean checkPrepareCalibPoints(List<MyPoint> points);

    /**
     * 通过手机侧期望移动的位移像素，查询计算 hid 发送数据
     * @param expectMovePixel 期望移动的像素值
     * @return Triple<发送的hid数据, 实际移动的像素值, 是否已经移动到位>
     */
    Triple<Integer, Float, Boolean> queryHidSendData(float expectMovePixel, boolean bTouchMoving);


    /**
     * 清空校准参数
     */
    void clearCalibrationParams();

    /**
     * 当前是否已经解析校准完成
     */
    boolean isCalibrationReady();

    /**
     * 单次移动最大像素对应的发送单位
     */
    int getMaxStepsHidUnit();


    /**
     * 单次移动手机侧最大移动像素
     */
    float getMaxMovePixel();
}
