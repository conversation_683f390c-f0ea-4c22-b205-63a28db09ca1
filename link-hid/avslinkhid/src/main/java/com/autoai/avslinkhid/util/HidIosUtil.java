package com.autoai.avslinkhid.util;

import android.util.Log;

import com.autoai.avslinkhid.datatransfer.BluetoothHidManager;
import com.autoai.avslinkhid.hidtouchscreen.HidScreenConstantsKt;
import com.autoai.avslinkhid.model.MyPoint;
import com.autoai.common.util.LogUtil;


/**
 * <AUTHOR>
 */
public class HidIosUtil {
    public static final String TAG_IOS_CALIB = ".iOSCalib";
    private static final String TAG = HidScreenConstantsKt.HidScreen + "IosHidUtil";

    /**
     * 互联投屏的ios手机实际的 - 宽
     */
    private static int targetPhonePixelX = 0;

    /**
     * 互联投屏的ios手机实际的 - 高
     */
    private static int targetPhonePixelY = 0;

    /**
     * 车机端实际投屏区域的 - 宽
     */
    private static int viewPixelx = 0;

    /**
     * 车机端实际投屏区域的 - 高
     */
    private static int viewPixelY = 0;

    /**
     * 比率：
     * 实际手机 - 宽 / 车机端投屏 - 宽
     */
    private static float proportionX = 0;

    /**
     * 比率：
     * 实际手机 - 高 / 车机端投屏 - 高
     */
    private static float proportionY = 0;

    /**
     * 横竖屏flag
     */
    private static boolean verticalScreen = true;
    private static MyPoint[] myPoint = new MyPoint[2];
    private static int ARRAY_X_LEN = 80;
    private static int ARRAY_Y_LEN = 40;
    public static int getViewPixelx() {
        return viewPixelx;
    }

    public static int getViewPixelY() {
        return viewPixelY;
    }

    /**
     * 计算比率 {@link #proportionX} 赋值
     * @return
     */
    public static float getProportionX() {
        if(targetPhonePixelX == 0 || viewPixelx == 0 || viewPixelY == 0){
            LogUtil.d(TAG,"targetPhonePixelX  = " + targetPhonePixelX + ", viewPixelx = " + viewPixelx + ", viewPixelY = " + viewPixelY);
            return -1;
        }

        //--> 横屏时候
        if(!verticalScreen){
            proportionX = targetPhonePixelY * 1f / viewPixelx ;
            return proportionX;
        }

        proportionX = targetPhonePixelX * 1f / viewPixelx;
        LogUtil.d(TAG,"targetPhonePixelX = " + targetPhonePixelX +  ", viewPixelX = " + viewPixelx);
        return proportionX;
    }

    /**
     * 计算比率 {@link #proportionY} 赋值
     * @return
     */
    public static float getProportionY() {
        if(targetPhonePixelX == 0 || targetPhonePixelY == 0 || viewPixelx == 0 || viewPixelY == 0){
            return -1;
        }
        //--> 横屏时候
        if(!verticalScreen){
            proportionY = targetPhonePixelX * 1f / viewPixelY;
            return proportionY;
        }

        proportionY = targetPhonePixelY * 1f / viewPixelY;
        LogUtil.d(TAG,"targetPhonePixelY = " + targetPhonePixelY  + ", viewPixelY = " + viewPixelY);
        return proportionY;
    }

    /**
     * 传入实际互联ios设备宽高
     * @param pixelX
     * @param pixelY
     */
    public static void setTargetPhonePixel(int pixelX, int pixelY){
        targetPhonePixelX = pixelX;
        targetPhonePixelY = pixelY;
    }

    /**
     * 获取手机侧分辨率X
     */
    public static int getTargetPhonePixelX() { return targetPhonePixelX; }

    /**
     * 获取手机侧分辨率Y
     */
    public static int getTargetPhonePixelY() { return targetPhonePixelY; }

    /**
     * 设置当前投屏view的宽高
     * @param pixelX
     * @param pixelY
     */
    public static void setViewPixel(int pixelX, int  pixelY){
        viewPixelx = pixelX;
        viewPixelY = pixelY;
        LogUtil.d(TAG,"pixelX = " + pixelX + ", pixelY = " + pixelY);
    }

    /**
     * 设置当前横竖屏状态
     * @param vertical
     */
    public static void setVerticalScreen(boolean vertical){
       verticalScreen = vertical;
       //--> 重新计算右下角复归点
        BluetoothHidManager.getInstance().calibration(300);
    }

    public static boolean getVerticalScreen(){
        return verticalScreen;
    }

    /**
     * 设置 ios home键区域
     * fixme：可以用rect代替
     */
    private static void setPoint(){
        //--> home 键盘区域，
        // X -->（居中 -40）；Y -->（底部 - 80）
        float x =  (viewPixelx - ARRAY_X_LEN) / 2f ;
        float y =  (viewPixelY - ARRAY_Y_LEN) ;
        MyPoint p0 = new MyPoint();
        p0.setX(x);
        p0.setY(y);
        Log.d(TAG,"p0 = " + p0);

        //设置右下角
        x = (viewPixelx + ARRAY_X_LEN) / 2f;
        y = viewPixelY;
        MyPoint p1 = new MyPoint();
        p1.setX(x);
        p1.setY(y);
        LogUtil.d(TAG,"p1 = " + p1);

        myPoint[0] = p0;
        myPoint[1] = p1;
    }

    public static boolean isClickBtn(float x, float y){
        setPoint();
        if(x >= myPoint[0].getX() && x <= myPoint[1].getX() && y >= myPoint[0].getY() && y <= myPoint[1].getY()) {
            return true;
        }
        return false;
    }

    public static boolean areFloatsEqual(float a, float b) {
        final float EPSILON = 0.0001f;
        return Math.abs(a - b) < EPSILON;
    }

    public static boolean getArea(float x){
        LogUtil.d(TAG,"viewPixelx = " + viewPixelx + ", viewPixelY = " + viewPixelY + ", x = " + x);
        if(x >= (viewPixelx /3f) && x <= (viewPixelx /3f * 2)){
            return false;
        }
        return true;
    }

    public static void clean(){
        viewPixelx = 0;
        viewPixelY = 0;
    }

}