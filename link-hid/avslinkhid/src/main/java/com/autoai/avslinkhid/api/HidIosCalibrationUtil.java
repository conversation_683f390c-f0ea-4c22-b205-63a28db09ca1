package com.autoai.avslinkhid.api;

import android.content.Context;

import com.autoai.avslinkhid.datatransfer.BluetoothHidManager;
import com.autoai.avslinkhid.datatransfer.HidIosCalibrationManager;
import com.autoai.avslinkhid.model.MyPoint;
import com.autoai.avslinkhid.util.HidIosUtil;
import com.autoai.common.util.LogUtil;

import java.util.List;

/**
 * <AUTHOR>
 */
public class HidIosCalibrationUtil {
    private static final String TAG = "HidIosCalibrationUtil";
    private static final String TAG_IOSHID = "IOSHID";

    public static void setContext(Context context){
        HidIosCalibrationManager.setContext(context);
    }

    public static boolean getBreakDown(){
        return HidIosCalibrationManager.getBreakDown();
    }

    public static void setBreakDown(boolean breakDown){
        HidIosCalibrationManager.setBreakDown(breakDown);
    }

    public static void requestCalibration(){
        LogUtil.d(TAG + TAG_IOSHID, "HidIosCalibrationUtil::requestCalibration calling BluetoothHidManager.requestCalibration");
        BluetoothHidManager.getInstance().requestCalibration();
    }

    public static boolean checkPoint(List<Double> points){
        return HidIosCalibrationManager.checkPoint(points);
    }


    public static boolean checkPrepareCalibPoints(List<Double>  points) { return HidIosCalibrationManager.checkPrepareCalibPoints(points); }
    public static int getMaxResetStep(){
        return HidIosCalibrationManager.getMaxResetStep();
    }
    public static void startVerification() {
        HidIosCalibrationManager.startVerification();
    }

    public static void startPrepareVerification() {
        HidIosCalibrationManager.startPrepareVerification();
    }
    public static int getHidVerificationState() {
        return HidIosCalibrationManager.getHidVerificationState();
    }

    public static void setHidVerificationState(int hidVerificationState) {
        HidIosCalibrationManager.setHidVerificationState(hidVerificationState);
    }

    public static void registerCallback(CalibrationCallBack callBack){
        LogUtil.d(TAG + TAG_IOSHID, "HidIosCalibrationUtil::registerCallback registering calibration callback");
        HidIosCalibrationManager.registerCallback(callBack);
    }

    public static CalibrationCallBack getBack() {
        return HidIosCalibrationManager.getBack();
    }

    public static boolean isCheckPointFlag() {
        return HidIosCalibrationManager.isCheckPointFlag();
    }

    public static void setCheckPointFlag(boolean checkPointFlag) {
        HidIosCalibrationManager.setCheckPointFlag(checkPointFlag);
    }

    public static boolean isTouchBlocked() {
        return HidIosCalibrationManager.isTouchBlocked();
    }

    public static void setTouchBlocked(boolean touchBlocked) {
        HidIosCalibrationManager.setTouchBlocked(touchBlocked);
    }

    public static void cancelHeartbeat(){
        BluetoothHidManager.getInstance().cancelHeartbeat();
    }

    public static void cleanCache(){
        HidIosCalibrationManager.cleanCache();
    }

    public static void setControlAvailable(boolean controlAvailable) {
        HidIosCalibrationManager.setControlAvailable(controlAvailable);
    }

    /**
     * 更新窗口大小
     * @param currentWidth 宽度
     * @param currentHeight 高度
     */
    public static void updateViewSize(int currentWidth, int currentHeight) {
        LogUtil.i("LinkHid", "LinkHid!! currentWidth = " + currentWidth + ",currentHeight = " + currentHeight + " " + HidIosUtil.getViewPixelx() + "  " + HidIosUtil.getViewPixelY());
        if(HidIosUtil.getViewPixelx() != currentWidth || HidIosUtil.getViewPixelY() != currentHeight){
            //-->投屏区域发生变化，重新更新ios mouse 复归参数
            HidIosUtil.setViewPixel(currentWidth,currentHeight);
            BluetoothHidManager.getInstance().calibration(0);
        }
    }
}
