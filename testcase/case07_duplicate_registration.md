# Case 7: 重复注册场景

## 测试目标
验证重复SDP注册的处理和防护机制

## 前置条件
- 车机蓝牙功能正常
- 手机蓝牙功能正常
- 已建立正常的A2DP连接和互联

## 测试步骤

### 步骤1: 建立正常连接
1. 按照Case 1建立A2DP连接和互联
2. 确认SDP注册成功
3. 启动日志监控：
   ```bash
   adb logcat | grep -E "(HidScreenScreenIOSHID|BtConnectionManager|SdkViewModelBTPair|HidScreenConnectManager::startGetHidProfileAndRegisterScreenDigitizer|HidScreenConnectManager::registerTouchScreenApp)"
   ```

### 步骤2: 重复注册测试
1. 手动触发重复的SDP注册
2. 观察重复注册的处理逻辑
3. 验证防护机制是否生效

### 步骤3: 设备类型切换注册
1. 切换设备类型（iOS/Android）
2. 观察是否正确处理设备类型变化
3. 验证先注销再注册的逻辑

### 步骤4: 异常状态下的注册
1. 在异常状态下尝试注册
2. 验证状态检查和保护机制
3. 确认不会产生资源泄漏

## 预期结果

### 重复注册防护
```
HidScreenConnectManager::startGetHidProfileAndRegisterScreenDigitizer isRegistered=true, isAndroid=false
# 应该直接返回，不重复注册
```

### 设备类型变化时的处理
```
HidUtil::registerHID device type changed, unregistering first
HidScreenConnectManager::unregisterTouchScreenApp: unregisterResult = true
HidUtil::registerHID delaying registration by 500ms due to device type change
HidScreenConnectManager::registerTouchScreenApp registerApp result=true
```

### 状态检查保护
```
HidScreenConnectManager::registerTouchScreenApp: btHidProfileProxy is null, skipping registration
HidScreenConnectManager::startGetHidProfileAndRegisterScreenDigitizer: invalid state, aborting
```

## 验证点

1. **重复注册防护**: 已注册状态下不会重复注册
2. **设备类型处理**: 设备类型变化时正确处理注销和重注册
3. **状态检查**: 注册前进行必要的状态检查
4. **资源管理**: 重复操作不会导致资源泄漏
5. **异常保护**: 异常状态下的保护机制有效

## 特殊测试场景

### 场景1: 快速重复注册
1. 快速连续调用注册接口
2. 验证并发保护机制
3. 确认状态一致性

### 场景2: 异常状态注册
1. 在蓝牙未连接时尝试注册
2. 在互联未建立时尝试注册
3. 验证错误处理

### 场景3: 资源竞争
1. 多个模块同时尝试注册
2. 验证资源访问保护
3. 确认操作原子性

## 预期保护机制

### 状态标志保护
```
HidScreenConnectManager::startGetHidProfileAndRegisterScreenDigitizer: checking isRegistered flag
if (isRegistered) {
    return; // 已注册，直接返回
}
```

### 设备类型变化处理
```
HidUtil::registerHID: deviceType changed from iOS to Android
HidUtil::unregisterHID: unregistering previous device type
Handler::postDelayed: delaying new registration by 500ms
```

### 异常状态检查
```
HidScreenConnectManager::registerTouchScreenApp: btHidProfileProxy validation failed
HidScreenConnectManager::registerTouchScreenApp: device validation failed
```

## 验证点 - 资源管理

1. **内存使用**: 重复注册不应增加内存使用
2. **句柄管理**: 不应产生句柄泄漏
3. **线程安全**: 并发注册应该安全
4. **状态一致性**: 各模块状态应保持一致

## 失败处理

如果测试失败，检查以下项目：
1. 重复注册防护是否正确实现
2. 状态标志管理是否正确
3. 设备类型切换逻辑是否完善
4. 异常处理是否充分

## 清理步骤

1. 确保注册状态正确
2. 断开所有连接
3. 重置注册状态
4. 停止日志监控
