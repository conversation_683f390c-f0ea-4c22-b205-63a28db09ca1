# 测试执行指南

## 测试前准备

### 环境检查
1. **硬件准备**
   - 车机设备正常工作
   - 测试手机（建议准备iPhone和Android各一台）
   - USB数据线（用于有线连接测试）
   - WiFi网络环境稳定

2. **软件准备**
   - 车机应用已安装并可正常启动
   - 手机互联应用已安装
   - adb工具已安装并可正常使用
   - 日志查看工具准备就绪

3. **权限确认**
   - 蓝牙权限已授予
   - 网络权限已授予
   - 位置权限已授予（蓝牙扫描需要）

### 日志监控设置

#### 基础日志过滤
```bash
adb logcat | grep -E "(HidScreenScreenIOSHID|BtConnectionManager|SdkViewModelBTPair|HidScreenConnectManager::getBondedClassicDevice|HidScreenConnectManager::getA2DPConnectedDevice)"
```

#### 详细日志过滤（用于调试）
```bash
adb logcat | grep -E "(HidScreenScreenIOSHID|BtConnectionManager|SdkViewModelBTPair|HidScreenConnectManager|PopWinService::onCarBtPairedConnected|cancelPendingHidConnection|registerTouchScreenApp|unregisterTouchScreenApp)"
```

#### 日志保存
```bash
adb logcat | grep -E "(HidScreenScreenIOSHID|BtConnectionManager|SdkViewModelBTPair)" > test_log_$(date +%Y%m%d_%H%M%S).txt
```

## 测试执行顺序

### 阶段1: 基础功能验证
1. [Case 1: 正常流程 - 先A2DP连接，后互联](./case01_normal_a2dp_first.md)
2. [Case 2: 正常流程 - 先互联，后A2DP连接](./case02_normal_link_first.md)

**目标**: 验证基本的A2DP优先选择逻辑

### 阶段2: 设备管理验证
3. [Case 3: 设备切换场景](./case03_device_switch.md)
4. [Case 5: 边界场景 - 无A2DP设备时的fallback](./case05_no_a2dp_fallback.md)

**目标**: 验证多设备和边界情况的处理

### 阶段3: 异常处理验证
4. [Case 4: 异常场景 - A2DP断开时的处理](./case04_a2dp_disconnect.md)
6. [Case 6: 时序竞争场景](./case06_timing_race.md)
7. [Case 7: 重复注册场景](./case07_duplicate_registration.md)

**目标**: 验证异常情况和边界条件的稳定性

### 阶段4: 配对功能验证
8. [Case 8: 蓝牙配对请求场景](./case08_pairing_request.md)
9. [Case 9: 蓝牙配对状态变化场景](./case09_pairing_state_change.md)
10. [Case 10: 互联过程中的蓝牙配对场景](./case10_pairing_during_link.md)

**目标**: 验证蓝牙配对相关的完整流程

## 测试结果记录

### 测试结果模板
```
测试用例: Case X - 测试名称
执行时间: YYYY-MM-DD HH:MM:SS
测试结果: PASS/FAIL
执行人员: [姓名]

关键日志:
[粘贴关键日志内容]

验证点检查:
□ 验证点1: [结果]
□ 验证点2: [结果]
...

问题记录:
[如有问题，详细描述]

备注:
[其他需要说明的内容]
```

### 汇总报告模板
```
蓝牙A2DP设备优先选择验证测试报告

测试概述:
- 测试时间: [开始时间] - [结束时间]
- 测试环境: [车机型号] + [手机型号]
- 测试人员: [姓名]

测试结果汇总:
- 总用例数: 10
- 通过数: X
- 失败数: Y
- 通过率: Z%

详细结果:
Case 1: PASS/FAIL
Case 2: PASS/FAIL
...

主要问题:
1. [问题描述]
2. [问题描述]

建议:
1. [改进建议]
2. [改进建议]
```

## 常见问题处理

### 问题1: 日志过滤无输出
**原因**: TAG名称可能有变化或日志级别设置问题
**解决**: 
1. 检查实际的TAG名称
2. 调整日志级别：`adb shell setprop log.tag.HidScreenScreenIOSHID VERBOSE`

### 问题2: A2DP连接不稳定
**原因**: 蓝牙干扰或设备兼容性问题
**解决**:
1. 清除蓝牙缓存
2. 重启蓝牙服务
3. 更换测试设备

### 问题3: 互联连接失败
**原因**: 网络问题或应用版本问题
**解决**:
1. 检查网络连接
2. 重启应用
3. 检查应用版本兼容性

### 问题4: 日志信息不完整
**原因**: 日志缓冲区溢出或过滤条件过严
**解决**:
1. 增加日志缓冲区大小
2. 调整过滤条件
3. 分段执行测试

## 测试注意事项

1. **测试环境隔离**: 每个测试用例执行前确保环境清洁
2. **日志完整性**: 确保关键操作的日志都被记录
3. **时序敏感**: 注意操作的时序，特别是延迟相关的测试
4. **异常恢复**: 测试失败后要确保系统能正常恢复
5. **资源清理**: 每个测试用例结束后要清理相关资源

## 测试完成检查

- [ ] 所有测试用例已执行
- [ ] 测试结果已记录
- [ ] 问题已分类整理
- [ ] 日志文件已保存
- [ ] 测试环境已清理
- [ ] 测试报告已生成
