# Case 4: 异常场景 - A2DP断开时的处理

## 测试目标
验证A2DP断开时的SDP取消注册和异常处理

## 前置条件
- 车机和手机已建立A2DP连接
- 互联连接已建立
- SDP已成功注册

## 测试步骤

### 步骤1: 建立正常连接
1. 按照Case 1建立A2DP连接和互联
2. 确认SDP注册成功
3. 启动日志监控：
   ```bash
   adb logcat | grep -E "(HidScreenScreenIOSHID|BtConnectionManager|SdkViewModelBTPair|HidScreenConnectManager::getBondedClassicDevice|HidScreenConnectManager::getA2DPConnectedDevice)"
   ```

### 步骤2: A2DP断开测试
1. **方式1**: 关闭手机蓝牙
2. **方式2**: 在手机上断开与车机的蓝牙连接
3. **方式3**: 在车机上断开蓝牙连接
4. 观察断开处理日志

### 步骤3: 验证SDP注销
1. 确认SDP注销日志
2. 验证资源清理
3. 检查设备选择逻辑变化

### 步骤4: 验证互联状态
1. 确认互联连接是否保持
2. 验证应用状态是否正确更新
3. 检查是否有异常或崩溃

### 步骤5: 重连测试
1. 重新建立A2DP连接
2. 验证SDP重新注册
3. 确认功能恢复正常

## 预期结果

### A2DP断开检测
```
BtConnectionManager::handleA2DPConnectionStateChange: 蓝牙连接状态变化: device=iPhone(XX:XX:XX:XX:XX:XX), prevState=CONNECTED, nowState=DISCONNECTED
BtConnectionManager::handleA2DPConnectionStateChange: 经典蓝牙设备已断开, device=iPhone
```

### 待处理任务取消
```
BtConnectionManager::handleA2DPConnectionStateChange: 取消待处理的HID连接任务
BtConnectionManager::cancelPendingHidConnection: cancelled pending HID connection
```

### SDP注销
```
HidScreenConnectManager::unregisterTouchScreenApp: btHidDevice = [object], btDevice = [device]
HidScreenConnectManager::unregisterTouchScreenApp: unregisterResult = true
```

### 设备集合更新
```
BtConnectionManager::checkRemainingConnections: remaining devices=0
HidScreenConnectManager::getA2DPConnectedDevice: no connected devices
HidScreenConnectManager::getBondedClassicDevice: no suitable device found
```

### 配对状态更新
```
PopWinService::onCarBtPairedConnected: false
SdkViewModel::updateBTPairedWithPhone BT paired status=false
```

## 验证点

1. **断开检测**: A2DP断开能被正确检测
2. **任务取消**: 待处理的HID连接任务能被正确取消
3. **SDP注销**: SDP能被正确注销
4. **资源清理**: 相关资源能被正确清理
5. **状态更新**: 各模块状态能正确更新
6. **互联保持**: 互联连接不受A2DP断开影响

## 异常场景测试

### 场景1: 异常断开
1. 手机突然关机或移出范围
2. 验证超时处理和资源清理

### 场景2: 部分断开
1. 只断开A2DP，保持其他蓝牙连接
2. 验证选择性处理

### 场景3: 快速重连
1. 快速断开后立即重连
2. 验证状态机的稳定性

## 预期异常处理结果

### 超时处理
```
BtConnectionManager::handleA2DPConnectionStateChange: connection timeout detected
BtConnectionManager::checkRemainingConnections: cleaning up disconnected device
```

### 异常保护
```
HidScreenConnectManager::unregisterTouchScreenApp: handling exception during unregister
HidScreenConnectManager::getBondedClassicDevice: btHidProfileProxy is null, returning null
```

## 验证点 - 异常处理

1. **超时处理**: 异常断开时的超时处理是否正确
2. **异常保护**: 各种异常情况下不应崩溃
3. **状态一致性**: 异常后各模块状态应保持一致
4. **资源安全**: 不应有内存泄漏或资源未释放

## 失败处理

如果测试失败，检查以下项目：
1. 断开检测机制是否正常
2. SDP注销逻辑是否正确
3. 异常处理是否完善
4. 状态同步是否及时

## 清理步骤

1. 确保所有连接已断开
2. 验证资源已清理
3. 停止日志监控
4. 重启应用（如有必要）
