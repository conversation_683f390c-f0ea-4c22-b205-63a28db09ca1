# Case 10: 互联过程中的蓝牙配对场景

## 测试目标
验证互联过程中进行蓝牙配对的处理和状态同步

## 前置条件
- 车机蓝牙功能正常
- 手机蓝牙功能正常
- 车机和手机未配对
- 网络环境稳定（用于WiFi互联）

## 测试步骤

### 步骤1: 环境准备
1. 确保车机蓝牙开启
2. 确保手机蓝牙开启但未连接车机
3. 清除之前的配对记录
4. 启动日志监控：
   ```bash
   adb logcat | grep -E "(HidScreenScreenIOSHID|BtConnectionManager|SdkViewModelBTPair|HidScreenConnectManager::getBondedClassicDevice|PopWinService::onCarBtPairedConnected)"
   ```

### 步骤2: 启动互联（无蓝牙配对）
1. 在手机上启动互联应用
2. 选择WiFi连接方式
3. 等待互联连接成功
4. 观察无蓝牙配对时的状态

### 步骤3: 互联过程中配对
1. 在互联建立后，开始蓝牙配对过程
2. 在手机上搜索并选择车机蓝牙
3. 完成配对和A2DP连接
4. 观察配对过程中的状态变化

### 步骤4: 验证状态同步
1. 确认配对状态正确同步
2. 观察SDP注册触发
3. 验证设备选择逻辑更新
4. 检查MAC地址匹配

### 步骤5: 功能验证
1. 验证互联功能正常
2. 确认蓝牙功能正常
3. 测试设备选择优先级
4. 验证配对状态持久性

## 预期结果

### 互联成功（无蓝牙配对）
```
SdkViewModel::handleCarBTConnected no A2DP connected device found
HidScreenConnectManager::getA2DPConnectedDevice: no connected devices
SdkViewModel::handleCarBTConnected pairing status: connected=false, phoneMac=, connectedPhoneMac=, isPhoneMacMatched=false, finalPairedStatus=false
```

### 配对过程开始
```
BtConnectionManager::handleDeviceConnected: 当前连接的蓝牙设备=iPhone(XX:XX:XX:XX:XX:XX), total=1
BtConnectionManager::handleA2DPConnectionStateChange: 蓝牙连接状态变化: device=iPhone(XX:XX:XX:XX:XX:XX), prevState=DISCONNECTED, nowState=CONNECTED
```

### 设备选择更新
```
HidScreenConnectManager::getA2DPConnectedDevice: found A2DP device=iPhone(XX:XX:XX:XX:XX:XX)
HidScreenConnectManager::getBondedClassicDevice: switching to A2DP device=iPhone(XX:XX:XX:XX:XX:XX)
```

### 配对状态同步
```
PopWinService::onCarBtPairedConnected: true
SdkViewModel::updateBTPairedWithPhone BT paired status=true
```

### SDP注册触发
```
HidScreenConnectManager::registerTouchScreenApp registerApp result=true
SdkViewModel::handleCarBTConnected >>> sent A2DP connected phone MAC to phone: XX:XX:XX:XX:XX:XX
```

### 最终状态确认
```
SdkViewModel::handleCarBTConnected pairing status: connected=true, phoneMac=XX:XX:XX:XX:XX:XX, connectedPhoneMac=XX:XX:XX:XX:XX:XX, isPhoneMacMatched=true, finalPairedStatus=true
```

## 验证点

1. **互联独立性**: 互联功能不依赖蓝牙配对
2. **动态配对**: 互联过程中能正确处理蓝牙配对
3. **状态同步**: 配对状态能实时同步到互联模块
4. **设备选择**: 配对后设备选择逻辑能正确更新
5. **SDP触发**: 配对成功后能正确触发SDP注册
6. **功能协同**: 互联和蓝牙功能能正确协同工作

## 特殊测试场景

### 场景1: 互联建立中配对
1. 在互联连接建立过程中进行配对
2. 观察并发处理能力
3. 验证状态机稳定性

### 场景2: 配对失败处理
1. 在互联过程中配对失败
2. 观察失败后的状态处理
3. 验证互联功能不受影响

### 场景3: 多次配对尝试
1. 在互联过程中多次尝试配对
2. 观察重复操作的处理
3. 验证状态一致性

## 时序验证点

### 正确的时序应该是：
```
1. 互联连接建立
2. 互联状态：无蓝牙配对
3. 蓝牙配对开始
4. A2DP连接建立
5. 配对状态同步
6. 设备选择更新
7. SDP注册触发
8. 最终状态：互联+蓝牙配对
```

## 验证点 - 协同工作

1. **状态独立**: 互联和蓝牙状态应该相对独立
2. **功能增强**: 蓝牙配对应该增强而不是替代互联功能
3. **用户体验**: 配对过程不应影响互联的用户体验
4. **资源共享**: 两个功能应该正确共享资源

## 失败处理

如果测试失败，检查以下项目：
1. 互联和蓝牙模块的协调是否正确
2. 状态同步机制是否及时
3. 并发处理是否稳定
4. 资源竞争是否得到正确处理

## 清理步骤

1. 断开蓝牙连接
2. 断开互联连接
3. 清理配对记录
4. 停止日志监控
5. 恢复测试环境
