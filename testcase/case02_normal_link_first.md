# Case 2: 正常流程 - 先互联，后A2DP连接

## 测试目标
验证互联后A2DP连接时的SDP注册和设备选择逻辑

## 前置条件
- 车机蓝牙功能正常
- 手机蓝牙功能正常
- 车机和手机未连接

## 测试步骤

### 步骤1: 环境准备
1. 确保车机蓝牙开启
2. 确保手机蓝牙关闭或未连接车机
3. 启动日志监控：
   ```bash
   adb logcat | grep -E "(HidScreenScreenIOSHID|BtConnectionManager|SdkViewModelBTPair|HidScreenConnectManager::getBondedClassicDevice|HidScreenConnectManager::getA2DPConnectedDevice)"
   ```

### 步骤2: 启动互联
1. 在手机上启动互联应用
2. 选择WiFi或USB连接方式
3. 等待互联连接成功
4. 观察日志输出（此时应该没有A2DP设备）

### 步骤3: A2DP连接
1. 开启手机蓝牙
2. 在手机上搜索并连接车机蓝牙
3. 确认A2DP连接成功
4. 观察日志输出

### 步骤4: 验证设备选择和SDP注册
1. 观察A2DP连接后的设备选择逻辑
2. 确认SDP重新注册或更新
3. 验证配对状态变化

## 预期结果

### 互联成功阶段（无A2DP）
```
SdkViewModel::handleCarBTConnected no A2DP connected device found
HidScreenConnectManager::getA2DPConnectedDevice: no connected devices
HidScreenConnectManager::getBondedClassicDevice: using fallback device=null
```

### A2DP连接后
```
BtConnectionManager::handleA2DPConnectionStateChange: 蓝牙连接状态变化: device=iPhone(XX:XX:XX:XX:XX:XX), prevState=DISCONNECTED, nowState=CONNECTED
BtConnectionManager::handleA2DPConnectionStateChange: 经典蓝牙设备已连接, device=iPhone
HidScreenConnectManager::getA2DPConnectedDevice: found A2DP device=iPhone(XX:XX:XX:XX:XX:XX)
HidScreenConnectManager::getBondedClassicDevice: using A2DP device=iPhone(XX:XX:XX:XX:XX:XX)
```

### SDP注册更新
```
HidScreenConnectManager::registerTouchScreenApp registerApp result=true
SdkViewModel::handleCarBTConnected >>> sent A2DP connected phone MAC to phone: XX:XX:XX:XX:XX:XX
```

### 配对状态更新
```
SdkViewModel::handleCarBTConnected pairing status: connected=true, phoneMac=XX:XX:XX:XX:XX:XX, connectedPhoneMac=XX:XX:XX:XX:XX:XX, isPhoneMacMatched=true, finalPairedStatus=true
PopWinService::onCarBtPairedConnected: true
SdkViewModel::updateBTPairedWithPhone BT paired status=true
```

## 验证点

1. **互联成功无A2DP**: 初始互联时没有A2DP设备被选择
2. **A2DP连接检测**: A2DP连接后能正确检测到设备
3. **设备选择更新**: `getBondedClassicDevice` 能正确切换到A2DP设备
4. **SDP重新注册**: A2DP连接后触发SDP注册
5. **配对状态同步**: 配对状态能正确更新到应用层

## 特殊验证点

1. **时序处理**: 验证互联和A2DP连接的时序处理是否正确
2. **状态同步**: 验证各模块间的状态同步是否及时
3. **资源管理**: 验证是否有资源泄漏或重复注册

## 失败处理

如果测试失败，检查以下项目：
1. 互联连接是否稳定
2. A2DP连接是否正常
3. 状态同步机制是否工作正常
4. 是否存在时序竞争问题

## 清理步骤

1. 断开A2DP连接
2. 断开互联连接
3. 停止日志监控
4. 清理测试环境
