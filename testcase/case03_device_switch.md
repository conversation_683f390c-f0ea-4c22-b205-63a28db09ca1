# Case 3: 设备切换场景

## 测试目标
验证多设备时A2DP设备的优先选择和设备切换处理

## 前置条件
- 车机蓝牙功能正常
- 两台手机（手机A和手机B）蓝牙功能正常
- 车机支持多设备连接或快速切换

## 测试步骤

### 步骤1: 环境准备
1. 确保车机蓝牙开启
2. 准备两台手机（手机A和手机B）
3. 启动日志监控：
   ```bash
   adb logcat | grep -E "(HidScreenScreenIOSHID|BtConnectionManager|SdkViewModelBTPair|HidScreenConnectManager::getBondedClassicDevice|HidScreenConnectManager::getA2DPConnectedDevice)"
   ```

### 步骤2: 手机A连接和互联
1. 手机A连接车机蓝牙（A2DP连接）
2. 手机A启动互联
3. 确认连接成功，观察日志
4. 记录手机A的MAC地址

### 步骤3: 设备切换 - 断开手机A
1. 断开手机A的蓝牙连接
2. 观察断开日志
3. 确认SDP注销或设备选择变化

### 步骤4: 手机B连接
1. 手机B连接车机蓝牙（A2DP连接）
2. 观察设备选择变化
3. 确认新设备被正确选择

### 步骤5: 验证设备优先级
1. 如果手机A重新连接，观察设备选择
2. 验证A2DP设备的优先级处理
3. 确认MAC地址匹配逻辑

## 预期结果

### 手机A连接时
```
BtConnectionManager::handleA2DPConnectionStateChange: 蓝牙连接状态变化: device=PhoneA(AA:AA:AA:AA:AA:AA), prevState=DISCONNECTED, nowState=CONNECTED
HidScreenConnectManager::getA2DPConnectedDevice: found A2DP device=PhoneA(AA:AA:AA:AA:AA:AA)
HidScreenConnectManager::getBondedClassicDevice: using A2DP device=PhoneA(AA:AA:AA:AA:AA:AA)
HidScreenConnectManager::registerTouchScreenApp registerApp result=true
```

### 手机A断开时
```
BtConnectionManager::handleA2DPConnectionStateChange: 蓝牙连接状态变化: device=PhoneA(AA:AA:AA:AA:AA:AA), prevState=CONNECTED, nowState=DISCONNECTED
BtConnectionManager::handleA2DPConnectionStateChange: 经典蓝牙设备已断开, device=PhoneA
BtConnectionManager::checkRemainingConnections: remaining devices=0
HidScreenConnectManager::unregisterTouchScreenApp: unregisterResult=true
```

### 手机B连接时
```
BtConnectionManager::handleA2DPConnectionStateChange: 蓝牙连接状态变化: device=PhoneB(BB:BB:BB:BB:BB:BB), prevState=DISCONNECTED, nowState=CONNECTED
HidScreenConnectManager::getA2DPConnectedDevice: found A2DP device=PhoneB(BB:BB:BB:BB:BB:BB)
HidScreenConnectManager::getBondedClassicDevice: using A2DP device=PhoneB(BB:BB:BB:BB:BB:BB)
HidScreenConnectManager::registerTouchScreenApp registerApp result=true
```

### MAC地址更新
```
SdkViewModel::getCurrentA2DPConnectedDeviceMac: found A2DP device MAC=PhoneB(BB:BB:BB:BB:BB:BB)
SdkViewModel::handleCarBTConnected >>> sent A2DP connected phone MAC to phone: BB:BB:BB:BB:BB:BB
```

## 验证点

1. **设备断开检测**: 手机A断开时能正确检测到断开事件
2. **SDP注销**: 设备断开时能正确注销SDP
3. **新设备检测**: 手机B连接时能正确检测到新设备
4. **设备选择切换**: `getBondedClassicDevice` 能正确切换到新的A2DP设备
5. **SDP重新注册**: 新设备连接时能重新注册SDP
6. **MAC地址更新**: MAC地址能正确更新为新设备的地址

## 特殊验证点

1. **设备优先级**: 如果多个设备同时连接，验证A2DP设备的优先级
2. **状态一致性**: 验证各模块间的设备状态是否一致
3. **资源清理**: 验证旧设备断开时的资源清理是否完整

## 边界测试

1. **快速切换**: 快速断开和连接设备，验证处理稳定性
2. **同时连接**: 如果支持，测试多设备同时连接的处理
3. **异常断开**: 模拟异常断开（如手机关机）的处理

## 失败处理

如果测试失败，检查以下项目：
1. 设备切换逻辑是否正确
2. 状态同步是否及时
3. 资源清理是否完整
4. 是否存在内存泄漏

## 清理步骤

1. 断开所有蓝牙连接
2. 断开互联连接
3. 停止日志监控
4. 清理测试环境
