# Case 8: 蓝牙配对请求场景

## 测试目标
验证蓝牙配对请求时的处理和配对成功后的设备选择

## 前置条件
- 车机蓝牙功能正常
- 手机蓝牙功能正常
- 车机和手机未配对

## 测试步骤

### 步骤1: 环境准备
1. 确保车机蓝牙开启且可被发现
2. 确保手机蓝牙开启
3. 清除之前的配对记录
4. 启动日志监控：
   ```bash
   adb logcat | grep -E "(HidScreenScreenIOSHID|BtConnectionManager|SdkViewModelBTPair|HidScreenConnectManager::getBondedClassicDevice|PopWinService::onCarBtPairedConnected)"
   ```

### 步骤2: 发起配对请求
1. 在手机上搜索蓝牙设备
2. 选择车机蓝牙设备
3. 发起配对请求
4. 观察车机端的配对处理

### 步骤3: 完成配对
1. 在车机端确认配对
2. 等待配对完成
3. 观察配对成功后的状态变化

### 步骤4: 验证A2DP连接
1. 确认A2DP连接是否自动建立
2. 观察设备选择逻辑
3. 验证设备信息记录

### 步骤5: 启动互联
1. 在配对成功后启动互联
2. 观察配对状态识别
3. 验证SDP注册

## 预期结果

### 配对过程
```
BtConnectionManager::handleDeviceConnected: 当前连接的蓝牙设备=iPhone, total=1
BtConnectionManager::handleA2DPConnectionStateChange: 蓝牙连接状态变化: device=iPhone(XX:XX:XX:XX:XX:XX), prevState=DISCONNECTED, nowState=CONNECTED
```

### 设备选择
```
HidScreenConnectManager::getA2DPConnectedDevice: found A2DP device=iPhone(XX:XX:XX:XX:XX:XX)
HidScreenConnectManager::getBondedClassicDevice: using A2DP device=iPhone(XX:XX:XX:XX:XX:XX)
```

### 配对状态确认
```
PopWinService::onDeviceConnected: iPhone(XX:XX:XX:XX:XX:XX)
PopWinService::onCarBtPairedConnected: true
SdkViewModel::updateBTPairedWithPhone BT paired status=true
```

### 互联后的处理
```
SdkViewModel::handleCarBTConnected >>> sent A2DP connected phone MAC to phone: XX:XX:XX:XX:XX:XX
SdkViewModel::handleCarBTConnected pairing status: connected=true, phoneMac=XX:XX:XX:XX:XX:XX, connectedPhoneMac=XX:XX:XX:XX:XX:XX, isPhoneMacMatched=true, finalPairedStatus=true
```

## 验证点

1. **配对检测**: 配对请求和完成能被正确检测
2. **A2DP建立**: 配对后A2DP连接能正确建立
3. **设备选择**: 新配对的A2DP设备能被正确选择
4. **状态同步**: 配对状态能正确同步到各模块
5. **MAC地址记录**: 设备MAC地址能正确记录和匹配

## 特殊测试场景

### 场景1: 配对失败处理
1. 故意让配对失败（拒绝配对）
2. 观察失败后的状态处理
3. 验证资源清理

### 场景2: 配对超时
1. 配对过程中不响应
2. 观察超时处理机制
3. 验证状态恢复

### 场景3: 重复配对
1. 对已配对设备重新配对
2. 观察重复配对的处理
3. 验证状态一致性

## 预期配对流程日志

### 配对开始
```
BtConnectionManager::SystemBluetoothReceiver: 经典蓝牙状态变化 action=android.bluetooth.device.action.PAIRING_REQUEST
# 注意：实际代码中可能没有直接处理配对请求，但会有连接状态变化
```

### 配对成功
```
BtConnectionManager::handleDeviceConnected: 当前连接的蓝牙设备=iPhone(XX:XX:XX:XX:XX:XX), total=1
BtConnectionManager::handleA2DPConnectionStateChange: 经典蓝牙设备已连接, device=iPhone
```

### 设备信息记录
```
HidScreenConnectManager::getCurrentA2DPConnectedDeviceMac: 找到A2DP连接设备: iPhone (XX:XX:XX:XX:XX:XX)
```

## 验证点 - 配对状态

1. **配对完成检测**: 配对完成能被正确检测
2. **设备信息获取**: 配对设备的名称和MAC地址能正确获取
3. **连接状态管理**: 配对后的连接状态能正确管理
4. **优先级应用**: 新配对的A2DP设备能获得正确优先级

## 失败处理

如果测试失败，检查以下项目：
1. 配对流程是否正常
2. A2DP Profile是否正确建立
3. 设备信息获取是否正确
4. 状态同步是否及时

## 清理步骤

1. 断开蓝牙连接
2. 清除配对记录（如需要）
3. 断开互联连接
4. 停止日志监控
