# Case 5: 边界场景 - 无A2DP设备时的fallback

## 测试目标
验证无A2DP设备时的降级处理和fallback机制

## 前置条件
- 车机蓝牙功能正常
- 手机支持互联但不连接A2DP
- 或者有其他蓝牙设备但非A2DP类型

## 测试步骤

### 步骤1: 环境准备
1. 确保车机蓝牙开启
2. 确保没有A2DP连接的设备
3. 可选：连接非A2DP蓝牙设备（如键盘、鼠标等）
4. 启动日志监控：
   ```bash
   adb logcat | grep -E "(HidScreenScreenIOSHID|BtConnectionManager|SdkViewModelBTPair|HidScreenConnectManager::getBondedClassicDevice|HidScreenConnectManager::getA2DPConnectedDevice)"
   ```

### 步骤2: 启动互联（无A2DP）
1. 在手机上启动互联应用
2. 选择WiFi或USB连接方式
3. 等待互联连接成功
4. 观察设备选择逻辑

### 步骤3: 验证fallback机制
1. 观察 `getA2DPConnectedDevice` 的返回结果
2. 确认 `getBondedClassicDevice` 的fallback逻辑
3. 验证SDP注册行为

### 步骤4: 添加非A2DP设备测试
1. 连接非A2DP蓝牙设备（如果可能）
2. 观察设备选择是否受影响
3. 验证A2DP设备的优先级

### 步骤5: A2DP设备后加入
1. 在互联过程中连接A2DP设备
2. 验证设备选择是否正确切换
3. 确认优先级处理

## 预期结果

### 无A2DP设备时
```
HidScreenConnectManager::getA2DPConnectedDevice: no connected devices
HidScreenConnectManager::getBondedClassicDevice: no A2DP device found
```

### fallback处理
```
HidScreenConnectManager::getBondedClassicDevice: using fallback device=null
# 或者如果有其他设备
HidScreenConnectManager::getBondedClassicDevice: using fallback device=OtherDevice(ZZ:ZZ:ZZ:ZZ:ZZ:ZZ)
```

### SDP注册处理
```
HidScreenConnectManager::registerTouchScreenApp: no suitable device for SDP registration
# 或者
HidScreenConnectManager::registerTouchScreenApp registerApp result=false
```

### 互联状态处理
```
SdkViewModel::handleCarBTConnected no A2DP connected device found
SdkViewModel::handleCarBTConnected pairing status: connected=false, phoneMac=, connectedPhoneMac=, isPhoneMacMatched=false, finalPairedStatus=false
```

### A2DP设备后加入时
```
BtConnectionManager::handleA2DPConnectionStateChange: 蓝牙连接状态变化: device=iPhone(XX:XX:XX:XX:XX:XX), prevState=DISCONNECTED, nowState=CONNECTED
HidScreenConnectManager::getA2DPConnectedDevice: found A2DP device=iPhone(XX:XX:XX:XX:XX:XX)
HidScreenConnectManager::getBondedClassicDevice: switching to A2DP device=iPhone(XX:XX:XX:XX:XX:XX)
HidScreenConnectManager::registerTouchScreenApp registerApp result=true
```

## 验证点

1. **无A2DP检测**: 能正确检测到没有A2DP连接设备
2. **fallback逻辑**: fallback机制能正确处理无A2DP情况
3. **SDP注册策略**: 无合适设备时的SDP注册策略
4. **互联功能**: 互联功能不受A2DP缺失影响
5. **优先级切换**: A2DP设备后加入时能正确切换优先级

## 边界测试场景

### 场景1: 完全无蓝牙设备
1. 关闭所有蓝牙设备
2. 仅通过WiFi/USB互联
3. 验证系统稳定性

### 场景2: 只有非A2DP设备
1. 连接蓝牙键盘、鼠标等
2. 验证这些设备不被误选为A2DP设备
3. 确认优先级处理正确

### 场景3: A2DP设备间歇连接
1. A2DP设备连接后立即断开
2. 验证快速切换的处理
3. 确认状态机稳定性

## 特殊验证点

1. **空指针处理**: 各种空设备情况下不应出现空指针异常
2. **资源管理**: 无设备时的资源管理是否正确
3. **状态一致性**: 各模块对无设备状态的理解是否一致
4. **用户体验**: 无A2DP时用户是否能正常使用互联功能

## 预期异常保护

```
HidScreenConnectManager::getA2DPConnectedDevice: deviceSet is empty, returning null
HidScreenConnectManager::getBondedClassicDevice: btHidProfileProxy is null, returning null
SdkViewModel::getCurrentA2DPConnectedDeviceMac: no A2DP device found, returning empty string
```

## 失败处理

如果测试失败，检查以下项目：
1. fallback逻辑是否正确实现
2. 空指针保护是否完善
3. 设备类型判断是否准确
4. 状态同步是否正常

## 清理步骤

1. 断开所有蓝牙连接
2. 断开互联连接
3. 停止日志监控
4. 恢复正常测试环境
