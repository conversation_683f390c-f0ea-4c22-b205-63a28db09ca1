# Case 9: 蓝牙配对状态变化场景

## 测试目标
验证蓝牙配对状态变化时的处理，包括取消配对和重新配对

## 前置条件
- 车机和手机已经配对并连接
- A2DP连接已建立
- 互联连接已建立
- SDP已成功注册

## 测试步骤

### 步骤1: 建立完整连接
1. 按照Case 1建立完整的连接状态
2. 确认所有功能正常工作
3. 启动日志监控：
   ```bash
   adb logcat | grep -E "(HidScreenScreenIOSHID|BtConnectionManager|SdkViewModelBTPair|PopWinService::onCarBtPairedConnected|checkRemainingConnections)"
   ```

### 步骤2: 取消配对
1. 在手机上进入蓝牙设置
2. 找到车机设备，选择"取消配对"或"忽略此设备"
3. 确认取消配对操作
4. 观察车机端的状态变化

### 步骤3: 验证状态清理
1. 观察配对状态变化日志
2. 确认SDP注销
3. 验证设备选择逻辑变化
4. 检查资源清理

### 步骤4: 重新配对
1. 重新搜索并配对车机设备
2. 观察重新配对的处理
3. 验证状态恢复
4. 确认功能正常

### 步骤5: 验证互联状态
1. 确认互联连接是否受影响
2. 验证配对状态同步
3. 检查SDP重新注册

## 预期结果

### 取消配对时
```
BtConnectionManager::handleA2DPConnectionStateChange: 蓝牙连接状态变化: device=iPhone(XX:XX:XX:XX:XX:XX), prevState=CONNECTED, nowState=DISCONNECTED
BtConnectionManager::handleA2DPConnectionStateChange: 经典蓝牙设备已断开, device=iPhone
BtConnectionManager::checkRemainingConnections: remaining devices=0
```

### 配对状态更新
```
PopWinService::onCarBtPairedConnected: false
SdkViewModel::updateBTPairedWithPhone BT paired status=false
```

### SDP注销
```
HidScreenConnectManager::unregisterTouchScreenApp: btHidDevice = [object], btDevice = [device]
HidScreenConnectManager::unregisterTouchScreenApp: unregisterResult = true
```

### 设备选择变化
```
HidScreenConnectManager::getA2DPConnectedDevice: no connected devices
HidScreenConnectManager::getBondedClassicDevice: no suitable device found
```

### 重新配对后
```
BtConnectionManager::handleDeviceConnected: 当前连接的蓝牙设备=iPhone(XX:XX:XX:XX:XX:XX), total=1
PopWinService::onCarBtPairedConnected: true
SdkViewModel::updateBTPairedWithPhone BT paired status=true
HidScreenConnectManager::registerTouchScreenApp registerApp result=true
```

## 验证点

1. **配对状态检测**: 取消配对能被正确检测
2. **连接断开处理**: 配对取消导致的连接断开能正确处理
3. **SDP注销**: 配对取消时SDP能正确注销
4. **状态清理**: 相关状态能被正确清理
5. **重新配对**: 重新配对能正确恢复功能
6. **互联保持**: 互联连接不受配对状态影响

## 特殊测试场景

### 场景1: 部分取消配对
1. 只取消A2DP连接，保持其他蓝牙服务
2. 观察选择性处理
3. 验证状态一致性

### 场景2: 强制断开
1. 通过车机端强制断开配对
2. 观察主动断开的处理
3. 验证双向状态同步

### 场景3: 配对冲突
1. 同时有多个设备尝试配对
2. 观察冲突处理机制
3. 验证优先级处理

## 预期状态变化序列

### 取消配对序列
```
1. BtConnectionManager::handleA2DPConnectionStateChange: DISCONNECTED
2. BtConnectionManager::checkRemainingConnections: device removed
3. PopWinService::onCarBtPairedConnected: false
4. HidScreenConnectManager::unregisterTouchScreenApp: unregister
5. SdkViewModel::updateBTPairedWithPhone: false
```

### 重新配对序列
```
1. BtConnectionManager::handleDeviceConnected: new device
2. BtConnectionManager::handleA2DPConnectionStateChange: CONNECTED
3. PopWinService::onCarBtPairedConnected: true
4. HidScreenConnectManager::registerTouchScreenApp: register
5. SdkViewModel::updateBTPairedWithPhone: true
```

## 验证点 - 状态一致性

1. **模块同步**: 各模块的配对状态应保持一致
2. **时序正确**: 状态变化的时序应该正确
3. **资源管理**: 配对状态变化时资源管理正确
4. **用户体验**: 状态变化对用户体验的影响最小

## 失败处理

如果测试失败，检查以下项目：
1. 配对状态检测是否正确
2. 状态同步机制是否正常
3. 资源清理是否完整
4. 重新配对逻辑是否正确

## 清理步骤

1. 确保配对状态正确
2. 断开所有连接
3. 清理配对记录
4. 停止日志监控
5. 恢复测试环境
