# 蓝牙A2DP设备优先选择验证测试用例

## 概述

本测试用例集用于验证 `getBondedClassicDevice` 方法优化后的A2DP设备优先选择逻辑，以及SDP注册和取消注册的完整流程。

## 测试目标

1. 验证A2DP连接设备的优先选择逻辑
2. 验证SDP注册和取消注册的时机
3. 验证设备切换时的处理
4. 验证各种异常场景的处理
5. 验证蓝牙配对相关的处理

## 测试环境

- **设备**: 车机 + 手机（iPhone/Android）
- **工具**: adb logcat
- **日志过滤命令**: 
  ```bash
  adb logcat | grep -E "(HidScreenScreenIOSHID|BtConnectionManager|SdkViewModelBTPair|HidScreenConnectManager::getBondedClassicDevice|HidScreenConnectManager::getA2DPConnectedDevice)"
  ```

## 关键日志TAG

- **SDP注册**: `HidScreenScreenIOSHID`
- **A2DP连接**: `BtConnectionManager`
- **设备选择**: `HidScreenConnectManager::getBondedClassicDevice`
- **配对状态**: `SdkViewModelBTPair`

## 测试用例列表

1. [Case 1: 正常流程 - 先A2DP连接，后互联](./case01_normal_a2dp_first.md)
2. [Case 2: 正常流程 - 先互联，后A2DP连接](./case02_normal_link_first.md)
3. [Case 3: 设备切换场景](./case03_device_switch.md)
4. [Case 4: 异常场景 - A2DP断开时的处理](./case04_a2dp_disconnect.md)
5. [Case 5: 边界场景 - 无A2DP设备时的fallback](./case05_no_a2dp_fallback.md)
6. [Case 6: 时序竞争场景](./case06_timing_race.md)
7. [Case 7: 重复注册场景](./case07_duplicate_registration.md)
8. [Case 8: 蓝牙配对请求场景](./case08_pairing_request.md)
9. [Case 9: 蓝牙配对状态变化场景](./case09_pairing_state_change.md)
10. [Case 10: 互联过程中的蓝牙配对场景](./case10_pairing_during_link.md)

## 验证成功标准

- 所有case的预期日志都能正确输出
- A2DP设备始终被优先选择
- SDP注册/注销时机正确
- 配对状态变化能正确反映在应用中
- 无异常崩溃或ANR
- 设备切换时能正确响应

## 执行顺序建议

建议按照以下顺序执行测试用例：
1. 先执行正常流程用例（Case 1-2）
2. 再执行设备切换用例（Case 3）
3. 然后执行异常和边界用例（Case 4-7）
4. 最后执行配对相关用例（Case 8-10）
