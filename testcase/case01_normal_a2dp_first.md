# Case 1: 正常流程 - 先A2DP连接，后互联

## 测试目标
验证A2DP设备优先选择逻辑在正常流程中的表现

## 前置条件
- 车机蓝牙功能正常
- 手机蓝牙功能正常
- 车机和手机未连接

## 测试步骤

### 步骤1: 环境准备
1. 确保车机蓝牙开启
2. 确保手机蓝牙开启
3. 清除之前的配对记录（可选）
4. 启动日志监控：
   ```bash
   adb logcat | grep -E "(HidScreenScreenIOSHID|BtConnectionManager|SdkViewModelBTPair|HidScreenConnectManager::getBondedClassicDevice|HidScreenConnectManager::getA2DPConnectedDevice)"
   ```

### 步骤2: A2DP连接
1. 在手机上搜索蓝牙设备
2. 选择车机蓝牙设备进行连接
3. 确认A2DP连接成功
4. 等待3秒，观察日志输出

### 步骤3: 启动互联
1. 在手机上启动互联应用
2. 选择WiFi或USB连接方式
3. 等待互联连接成功
4. 观察日志输出

### 步骤4: 验证设备选择
1. 观察 `getBondedClassicDevice` 方法的调用日志
2. 确认A2DP设备被优先选择
3. 观察SDP注册日志

## 预期结果

### A2DP连接阶段
```
BtConnectionManager::handleA2DPConnectionStateChange: 蓝牙连接状态变化: device=iPhone(XX:XX:XX:XX:XX:XX), prevState=DISCONNECTED, nowState=CONNECTED
BtConnectionManager::handleA2DPConnectionStateChange: 经典蓝牙设备已连接, device=iPhone
BtConnectionManager::handleA2DPConnectionStateChange: 延迟连接HID设备, device=iPhone
```

### 互联成功阶段
```
HidScreenConnectManager::getA2DPConnectedDevice: found A2DP device=iPhone(XX:XX:XX:XX:XX:XX)
HidScreenConnectManager::getBondedClassicDevice: using A2DP device=iPhone(XX:XX:XX:XX:XX:XX)
HidScreenConnectManager::registerTouchScreenApp registerApp result=true
SdkViewModel::handleCarBTConnected >>> sent A2DP connected phone MAC to phone: XX:XX:XX:XX:XX:XX
```

### 配对状态确认
```
SdkViewModel::handleCarBTConnected pairing status: connected=true, phoneMac=XX:XX:XX:XX:XX:XX, connectedPhoneMac=XX:XX:XX:XX:XX:XX, isPhoneMacMatched=true, finalPairedStatus=true
PopWinService::onCarBtPairedConnected: true
```

## 验证点

1. **A2DP连接成功**: 日志显示A2DP连接状态从DISCONNECTED变为CONNECTED
2. **设备优先选择**: `getBondedClassicDevice` 返回A2DP连接的设备
3. **SDP注册成功**: `registerTouchScreenApp` 返回true
4. **MAC地址匹配**: 手机MAC地址与A2DP连接设备MAC地址一致
5. **配对状态正确**: `finalPairedStatus` 为true

## 失败处理

如果测试失败，检查以下项目：
1. 蓝牙权限是否正确授予
2. A2DP Profile是否正常工作
3. 互联连接是否稳定
4. 日志过滤条件是否正确

## 清理步骤

1. 断开互联连接
2. 断开蓝牙连接
3. 停止日志监控
4. 清理测试环境
