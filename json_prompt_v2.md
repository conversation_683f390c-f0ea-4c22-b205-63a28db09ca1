# JSON 异常检查规则 v2

## 检查规则

### 1. Java/Kotlin 检查项
- **JSONObject 构造和方法调用**
  - 构造函数：`new JSONObject(String)`
  - 方法：`getString()`, `getInt()`, `getBoolean()`, `getJSONObject()`, `getJSONArray()`
  - 异常：`JSONException`

- **Gson 操作**
  - 方法：`fromJson()`, `toJson()`
  - 异常：`JsonSyntaxException`, `JsonIOException`, `JsonParseException`

- **Jackson 操作**
  - 方法：`readValue()`, `writeValueAsString()`
  - 异常：`JsonProcessingException`, `IOException`

- **原生 JSON 字符串操作**
  - 手动字符串拼接构建 JSON
  - 无异常处理的字符串解析

### 2. 检查标准
- **必须有异常处理**：try-catch 块包围 JSON 操作
- **错误返回值检查**：检查 null 返回值
- **输入验证**：检查输入字符串是否为 null 或空
- **默认值处理**：提供合理的默认值

### 3. 问题严重级别
- **严重**：没有任何异常处理的 JSON 操作
- **中等**：有异常处理但没有提供默认值
- **轻微**：异常处理完善但日志记录不足

### 4. 排除项
- 测试代码中的 JSON 操作（可选）
- 已知安全的配置文件读取（需要人工确认）
- 有完整异常处理的代码块
