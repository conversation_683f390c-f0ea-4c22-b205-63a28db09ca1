# Suggested Commands

## Build Commands
- `./gradlew build` - Build the project
- `./gradlew clean` - Clean build artifacts
- `./gradlew assembleDebug` - Build debug APK
- `./gradlew assembleRelease` - Build release APK

## Testing Commands
- `./gradlew test` - Run unit tests
- `./gradlew connectedAndroidTest` - Run instrumented tests

## System Commands (Darwin/macOS)
- `find . -name "*.kt" -o -name "*.java"` - Find Kotlin/Java files
- `grep -r "pattern" .` - Search for patterns
- `ls -la` - List files with details
- `git status` - Check git status