// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    id 'com.android.application' version '7.1.3' apply false
    id 'com.android.library' version '7.1.3' apply false
    id 'org.jetbrains.kotlin.android' version '1.8.0' apply false
    id 'org.jetbrains.kotlin.plugin.serialization' version '1.8.0' apply false
}

allprojects {
    configurations.all {
        resolutionStrategy {
            force 'androidx.core:core-ktx:1.8.0+'
        }
    }
}

apply from: "version.gradle"
apply from: "functions.gradle"
task clean(type: Delete) {
    delete rootProject.buildDir
}

ext {
    linkCompileSdkVersion = 34
    linkMinSdkVersion = 27
    linkTargetSdkVersion = 34
    linkVersionCode = 1
    linkVersionName = "1.0"
}