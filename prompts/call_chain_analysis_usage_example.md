# 调用链异常处理分析使用示例

## 基本使用方法

### 示例 1：蓝牙连接模块调用链分析
```
请使用 prompts/module_call_chain_exception_analysis.md 中的调用链异常处理分析 prompt 对以下模块进行深度分析：

- 分析模块：BluetoothManager
- 关注调用链：connect(deviceAddress) -> establishConnection() -> createSocket() -> sendHandshake()
- 业务场景：用户点击连接蓝牙设备，建立数据通信
- 已知问题：偶尔出现连接建立后无法通信，用户需要重新连接
- 运行环境：Android API 21+，支持经典蓝牙和 BLE

🔒 强制执行要求：
1. 必须完成完整的调用链调研和映射
2. 必须对每个调用节点进行异常注入分析
3. 必须基于实际代码验证现有异常处理机制
4. 必须提供具体可执行的改进建议

请严格按照三阶段分析流程执行。
```

### 示例 2：网络请求模块调用链分析
```
请使用调用链异常处理分析 prompt 对以下模块进行深度分析：

- 分析模块：NetworkManager
- 关注调用链：sendRequest() -> buildRequest() -> executeCall() -> parseResponse()
- 业务场景：应用向服务器发送业务数据请求
- 已知问题：网络不稳定时偶尔出现请求卡死，用户界面无响应
- 运行环境：Android API 23+，使用 Retrofit + OkHttp

重点关注：
1. 网络超时异常处理
2. 请求重试机制
3. 异步回调的异常传播
4. 用户界面状态更新
```

## AI 分析响应示例

### 第一阶段：深度调用链调研示例
```
## 第一阶段：深度调用链调研 (Call Chain Investigation)

### 调研步骤 0：模块边界分析
我已分析 BluetoothManager 模块的边界：
- 入口点：BluetoothManager.connect(String deviceAddress)
- 出口点：ConnectionCallback.onConnected() / onError()
- 依赖关系：依赖 BluetoothAdapter、BluetoothSocket、Handler

### 调研步骤 1：调用链路径映射
通过代码搜索，我发现完整的调用链路径：

**主调用路径：**
1. BluetoothManager.connect(deviceAddress) [BluetoothManager.java:45]
2. → validateDeviceAddress(deviceAddress) [BluetoothManager.java:67]
3. → BtConnectionManager.establishConnection(device) [BtConnectionManager.java:89]
4. → createBluetoothSocket(device) [BtConnectionManager.java:123]
5. → socket.connect() [BtConnectionManager.java:145]
6. → sendHandshakeData() [BtConnectionManager.java:167]
7. → notifyConnectionSuccess() [BtConnectionManager.java:189]

**异步回调路径：**
- ConnectionTimeoutHandler.onTimeout() [BtConnectionManager.java:201]
- DataReceiveCallback.onDataReceived() [BtConnectionManager.java:223]

### 调研步骤 2：异常处理模式识别
发现的现有异常处理模式：
✅ 连接超时处理：使用 Handler.postDelayed() 实现超时机制
✅ 基本异常捕获：在 socket.connect() 外层有 try-catch
❌ 缺少重试机制：未发现自动重试实现
❌ 缺少状态恢复：连接失败后状态未正确重置

### 调研步骤 3：依赖服务异常分析
依赖服务及其异常类型：
- BluetoothAdapter：可能抛出 SecurityException（权限不足）
- BluetoothSocket：可能抛出 IOException（连接失败）
- Handler：可能因为 Looper 问题导致回调失败
```

### 第二阶段：调用链异常推理分析示例
```
## 第二阶段：调用链异常推理分析

### 第一步：理解业务调用意图
BluetoothManager 的核心业务调用目标是：建立稳定的蓝牙设备连接，为后续数据传输提供可靠通道。

### 第二步：梳理主调用路径
主调用路径详细步骤：
1. 用户触发连接 → BluetoothManager.connect()
2. 验证设备地址有效性 → validateDeviceAddress()
3. 委托连接管理器建立连接 → BtConnectionManager.establishConnection()
4. 创建蓝牙套接字 → createBluetoothSocket()
5. 执行底层连接 → socket.connect()
6. 发送握手数据验证连接 → sendHandshakeData()
7. 通知上层连接成功 → notifyConnectionSuccess()

### 第三步：进行"异常注入"式推理

**节点 1：BluetoothManager.connect() 异常注入**
- 如果 deviceAddress 为 null 会发生什么？
  - 现状：会传递到 validateDeviceAddress() 
  - 风险：可能导致 NullPointerException
  - 影响：应用崩溃，用户体验极差

**节点 2：validateDeviceAddress() 异常注入**
- 如果设备地址格式无效会发生什么？
  - 现状：通过代码搜索发现有基本验证 [BluetoothManager.java:67-75]
  - 防护：✅ 已有格式验证和异常返回

**节点 3：createBluetoothSocket() 异常注入**
- 如果蓝牙适配器未启用会发生什么？
  - 现状：会抛出 IOException
  - 风险：异常未被正确处理，可能导致连接状态不一致
  - 影响：用户界面显示连接中，但实际连接已失败

**节点 4：socket.connect() 异常注入**
- 如果设备不在范围内会发生什么？
  - 现状：会抛出 IOException，有 try-catch 捕获
  - 风险：捕获后只是简单 log，未通知上层
  - 影响：用户界面可能一直显示连接中状态

**节点 5：sendHandshakeData() 异常注入**
- 如果握手数据发送失败会发生什么？
  - 现状：通过搜索未发现异常处理
  - 风险：可能导致连接建立但无法通信的状态
  - 影响：用户以为连接成功，但实际无法使用

### 第四步：现有异常防护评估
基于代码搜索验证的结果：
✅ **已有防护**：
- 设备地址格式验证 [BluetoothManager.java:67-75]
- 连接超时处理 [BtConnectionManager.java:201-210]
- 基本 IOException 捕获 [BtConnectionManager.java:145-155]

❌ **缺少防护**：
- 握手数据发送异常处理
- 连接失败后的状态重置
- 用户界面状态同步机制
- 自动重试机制

### 第五步：真实异常风险识别
经过代码验证确实缺失的风险点：
1. 握手阶段异常处理缺失
2. 连接失败后状态不一致
3. 异常信息未正确传播到 UI 层
4. 缺少连接质量检测机制

### 第六步：针对性异常处理改进策略
基于现有架构的增量改进方案：
1. 在现有 try-catch 基础上增加握手异常处理
2. 扩展现有回调机制，增加详细错误信息
3. 利用现有 Handler 机制实现状态同步
4. 在现有超时机制基础上增加重试逻辑
```

### 第三阶段：DRBFM 报告示例
```
## 第三阶段：调用链异常 DRBFM 报告

### 调用链异常处理 DRBFM 风险分析表

| 调用节点 | 调用路径 | 潜在异常类型 | 异常影响 | 异常原因 | 当前异常处理 | 风险等级 | 改进对策 | 责任人 | 完成时间 |
|----------|----------|-------------|----------|----------|-------------|----------|----------|--------|----------|
| sendHandshakeData() | connect() → establishConnection() → sendHandshakeData() | 🌐 网络异常 | 连接建立但无法通信，用户误以为连接成功 | 握手数据发送失败，socket 状态异常 | ❌ 无异常处理 | 🔴 高风险 | 添加握手异常捕获和连接状态重置 | Android团队 | 1周 |
| socket.connect() | connect() → establishConnection() → socket.connect() | 🌐 网络异常 | UI 状态不同步，用户体验差 | IOException 被捕获但未通知 UI | ⚠️ 部分处理 | 🟡 中等风险 | 扩展现有异常处理，增加 UI 状态回调 | Android团队 | 3天 |
| createBluetoothSocket() | connect() → establishConnection() → createBluetoothSocket() | 🔧 系统异常 | 应用可能崩溃 | 蓝牙适配器未启用或权限不足 | ❌ 无异常处理 | 🔴 高风险 | 添加蓝牙状态检查和权限验证 | Android团队 | 2天 |
| notifyConnectionSuccess() | 整个连接流程 → notifyConnectionSuccess() | 🧵 并发异常 | 回调可能在错误线程执行 | Handler 线程问题 | ⚠️ 部分处理 | 🟡 中等风险 | 确保回调在主线程执行 | Android团队 | 1天 |

### 改进优先级建议
**立即修复（本周内）：**
1. 添加握手数据发送异常处理
2. 添加蓝牙适配器状态检查

**近期修复（2周内）：**
1. 完善 UI 状态同步机制
2. 确保回调线程安全

**长期优化（1个月内）：**
1. 实现智能重试机制
2. 添加连接质量监控
```

## 使用技巧

### 1. 明确调用链边界
- 确定分析的起始点和结束点
- 识别所有分支路径和异步回调
- 理解调用链在整个业务流程中的位置

### 2. 系统性异常注入
- 对每个调用节点都要进行异常注入分析
- 考虑网络、系统、并发、业务等各类异常
- 关注异常的传播路径和影响范围

### 3. 基于实际代码验证
- 使用代码搜索工具验证现有异常处理
- 不要基于假设或经验进行分析
- 重点关注现有机制的盲点和边界条件

### 4. 提供可执行的改进方案
- 基于现有架构进行增量改进
- 避免大规模重构和重复实现
- 确保改进方案的可操作性和时间可控性
