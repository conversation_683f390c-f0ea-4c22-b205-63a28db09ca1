# Android 代码审核 Prompt 使用示例

## 基本使用方法

### 示例 1：Android 蓝牙模块强制逐项审核
```
请使用 prompts/senior_code_review_analysis.md 中的 Android 资深程序员代码审核 prompt 对以下模块进行强制逐项深度分析：

- 审核模块：[BluetoothManager, BtConnectionManager, DeviceScanner, BluetoothService]
- 关注重点：内存管理和生命周期
- 业务场景：蓝牙设备连接、数据传输、设备管理
- 已知问题：偶尔出现连接断开、内存使用较高、应用后台时连接丢失
- 运行环境：Android API 21+，支持多设备连接，目标设备内存 2-4GB
- 开发语言：Java 和 Kotlin 混合
- 审核深度：强制深度分析

🔒 强制执行要求：
1. 必须按照检查清单逐项检查所有 65+ 个检查项
2. 每个检查项都必须进行深度分析，不得简单回答"正常"
3. 即使表面正常也要挖掘潜在风险和改进空间
4. 必须提供具体的代码位置和证据支撑
5. 完成后必须提供完整的检查执行确认

请严格按照强制执行指令，逐项深度分析所有检查项。
```

### 示例 2：Android UI 性能专项审核
```
请使用 Android 资深程序员代码审核 prompt 对以下模块进行性能专项审核：

- 审核模块：[MainActivity, RecyclerViewAdapter, ImageLoader, DataProcessor]
- 关注重点：UI 性能和响应性
- 业务场景：列表展示、图片加载、数据处理
- 已知问题：列表滑动卡顿、图片加载慢、偶尔出现 ANR
- 运行环境：Android API 23+，低端设备内存 1-2GB
- 开发语言：主要使用 Kotlin
- 审核深度：标准审核

重点分析：
1. RecyclerView 性能优化
2. 图片加载和内存管理
3. 主线程阻塞问题
4. 布局性能优化
```

### 示例 3：Android 内存泄漏专项审核
```
请使用 Android 资深程序员代码审核 prompt 对以下模块进行内存泄漏专项审核：

- 审核模块：[BaseActivity, NetworkManager, CacheManager, EventBus]
- 关注重点：内存管理
- 业务场景：Activity 管理、网络请求、数据缓存、事件通信
- 已知问题：长时间使用后内存占用持续增长，偶尔 OOM
- 运行环境：Android API 19+，需要支持低内存设备
- 开发语言：Java 为主
- 审核深度：深度分析

重点检查：
1. Activity/Fragment 内存泄漏
2. 静态变量持有 Context 引用
3. 监听器和回调的注销
4. Handler 内存泄漏
5. 单例类的内存管理
```

## 交互式审核示例

### 用户输入
```
请审核 BluetoothManager 模块，重点关注连接稳定性问题。
```

### AI 强制执行响应示例
```
我将对 BluetoothManager 模块进行强制逐项深度审核。按照强制执行指令，我必须检查所有 65+ 个检查项。

## 📋 检查执行确认
- 审核模块：BluetoothManager, BtConnectionManager, DeviceScanner, BluetoothService
- 检查维度：10个主要维度
- 检查项目：65个具体检查项
- 执行状态：🔄 开始强制逐项检查

## 🔍 逐项检查结果

### 维度 1：Android 架构与组件设计

#### 1.1 组件职责分离
**检查状态**：⚠️ 存在问题
**分析过程**：我检查了 BluetoothManager 类的职责分配，发现该类同时承担了设备管理、连接管理、数据传输和UI更新等多重职责，违反了单一职责原则。
**发现内容**：
- BluetoothManager.java:45-120 包含设备扫描逻辑
- BluetoothManager.java:121-200 包含连接管理逻辑
- BluetoothManager.java:201-280 包含数据传输逻辑
- BluetoothManager.java:281-320 包含UI回调逻辑
**改进建议**：建议拆分为 DeviceScanner、ConnectionManager、DataTransfer、UICallback 四个独立组件

#### 1.2 MVP/MVVM 架构
**检查状态**：🔴 严重问题
**分析过程**：检查了整个蓝牙模块的架构模式，发现没有明确的架构分层，业务逻辑与UI逻辑混合在一起。
**发现内容**：
- Activity 直接调用蓝牙API（MainActivity.java:67-89）
- 业务逻辑散布在多个Activity中
- 缺少统一的数据管理层
**改进建议**：建议引入MVVM架构，使用ViewModel管理蓝牙状态，Repository管理数据

#### 1.3 依赖注入使用
**检查状态**：⚪ 观察项
**分析过程**：检查了依赖管理方式，发现使用了传统的单例模式和直接实例化，没有使用现代的依赖注入框架。
**发现内容**：
- BluetoothManager 使用单例模式（BluetoothManager.java:25）
- 各组件间通过直接引用耦合
- 缺少接口抽象和依赖注入
**改进建议**：考虑引入 Hilt 进行依赖注入，提高可测试性

[继续检查剩余 62 个检查项...]

### 维度 2：Android 生命周期与状态管理

#### 2.1 Activity/Fragment 生命周期
**检查状态**：🔴 严重问题
**分析过程**：详细检查了所有生命周期方法的实现，发现多个严重的生命周期管理问题。
**发现内容**：
- onCreate 中初始化蓝牙但未检查权限（MainActivity.java:34）
- onPause 中未停止蓝牙扫描，导致后台电池消耗
- onDestroy 中未注销监听器，存在内存泄漏风险
- 配置变更时蓝牙连接状态丢失
**改进建议**：在每个生命周期方法中添加相应的蓝牙状态管理逻辑

[必须继续检查所有剩余检查项，不得跳过...]

## 📊 当前检查进度
已完成：3/65 检查项
剩余：62 个检查项待深度分析
预计发现问题：15-25 个

我将继续按照强制执行指令完成所有检查项的深度分析...
```

### 用户回复
```
1. 主要连接蓝牙耳机和智能手表
2. 断开主要发生在设备距离较远或设备进入休眠状态
3. 30秒超时用户觉得太长了
4. 需要支持后台保持连接
```

### AI 深度分析
```
基于您的回复，我进行了 Android 专项深度分析，发现以下问题：

## 🔴 严重问题 #1：BluetoothGattCallback 内存泄漏

### 问题描述
BluetoothGattCallback 作为非静态内部类实现，持有外部 Activity 引用，在连接过程中可能导致 Activity 无法被 GC 回收。

### 影响分析
- **内存影响**：Activity 无法及时回收，导致内存泄漏
- **系统影响**：长时间使用可能导致 OOM
- **用户体验影响**：应用响应变慢，可能出现卡顿

### Android 特定解决方案
- **短期方案**：使用 WeakReference 持有 Activity 引用
- **长期方案**：将 BluetoothGattCallback 提取为静态内部类或独立类
```kotlin
// 推荐实现方式
private static class MyBluetoothGattCallback extends BluetoothGattCallback {
    private final WeakReference<Activity> activityRef;

    MyBluetoothGattCallback(Activity activity) {
        this.activityRef = new WeakReference<>(activity);
    }

    @Override
    public void onConnectionStateChange(BluetoothGatt gatt, int status, int newState) {
        Activity activity = activityRef.get();
        if (activity != null && !activity.isFinishing()) {
            // 处理连接状态变化
        }
    }
}
```

## 🟡 重要问题 #2：生命周期管理不当

### 问题描述
蓝牙扫描和连接操作未与 Activity 生命周期正确绑定，可能导致资源泄漏和电池消耗。

### Android 特定解决方案
```kotlin
override fun onResume() {
    super.onResume()
    // 恢复蓝牙操作
    bluetoothManager.resumeOperations()
}

override fun onPause() {
    super.onPause()
    // 暂停扫描以节省电池
    bluetoothManager.pauseScanning()
}

override fun onDestroy() {
    super.onDestroy()
    // 清理所有蓝牙资源
    bluetoothManager.cleanup()
}
```

继续分析其他 Android 特定问题...
```

## 强制执行完成示例

### 最终检查完成确认
```
## 📋 强制执行完成确认

### 检查完整性验证
✅ 已按顺序检查所有 10 个主要维度
✅ 每个维度的所有子项都已深度分析（共65个检查项）
✅ 没有跳过任何检查项
✅ 所有"不适用"的项目都有说明原因

### 分析深度验证
✅ 每个检查项都有详细的分析过程
✅ 所有结论都有具体的代码证据（文件名:行号）
✅ 挖掘了潜在风险和改进空间
✅ 提供了具体可执行的建议

### 问题发现验证
✅ 统计了所有发现的问题数量（共23个问题）
✅ 按优先级对问题进行了分类
✅ 每个问题都有具体的位置和描述
✅ 提供了修复方案和时间估算

### 报告质量验证
✅ 报告结构完整，格式规范
✅ 技术分析准确，逻辑清晰
✅ 建议具体可行，优先级合理
✅ 适合 Android 开发的实际情况

## 📊 最终问题统计
- 🔴 严重问题：5个（内存泄漏、生命周期、线程安全）
- 🟡 重要问题：8个（权限处理、性能优化、架构设计）
- 🟢 一般问题：6个（代码规范、命名一致性）
- 🔵 建议优化：4个（现代化改进、最佳实践）
- ⚪ 观察项：0个

所有65个检查项已完成深度分析，报告质量符合强制执行标准。
```

## 用户验收检查

### 如何验证 Claude 是否完成了强制检查
1. **检查项数量验证**：确认报告中包含所有65个检查项
2. **分析深度验证**：每个检查项至少有3-5句详细分析
3. **证据支撑验证**：每个结论都有具体的代码位置
4. **完整性验证**：报告末尾包含强制执行完成确认

### 不合格报告的特征
- ❌ 跳过了某些检查项
- ❌ 简单回答"正常"或"无问题"
- ❌ 缺少具体的代码证据
- ❌ 没有挖掘潜在风险
- ❌ 缺少强制执行完成确认

### 要求重新检查的情况
```
如果发现以下情况，请要求 Claude 重新执行：

"请重新执行强制检查，我发现以下问题：
1. 检查项 X.Y 没有深度分析，只是简单说明
2. 缺少具体的代码位置证据
3. 没有挖掘潜在风险和改进建议
4. 请按照强制执行指令重新完成所有检查项"
```

## Android 审核结果示例

### Android 专项问题优先级分类示例
```
## Android 蓝牙模块审核结果总览

### 问题统计
- 🔴 严重问题：3 个（内存泄漏、生命周期管理）
- 🟡 重要问题：6 个（权限处理、线程安全）
- 🟢 一般问题：8 个（代码规范、性能优化）
- 🔵 建议优化：10 个（架构改进、用户体验）
- ⚪ 观察项：4 个（兼容性、未来风险）

### Android 特定核心发现
1. **内存泄漏风险**：BluetoothGattCallback、Handler、监听器未正确管理
2. **生命周期问题**：蓝牙操作未与 Activity 生命周期绑定
3. **权限处理不完整**：运行时权限申请流程不完善
4. **线程安全问题**：UI 线程阻塞、后台线程更新 UI

### Android 修复优先级建议
**立即修复（本周内）：**
- 修复 BluetoothGattCallback 内存泄漏
- 添加生命周期管理（onPause 停止扫描）
- 修复主线程阻塞问题

**近期修复（2周内）：**
- 完善运行时权限申请流程
- 实现后台连接保持机制
- 优化 RecyclerView 性能

**计划修复（1个月内）：**
- 重构为 MVVM 架构
- 添加 Room 数据库支持
- 实现 WorkManager 后台任务

**持续改进：**
- 迁移到 Kotlin Coroutines
- 添加 Jetpack 组件使用
- 完善单元测试和 UI 测试
```

### Android 具体问题示例
```
## 🔴 严重问题详情

### 问题 #1：BluetoothGattCallback 内存泄漏
**位置**：BluetoothManager.java:45-78
**问题**：非静态内部类持有 Activity 引用
**影响**：Activity 无法被 GC 回收，导致内存泄漏
**修复**：使用静态内部类 + WeakReference

### 问题 #2：主线程执行蓝牙扫描
**位置**：DeviceScanner.java:123
**问题**：在主线程执行蓝牙设备扫描
**影响**：可能导致 ANR
**修复**：移至后台线程执行

## 🟡 重要问题详情

### 问题 #3：权限申请不完整
**位置**：MainActivity.java:67
**问题**：未检查位置权限（蓝牙扫描必需）
**影响**：Android 6.0+ 设备扫描失败
**修复**：添加位置权限申请和检查

### 问题 #4：Fragment 重叠问题
**位置**：BluetoothFragment.java
**问题**：配置变更时 Fragment 重复添加
**影响**：界面显示异常
**修复**：检查 savedInstanceState
```

## Android 开发使用技巧

### 1. 明确 Android 审核目标
- **生命周期问题**：Activity/Fragment/Service 的生命周期管理
- **内存管理**：内存泄漏、OOM、GC 压力
- **性能优化**：UI 响应性、启动速度、电池消耗
- **线程安全**：主线程保护、异步处理、并发安全
- **架构设计**：MVP/MVVM、模块化、组件化

### 2. 提供 Android 特定上下文
- **API 级别**：最低支持版本、目标版本、编译版本
- **设备信息**：内存限制、屏幕尺寸、性能要求
- **开发语言**：Java/Kotlin 比例、迁移计划
- **架构组件**：使用的 Jetpack 组件、第三方库
- **测试环境**：真机测试、模拟器、自动化测试

### 3. Android 分阶段审核策略
**第一阶段：快速扫描**
- 检查明显的内存泄漏模式
- 识别主线程阻塞问题
- 查看生命周期方法实现

**第二阶段：深度分析**
- 分析复杂的异步处理逻辑
- 评估架构设计合理性
- 检查性能瓶颈和优化空间

**第三阶段：制定改进计划**
- 按优先级排序问题
- 制定分阶段修复计划
- 考虑重构和架构升级

### 4. Android 特定验证方法
- **内存分析**：使用 Memory Profiler 验证内存泄漏修复
- **性能测试**：使用 GPU Profiler 检查过度绘制
- **生命周期测试**：模拟配置变更、应用切换场景
- **兼容性测试**：在不同 API 级别和设备上测试
- **自动化测试**：编写 Espresso UI 测试验证修复效果

### 5. 常见 Android 审核重点
- **内存泄漏检查**：重点关注 Context、Handler、监听器
- **生命周期管理**：确保资源在正确时机释放
- **权限处理**：运行时权限的完整申请流程
- **线程安全**：主线程保护和异步处理
- **性能优化**：启动速度、UI 响应性、电池消耗
