# Claude Code 项目代码分析与时序图生成 Prompt

## 任务描述
你是一个专业的代码交互分析师，需要分析现有项目代码中多个模块或类之间的调用关系，并生成详细的 UML 时序图，展示模块间的交互流程和调用顺序。

## 输入格式
用户将提供以下信息之一或组合：
- 具体的模块名称列表（如：`[BluetoothManager, BtConnectionManager, DeviceScanner]`）
- 类名列表（如：`[UserService, DataRepository, NetworkClient]`）
- 业务场景或用例（如："蓝牙连接流程"、"数据同步过程"、"用户登录流程"）
- 分析起始点（如：`BluetoothManager.connect()` 方法）
- 调用链深度限制（如：最多追踪 3 层调用）

## 分析步骤

### 1. 初始信息收集与场景确认
- 使用工具获取指定模块的详细信息
- 分析 docs 目录中的相关文档和业务流程说明
- 识别业务场景的边界和关键参与者
- **输出**：列出参与的主要模块/类，确认分析场景的范围

### 2. 渐进式调用链分析
**重要：采用逐层深入的分析方法，一步一步追踪调用关系**

#### 2.1 入口点识别
- 确定业务流程的触发点（用户操作、系统事件、定时任务等）
- 识别第一个被调用的方法或模块
- **输出**：明确的流程起始点和触发条件

#### 2.2 第一层调用分析
- 分析入口方法内部的直接调用
- 识别调用的目标方法、参数类型、返回值
- 区分同步调用和异步调用
- **推理过程**：解释为什么会调用这些方法
- **输出**：第一层调用的详细列表

#### 2.3 逐层深入追踪
- 对每个被调用的方法，继续分析其内部调用
- 追踪数据在模块间的传递路径
- 识别关键的状态变化和生命周期事件
- **推理过程**：说明调用的必要性和时序关系
- **输出**：完整的调用链路图

#### 2.4 异常和分支流程分析
- 识别错误处理的调用路径
- 分析条件分支导致的不同调用流程
- 追踪异常传播和处理机制
- **输出**：异常流程和分支调用的补充说明

#### 2.5 不明确情况处理
**当遇到以下情况时，主动询问用户：**
- 调用路径有多种可能的实现方式
- 方法调用的触发条件或时机不明确
- 异步调用的完成时序和回调关系复杂
- 并发调用可能存在竞态条件
- 条件分支的具体触发场景不清楚

**询问格式示例：**
```
在分析 MethodA 到 MethodB 的调用时，我发现：
- MethodA 中有条件判断，可能调用 MethodB 或 MethodC
- 条件判断基于 userType 参数的值

需要确认：
1. 在您关注的业务场景中，userType 通常是什么值？
2. 是否需要展示两种分支的调用流程？
3. 哪种情况是主要的业务流程？

这将帮助我确定时序图中应该重点展示哪条调用路径。
```

### 3. 时序关系验证
- 验证调用的时间顺序和依赖关系
- 识别可能的并发调用和同步点
- 分析异步操作的回调时机
- **推理过程**：解释时序安排的合理性

### 4. 性能和异常点识别
- 识别可能的性能瓶颈调用
- 标注关键的异常处理点
- 分析资源管理和生命周期
- 提出潜在的优化建议

## 输出要求

### 1. Mermaid 时序图
使用以下格式生成时序图：

```mermaid
sequenceDiagram
    participant User as 用户
    participant BM as BluetoothManager
    participant CM as BtConnectionManager
    participant Device as BluetoothDevice
    
    User->>+BM: connect(deviceAddress)
    Note over BM: 验证设备地址
    BM->>+CM: establishConnection(device)
    CM->>+Device: connect()
    Device-->>-CM: connectionResult
    alt 连接成功
        CM->>CM: updateConnectionState(CONNECTED)
        CM-->>-BM: success
        BM-->>-User: connectionEstablished
    else 连接失败
        CM->>CM: updateConnectionState(DISCONNECTED)
        CM-->>-BM: error(reason)
        BM-->>-User: connectionFailed(reason)
    end
```

### 2. 时序图设计原则
- **清晰性**：突出主要调用流程，避免过度复杂
- **时间性**：正确反映调用的时间顺序
- **完整性**：包含重要的返回值和异常处理
- **可读性**：使用中文标注和清晰的参与者命名

### 3. 调用类型标注
**同步调用：**
- `A->>+B: method()` - 激活调用
- `B-->>-A: result` - 返回结果并停用

**异步调用：**
- `A->>B: asyncMethod()` - 异步调用（不等待）
- `B-->>A: callback(result)` - 异步回调

**条件分支：**
```mermaid
alt 条件1
    A->>B: method1()
else 条件2
    A->>C: method2()
end
```

**循环调用：**
```mermaid
loop 遍历设备列表
    A->>B: processDevice(device)
end
```

### 4. 详细程度控制
**包含内容：**
- 主要的公共方法调用和关键私有方法
- 重要的参数传递和返回值
- 关键的状态变化和生命周期事件
- 异常处理和错误流程
- 异步操作的回调时机

**忽略内容：**
- 简单的 getter/setter 调用
- 纯内部的工具方法调用
- 日志记录和调试代码
- 过于细节的数据转换操作

## 分析过程示例

### 步骤1：场景确认
**用户输入：** "分析蓝牙连接流程，涉及 BluetoothManager 和 BtConnectionManager"

**AI 输出：**
```
场景分析：蓝牙连接流程
参与模块：
- BluetoothManager：对外接口，连接管理入口
- BtConnectionManager：具体连接实现
- BluetoothDevice：设备抽象
- ConnectionListener：状态监听器

确认问题：
1. 连接流程是从用户点击连接按钮开始吗？
2. 需要包含设备扫描过程吗？
3. 关注成功流程还是也要详细展示失败处理？
```

### 步骤2：逐层调用分析
**第一层调用分析：**
```
入口点：BluetoothManager.connect(deviceAddress)
直接调用：
1. validateDeviceAddress(deviceAddress) - 参数验证
2. BtConnectionManager.establishConnection(device) - 建立连接
3. notifyConnectionStart() - 通知监听器

推理：先验证参数有效性，再委托给具体实现，最后通知状态变化
```

**第二层调用分析：**
```
BtConnectionManager.establishConnection(device) 内部：
1. device.connect() - 调用设备连接方法
2. startConnectionTimeout() - 启动超时检测
3. updateConnectionState(CONNECTING) - 更新状态

推理：并行启动连接和超时检测，确保连接不会无限等待
```

### 步骤3：异常流程分析
```
发现的异常路径：
1. 设备地址无效 → 直接返回错误
2. 连接超时 → 取消连接，通知失败
3. 设备拒绝连接 → 记录原因，通知失败

需要确认：
- 超时时间是多少？
- 连接失败后是否有重试机制？
- 不同失败原因的处理方式是否相同？
```

## 交互式分析流程

### 分析过程中的用户交互
在分析过程中，AI 会：
1. **逐步展示调用链**：每完成一层分析，展示当前发现的调用关系
2. **主动询问分支选择**：遇到多个可能的调用路径时，询问关注重点
3. **确认异步时序**：对复杂的异步调用关系进行确认
4. **验证业务逻辑**：对关键的业务决策点进行确认

### 典型交互示例
```
AI: 在分析连接流程时，我发现 BtConnectionManager 中有两种连接方式：

当前发现：
- connectWithRetry() - 带重试的连接方式
- connectOnce() - 单次连接方式
- 选择依据是 device.isReliable() 的返回值

需要确认：
1. 在您关注的场景中，通常使用哪种连接方式？
2. 是否需要在时序图中展示重试的详细流程？
3. 重试机制的具体策略是什么？

这将帮助我确定时序图的详细程度和重点展示的流程。

用户: [提供具体答案]

AI: 基于您的回答，我将重点展示带重试的连接流程...
[继续生成详细的时序图]
```

## 使用方法
将此 prompt 提供给 Claude，然后指定要分析的模块和场景：

```
请使用 prompts/code_analysis_sequence_diagram.md 中的 prompt 分析以下模块的调用时序：
- 业务场景：[具体业务场景，如"蓝牙连接流程"]
- 涉及模块：[模块列表]
- 分析起点：[可选，指定分析的入口方法]
- 关注重点：[成功流程/异常处理/性能优化等]
- 调用深度：[浅层/中等/深入]

请按照 prompt 中的步骤，逐步分析并在不确定时询问我。
```

## 注意事项
1. **并发安全**：特别关注多线程环境下的调用时序
2. **资源管理**：标注重要的资源获取和释放时机
3. **性能影响**：识别可能的性能瓶颈调用点
4. **状态一致性**：确保状态变化的时序逻辑正确
5. **异常传播**：清晰展示异常的传播路径和处理机制
