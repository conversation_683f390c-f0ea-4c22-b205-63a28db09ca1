# iOS HID 反控时序图

```mermaid
sequenceDiagram
    participant User as 用户触摸
    participant IFL as InterceptFrameLayout
    participant HSTM as HidScreenTouchManager
    participant H<PERSON> as HidWlanIos
    participant BHM as BluetoothHidManager
    participant HCM as HidScreenConnectManager
    participant AF as Android Framework
    participant Phone as iOS设备

    Note over User, Phone: iOS HID 触摸事件反控流程

    %% 触摸开始 (ACTION_DOWN)
    User->>IFL: 触摸屏幕 (ACTION_DOWN)
    IFL->>IFL: onInterceptTouchEvent()
    IFL->>HSTM: onTouch(view, motionEvent)
    
    HSTM->>HSTM: 检查设备连接状态
    HSTM->>HSTM: calculateSendPoint()<br/>(坐标转换和屏幕旋转)
    
    HSTM->>HWI: 转发触摸事件 (iOS模式)
    HWI->>HWI: 检查校准状态<br/>getControlAvailable()
    
    alt 需要校准
        HWI->>BHM: startCalibration()
        BHM->>BHM: mouseCalibrationByStrategy()
        BHM-->>HWI: 校准完成
    end
    
    HWI->>BHM: hidSendPointEvent(x, y, DOWN)
    BHM->>BHM: hidThrEvent()<br/>(转换为鼠标移动)
    BHM->>BHM: moveToTarget()<br/>(精确定位)
    BHM->>BHM: 生成5字节鼠标HID报告<br/>bytes[0]=按钮状态<br/>bytes[1]=dx, bytes[2]=dy
    
    BHM->>HCM: sendReport(reportId=1, mouseReport)
    HCM->>AF: BluetoothHidDevice.sendReport()
    AF->>Phone: HID鼠标按下事件

    %% 触摸移动 (ACTION_MOVE)
    User->>IFL: 触摸移动 (ACTION_MOVE)
    IFL->>HSTM: onTouch(view, motionEvent)
    HSTM->>HSTM: calculateSendPoint()
    HSTM->>HWI: 移动事件
    HWI->>BHM: hidSendPointEvent(x, y, MOVE)
    BHM->>BHM: hidThrEvent()
    BHM->>BHM: scollMinStep()<br/>(微调移动)
    BHM->>BHM: 生成鼠标移动报告
    BHM->>HCM: sendReport(reportId=1, mouseReport)
    HCM->>AF: BluetoothHidDevice.sendReport()
    AF->>Phone: HID鼠标移动事件

    %% 触摸结束 (ACTION_UP)
    User->>IFL: 释放触摸 (ACTION_UP)
    IFL->>HSTM: onTouch(view, motionEvent)
    HSTM->>HWI: 释放事件
    HWI->>BHM: hidSendPointEvent(x, y, UP)
    BHM->>BHM: hidThrEvent()
    BHM->>BHM: 生成鼠标释放报告<br/>bytes[0]=0 (无按钮)
    BHM->>HCM: sendReport(reportId=1, mouseReport)
    HCM->>AF: BluetoothHidDevice.sendReport()
    AF->>Phone: HID鼠标释放事件

    Note over User, Phone: 特殊处理：防锁屏机制 (仅Android)
    alt Android设备防锁屏
        HSTM->>HSTM: sendKeepAliveEvent()
        HSTM->>HCM: sendConsumerControlEvent(0x02B0)
        HCM->>AF: BluetoothHidDevice.sendReport()
        AF->>Phone: Consumer Control命令
    end

    Note over User, Phone: 手势识别 (iOS特有)
    alt 检测到特殊手势
        BHM->>BHM: 识别Home手势
        BHM->>HCM: 发送Home键HID事件
        HCM->>AF: BluetoothHidDevice.sendReport()
        AF->>Phone: Home按键事件
    end
```

## 主要组件说明

### 1. InterceptFrameLayout (IFL)
- **文件位置**: `app/src/main/java/com/autoai/wdlink/hu/module/hidscreen/InterceptFrameLayout.kt`
- **作用**: 自定义触摸拦截容器，替代传统PopupWindow
- **关键方法**: 
  - `onInterceptTouchEvent()`: 决定是否拦截触摸事件
  - 支持动态启用/禁用触摸拦截

### 2. HidScreenTouchManager (HSTM)  
- **文件位置**: `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/hidtouchscreen/HidScreenTouchManager.java`
- **作用**: 中央触摸事件处理器
- **关键功能**:
  - 坐标变换 (`calculateSendPoint()`)
  - 屏幕旋转适配 (0°/90°/180°/270°)
  - 设备类型判断和事件分发

### 3. HidWlanIos (HWI)
- **文件位置**: `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/api/HidWlanIos.java`  
- **作用**: iOS设备专用HID处理器
- **特点**: 
  - 鼠标协议模式
  - 校准机制集成
  - 无防锁屏功能

### 4. BluetoothHidManager (BHM)
- **文件位置**: `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/datatransfer/BluetoothHidManager.java`
- **作用**: iOS鼠标协议核心实现
- **关键方法**:
  - `hidSendPointEvent()`: 主要触摸处理入口
  - `hidThrEvent()`: 触摸到鼠标事件转换
  - `moveToTarget()`: 精确光标定位
  - `scollMinStep()`: 微调移动控制
  - `mouseCalibrationByStrategy()`: 校准算法

### 5. HidScreenConnectManager (HCM)
- **文件位置**: `link-hid/avslinkhid/src/main/java/com/autoai/avslinkhid/hidtouchscreen/HidScreenConnectManager.java`
- **作用**: 蓝牙HID设备连接管理
- **功能**: 
  - `sendReport()`: HID报告发送
  - 蓝牙设备注册和连接状态管理

### 6. Android Framework (AF)
- **核心API**: `BluetoothHidDevice.sendReport()`
- **作用**: 系统级蓝牙HID服务
- **协议**: HID over Bluetooth (HoB)

## HID协议细节

### iOS鼠标协议 (5字节报告)
```java
byte[] mouseReport = new byte[5];
mouseReport[0] = buttonState;    // 按钮状态 (左键/右键)
mouseReport[1] = deltaX;         // X轴移动增量
mouseReport[2] = deltaY;         // Y轴移动增量  
mouseReport[3] = verticalScroll; // 垂直滚动
mouseReport[4] = horizScroll;    // 水平滚动
```

### 触摸事件映射
- **ACTION_DOWN** → 鼠标左键按下 (`buttonState = 0x01`)
- **ACTION_MOVE** → 鼠标移动 (`deltaX/deltaY`)  
- **ACTION_UP** → 鼠标左键释放 (`buttonState = 0x00`)
- **ACTION_CANCEL** → 重置状态

### 校准系统
- **策略模式**: 线性、曲线、常规校准算法
- **坐标变换**: 车机屏幕 → 手机屏幕坐标映射
- **精度控制**: 亚像素级移动精度

这套系统实现了精确的iOS设备触摸反控，通过蓝牙HID鼠标协议提供流畅的跨设备交互体验。