{"permissions": {"allow": ["Bash(grep:*)", "Bash(rg:*)", "Bash(find:*)", "<PERSON><PERSON>(./gradlew:*)", "Bash(terminal-notifier:*)", "Bash(git submodule:*)", "Bash(git pull:*)", "Bash(git fetch:*)", "Bash(git config:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(terminal-notifier -title \"任务完成了✌️\" -sound 'Glass' -message \"\")", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(ls:*)", "<PERSON><PERSON>(mv:*)", "mcp__sequential-thinking__sequentialthinking", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(touch:*)", "WebFetch(domain:github.com)", "Bash(pip install:*)", "Bash(pip3 install:*)", "Bash(brew install:*)", "Bash(uvx excel-mcp-server:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(python3:*)", "Bash(rm:*)", "<PERSON><PERSON>(env)", "Bash(open \"/Volumes/Macintosh HD/Users/<USER>/WorkSpace/Link/a55haiwai/avslink/linkHost/build/reports/tests/testDebugUnitTest/index.html\")", "Bash(open \"/Volumes/Macintosh HD/Users/<USER>/WorkSpace/Link/a55haiwai/app/build/reports/tests/testBaselineDebugUnitTest/index.html\")", "<PERSON><PERSON>(open:*)", "Bash(java:*)", "Bash(cp:*)", "Bash(./gradlew app:testBaselineDebugUnitTest --tests=\"com.autoai.welinkapp.checkconnect.AOACheckDeviceTest\" --stacktrace)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(python3:*)", "Bash(uvx excel-mcp-server:*)", "mcp__context7__resolve-library-id", "<PERSON><PERSON>(killall:*)", "Bash(uvx:*)", "Bash(EXCEL_FILES_PATH=\"/Volumes/Macintosh HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/OUTPUT\" uvx excel-mcp-server stdio --help)", "<PERSON>sh(claude config show)", "Bash(export EXCEL_FILES_PATH=\"/Volumes/Macintosh HD/Users/<USER>/WorkSpace/Link/a55haiwai/docs/OUTPUT\")", "<PERSON><PERSON>(echo:*)", "Ba<PERSON>(log show:*)", "Bash(ps:*)", "Bash(kill:*)", "WebFetch(domain:modelcontextprotocol.io)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(pkill:*)", "mcp__magic__logo_search", "Bash(\"/Volumes/Macintosh HD/Users/<USER>/WorkSpace/Link/a55haiwai/scripts/excel_mcp_wrapper.sh\" list-tools)", "Bash(\"/Volumes/Macintosh HD/Users/<USER>/WorkSpace/Link/a55haiwai/scripts/excel_mcp_wrapper.sh\" list-files)", "mcp__excel__read_data_from_excel", "mcp__excel__get_workbook_metadata", "mcp__excel__get_merged_cells", "mcp__excel__create_worksheet", "mcp__excel__write_data_to_excel", "mcp__excel__merge_cells", "mcp__excel__copy_worksheet", "mcp__excel__delete_range", "mcp__excel__delete_worksheet", "mcp__excel__rename_worksheet", "mcp__excel__copy_range", "mcp__excel__format_range", "mcp__excel__create_workbook", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(git clone:*)", "Bash(uv pip install:*)", "<PERSON><PERSON>(uv venv:*)", "<PERSON><PERSON>(source:*)", "<PERSON><PERSON>(python:*)", "Bash(npm run test-serena:*)", "Bash(PYTHONPATH=/Volumes/Macintosh HD/Users/<USER>/WorkSpace/Link/a55haiwai/serena/src python -c \"\nimport sys\nprint(''Python executable:'', sys.executable)\nprint(''Python path:'', sys.path[:3])\ntry:\n    import serena.mcp\n    print(''✅ Serena MCP module imported successfully'')\n    print(''Module location:'', serena.mcp.__file__)\nexcept ImportError as e:\n    print(f''❌ Import error: {e}'')\n\")", "mcp__serena__list_dir", "mcp__serena__search_for_pattern", "mcp__serena__find_file"], "deny": []}}