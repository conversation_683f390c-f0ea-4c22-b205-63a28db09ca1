# Gemini Customization

This file helps <PERSON> understand your project better.

## Project Overview

(Describe your project here. What does it do? What are the main goals?)

## Tech Stack

*   **Languages:** (e.g., Java, Kotlin, Groovy)
*   **Frameworks:** (e.g., Android)
*   **Build Tool:** (e.g., Gradle)

## Build & Run Commands

*   **Build:** `./gradlew build`
*   **Run:** (How do you run the application?)
*   **Test:** (What command runs the tests?)

## Coding Conventions

(Are there any specific coding styles, naming conventions, or patterns <PERSON> should follow?)

## Important Files & Directories

*   `app/`: Main application module.
*   `avslink/`: Submodule for...
*   `link-hid/`: Submodule for...
*   `build.gradle`: Main build script.
*   `settings.gradle`: Project settings and sub-module definitions.
