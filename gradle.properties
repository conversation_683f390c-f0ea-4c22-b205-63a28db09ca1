# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx2048m -Dfile.encoding=UTF-8
# When configured, <PERSON><PERSON><PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true
# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app"s APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true
# Kotlin code style for this project: "official" or "obsolete":
kotlin.code.style=official
# Enables namespacing of each library's R class so that its R class includes only the
# resources declared in the library itself and none from the library's dependencies,
# thereby reducing the size of the R class for that library
android.nonTransitiveRClass=true
android.enableJetifier=true
android.injected.testOnly=false
isOfficialVersion=false

SDK_TEST=false


LINK_HOST_VERSION=*********
LINK_SDK_VERSION=*********
WLHARDWARE_VERSION=********
LINK_COMMON_VERSION=*******
LINK_HID_VERSION=********

# Gradle Proxy Configuration
#systemProp.http.proxyHost=**************
#systemProp.http.proxyPort=7890
#systemProp.https.proxyHost=**************
#systemProp.https.proxyPort=7890

# ?????
#+--- project :linkHost
#|    +--- project :linkCommon
#|    +--- project :linkSdk
#|    |    +--- project :wlhardware
#|    |    \--- project :linkCommon
JUNIT_VERSION=5.9.3
MOCKITO_VERSION=3.12.4